#!/bin/bash

# 🔗 TESTE DE INTEGRAÇÃO - FLUXO COMPLETO DE RESET DE SENHA
# =========================================================

echo "🔗 TESTE DE INTEGRAÇÃO - FLUXO COMPLETO DE RESET DE SENHA"
echo "=========================================================="
echo ""

# Configurações
API_URL="https://portal.evo-eden.site/api"
FRONTEND_URL="https://portal.evo-eden.site"
TEST_EMAIL="<EMAIL>"

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para verificar resposta da API
check_api_response() {
    local response="$1"
    local expected_status="$2"
    local test_name="$3"
    
    if echo "$response" | jq -e . >/dev/null 2>&1; then
        local status=$(echo "$response" | jq -r '.success // .error // "unknown"')
        echo -e "${GREEN}✅ PASS:${NC} $test_name - Resposta JSON válida"
        echo -e "${BLUE}   Status:${NC} $status"
        return 0
    else
        echo -e "${RED}❌ FAIL:${NC} $test_name - Resposta inválida"
        echo -e "${BLUE}   Resposta:${NC} $response"
        return 1
    fi
}

# Função para testar endpoint
test_endpoint() {
    local method="$1"
    local endpoint="$2"
    local data="$3"
    local test_name="$4"
    
    echo -e "${BLUE}ℹ️  TESTE:${NC} $test_name"
    echo -e "${BLUE}   Endpoint:${NC} $method $endpoint"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s "$API_URL$endpoint")
    else
        response=$(curl -s -X "$method" "$API_URL$endpoint" \
            -H "Content-Type: application/json" \
            -d "$data")
    fi
    
    check_api_response "$response" "success" "$test_name"
    echo ""
}

echo -e "${YELLOW}🔍 FASE 1: VERIFICAÇÃO DE INFRAESTRUTURA${NC}"
echo "============================================="
echo ""

# Teste 1: Verificar se API está funcionando
echo -e "${BLUE}ℹ️  TESTE:${NC} Health Check da API"
health_response=$(curl -s "$API_URL/health")
if echo "$health_response" | jq -e . >/dev/null 2>&1; then
    status=$(echo "$health_response" | jq -r '.status')
    if [ "$status" = "OK" ]; then
        echo -e "${GREEN}✅ PASS:${NC} API está funcionando"
    else
        echo -e "${RED}❌ FAIL:${NC} API com problemas"
    fi
else
    echo -e "${RED}❌ FAIL:${NC} API não responde"
fi
echo ""

# Teste 2: Verificar se frontend está acessível
echo -e "${BLUE}ℹ️  TESTE:${NC} Acessibilidade do Frontend"
frontend_status=$(curl -s -o /dev/null -w "%{http_code}" "$FRONTEND_URL")
if [ "$frontend_status" = "200" ]; then
    echo -e "${GREEN}✅ PASS:${NC} Frontend acessível"
else
    echo -e "${RED}❌ FAIL:${NC} Frontend inacessível (Status: $frontend_status)"
fi
echo ""

echo -e "${YELLOW}🔍 FASE 2: TESTE DO FLUXO DE RESET DE SENHA${NC}"
echo "==============================================="
echo ""

# Teste 3: Solicitar reset de senha
echo -e "${BLUE}ℹ️  TESTE:${NC} Solicitação de reset de senha"
echo -e "${BLUE}   Email:${NC} $TEST_EMAIL"

reset_response=$(curl -s -X POST "$API_URL/auth/forgot-password" \
    -H "Content-Type: application/json" \
    -d "{\"email\": \"$TEST_EMAIL\"}")

if echo "$reset_response" | jq -e . >/dev/null 2>&1; then
    success=$(echo "$reset_response" | jq -r '.success // false')
    error=$(echo "$reset_response" | jq -r '.error // "none"')
    
    if [ "$success" = "true" ]; then
        echo -e "${GREEN}✅ PASS:${NC} Reset solicitado com sucesso"
    elif [ "$error" = "Email não encontrado no sistema" ]; then
        echo -e "${YELLOW}⚠️  EXPECTED:${NC} Email não encontrado (comportamento esperado para email de teste)"
    else
        echo -e "${RED}❌ FAIL:${NC} Erro inesperado: $error"
    fi
else
    echo -e "${RED}❌ FAIL:${NC} Resposta inválida da API"
fi
echo ""

echo -e "${YELLOW}🔍 FASE 3: TESTE DAS NOVAS FUNCIONALIDADES${NC}"
echo "==============================================="
echo ""

# Teste 4: Verificar critérios de senha (nova funcionalidade)
test_endpoint "GET" "/auth/password-criteria" "" "Obter critérios de senha"

# Teste 5: Validar senha inválida (nova funcionalidade)
test_endpoint "POST" "/auth/validate-password" '{"senha": "nbr5410!@#"}' "Validar senha sem maiúscula"

# Teste 6: Validar senha válida (nova funcionalidade)
test_endpoint "POST" "/auth/validate-password" '{"senha": "Nbr5410!@#"}' "Validar senha com maiúscula"

# Teste 7: Validar exemplo de senha válida
test_endpoint "POST" "/auth/validate-password" '{"senha": "MinhaSenh@123"}' "Validar exemplo de senha válida"

echo -e "${YELLOW}🔍 FASE 4: TESTE DE CENÁRIOS ESPECÍFICOS${NC}"
echo "============================================="
echo ""

# Teste 8: Senha muito curta
test_endpoint "POST" "/auth/validate-password" '{"senha": "123"}' "Senha muito curta"

# Teste 9: Senha muito comum
test_endpoint "POST" "/auth/validate-password" '{"senha": "123456"}' "Senha muito comum"

# Teste 10: Senha sem caractere especial
test_endpoint "POST" "/auth/validate-password" '{"senha": "MinhaSenh123"}' "Senha sem caractere especial"

echo -e "${YELLOW}🔍 FASE 5: VERIFICAÇÃO DE COMPATIBILIDADE${NC}"
echo "=============================================="
echo ""

# Teste 11: Login com credenciais existentes
echo -e "${BLUE}ℹ️  TESTE:${NC} Compatibilidade com login existente"
login_response=$(curl -s -X POST "$API_URL/auth/login" \
    -H "Content-Type: application/json" \
    -d '{"email": "admin", "senha": "adminnbr5410!"}')

if echo "$login_response" | jq -e . >/dev/null 2>&1; then
    token=$(echo "$login_response" | jq -r '.token // "none"')
    if [ "$token" != "none" ] && [ "$token" != "null" ]; then
        echo -e "${GREEN}✅ PASS:${NC} Login funcionando (compatibilidade mantida)"
    else
        echo -e "${RED}❌ FAIL:${NC} Login não funcionando"
    fi
else
    echo -e "${RED}❌ FAIL:${NC} Resposta de login inválida"
fi
echo ""

echo "=============================================="
echo -e "${YELLOW}🎯 RESUMO DOS TESTES DE INTEGRAÇÃO${NC}"
echo "=============================================="
echo ""

echo -e "${BLUE}📋 FUNCIONALIDADES TESTADAS:${NC}"
echo "✅ Health Check da API"
echo "✅ Acessibilidade do Frontend"
echo "✅ Fluxo de reset de senha"
echo "✅ Nova API de critérios de senha"
echo "✅ Nova API de validação de senha"
echo "✅ Cenários específicos de validação"
echo "✅ Compatibilidade com sistema existente"
echo ""

echo -e "${BLUE}🔍 CASOS DE SENHA TESTADOS:${NC}"
echo "❌ 'nbr5410!@#' (sem maiúscula) - deve falhar"
echo "✅ 'Nbr5410!@#' (com maiúscula) - deve passar"
echo "✅ 'MinhaSenh@123' (exemplo válido) - deve passar"
echo "❌ '123' (muito curta) - deve falhar"
echo "❌ '123456' (muito comum) - deve falhar"
echo "❌ 'MinhaSenh123' (sem especial) - deve falhar"
echo ""

echo -e "${GREEN}🎉 TESTES DE INTEGRAÇÃO CONCLUÍDOS${NC}"
echo ""

echo -e "${BLUE}📋 PRÓXIMOS PASSOS:${NC}"
echo "1. Deploy das alterações para produção"
echo "2. Teste manual no frontend"
echo "3. Verificação de emails de reset"
echo "4. Monitoramento de logs"
echo ""

echo -e "${BLUE}🌐 URLs PARA TESTE MANUAL:${NC}"
echo "Frontend: $FRONTEND_URL"
echo "Reset de senha: $FRONTEND_URL/login (clique em 'Esqueci minha senha')"
echo "API Health: $API_URL/health"
