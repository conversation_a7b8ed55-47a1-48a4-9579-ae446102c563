-- Portal Evolution Database Schema - REESTRUTURADO
-- Sistema de gerenciamento de ETENs e Ossuários
-- NOVA ESTRUTURA: Referências por códigos ao invés de IDs

-- Tabela de clientes (base da hierarquia)
CREATE TABLE IF NOT EXISTS clientes (
    codigo_cliente VARCHAR(50) PRIMARY KEY,
    cnpj VARCHAR(18) UNIQUE NOT NULL,
    nome_fantasia VARCHAR(255) NOT NULL,
    razao_social VARCHAR(255) NOT NULL,
    cep VARCHAR(9),
    logradouro VARCHAR(255),
    numero VARCHAR(20),
    complemento VARCHAR(100),
    bairro VARCHAR(100),
    cidade VARCHAR(100),
    estado VARCHAR(2),
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de usuários
CREATE TABLE IF NOT EXISTS usuarios (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    se<PERSON>a VARCHAR(255) NOT NULL,
    tipo_usuario VARCHAR(20) NOT NULL CHECK (tipo_usuario IN ('admin', 'cliente')),
    codigo_cliente VARCHAR(50),
    nome VARCHAR(255) NOT NULL,
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (codigo_cliente) REFERENCES clientes(codigo_cliente)
);

-- Tabela de produtos (ETENs e Ossuários)
-- Referência: codigo_cliente + codigo_estacao
CREATE TABLE IF NOT EXISTS produtos (
    codigo_cliente VARCHAR(50) NOT NULL,
    codigo_estacao VARCHAR(50) NOT NULL,
    meses_para_exumar INTEGER NOT NULL DEFAULT 24,
    denominacao VARCHAR(255) NOT NULL,
    observacao TEXT,
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (codigo_cliente, codigo_estacao),
    FOREIGN KEY (codigo_cliente) REFERENCES clientes(codigo_cliente)
);

-- Tabela de blocos
-- Referência: codigo_cliente + codigo_estacao + codigo_bloco
CREATE TABLE IF NOT EXISTS blocos (
    codigo_cliente VARCHAR(50) NOT NULL,
    codigo_estacao VARCHAR(50) NOT NULL,
    codigo_bloco VARCHAR(50) NOT NULL,
    denominacao VARCHAR(255) NOT NULL,
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (codigo_cliente, codigo_estacao, codigo_bloco),
    FOREIGN KEY (codigo_cliente, codigo_estacao) REFERENCES produtos(codigo_cliente, codigo_estacao)
);

-- Tabela de sub-blocos
-- Referência: codigo_cliente + codigo_estacao + codigo_bloco + codigo_sub_bloco
CREATE TABLE IF NOT EXISTS sub_blocos (
    codigo_cliente VARCHAR(50) NOT NULL,
    codigo_estacao VARCHAR(50) NOT NULL,
    codigo_bloco VARCHAR(50) NOT NULL,
    codigo_sub_bloco VARCHAR(50) NOT NULL,
    denominacao VARCHAR(255) NOT NULL,
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco),
    FOREIGN KEY (codigo_cliente, codigo_estacao, codigo_bloco) REFERENCES blocos(codigo_cliente, codigo_estacao, codigo_bloco)
);

-- Tabela de numerações de gavetas (ranges de numeração por sub-bloco)
-- Referência: codigo_cliente + codigo_estacao + codigo_bloco + codigo_sub_bloco
CREATE TABLE IF NOT EXISTS numeracoes_gavetas (
    codigo_cliente VARCHAR(50) NOT NULL,
    codigo_estacao VARCHAR(50) NOT NULL,
    codigo_bloco VARCHAR(50) NOT NULL,
    codigo_sub_bloco VARCHAR(50) NOT NULL,
    numero_inicio INTEGER NOT NULL,
    numero_fim INTEGER NOT NULL,
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_inicio),
    FOREIGN KEY (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco)
        REFERENCES sub_blocos(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco)
);

-- Tabela de gavetas (gerada automaticamente baseada nas numerações)
-- Referência: codigo_cliente + codigo_estacao + codigo_bloco + codigo_sub_bloco + numero_gaveta
CREATE TABLE IF NOT EXISTS gavetas (
    codigo_cliente VARCHAR(50) NOT NULL,
    codigo_estacao VARCHAR(50) NOT NULL,
    codigo_bloco VARCHAR(50) NOT NULL,
    codigo_sub_bloco VARCHAR(50) NOT NULL,
    numero_gaveta INTEGER NOT NULL,
    posicao_x INTEGER DEFAULT 1,
    posicao_y INTEGER DEFAULT 1,
    altura_especial DECIMAL(3,1) DEFAULT 1.0,
    disponivel BOOLEAN DEFAULT true,
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta),
    FOREIGN KEY (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco)
        REFERENCES sub_blocos(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco)
);

-- Tabela de sepultamentos
-- Referência: codigo_cliente + codigo_estacao + codigo_bloco + codigo_sub_bloco + numero_gaveta
CREATE TABLE IF NOT EXISTS sepultamentos (
    id SERIAL PRIMARY KEY,
    codigo_cliente VARCHAR(50) NOT NULL,
    codigo_estacao VARCHAR(50) NOT NULL,
    codigo_bloco VARCHAR(50) NOT NULL,
    codigo_sub_bloco VARCHAR(50) NOT NULL,
    numero_gaveta INTEGER NOT NULL,
    nome_sepultado VARCHAR(255) NOT NULL,
    data_sepultamento DATE NOT NULL,
    hora_sepultamento TIME NOT NULL,
    data_exumacao TIMESTAMP NULL,
    observacoes TEXT,
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta)
        REFERENCES gavetas(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta)
);

-- Tabela de logs de auditoria
-- Referência: codigo_cliente (contexto da ação) + dados específicos da hierarquia
CREATE TABLE IF NOT EXISTS logs_auditoria (
    id SERIAL PRIMARY KEY,
    usuario_id INTEGER NOT NULL,
    codigo_cliente VARCHAR(50), -- Referência hierárquica para facilitar importação
    codigo_estacao VARCHAR(50), -- Contexto do produto (quando aplicável)
    codigo_bloco VARCHAR(50), -- Contexto do bloco (quando aplicável)
    codigo_sub_bloco VARCHAR(50), -- Contexto do sub-bloco (quando aplicável)
    acao VARCHAR(50) NOT NULL,
    tabela_afetada VARCHAR(50) NOT NULL,
    registro_id INTEGER,
    descricao TEXT, -- Descrição da ação realizada
    dados_anteriores JSONB,
    dados_novos JSONB,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id),
    FOREIGN KEY (codigo_cliente) REFERENCES clientes(codigo_cliente)
);

-- Índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_usuarios_email ON usuarios(email);
CREATE INDEX IF NOT EXISTS idx_usuarios_codigo_cliente ON usuarios(codigo_cliente);
CREATE INDEX IF NOT EXISTS idx_clientes_codigo ON clientes(codigo_cliente);
CREATE INDEX IF NOT EXISTS idx_produtos_codigo_cliente ON produtos(codigo_cliente);
CREATE INDEX IF NOT EXISTS idx_produtos_codigo_estacao ON produtos(codigo_cliente, codigo_estacao);
CREATE INDEX IF NOT EXISTS idx_blocos_codigo_cliente ON blocos(codigo_cliente);
CREATE INDEX IF NOT EXISTS idx_blocos_codigo_estacao ON blocos(codigo_cliente, codigo_estacao);
CREATE INDEX IF NOT EXISTS idx_blocos_codigo_bloco ON blocos(codigo_cliente, codigo_estacao, codigo_bloco);
CREATE INDEX IF NOT EXISTS idx_sub_blocos_codigo_cliente ON sub_blocos(codigo_cliente);
CREATE INDEX IF NOT EXISTS idx_sub_blocos_codigo_estacao ON sub_blocos(codigo_cliente, codigo_estacao);
CREATE INDEX IF NOT EXISTS idx_sub_blocos_codigo_bloco ON sub_blocos(codigo_cliente, codigo_estacao, codigo_bloco);
CREATE INDEX IF NOT EXISTS idx_gavetas_codigo_cliente ON gavetas(codigo_cliente);
CREATE INDEX IF NOT EXISTS idx_gavetas_codigo_estacao ON gavetas(codigo_cliente, codigo_estacao);
CREATE INDEX IF NOT EXISTS idx_gavetas_codigo_bloco ON gavetas(codigo_cliente, codigo_estacao, codigo_bloco);
CREATE INDEX IF NOT EXISTS idx_gavetas_disponivel ON gavetas(disponivel);
CREATE INDEX IF NOT EXISTS idx_sepultamentos_codigo_cliente ON sepultamentos(codigo_cliente);
CREATE INDEX IF NOT EXISTS idx_sepultamentos_codigo_estacao ON sepultamentos(codigo_cliente, codigo_estacao);
CREATE INDEX IF NOT EXISTS idx_sepultamentos_codigo_bloco ON sepultamentos(codigo_cliente, codigo_estacao, codigo_bloco);
CREATE INDEX IF NOT EXISTS idx_sepultamentos_gaveta ON sepultamentos(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta);
CREATE INDEX IF NOT EXISTS idx_sepultamentos_data ON sepultamentos(data_sepultamento);
CREATE INDEX IF NOT EXISTS idx_logs_usuario ON logs_auditoria(usuario_id);
CREATE INDEX IF NOT EXISTS idx_logs_data ON logs_auditoria(created_at);
CREATE INDEX IF NOT EXISTS idx_logs_codigo_cliente ON logs_auditoria(codigo_cliente);
CREATE INDEX IF NOT EXISTS idx_logs_codigo_estacao ON logs_auditoria(codigo_cliente, codigo_estacao);
CREATE INDEX IF NOT EXISTS idx_logs_codigo_bloco ON logs_auditoria(codigo_cliente, codigo_estacao, codigo_bloco);
CREATE INDEX IF NOT EXISTS idx_logs_codigo_sub_bloco ON logs_auditoria(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco);
CREATE INDEX IF NOT EXISTS idx_logs_acao ON logs_auditoria(acao);
CREATE INDEX IF NOT EXISTS idx_logs_tabela ON logs_auditoria(tabela_afetada);

-- Inserir dados de exemplo
-- Cliente de exemplo
INSERT INTO clientes (codigo_cliente, cnpj, nome_fantasia, razao_social, cep, logradouro, numero, bairro, cidade, estado)
VALUES ('SAF_001', '18.384.274/0001-78', 'Safra Cemitérios', 'Safra Cemitérios Ltda', '01310-100', 'Av. Paulista', '1000', 'Bela Vista', 'São Paulo', 'SP')
ON CONFLICT (codigo_cliente) DO NOTHING;

-- Usuário admin padrão
INSERT INTO usuarios (email, senha, tipo_usuario, nome)
VALUES ('admin', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'Administrador')
ON CONFLICT (email) DO NOTHING;

-- Usuário cliente de exemplo
INSERT INTO usuarios (email, senha, tipo_usuario, codigo_cliente, nome)
VALUES ('<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'cliente', 'SAF_001', 'Cliente Teste')
ON CONFLICT (email) DO NOTHING;
