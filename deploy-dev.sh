#!/bin/bash

# Script para deploy do ambiente de desenvolvimento
# Portal Evolution - Ultrathink

echo "🔧 PORTAL EVOLUTION - DEPLOY DESENVOLVIMENTO"
echo "============================================="
echo ""

echo "📋 INICIANDO DEPLOY DO AMBIENTE DE DESENVOLVIMENTO..."
echo ""

# Etapa 1: Limpar imagens antigas de desenvolvimento
echo "🗑️  ETAPA 1: Limpando imagens antigas de desenvolvimento..."
docker rmi portal-evolution-backend-dev:latest 2>/dev/null || true
docker rmi portal-evolution-frontend-dev:latest 2>/dev/null || true

# Etapa 2: Construir nova imagem do backend
echo "🔨 ETAPA 2: Construindo nova imagem do backend..."
docker build -f portalcliente/docker/production/Dockerfile.backend -t portal-evolution-backend-dev:latest portalcliente --no-cache

# Etapa 3: Construir nova imagem do frontend
echo "🎨 ETAPA 3: Construindo nova imagem do frontend..."
docker build -f portalcliente/Dockerfile -t portal-evolution-frontend-dev:latest portalcliente --no-cache

# Etapa 4: Atualizar stack de desenvolvimento
echo "🔄 ETAPA 4: Atualizando stack de desenvolvimento..."
docker stack deploy -c docker-compose.dev.yml portal-evolution-dev

# Etapa 5: Aguardar estabilização
echo "⏳ ETAPA 5: Aguardando estabilização dos serviços..."
sleep 20

# Etapa 6: Verificar status
echo "✅ ETAPA 6: Verificando status dos serviços..."
echo ""
echo "📊 STATUS DOS SERVIÇOS DE DESENVOLVIMENTO:"
docker service ls | grep portal-evolution-dev_

echo ""
echo "🐳 CONTAINERS ATIVOS:"
docker ps | grep portal-evolution-dev_

echo ""
echo "🎉 DEPLOY DE DESENVOLVIMENTO CONCLUÍDO!"
echo ""
echo "🔧 Dev: https://portaldev.evo-eden.site"
echo "🌐 Prod: https://portal.evo-eden.site"
echo ""
