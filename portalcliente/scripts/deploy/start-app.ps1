# ===================================
# PORTAL EVOLUTION - DEPLOY AUTOMATICO
# ===================================
# Script para deploy automatico na VPS
# Execute: .\start-app.ps1

param(
    [switch]$Local,      # Executar apenas localmente
    [switch]$Deploy,     # Fazer deploy na VPS
    [switch]$Force,      # Forcar deploy sem confirmacao
    [string]$Message = "Atualizacao automatica via start-app"  # Mensagem do commit
)

# Funcao para log
function Write-Log {
    param([string]$Message, [string]$Color = "Green")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] $Message" -ForegroundColor $Color
}

function Write-Info {
    param([string]$Message)
    Write-Log $Message "Cyan"
}

function Write-Success {
    param([string]$Message)
    Write-Log $Message "Green"
}

function Write-Warning {
    param([string]$Message)
    Write-Log $Message "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-Log $Message "Red"
}

# Definir diretorio do script
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $scriptPath

Write-Host ""
Write-Host "PORTAL EVOLUTION - DEPLOY AUTOMATICO" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan
Write-Host ""

# Verificar se esta no diretorio correto
if (-not (Test-Path "configuracao.env")) {
    Write-Error "Arquivo configuracao.env nao encontrado!"
    Write-Host "Execute este script no diretorio portalcliente" -ForegroundColor Yellow
    exit 1
}

# Carregar configuracoes
Write-Info "Carregando configuracoes..."
$config = @{}
Get-Content "configuracao.env" | ForEach-Object {
    if ($_ -match "^([^#][^=]+)=(.*)$") {
        $config[$matches[1].Trim()] = $matches[2].Trim()
    }
}

$VPS_HOST = $config["VPS_HOST"]
$VPS_USER = $config["VPS_USER"] 
$DOMAIN = $config["DOMAIN"]

Write-Success "Configuracoes carregadas:"
Write-Host "   VPS: $VPS_USER@$VPS_HOST" -ForegroundColor White
Write-Host "   Dominio: $DOMAIN" -ForegroundColor White
Write-Host ""

# Verificar Git
Write-Info "Verificando Git..."
try {
    $gitStatus = git status --porcelain 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Git nao encontrado ou nao e um repositorio Git!"
        exit 1
    }
    Write-Success "Git OK"
} catch {
    Write-Error "Erro ao verificar Git: $_"
    exit 1
}

# Verificar se ha alteracoes
$hasChanges = $false
if ($gitStatus) {
    $hasChanges = $true
    Write-Warning "Alteracoes detectadas:"
    $gitStatus | ForEach-Object { Write-Host "   $_" -ForegroundColor Yellow }
    Write-Host ""
}

# Verificar Node.js
Write-Info "Verificando Node.js..."
$nodeAvailable = $false
try {
    $nodeVersion = node --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Node.js: $nodeVersion"
        $nodeAvailable = $true
    } else {
        Write-Warning "Node.js nao encontrado - apenas deploy sera executado"
    }
} catch {
    Write-Warning "Node.js nao encontrado - apenas deploy sera executado"
}

Write-Host ""

# Decidir acao baseada nos parametros
if ($Local) {
    Write-Info "Modo LOCAL selecionado - executando apenas localmente"

    # Verificar se Node.js esta disponivel
    if (-not $nodeAvailable) {
        Write-Error "Node.js nao encontrado! Instale o Node.js para executar localmente."
        exit 1
    }

    # Verificar se arquivos necessarios existem
    if (-not (Test-Path "server\index.js")) {
        Write-Error "Arquivo server\index.js nao encontrado!"
        exit 1
    }

    if (-not (Test-Path "package.json")) {
        Write-Error "Arquivo package.json nao encontrado!"
        exit 1
    }

    Write-Info "Iniciando aplicacao localmente..."

    # Finalizar processos Node.js existentes
    Get-Process | Where-Object {$_.ProcessName -eq "node"} | Stop-Process -Force -ErrorAction SilentlyContinue
    Start-Sleep -Seconds 2

    # Iniciar backend
    Write-Info "Iniciando backend..."
    $backendScript = @"
Set-Location '$scriptPath\server'
Write-Host 'Iniciando servidor backend...' -ForegroundColor Green
node index.js
"@

    $backend = Start-Process powershell -ArgumentList "-NoExit", "-Command", $backendScript -PassThru
    Write-Success "Backend iniciado (PID: $($backend.Id))"
    Start-Sleep -Seconds 5

    # Iniciar frontend
    Write-Info "Iniciando frontend..."
    $frontendScript = @"
Set-Location '$scriptPath'
Write-Host 'Iniciando servidor frontend...' -ForegroundColor Green
npx vite --host 0.0.0.0 --port 5173
"@

    $frontend = Start-Process powershell -ArgumentList "-NoExit", "-Command", $frontendScript -PassThru
    Write-Success "Frontend iniciado (PID: $($frontend.Id))"
    Start-Sleep -Seconds 10

    # Testar conectividade
    Write-Info "Testando conectividade..."

    # Funcao para testar porta
    function Test-Port {
        param([int]$Port)
        try {
            $connection = New-Object System.Net.Sockets.TcpClient
            $connection.Connect("localhost", $Port)
            $connection.Close()
            return $true
        } catch {
            return $false
        }
    }

    # Testar backend
    $backendReady = $false
    for ($i = 1; $i -le 15; $i++) {
        if (Test-Port 3001) {
            Write-Success "Backend respondendo na porta 3001!"
            $backendReady = $true
            break
        }
        Write-Host "   Aguardando backend... ($i/15)" -ForegroundColor Gray
        Start-Sleep -Seconds 2
    }

    # Testar frontend
    $frontendReady = $false
    for ($i = 1; $i -le 15; $i++) {
        if (Test-Port 5173) {
            Write-Success "Frontend respondendo na porta 5173!"
            $frontendReady = $true
            break
        }
        Write-Host "   Aguardando frontend... ($i/15)" -ForegroundColor Gray
        Start-Sleep -Seconds 2
    }

    Write-Host ""
    if ($backendReady -and $frontendReady) {
        Write-Success "PORTAL EVOLUTION INICIADO COM SUCESSO!"
        Write-Host ""
        Write-Host "ACESSO LOCAL:" -ForegroundColor Cyan
        Write-Host "   Aplicacao: http://localhost:5173" -ForegroundColor White
        Write-Host "   Backend: http://localhost:3001" -ForegroundColor White
        Write-Host ""
        Write-Host "CREDENCIAIS:" -ForegroundColor Cyan
        Write-Host "   Admin: <EMAIL> / adminnbr5410!" -ForegroundColor White
        Write-Host "   Cliente: <EMAIL> / 54321" -ForegroundColor White
        Write-Host ""

        # Abrir navegador
        Write-Info "Abrindo navegador..."
        Start-Process "http://localhost:5173"

    } else {
        Write-Warning "PORTAL EVOLUTION INICIADO COM PROBLEMAS"
        if (-not $backendReady) {
            Write-Warning "Backend nao esta respondendo na porta 3001"
        }
        if (-not $frontendReady) {
            Write-Warning "Frontend nao esta respondendo na porta 5173"
        }
    }

    Write-Host ""
    Write-Host "Aplicacao rodando em background..." -ForegroundColor Green
    Write-Host "Para parar: Get-Process | Where-Object {`$_.ProcessName -eq 'node'} | Stop-Process" -ForegroundColor Gray

    exit 0
}

if (-not $Deploy -and -not $Force) {
    Write-Host "O que voce deseja fazer?" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "1. Executar APENAS LOCALMENTE (desenvolvimento)" -ForegroundColor White
    Write-Host "2. DEPLOY na VPS (producao)" -ForegroundColor White
    Write-Host "3. AMBOS (local + deploy)" -ForegroundColor White
    Write-Host "4. Cancelar" -ForegroundColor White
    Write-Host ""
    
    do {
        $choice = Read-Host "Escolha uma opcao (1-4)"
    } while ($choice -notin @("1", "2", "3", "4"))
    
    switch ($choice) {
        "1" { 
            Write-Info "Executando apenas localmente..."
            & "$PSScriptRoot\scripts\start-portal.ps1"
            exit 0
        }
        "2" { 
            $Deploy = $true 
        }
        "3" { 
            $Deploy = $true
            $runLocal = $true
        }
        "4" { 
            Write-Info "Operacao cancelada."
            exit 0 
        }
    }
}

# Se ha alteracoes e vai fazer deploy, perguntar sobre commit
if ($hasChanges -and $Deploy -and -not $Force) {
    Write-Host ""
    Write-Warning "Ha alteracoes nao commitadas."
    Write-Host "Deseja fazer commit e push antes do deploy? (s/N): " -NoNewline -ForegroundColor Yellow
    $commitChoice = Read-Host
    
    if ($commitChoice -eq "s" -or $commitChoice -eq "S") {
        Write-Info "Fazendo commit das alteracoes..."
        
        # Adicionar todos os arquivos
        git add .
        
        # Fazer commit
        git commit -m $Message
        
        # Fazer push
        Write-Info "Fazendo push para o repositorio..."
        git push origin master
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Codigo enviado para o repositorio!"
        } else {
            Write-Error "Erro ao fazer push!"
            exit 1
        }
    }
}

# Executar localmente se solicitado
if ($runLocal) {
    Write-Host ""
    Write-Info "Iniciando aplicacao localmente..."
    
    # Executar em background
    Start-Job -ScriptBlock {
        Set-Location $using:scriptPath
        & "$using:scriptPath\scripts\start-portal.ps1"
    } -Name "LocalPortal" | Out-Null
    
    Write-Success "Aplicacao local iniciada em background"
    Write-Host "   Acesse: http://localhost:5173" -ForegroundColor White
    Write-Host ""
}

# Deploy na VPS
if ($Deploy) {
    Write-Host ""
    Write-Info "Executando deploy na VPS..."

    # Usar script de deploy dedicado
    if (Test-Path "deploy-vps.ps1") {
        if ($Force) {
            & ".\deploy-vps.ps1" -Force -Message $Message
        } else {
            & ".\deploy-vps.ps1" -Message $Message
        }
    } else {
        Write-Error "Script deploy-vps.ps1 nao encontrado!"
        Write-Host "Execute: .\quick-setup.ps1 para recriar os scripts" -ForegroundColor Yellow
        exit 1
    }
}

Write-Host ""
Write-Info "Operacao concluida!"

# Mostrar jobs em execucao se houver
$jobs = Get-Job -State Running 2>$null
if ($jobs) {
    Write-Host ""
    Write-Info "Jobs em execucao:"
    $jobs | ForEach-Object { Write-Host "   $($_.Name): $($_.State)" -ForegroundColor White }
    Write-Host ""
    Write-Host "Para parar jobs locais: Get-Job | Stop-Job; Get-Job | Remove-Job" -ForegroundColor Gray
}
