# Portal Evolution

Sistema completo de gestão de cemitérios e sepultamentos com interface moderna e funcionalidades avançadas.

## 🚀 Início Rápido

### Opção 1: Script Automático (Recomendado)
```powershell
# Iniciar todo o sistema (Backend + Frontend)
.\start-portal.ps1

# Parar todo o sistema
.\stop-portal.ps1
```

### Opção 2: Scripts Individuais
```powershell
# Iniciar apenas o Backend
.\start-backend.ps1

# Iniciar apenas o Frontend
.\start-frontend.ps1
```

### Opção 3: Manual
```powershell
# Backend
cd server
node index.js

# Frontend (em outro terminal)
cd ..
npm run dev
```

## 🌐 Acesso ao Sistema

- **Frontend**: http://localhost:5173 ou http://localhost:5174
- **Backend API**: http://localhost:3001/api

## 🔑 Credenciais de Teste

### Administrador
- **Email**: `admin`
- **Senha**: `adminnbr5410!`
- **Permissões**: Acesso total ao sistema

### Cliente
- **Email**: `<EMAIL>`
- **Senha**: `cliente123`
- **Permissões**: Gestão de sepultamentos apenas

## 📋 Funcionalidades

### ✅ Dashboard Geral
- Visualização unificada de produtos e gavetas
- Estatísticas em tempo real
- Cards interativos com detalhes
- Indicadores de ocupação

### ✅ Gestão de Sepultamentos
- Cadastro completo de sepultamentos
- Controle de exumações
- Histórico detalhado
- Filtros avançados

### ✅ Gestão de Produtos
- CRUD completo de produtos
- Estrutura hierárquica (Produto → Bloco → Sub-bloco → Gaveta)
- Estatísticas de ocupação
- Associação com clientes

### ✅ Gestão de Usuários
- Criação de usuários admin e cliente
- Controle de acesso baseado em roles
- Associação com produtos específicos
- Gestão de permissões

### ✅ Sistema de Detalhes
- Modais informativos para todos os itens
- Ações contextuais baseadas em permissões
- Visualização completa de dados

## 🔐 Controle de Acesso

### Administrador
- ✅ Criar, editar e remover produtos
- ✅ Criar, editar e desativar usuários
- ✅ Deletar sepultamentos
- ✅ Acesso a logs de auditoria
- ✅ Gestão de clientes

### Cliente
- ✅ Adicionar novos sepultamentos
- ✅ Editar sepultamentos existentes
- ✅ Exumar sepultamentos
- ✅ Visualizar detalhes de produtos
- ❌ Não pode deletar sepultamentos
- ❌ Não pode gerenciar produtos ou usuários

## 🛠️ Tecnologias

### Frontend
- **React 18** com Hooks
- **Vite** para build e desenvolvimento
- **Styled Components** para estilização
- **React Router** para navegação
- **Axios** para requisições HTTP

### Backend
- **Node.js** com Express
- **PostgreSQL** como banco de dados
- **JWT** para autenticação
- **bcrypt** para hash de senhas
- **CORS** configurado

## 📁 Estrutura do Projeto

```
portal-evolution/
├── src/                    # Frontend React
│   ├── components/         # Componentes reutilizáveis
│   ├── pages/             # Páginas da aplicação
│   ├── services/          # Serviços de API
│   └── contexts/          # Contextos React
├── server/                # Backend Node.js
│   ├── routes/            # Rotas da API
│   ├── middleware/        # Middlewares
│   └── database/          # Configuração do banco
├── start-portal.ps1       # Script para iniciar tudo
├── start-backend.ps1      # Script para backend
├── start-frontend.ps1     # Script para frontend
└── stop-portal.ps1        # Script para parar tudo
```

## 🔧 Solução de Problemas

### Erro "net::ERR_CONNECTION_REFUSED"
1. Verifique se o backend está rodando: `.\start-backend.ps1`
2. Aguarde alguns segundos para inicialização
3. Teste a API: http://localhost:3001/api/health

### Tela Branca no Frontend
1. Verifique o console do navegador para erros
2. Certifique-se que o backend está rodando
3. Reinicie o frontend: `.\start-frontend.ps1`

### Problemas de Porta
- Frontend usa porta 5173 (ou 5174 se ocupada)
- Backend usa porta 3001
- Use `.\stop-portal.ps1` para liberar todas as portas

### Problemas de Dependências
```powershell
# Reinstalar dependências do frontend
npm install

# Reinstalar dependências do backend
cd server
npm install
```

## 📞 Suporte

Para problemas ou dúvidas:
1. Verifique os logs no terminal
2. Teste as APIs individualmente
3. Use os scripts de diagnóstico incluídos

## 🎯 Próximos Passos

- [ ] Implementar backup automático
- [ ] Adicionar relatórios em PDF
- [ ] Implementar notificações
- [ ] Adicionar dashboard de analytics
- [ ] Implementar sistema de arquivos
