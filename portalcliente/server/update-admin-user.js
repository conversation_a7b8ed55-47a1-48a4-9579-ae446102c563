const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

// Configuração do banco de dados
const pool = new Pool({
  host: '************',
  port: 5432,
  database: 'dbetens',
  user: 'postgres',
  password: 'ab3780bd73ee4e2804d566ce6fd96209',
  ssl: false,
});

async function updateAdminUser() {
  try {
    console.log('🔧 ATUALIZANDO USUÁRIO ADMIN\n');
    
    // Verificar usuário atual
    console.log('1. 🔍 Verificando usuário admin atual...');
    const currentUser = await pool.query('SELECT * FROM usuarios WHERE email = $1', ['admin']);
    
    if (currentUser.rows.length > 0) {
      console.log('   📋 Usuário admin encontrado:', {
        id: currentUser.rows[0].id,
        email: currentUser.rows[0].email,
        nome: currentUser.rows[0].nome,
        tipo_usuario: currentUser.rows[0].tipo_usuario,
        ativo: currentUser.rows[0].ativo
      });
    } else {
      console.log('   ❌ Usuário admin não encontrado com email "admin"');
      
      // Verificar se já existe com o novo email
      const existingUser = await pool.query('SELECT * FROM usuarios WHERE email = $1', ['<EMAIL>']);
      if (existingUser.rows.length > 0) {
        console.log('   ✅ Usuário já existe <NAME_EMAIL>');
        return;
      }
    }
    
    // Criptografar nova senha
    console.log('\n2. 🔐 Criptografando nova senha...');
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash('adminnbr5410!', saltRounds);
    console.log('   ✅ Senha criptografada');
    
    // Atualizar usuário admin
    console.log('\n3. 📝 Atualizando usuário admin...');
    
    if (currentUser.rows.length > 0) {
      // Atualizar usuário existente
      const updateResult = await pool.query(`
        UPDATE usuarios 
        SET email = $1, senha = $2, nome = $3, updated_at = CURRENT_TIMESTAMP
        WHERE email = $4
        RETURNING *
      `, [
        '<EMAIL>',
        hashedPassword,
        'Maurício Filho',
        'admin'
      ]);
      
      console.log('   ✅ Usuário admin atualizado:', {
        id: updateResult.rows[0].id,
        email: updateResult.rows[0].email,
        nome: updateResult.rows[0].nome,
        tipo_usuario: updateResult.rows[0].tipo_usuario
      });
    } else {
      // Criar novo usuário admin se não existir
      const insertResult = await pool.query(`
        INSERT INTO usuarios (email, senha, nome, tipo_usuario, ativo, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING *
      `, [
        '<EMAIL>',
        hashedPassword,
        'Maurício Filho',
        'admin',
        true
      ]);
      
      console.log('   ✅ Novo usuário admin criado:', {
        id: insertResult.rows[0].id,
        email: insertResult.rows[0].email,
        nome: insertResult.rows[0].nome,
        tipo_usuario: insertResult.rows[0].tipo_usuario
      });
    }
    
    // Verificar resultado final
    console.log('\n4. ✅ Verificando resultado final...');
    const finalUser = await pool.query('SELECT * FROM usuarios WHERE email = $1', ['<EMAIL>']);
    
    if (finalUser.rows.length > 0) {
      console.log('   🎉 Usuário admin configurado com sucesso:', {
        id: finalUser.rows[0].id,
        email: finalUser.rows[0].email,
        nome: finalUser.rows[0].nome,
        tipo_usuario: finalUser.rows[0].tipo_usuario,
        ativo: finalUser.rows[0].ativo
      });
      
      // Testar login
      console.log('\n5. 🧪 Testando login...');
      const isPasswordValid = await bcrypt.compare('adminnbr5410!', finalUser.rows[0].senha);
      console.log('   ✅ Senha válida:', isPasswordValid);
      
      console.log('\n🎯 ATUALIZAÇÃO CONCLUÍDA COM SUCESSO!');
      console.log('\n📋 DADOS PARA LOGIN:');
      console.log('   Email: <EMAIL>');
      console.log('   Senha: adminnbr5410!');
      console.log('   Tipo: admin');
      
    } else {
      console.log('   ❌ Erro: Usuário não foi criado/atualizado corretamente');
    }
    
  } catch (error) {
    console.error('❌ Erro ao atualizar usuário admin:', error);
  } finally {
    await pool.end();
  }
}

updateAdminUser();
