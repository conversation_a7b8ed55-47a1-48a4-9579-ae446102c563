const express = require('express');
const { query } = require('../database/connection');
const { logAction, logProduto, logBloco, logSubBloco } = require('../utils/logger');
const rangeValidationService = require('../services/rangeValidationService');

const router = express.Router();

// Listar produtos
router.get('/', async (req, res) => {
  try {
    const { codigo_cliente, tipo } = req.query;
    
    let whereClause = 'WHERE 1=1'; // Mostrar TODOS os produtos (ativos e inativos)
    let params = [];
    let paramCount = 0;

    // Se não for admin, filtrar por código do cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      paramCount++;
      whereClause += ` AND p.codigo_cliente = $${paramCount}`;
      params.push(req.user.codigo_cliente);
    } else if (codigo_cliente) {
      paramCount++;
      whereClause += ` AND p.codigo_cliente = $${paramCount}`;
      params.push(codigo_cliente);
    }

    const result = await query(`
      SELECT
        p.id,
        p.codigo_cliente,
        p.codigo_estacao,
        p.meses_para_exumar,
        p.denominacao,
        p.observacao,
        p.ativo,
        p.created_at,
        p.updated_at,
        c.nome_fantasia as nome_cliente
      FROM produtos p
      LEFT JOIN clientes c ON p.codigo_cliente = c.codigo_cliente
      ${whereClause}
      ORDER BY p.denominacao
    `, params);

    res.json(result.rows);
  } catch (error) {
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Buscar estatísticas de produtos
router.get('/estatisticas', async (req, res) => {
  try {
    let whereClause = 'WHERE p.ativo = true';
    let params = [];

    // Se não for admin, filtrar por código do cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      whereClause += ' AND p.codigo_cliente = $1';
      params.push(req.user.codigo_cliente);
    }

    const result = await query(`
      SELECT
        p.codigo_cliente,
        p.codigo_estacao,
        p.denominacao,
        p.meses_para_exumar,
        p.observacao,
        p.ativo,
        c.nome_fantasia as nome_cliente,
        COUNT(DISTINCT CONCAT(b.codigo_cliente, b.codigo_estacao, b.codigo_bloco)) as total_blocos,
        COUNT(DISTINCT CONCAT(sb.codigo_cliente, sb.codigo_estacao, sb.codigo_bloco, sb.codigo_sub_bloco)) as total_sub_blocos,
        COUNT(DISTINCT CONCAT(g.codigo_cliente, g.codigo_estacao, g.codigo_bloco, g.codigo_sub_bloco, g.numero_gaveta)) as total_gavetas,
        COUNT(DISTINCT CASE WHEN g.disponivel = false THEN CONCAT(g.codigo_cliente, g.codigo_estacao, g.codigo_bloco, g.codigo_sub_bloco, g.numero_gaveta) END) as gavetas_ocupadas,
        COUNT(DISTINCT CASE WHEN g.disponivel = true THEN CONCAT(g.codigo_cliente, g.codigo_estacao, g.codigo_bloco, g.codigo_sub_bloco, g.numero_gaveta) END) as gavetas_disponiveis,
        ROUND(
          (COUNT(DISTINCT CASE WHEN g.disponivel = false THEN CONCAT(g.codigo_cliente, g.codigo_estacao, g.codigo_bloco, g.codigo_sub_bloco, g.numero_gaveta) END)::decimal /
           NULLIF(COUNT(DISTINCT CONCAT(g.codigo_cliente, g.codigo_estacao, g.codigo_bloco, g.codigo_sub_bloco, g.numero_gaveta)), 0)) * 100, 2
        ) as percentual_ocupacao
      FROM produtos p
      LEFT JOIN clientes c ON p.codigo_cliente = c.codigo_cliente
      LEFT JOIN blocos b ON p.codigo_cliente = b.codigo_cliente AND p.codigo_estacao = b.codigo_estacao AND b.ativo = true
      LEFT JOIN sub_blocos sb ON b.codigo_cliente = sb.codigo_cliente AND b.codigo_estacao = sb.codigo_estacao AND b.codigo_bloco = sb.codigo_bloco AND sb.ativo = true
      LEFT JOIN gavetas g ON sb.codigo_cliente = g.codigo_cliente AND sb.codigo_estacao = g.codigo_estacao AND sb.codigo_bloco = g.codigo_bloco AND sb.codigo_sub_bloco = g.codigo_sub_bloco AND g.ativo = true
      ${whereClause}
      GROUP BY p.codigo_cliente, p.codigo_estacao, p.denominacao, p.meses_para_exumar, p.observacao, p.ativo, c.nome_fantasia
      ORDER BY p.denominacao
    `, params);

    res.json(result.rows);
  } catch (error) {
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Rota de compatibilidade para buscar produto completo por ID (para não quebrar frontend)
// DEVE vir ANTES das rotas com parâmetros dinâmicos
router.get('/:id/completo', async (req, res) => {
  try {
    const { id } = req.params;

    console.log('🔍 Buscando produto completo por ID (compatibilidade):', id);

    // Buscar produto por ID na estrutura antiga
    const produtoResult = await query(`
      SELECT * FROM produtos WHERE id = $1 AND ativo = true
    `, [id]);

    if (produtoResult.rows.length === 0) {
      return res.status(404).json({ error: 'Produto não encontrado' });
    }

    const produto = produtoResult.rows[0];
    console.log('✅ Produto encontrado:', produto.denominacao);

    // Buscar blocos usando a nova estrutura baseada em códigos
    const blocosResult = await query(`
      SELECT b.*
      FROM blocos b
      WHERE b.codigo_cliente = $1 AND b.codigo_estacao = $2 AND b.ativo = true
      ORDER BY b.codigo_bloco
    `, [produto.codigo_cliente, produto.codigo_estacao]);

    // Para cada bloco, buscar sub-blocos com gavetas
    const blocosCompletos = await Promise.all(
      blocosResult.rows.map(async (bloco) => {
        const subBlocosResult = await query(`
          SELECT sb.*
          FROM sub_blocos sb
          WHERE sb.codigo_cliente = $1 AND sb.codigo_estacao = $2 AND sb.codigo_bloco = $3 AND sb.ativo = true
          ORDER BY sb.codigo_sub_bloco
        `, [bloco.codigo_cliente, bloco.codigo_estacao, bloco.codigo_bloco]);

        // Para cada sub-bloco, buscar gavetas e calcular ranges
        const subBlocosComRanges = await Promise.all(
          subBlocosResult.rows.map(async (subBloco) => {
            const gavetasResult = await query(`
              SELECT numero_gaveta
              FROM gavetas
              WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 AND codigo_sub_bloco = $4 AND ativo = true
              ORDER BY numero_gaveta
            `, [subBloco.codigo_cliente, subBloco.codigo_estacao, subBloco.codigo_bloco, subBloco.codigo_sub_bloco]);

            if (gavetasResult.rows.length > 0) {
              const numeros = gavetasResult.rows.map(g => g.numero_gaveta).sort((a, b) => a - b);
              const ranges = [];
              let inicio = numeros[0];
              let fim = numeros[0];

              for (let i = 1; i < numeros.length; i++) {
                if (numeros[i] === fim + 1) {
                  fim = numeros[i];
                } else {
                  ranges.push(inicio === fim ? `${inicio}` : `${inicio}-${fim}`);
                  inicio = numeros[i];
                  fim = numeros[i];
                }
              }

              if (numeros.length > 0) {
                ranges.push(inicio === fim ? `${inicio}` : `${inicio}-${fim}`);
              }

              subBloco.ranges_gavetas = ranges.join(', ');
              subBloco.total_gavetas = numeros.length;
            } else {
              subBloco.ranges_gavetas = '';
              subBloco.total_gavetas = 0;
            }

            return subBloco;
          })
        );

        return {
          ...bloco,
          sub_blocos: subBlocosComRanges,
          total_sub_blocos: subBlocosComRanges.length,
          total_gavetas: subBlocosComRanges.reduce((total, sb) => total + (sb.total_gavetas || 0), 0)
        };
      })
    );

    console.log('✅ Retornando dados completos do produto (compatibilidade)');

    res.json({
      produto: produto,
      blocos: blocosCompletos
    });

  } catch (error) {
    console.error('❌ Erro ao buscar produto completo:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Listar blocos por ID do produto (rota simplificada para nova página de cadastros)
router.get('/:id/blocos', async (req, res) => {
  try {
    const { id } = req.params;

    console.log('🔍 Listando blocos por ID do produto:', id);

    // Buscar produto por ID para obter códigos
    const produtoResult = await query(`
      SELECT codigo_cliente, codigo_estacao FROM produtos WHERE id = $1 AND ativo = true
    `, [id]);

    if (produtoResult.rows.length === 0) {
      return res.status(404).json({ error: 'Produto não encontrado' });
    }

    const { codigo_cliente, codigo_estacao } = produtoResult.rows[0];

    // Buscar blocos usando os códigos
    const result = await query(`
      SELECT
        b.*,
        COUNT(sb.id) as total_sub_blocos
      FROM blocos b
      LEFT JOIN sub_blocos sb ON b.id = sb.bloco_id AND sb.ativo = true
      WHERE b.codigo_cliente = $1 AND b.codigo_estacao = $2 AND b.ativo = true
      GROUP BY b.id
      ORDER BY b.codigo_bloco
    `, [codigo_cliente, codigo_estacao]);

    console.log('✅ Blocos encontrados:', result.rows.length);
    res.json(result.rows);
  } catch (error) {
    console.error('❌ Erro ao listar blocos por ID do produto:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Buscar produto por códigos
router.get('/:codigo_cliente/:codigo_estacao', async (req, res) => {
  try {
    const { codigo_cliente, codigo_estacao } = req.params;
    
    let whereClause = 'WHERE p.codigo_cliente = $1 AND p.codigo_estacao = $2';
    let params = [codigo_cliente, codigo_estacao];

    // Se não for admin, filtrar por código do cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      if (req.user.codigo_cliente !== codigo_cliente) {
        return res.status(403).json({ error: 'Acesso negado' });
      }
    }

    const result = await query(`
      SELECT 
        p.*,
        c.nome_fantasia as nome_cliente
      FROM produtos p
      LEFT JOIN clientes c ON p.codigo_cliente = c.codigo_cliente
      ${whereClause}
    `, params);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Produto não encontrado' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Listar blocos de um produto
router.get('/:codigo_cliente/:codigo_estacao/blocos', async (req, res) => {
  try {
    const { codigo_cliente, codigo_estacao } = req.params;
    
    let whereClause = 'WHERE b.codigo_cliente = $1 AND b.codigo_estacao = $2 AND b.ativo = true';
    let params = [codigo_cliente, codigo_estacao];

    // Se não for admin, verificar se o produto pertence ao cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      if (req.user.codigo_cliente !== codigo_cliente) {
        return res.status(403).json({ error: 'Acesso negado' });
      }
    }

    const result = await query(`
      SELECT b.*
      FROM blocos b
      ${whereClause}
      ORDER BY b.codigo_bloco
    `, params);

    res.json(result.rows);
  } catch (error) {
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Listar sub-blocos de um bloco
router.get('/:codigo_cliente/:codigo_estacao/:codigo_bloco/sub-blocos', async (req, res) => {
  try {
    const { codigo_cliente, codigo_estacao, codigo_bloco } = req.params;

    let whereClause = 'WHERE sb.codigo_cliente = $1 AND sb.codigo_estacao = $2 AND sb.codigo_bloco = $3 AND sb.ativo = true';
    let params = [codigo_cliente, codigo_estacao, codigo_bloco];

    // Se não for admin, verificar se o bloco pertence ao cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      if (req.user.codigo_cliente !== codigo_cliente) {
        return res.status(403).json({ error: 'Acesso negado' });
      }
    }

    const result = await query(`
      SELECT sb.*
      FROM sub_blocos sb
      ${whereClause}
      ORDER BY sb.codigo_sub_bloco
    `, params);

    // Para cada sub-bloco, buscar as gavetas separadamente
    for (let subBloco of result.rows) {
      try {
        const gavetasResult = await query(`
          SELECT numero_gaveta
          FROM gavetas
          WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 AND codigo_sub_bloco = $4 AND ativo = true
          ORDER BY numero_gaveta
        `, [subBloco.codigo_cliente, subBloco.codigo_estacao, subBloco.codigo_bloco, subBloco.codigo_sub_bloco]);

        if (gavetasResult.rows.length > 0) {
          const numeros = gavetasResult.rows.map(g => g.numero_gaveta).sort((a, b) => a - b);
          const ranges = [];
          let inicio = numeros[0];
          let fim = numeros[0];

          for (let i = 1; i < numeros.length; i++) {
            if (numeros[i] === fim + 1) {
              fim = numeros[i];
            } else {
              ranges.push(inicio === fim ? `${inicio}` : `${inicio}-${fim}`);
              inicio = numeros[i];
              fim = numeros[i];
            }
          }

          if (numeros.length > 0) {
            ranges.push(inicio === fim ? `${inicio}` : `${inicio}-${fim}`);
          }

          subBloco.ranges_gavetas = ranges.join(', ');
          subBloco.total_gavetas = numeros.length;
        } else {
          subBloco.ranges_gavetas = '';
          subBloco.total_gavetas = 0;
        }
      } catch (error) {
        console.error('Erro ao buscar gavetas para sub-bloco:', error);
        subBloco.ranges_gavetas = '';
        subBloco.total_gavetas = 0;
      }
    }

    res.json(result.rows);
  } catch (error) {
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar novo produto (apenas admin)
router.post('/', async (req, res) => {
  if (req.user.tipo_usuario !== 'admin') {
    return res.status(403).json({ error: 'Acesso negado. Apenas administradores.' });
  }

  try {
    const { codigo_cliente, codigo_estacao, meses_para_exumar, denominacao, observacao, tipo } = req.body;

    console.log('🔍 Dados recebidos para criação de produto:', {
      codigo_cliente,
      codigo_estacao,
      meses_para_exumar,
      denominacao,
      observacao,
      tipo
    });

    if (!codigo_cliente || !codigo_estacao || !denominacao) {
      return res.status(400).json({ error: 'Código do cliente, código da estação e denominação são obrigatórios' });
    }

    // Verificar se o código da estação já existe para este cliente
    const existingEstacao = await query(
      'SELECT codigo_cliente, codigo_estacao FROM produtos WHERE codigo_cliente = $1 AND codigo_estacao = $2',
      [codigo_cliente, codigo_estacao]
    );

    if (existingEstacao.rows.length > 0) {
      return res.status(400).json({ error: 'Código da estação já existe para este cliente' });
    }

    console.log('✅ Validações passaram, inserindo produto...');

    const result = await query(`
      INSERT INTO produtos (codigo_cliente, codigo_estacao, meses_para_exumar, denominacao, observacao, nome, tipo, descricao, localizacao, ativo)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING *
    `, [
      codigo_cliente,
      codigo_estacao,
      meses_para_exumar || 24,
      denominacao,
      observacao || '',
      denominacao, // nome = denominacao
      tipo || 'ETEN', // tipo padrão
      observacao || '', // descricao = observacao
      codigo_estacao, // localizacao = codigo_estacao
      true
    ]);

    const novoProduto = result.rows[0];
    console.log('✅ Produto inserido no banco de dados:', novoProduto.id);

    // Registrar log de criação
    await logProduto(
      req.user.id,
      'CREATE',
      novoProduto,
      null,
      req.ip,
      req.get('User-Agent')
    );

    console.log('✅ Log de criação registrado');

    res.status(201).json({
      message: 'Produto cadastrado com sucesso',
      produto: novoProduto
    });

  } catch (error) {
    console.error('❌ Erro ao criar produto:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar produto (apenas admin)
router.put('/:codigo_cliente/:codigo_estacao', async (req, res) => {
  if (req.user.tipo_usuario !== 'admin') {
    return res.status(403).json({ error: 'Acesso negado. Apenas administradores.' });
  }

  try {
    const { codigo_cliente, codigo_estacao } = req.params;
    const { meses_para_exumar, denominacao, observacao, ativo } = req.body;

    // Buscar dados anteriores
    const dadosAnteriores = await query(
      'SELECT * FROM produtos WHERE codigo_cliente = $1 AND codigo_estacao = $2',
      [codigo_cliente, codigo_estacao]
    );

    if (dadosAnteriores.rows.length === 0) {
      return res.status(404).json({ error: 'Produto não encontrado' });
    }

    const result = await query(`
      UPDATE produtos
      SET meses_para_exumar = $1, denominacao = $2, observacao = $3, ativo = $4, updated_at = CURRENT_TIMESTAMP
      WHERE codigo_cliente = $5 AND codigo_estacao = $6
      RETURNING *
    `, [
      meses_para_exumar || dadosAnteriores.rows[0].meses_para_exumar,
      denominacao || dadosAnteriores.rows[0].denominacao,
      observacao !== undefined ? observacao : dadosAnteriores.rows[0].observacao,
      ativo !== undefined ? ativo : dadosAnteriores.rows[0].ativo,
      codigo_cliente,
      codigo_estacao
    ]);

    const produtoAtualizado = result.rows[0];

    // Registrar log de edição
    await logProduto(
      req.user.id,
      'EDIT',
      produtoAtualizado,
      dadosAnteriores.rows[0],
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      message: 'Produto atualizado com sucesso',
      produto: produtoAtualizado
    });

  } catch (error) {
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar produto - VERSÃO CORRIGIDA PARA produtos_new.js
router.delete('/:id', async (req, res) => {
  console.log('🗑️ Rota DELETE /produtos/:id chamada (produtos_new.js)');
  console.log('🗑️ Parâmetros:', req.params);
  console.log('🗑️ Usuário:', req.user?.email, 'Tipo:', req.user?.tipo_usuario);
  console.log('🗑️ Headers:', req.headers.authorization ? 'Token presente' : 'Token ausente');

  // VALIDAÇÃO DE AUTENTICAÇÃO MELHORADA
  if (!req.user) {
    console.log('❌ Usuário não autenticado');
    return res.status(401).json({ error: 'Usuário não autenticado' });
  }

  if (req.user.tipo_usuario !== 'admin') {
    console.log('❌ Acesso negado - usuário não é admin:', req.user.tipo_usuario);
    return res.status(403).json({ error: 'Acesso negado. Apenas administradores podem deletar produtos.' });
  }

  console.log('✅ Usuário autenticado e autorizado:', req.user.email);

  try {
    const { id } = req.params;

    console.log('🗑️ Tentando deletar produto:', id);

    // Verificar se o produto existe
    const produtoExistente = await query('SELECT * FROM produtos WHERE id = $1', [id]);

    if (produtoExistente.rows.length === 0) {
      console.log('❌ Produto não encontrado:', id);
      return res.status(404).json({ error: 'Produto não encontrado' });
    }

    console.log('✅ Produto encontrado:', produtoExistente.rows[0].denominacao);

    // VALIDAÇÃO HIERÁRQUICA MELHORADA
    console.log('🔍 Iniciando validação hierárquica para produto:', id);

    // CASO 1: Verificar se há blocos associados ao produto (APENAS ATIVOS - CORREÇÃO)
    const blocosAssociados = await query(`
      SELECT COUNT(*) as total, STRING_AGG(nome, ', ') as nomes
      FROM blocos
      WHERE codigo_cliente = (SELECT codigo_cliente FROM produtos WHERE id = $1)
        AND codigo_estacao = (SELECT codigo_estacao FROM produtos WHERE id = $1)
        AND ativo = true
    `, [id]);

    console.log('🔍 Blocos ATIVOS associados encontrados:', blocosAssociados.rows[0]);

    if (blocosAssociados.rows[0].total > 0) {
      console.log('❌ Não é possível deletar - há blocos ATIVOS associados');
      return res.status(400).json({
        error: `Não é possível deletar o produto pois há ${blocosAssociados.rows[0].total} bloco(s) ativo(s) associado(s): ${blocosAssociados.rows[0].nomes}. Delete os blocos primeiro.`
      });
    }

    // CASO 2: Verificar se há sub-blocos ATIVOS associados (CORREÇÃO)
    const subBlocosAssociados = await query(`
      SELECT COUNT(*) as total, STRING_AGG(nome, ', ') as nomes
      FROM sub_blocos
      WHERE codigo_cliente = (SELECT codigo_cliente FROM produtos WHERE id = $1)
        AND codigo_estacao = (SELECT codigo_estacao FROM produtos WHERE id = $1)
        AND ativo = true
    `, [id]);

    console.log('🔍 Sub-blocos ATIVOS associados encontrados:', subBlocosAssociados.rows[0]);

    if (subBlocosAssociados.rows[0].total > 0) {
      console.log('❌ Não é possível deletar - há sub-blocos ATIVOS associados');
      return res.status(400).json({
        error: `Não é possível deletar o produto pois há ${subBlocosAssociados.rows[0].total} sub-bloco(s) ativo(s) associado(s): ${subBlocosAssociados.rows[0].nomes}. Delete os sub-blocos primeiro.`
      });
    }

    // CASO 3: Verificar se há gavetas ATIVAS associadas (CORREÇÃO)
    const gavetasAssociadas = await query(`
      SELECT COUNT(*) as total
      FROM gavetas
      WHERE codigo_cliente = (SELECT codigo_cliente FROM produtos WHERE id = $1)
        AND codigo_estacao = (SELECT codigo_estacao FROM produtos WHERE id = $1)
        AND ativo = true
    `, [id]);

    console.log('🔍 Gavetas ATIVAS associadas encontradas:', gavetasAssociadas.rows[0]);

    if (gavetasAssociadas.rows[0].total > 0) {
      console.log('❌ Não é possível deletar - há gavetas ATIVAS associadas');
      return res.status(400).json({
        error: `Não é possível deletar o produto pois há ${gavetasAssociadas.rows[0].total} gaveta(s) ativa(s) associada(s). Delete os ranges de gavetas primeiro.`
      });
    }

    // CASO 4: Verificar se há sepultamentos ativos (CORREÇÃO)
    const sepultamentosAtivos = await query(`
      SELECT COUNT(*) as total
      FROM sepultamentos
      WHERE codigo_cliente = (SELECT codigo_cliente FROM produtos WHERE id = $1)
        AND codigo_estacao = (SELECT codigo_estacao FROM produtos WHERE id = $1)
        AND ativo = true AND data_exumacao IS NULL
    `, [id]);

    console.log('🔍 Sepultamentos ativos encontrados:', sepultamentosAtivos.rows[0]);

    if (sepultamentosAtivos.rows[0].total > 0) {
      console.log('❌ Não é possível deletar - há sepultamentos ativos');
      return res.status(400).json({
        error: `Não é possível deletar o produto pois há ${sepultamentosAtivos.rows[0].total} sepultamento(s) ativo(s). Exume os sepultamentos primeiro.`
      });
    }

    console.log('✅ Validação hierárquica aprovada - produto pode ser deletado');
    console.log('🔍 Resumo das validações (apenas registros ATIVOS):');
    console.log('  - Blocos ativos:', blocosAssociados.rows[0].total);
    console.log('  - Sub-blocos ativos:', subBlocosAssociados.rows[0].total);
    console.log('  - Gavetas ativas:', gavetasAssociadas.rows[0].total);
    console.log('  - Sepultamentos ativos:', sepultamentosAtivos.rows[0].total);

    // Se chegou até aqui, pode deletar o produto
    // DELEÇÃO FÍSICA EM CASCATA: gavetas → ranges → sub-blocos → blocos → produto

    const produtoInfo = await query('SELECT codigo_cliente, codigo_estacao FROM produtos WHERE id = $1', [id]);
    const { codigo_cliente, codigo_estacao } = produtoInfo.rows[0];

    console.log('🗑️ DELEÇÃO FÍSICA EM CASCATA para:', codigo_cliente, codigo_estacao);

    // 1. Deletar FISICAMENTE todas as gavetas (ativas e inativas)
    const gavetasResult = await query(`
      DELETE FROM gavetas
      WHERE codigo_cliente = $1 AND codigo_estacao = $2
    `, [codigo_cliente, codigo_estacao]);
    console.log('🗑️ Gavetas DELETADAS FISICAMENTE:', gavetasResult.rowCount);

    // 2. Deletar FISICAMENTE todos os ranges de gavetas
    const rangesResult = await query(`
      DELETE FROM numeracoes_gavetas
      WHERE codigo_cliente = $1 AND codigo_estacao = $2
    `, [codigo_cliente, codigo_estacao]);
    console.log('🗑️ Ranges DELETADOS FISICAMENTE:', rangesResult.rowCount);

    // 3. Deletar FISICAMENTE todos os sub-blocos (ativos e inativos)
    const subBlocosResult = await query(`
      DELETE FROM sub_blocos
      WHERE codigo_cliente = $1 AND codigo_estacao = $2
    `, [codigo_cliente, codigo_estacao]);
    console.log('🗑️ Sub-blocos DELETADOS FISICAMENTE:', subBlocosResult.rowCount);

    // 4. Deletar FISICAMENTE todos os blocos (ativos e inativos)
    const blocosResult = await query(`
      DELETE FROM blocos
      WHERE codigo_cliente = $1 AND codigo_estacao = $2
    `, [codigo_cliente, codigo_estacao]);
    console.log('🗑️ Blocos DELETADOS FISICAMENTE:', blocosResult.rowCount);

    // Registrar log antes de deletar (com tratamento de erro)
    try {
      await logProduto(
        req.user.id,
        'DELETE',
        produtoExistente.rows[0],
        null,
        req.ip,
        req.get('User-Agent')
      );
      console.log('✅ Log de deleção registrado com sucesso');
    } catch (logError) {
      console.error('⚠️ Erro ao registrar log (continuando com deleção):', logError);
      // Não interromper a deleção por erro de log
    }

    // Deletar o produto
    const produtoResult = await query('DELETE FROM produtos WHERE id = $1', [id]);
    console.log('✅ Produto deletado:', produtoResult.rowCount, 'linhas afetadas');

    res.json({
      message: 'Produto deletado com sucesso'
    });

  } catch (error) {
    console.error('❌ Erro ao deletar produto:', error);
    console.error('📋 Stack trace:', error.stack);
    console.error('📋 Detalhes do erro:', {
      message: error.message,
      code: error.code,
      detail: error.detail,
      hint: error.hint
    });

    // TRATAMENTO ESPECÍFICO DE ERROS
    if (error.code === '23503') {
      // Violação de chave estrangeira
      return res.status(400).json({
        error: 'Não é possível deletar o produto pois há registros dependentes associados.'
      });
    } else if (error.code === '23505') {
      // Violação de constraint única
      return res.status(400).json({
        error: 'Conflito de dados ao deletar o produto.'
      });
    } else if (error.message && error.message.includes('permission')) {
      // Erro de permissão
      return res.status(403).json({
        error: 'Permissão insuficiente para deletar o produto.'
      });
    } else {
      // Erro genérico
      return res.status(500).json({
        error: 'Erro interno do servidor ao deletar produto.',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }
});

// Criar novo bloco
router.post('/:codigo_cliente/:codigo_estacao/blocos', async (req, res) => {
  if (req.user.tipo_usuario !== 'admin') {
    return res.status(403).json({ error: 'Acesso negado. Apenas administradores.' });
  }

  try {
    const { codigo_cliente, codigo_estacao } = req.params;
    const { codigo_bloco, denominacao } = req.body;

    console.log('🔍 Dados recebidos para criação de bloco:', {
      codigo_cliente,
      codigo_estacao,
      codigo_bloco,
      denominacao
    });

    if (!codigo_bloco || !denominacao) {
      return res.status(400).json({ error: 'Código do bloco e denominação são obrigatórios' });
    }

    // Verificar se o bloco já existe
    const existingBloco = await query(
      'SELECT codigo_cliente, codigo_estacao, codigo_bloco FROM blocos WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3',
      [codigo_cliente, codigo_estacao, codigo_bloco]
    );

    if (existingBloco.rows.length > 0) {
      return res.status(400).json({ error: 'Código do bloco já existe para este produto' });
    }

    console.log('✅ Validações passaram, inserindo bloco...');

    const result = await query(`
      INSERT INTO blocos (codigo_cliente, codigo_estacao, codigo_bloco, denominacao, nome, descricao, ativo)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `, [codigo_cliente, codigo_estacao, codigo_bloco, denominacao, denominacao, denominacao, true]);

    const novoBloco = result.rows[0];
    console.log('✅ Bloco inserido no banco de dados:', novoBloco);

    // Registrar log da criação
    await logBloco(
      req.user.id,
      'CREATE',
      novoBloco,
      null,
      req.ip,
      req.get('User-Agent')
    );

    console.log('✅ Log de criação registrado');

    res.status(201).json({
      message: 'Bloco cadastrado com sucesso',
      bloco: novoBloco
    });

  } catch (error) {
    console.error('❌ Erro ao criar bloco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar bloco
router.put('/:codigo_cliente/:codigo_estacao/:codigo_bloco', async (req, res) => {
  if (req.user.tipo_usuario !== 'admin') {
    return res.status(403).json({ error: 'Acesso negado. Apenas administradores.' });
  }

  try {
    const { codigo_cliente, codigo_estacao, codigo_bloco } = req.params;
    const { denominacao, ativo } = req.body;

    const dadosAnteriores = await query(
      'SELECT * FROM blocos WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3',
      [codigo_cliente, codigo_estacao, codigo_bloco]
    );

    if (dadosAnteriores.rows.length === 0) {
      return res.status(404).json({ error: 'Bloco não encontrado' });
    }

    const result = await query(`
      UPDATE blocos
      SET denominacao = $1, ativo = $2, updated_at = CURRENT_TIMESTAMP
      WHERE codigo_cliente = $3 AND codigo_estacao = $4 AND codigo_bloco = $5
      RETURNING *
    `, [
      denominacao || dadosAnteriores.rows[0].denominacao,
      ativo !== undefined ? ativo : dadosAnteriores.rows[0].ativo,
      codigo_cliente,
      codigo_estacao,
      codigo_bloco
    ]);

    const blocoAtualizado = result.rows[0];

    // Registrar log da edição
    await logBloco(
      req.user.id,
      'EDIT',
      blocoAtualizado,
      dadosAnteriores.rows[0],
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      message: 'Bloco atualizado com sucesso',
      bloco: blocoAtualizado
    });

  } catch (error) {
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Listar sub-blocos por bloco ID (rota simplificada para nova página de cadastros)
router.get('/blocos/:bloco_id/sub-blocos', async (req, res) => {
  try {
    const { bloco_id } = req.params;

    console.log('🔍 ROTA CHAMADA: /blocos/' + bloco_id + '/sub-blocos');
    console.log('📋 Listando sub-blocos do bloco:', bloco_id);

    // Buscar bloco por ID para obter códigos
    console.log('🔍 Executando query para buscar bloco...');
    const blocoResult = await query(`
      SELECT codigo_cliente, codigo_estacao, codigo_bloco, nome FROM blocos WHERE id = $1 AND ativo = true
    `, [bloco_id]);

    console.log('📊 Resultado da busca do bloco:', blocoResult.rows);

    if (blocoResult.rows.length === 0) {
      console.log('❌ Bloco não encontrado:', bloco_id);
      return res.status(404).json({ error: 'Bloco não encontrado' });
    }

    const { codigo_cliente, codigo_estacao, codigo_bloco } = blocoResult.rows[0];
    console.log('✅ Bloco encontrado:', { codigo_cliente, codigo_estacao, codigo_bloco });

    // Buscar sub-blocos usando os códigos (estrutura corrigida e testada)
    const result = await query(`
      SELECT
        sb.*,
        COALESCE(
          (SELECT COUNT(*) FROM numeracoes_gavetas ng
           WHERE ng.codigo_cliente = sb.codigo_cliente
           AND ng.codigo_estacao = sb.codigo_estacao
           AND ng.codigo_bloco = sb.codigo_bloco
           AND ng.codigo_sub_bloco = sb.codigo_sub_bloco
           AND ng.ativo = true), 0
        ) as total_numeracoes,
        COALESCE(
          (SELECT STRING_AGG(ng.numero_inicio::text || '-' || ng.numero_fim::text, ', ' ORDER BY ng.numero_inicio)
           FROM numeracoes_gavetas ng
           WHERE ng.codigo_cliente = sb.codigo_cliente
           AND ng.codigo_estacao = sb.codigo_estacao
           AND ng.codigo_bloco = sb.codigo_bloco
           AND ng.codigo_sub_bloco = sb.codigo_sub_bloco
           AND ng.ativo = true), ''
        ) as ranges_gavetas,
        COALESCE(
          (SELECT SUM(ng.numero_fim - ng.numero_inicio + 1)
           FROM numeracoes_gavetas ng
           WHERE ng.codigo_cliente = sb.codigo_cliente
           AND ng.codigo_estacao = sb.codigo_estacao
           AND ng.codigo_bloco = sb.codigo_bloco
           AND ng.codigo_sub_bloco = sb.codigo_sub_bloco
           AND ng.ativo = true), 0
        ) as total_gavetas
      FROM sub_blocos sb
      WHERE sb.codigo_cliente = $1 AND sb.codigo_estacao = $2 AND sb.codigo_bloco = $3 AND sb.ativo = true
      ORDER BY sb.codigo_sub_bloco
    `, [codigo_cliente, codigo_estacao, codigo_bloco]);

    console.log('✅ Sub-blocos encontrados:', result.rows.length);

    // Log detalhado dos sub-blocos encontrados
    result.rows.forEach(sb => {
      console.log(`📋 Sub-bloco: ${sb.nome} | Ranges: ${sb.ranges_gavetas} | Total gavetas: ${sb.total_gavetas}`);
    });

    res.json(result.rows);
  } catch (error) {
    console.error('❌ Erro ao listar sub-blocos por ID do bloco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar bloco (rota simplificada)
router.post('/blocos', async (req, res) => {
  try {
    const { nome, codigo_bloco, descricao, produto_id, ativo = true } = req.body;

    console.log('🔨 Criando novo bloco:', { nome, codigo_bloco, produto_id });

    // Buscar produto para obter códigos
    const produtoResult = await query(`
      SELECT codigo_cliente, codigo_estacao FROM produtos WHERE id = $1 AND ativo = true
    `, [produto_id]);

    if (produtoResult.rows.length === 0) {
      return res.status(404).json({ error: 'Produto não encontrado' });
    }

    const { codigo_cliente, codigo_estacao } = produtoResult.rows[0];

    const result = await query(`
      INSERT INTO blocos (nome, codigo_bloco, descricao, codigo_cliente, codigo_estacao, ativo)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `, [nome, codigo_bloco, descricao, codigo_cliente, codigo_estacao, ativo]);

    console.log('✅ Bloco criado com sucesso:', result.rows[0]);
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('❌ Erro ao criar bloco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar bloco (rota simplificada)
router.put('/blocos/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { nome, codigo_bloco, descricao, ativo } = req.body;

    console.log('🔄 Atualizando bloco:', { id, nome, codigo_bloco });

    const result = await query(`
      UPDATE blocos
      SET nome = $1, codigo_bloco = $2, descricao = $3, ativo = $4
      WHERE id = $5
      RETURNING *
    `, [nome, codigo_bloco, descricao, ativo, id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Bloco não encontrado' });
    }

    console.log('✅ Bloco atualizado com sucesso:', result.rows[0]);
    res.json(result.rows[0]);
  } catch (error) {
    console.error('❌ Erro ao atualizar bloco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar bloco (rota simplificada)
router.delete('/blocos/:id', async (req, res) => {
  try {
    const { id } = req.params;

    console.log('🗑️ Deletando bloco:', id);

    // Buscar bloco para obter códigos
    const blocoResult = await query('SELECT * FROM blocos WHERE id = $1', [id]);
    if (blocoResult.rows.length === 0) {
      return res.status(404).json({ error: 'Bloco não encontrado' });
    }

    const bloco = blocoResult.rows[0];

    // Validar integridade hierárquica usando o novo serviço
    const validationResult = await rangeValidationService.validateDeletion('bloco', {
      codigo_cliente: bloco.codigo_cliente,
      codigo_estacao: bloco.codigo_estacao,
      codigo_bloco: bloco.codigo_bloco
    });

    if (!validationResult.canDelete) {
      return res.status(400).json({ error: validationResult.error });
    }

    const result = await query(`
      UPDATE blocos SET ativo = false WHERE id = $1 RETURNING *
    `, [id]);

    console.log('✅ Bloco deletado com sucesso:', result.rows[0]);
    res.json({ message: 'Bloco deletado com sucesso' });
  } catch (error) {
    console.error('❌ Erro ao deletar bloco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar sub-bloco (rota simplificada) - SEM RANGES OBRIGATÓRIOS
router.post('/sub-blocos', async (req, res) => {
  try {
    const { nome, codigo_sub_bloco, descricao, bloco_id, ativo = true } = req.body;

    console.log('🔨 Criando novo sub-bloco SEM ranges:', { nome, codigo_sub_bloco, bloco_id });

    // Validar campos obrigatórios
    if (!nome || !codigo_sub_bloco || !bloco_id) {
      return res.status(400).json({ error: 'Nome, código do sub-bloco e ID do bloco são obrigatórios' });
    }

    // Buscar bloco para obter códigos
    const blocoResult = await query(`
      SELECT codigo_cliente, codigo_estacao, codigo_bloco FROM blocos WHERE id = $1 AND ativo = true
    `, [bloco_id]);

    if (blocoResult.rows.length === 0) {
      return res.status(404).json({ error: 'Bloco não encontrado' });
    }

    const { codigo_cliente, codigo_estacao, codigo_bloco } = blocoResult.rows[0];

    // Verificar se código do sub-bloco já existe
    const existingSubBloco = await query(`
      SELECT id FROM sub_blocos
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 AND codigo_sub_bloco = $4 AND ativo = true
    `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco]);

    if (existingSubBloco.rows.length > 0) {
      return res.status(400).json({
        error: `Já existe um sub-bloco com o código "${codigo_sub_bloco}" neste bloco`
      });
    }

    // Criar sub-bloco SEM ranges (ranges serão gerenciados separadamente)
    const result = await query(`
      INSERT INTO sub_blocos (nome, codigo_sub_bloco, descricao, codigo_cliente, codigo_estacao, codigo_bloco, bloco_id, ativo)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `, [nome, codigo_sub_bloco, descricao, codigo_cliente, codigo_estacao, codigo_bloco, bloco_id, ativo]);

    console.log('✅ Sub-bloco criado com sucesso (sem ranges):', result.rows[0]);
    res.status(201).json({
      message: 'Sub-bloco criado com sucesso. Use "Gerenciar Ranges" para adicionar numerações de gavetas.',
      subBloco: result.rows[0]
    });
  } catch (error) {
    console.error('❌ Erro ao criar sub-bloco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar sub-bloco (rota simplificada)
router.put('/sub-blocos/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { nome, codigo_sub_bloco, descricao, numero_inicio, numero_fim, ativo } = req.body;

    console.log('🔄 Atualizando sub-bloco:', { id, nome, codigo_sub_bloco, numero_inicio, numero_fim });

    // Buscar sub-bloco atual
    const currentSubBloco = await query('SELECT * FROM sub_blocos WHERE id = $1', [id]);
    if (currentSubBloco.rows.length === 0) {
      return res.status(404).json({ error: 'Sub-bloco não encontrado' });
    }

    const subBloco = currentSubBloco.rows[0];

    // Verificar se há sepultamentos ativos no range atual antes de alterar
    if (numero_inicio !== subBloco.numero_inicio || numero_fim !== subBloco.numero_fim) {
      const sepultamentosAtivos = await query(`
        SELECT COUNT(*) as count, STRING_AGG(s.nome_sepultado, ', ') as nomes
        FROM sepultamentos s
        WHERE s.codigo_cliente = $1 AND s.codigo_estacao = $2 AND s.codigo_bloco = $3 AND s.codigo_sub_bloco = $4
        AND s.numero_gaveta BETWEEN $5 AND $6 AND s.ativo = true AND s.data_exumacao IS NULL
      `, [subBloco.codigo_cliente, subBloco.codigo_estacao, subBloco.codigo_bloco, subBloco.codigo_sub_bloco,
          Math.min(subBloco.numero_inicio, numero_inicio), Math.max(subBloco.numero_fim, numero_fim)]);

      if (parseInt(sepultamentosAtivos.rows[0].count) > 0) {
        return res.status(400).json({
          error: `Não é possível alterar o range de gavetas pois há ${sepultamentosAtivos.rows[0].count} sepultamento(s) ativo(s): ${sepultamentosAtivos.rows[0].nomes}. Exume os sepultamentos primeiro.`
        });
      }
    }

    // Verificar sobreposição de ranges (excluindo o próprio sub-bloco)
    const overlapCheck = await query(`
      SELECT * FROM sub_blocos
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 AND ativo = true AND id != $4
      AND (
        ($5 >= numero_inicio AND $5 <= numero_fim) OR
        ($6 >= numero_inicio AND $6 <= numero_fim) OR
        ($5 <= numero_inicio AND $6 >= numero_fim)
      )
    `, [subBloco.codigo_cliente, subBloco.codigo_estacao, subBloco.codigo_bloco, id, numero_inicio, numero_fim]);

    if (overlapCheck.rows.length > 0) {
      return res.status(400).json({
        error: `Range de gavetas conflita com o sub-bloco "${overlapCheck.rows[0].nome}" (gavetas ${overlapCheck.rows[0].numero_inicio}-${overlapCheck.rows[0].numero_fim})`
      });
    }

    const result = await query(`
      UPDATE sub_blocos
      SET nome = $1, codigo_sub_bloco = $2, descricao = $3, numero_inicio = $4, numero_fim = $5, ativo = $6
      WHERE id = $7
      RETURNING *
    `, [nome, codigo_sub_bloco, descricao, numero_inicio, numero_fim, ativo, id]);

    console.log('✅ Sub-bloco atualizado com sucesso:', result.rows[0]);
    res.json(result.rows[0]);
  } catch (error) {
    console.error('❌ Erro ao atualizar sub-bloco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar sub-bloco (rota simplificada)
router.delete('/sub-blocos/:id', async (req, res) => {
  try {
    const { id } = req.params;

    console.log('🗑️ Deletando sub-bloco:', id);

    // Buscar sub-bloco para obter códigos
    const subBlocoResult = await query('SELECT * FROM sub_blocos WHERE id = $1', [id]);
    if (subBlocoResult.rows.length === 0) {
      return res.status(404).json({ error: 'Sub-bloco não encontrado' });
    }

    const subBloco = subBlocoResult.rows[0];

    // Validar integridade hierárquica usando o novo serviço
    const validationResult = await rangeValidationService.validateDeletion('sub_bloco', {
      codigo_cliente: subBloco.codigo_cliente,
      codigo_estacao: subBloco.codigo_estacao,
      codigo_bloco: subBloco.codigo_bloco,
      codigo_sub_bloco: subBloco.codigo_sub_bloco
    });

    if (!validationResult.canDelete) {
      return res.status(400).json({ error: validationResult.error });
    }

    // Desativar sub-bloco
    const result = await query(`
      UPDATE sub_blocos SET ativo = false WHERE id = $1 RETURNING *
    `, [id]);

    // Desativar todas as numerações e gavetas associadas
    await query(`
      UPDATE numeracoes_gavetas SET ativo = false
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 AND codigo_sub_bloco = $4
    `, [subBloco.codigo_cliente, subBloco.codigo_estacao, subBloco.codigo_bloco, subBloco.codigo_sub_bloco]);

    await query(`
      UPDATE gavetas SET ativo = false
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 AND codigo_sub_bloco = $4
    `, [subBloco.codigo_cliente, subBloco.codigo_estacao, subBloco.codigo_bloco, subBloco.codigo_sub_bloco]);

    console.log('✅ Sub-bloco e dependências deletados com sucesso:', result.rows[0]);
    res.json({ message: 'Sub-bloco deletado com sucesso' });
  } catch (error) {
    console.error('❌ Erro ao deletar sub-bloco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Função para validar conflitos de numerações de gavetas
async function validateNumeracaoConflicts(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_inicio, numero_fim, excludeId = null) {
  try {
    console.log('🔍 Validando conflitos de numeração:', {
      codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco,
      numero_inicio, numero_fim, excludeId
    });

    // Buscar todas as numerações existentes para o mesmo sub-bloco
    let whereClause = `
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 AND codigo_sub_bloco = $4 AND ativo = true
    `;
    let params = [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco];

    // Excluir a numeração atual se estiver editando
    if (excludeId) {
      whereClause += ` AND id != $5`;
      params.push(excludeId);
    }

    const existingNumeracoes = await query(`
      SELECT id, numero_inicio, numero_fim
      FROM numeracoes_gavetas
      ${whereClause}
      ORDER BY numero_inicio
    `, params);

    console.log('📋 Numerações existentes:', existingNumeracoes.rows);

    // Verificar conflitos com cada numeração existente
    for (const numeracao of existingNumeracoes.rows) {
      const existeInicio = parseInt(numeracao.numero_inicio);
      const existeFim = parseInt(numeracao.numero_fim);
      const novoInicio = parseInt(numero_inicio);
      const novoFim = parseInt(numero_fim);

      // Verificar sobreposição de ranges
      const hasOverlap = (
        (novoInicio >= existeInicio && novoInicio <= existeFim) ||  // Novo início dentro do range existente
        (novoFim >= existeInicio && novoFim <= existeFim) ||        // Novo fim dentro do range existente
        (novoInicio <= existeInicio && novoFim >= existeFim)        // Novo range engloba o existente
      );

      if (hasOverlap) {
        const conflictNumbers = [];
        for (let i = Math.max(novoInicio, existeInicio); i <= Math.min(novoFim, existeFim); i++) {
          conflictNumbers.push(i);
        }

        console.log('❌ Conflito detectado:', {
          existente: `${existeInicio}-${existeFim}`,
          novo: `${novoInicio}-${novoFim}`,
          conflitos: conflictNumbers
        });

        return {
          hasConflict: true,
          message: `Conflito de numeração detectado! As gavetas ${conflictNumbers.join(', ')} já estão sendo usadas no range ${existeInicio}-${existeFim}.`,
          conflictRange: `${existeInicio}-${existeFim}`,
          conflictNumbers: conflictNumbers
        };
      }
    }

    console.log('✅ Nenhum conflito detectado');
    return { hasConflict: false };

  } catch (error) {
    console.error('❌ Erro ao validar conflitos de numeração:', error);
    throw error;
  }
}

// Criar numeração de gavetas
router.post('/numeracoes-gavetas', async (req, res) => {
  try {
    const { codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_inicio, numero_fim, descricao } = req.body;

    console.log('🔨 Criando nova numeração de gavetas:', {
      codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco,
      numero_inicio, numero_fim
    });

    // Validar dados obrigatórios
    if (!codigo_cliente || !codigo_estacao || !codigo_bloco || !codigo_sub_bloco || !numero_inicio || !numero_fim) {
      return res.status(400).json({ error: 'Todos os códigos e números são obrigatórios' });
    }

    // Validar range
    if (parseInt(numero_inicio) >= parseInt(numero_fim)) {
      return res.status(400).json({ error: 'O número de início deve ser menor que o número fim' });
    }

    // Verificar conflitos de numeração
    const conflictCheck = await validateNumeracaoConflicts(
      codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco,
      numero_inicio, numero_fim
    );

    if (conflictCheck.hasConflict) {
      return res.status(400).json({ error: conflictCheck.message });
    }

    // Criar numeração
    const result = await query(`
      INSERT INTO numeracoes_gavetas (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_inicio, numero_fim, descricao, ativo)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_inicio, numero_fim, descricao || '', true]);

    const novaNumeracao = result.rows[0];
    console.log('✅ Numeração criada:', novaNumeracao);

    // Criar gavetas individuais para o range
    console.log('🔨 Criando gavetas individuais...');
    for (let numero = parseInt(numero_inicio); numero <= parseInt(numero_fim); numero++) {
      await query(`
        INSERT INTO gavetas (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta, disponivel, ativo)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        ON CONFLICT (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta)
        DO UPDATE SET ativo = true, disponivel = EXCLUDED.disponivel
      `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero, true, true]);
    }

    console.log(`✅ Criadas ${numero_fim - numero_inicio + 1} gavetas`);

    res.status(201).json({
      message: 'Numeração de gavetas criada com sucesso',
      numeracao: novaNumeracao,
      gavetas_criadas: numero_fim - numero_inicio + 1
    });

  } catch (error) {
    console.error('❌ Erro ao criar numeração de gavetas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar numeração de gavetas
router.put('/numeracoes-gavetas/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { numero_inicio, numero_fim, descricao, ativo } = req.body;

    console.log('🔄 Atualizando numeração de gavetas:', { id, numero_inicio, numero_fim });

    // Buscar numeração atual
    const currentNumeracao = await query('SELECT * FROM numeracoes_gavetas WHERE id = $1', [id]);
    if (currentNumeracao.rows.length === 0) {
      return res.status(404).json({ error: 'Numeração não encontrada' });
    }

    const numeracao = currentNumeracao.rows[0];

    // Validar range
    if (parseInt(numero_inicio) >= parseInt(numero_fim)) {
      return res.status(400).json({ error: 'O número de início deve ser menor que o número fim' });
    }

    // Verificar se há sepultamentos ativos no range atual antes de alterar
    if (numero_inicio !== numeracao.numero_inicio || numero_fim !== numeracao.numero_fim) {
      const sepultamentosAtivos = await query(`
        SELECT COUNT(*) as count, STRING_AGG(s.nome_sepultado, ', ') as nomes
        FROM sepultamentos s
        WHERE s.codigo_cliente = $1 AND s.codigo_estacao = $2 AND s.codigo_bloco = $3 AND s.codigo_sub_bloco = $4
        AND s.numero_gaveta BETWEEN $5 AND $6 AND s.ativo = true AND s.data_exumacao IS NULL
      `, [numeracao.codigo_cliente, numeracao.codigo_estacao, numeracao.codigo_bloco, numeracao.codigo_sub_bloco,
          Math.min(numeracao.numero_inicio, numero_inicio), Math.max(numeracao.numero_fim, numero_fim)]);

      if (parseInt(sepultamentosAtivos.rows[0].count) > 0) {
        return res.status(400).json({
          error: `Não é possível alterar o range de gavetas pois há ${sepultamentosAtivos.rows[0].count} sepultamento(s) ativo(s): ${sepultamentosAtivos.rows[0].nomes}. Exume os sepultamentos primeiro.`
        });
      }
    }

    // Verificar conflitos de numeração (excluindo a numeração atual)
    const conflictCheck = await validateNumeracaoConflicts(
      numeracao.codigo_cliente, numeracao.codigo_estacao, numeracao.codigo_bloco, numeracao.codigo_sub_bloco,
      numero_inicio, numero_fim, id
    );

    if (conflictCheck.hasConflict) {
      return res.status(400).json({ error: conflictCheck.message });
    }

    // Atualizar numeração
    const result = await query(`
      UPDATE numeracoes_gavetas
      SET numero_inicio = $1, numero_fim = $2, descricao = $3, ativo = $4
      WHERE id = $5
      RETURNING *
    `, [numero_inicio, numero_fim, descricao || numeracao.descricao, ativo !== undefined ? ativo : numeracao.ativo, id]);

    console.log('✅ Numeração atualizada com sucesso:', result.rows[0]);

    // Sincronizar gavetas (remover antigas e criar novas)
    if (numero_inicio !== numeracao.numero_inicio || numero_fim !== numeracao.numero_fim) {
      console.log('🔄 Sincronizando gavetas...');

      // Desativar gavetas do range antigo
      await query(`
        UPDATE gavetas SET ativo = false
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 AND codigo_sub_bloco = $4
        AND numero_gaveta BETWEEN $5 AND $6
      `, [numeracao.codigo_cliente, numeracao.codigo_estacao, numeracao.codigo_bloco, numeracao.codigo_sub_bloco,
          numeracao.numero_inicio, numeracao.numero_fim]);

      // Criar gavetas do novo range
      for (let numero = parseInt(numero_inicio); numero <= parseInt(numero_fim); numero++) {
        await query(`
          INSERT INTO gavetas (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta, disponivel, ativo)
          VALUES ($1, $2, $3, $4, $5, $6, $7)
          ON CONFLICT (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta)
          DO UPDATE SET ativo = true, disponivel = EXCLUDED.disponivel
        `, [numeracao.codigo_cliente, numeracao.codigo_estacao, numeracao.codigo_bloco, numeracao.codigo_sub_bloco, numero, true, true]);
      }

      console.log(`✅ Gavetas sincronizadas: ${numero_fim - numero_inicio + 1} gavetas`);
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('❌ Erro ao atualizar numeração de gavetas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar numeração de gavetas
router.delete('/numeracoes-gavetas/:id', async (req, res) => {
  try {
    const { id } = req.params;

    console.log('🗑️ Deletando numeração de gavetas:', id);

    // Buscar numeração para obter códigos
    const numeracaoResult = await query('SELECT * FROM numeracoes_gavetas WHERE id = $1', [id]);
    if (numeracaoResult.rows.length === 0) {
      return res.status(404).json({ error: 'Numeração não encontrada' });
    }

    const numeracao = numeracaoResult.rows[0];

    // Verificar se há sepultamentos ativos no range
    const sepultamentosCheck = await query(`
      SELECT COUNT(*) as count, STRING_AGG(s.nome_sepultado, ', ') as nomes
      FROM sepultamentos s
      WHERE s.codigo_cliente = $1 AND s.codigo_estacao = $2 AND s.codigo_bloco = $3 AND s.codigo_sub_bloco = $4
      AND s.numero_gaveta BETWEEN $5 AND $6 AND s.ativo = true AND s.data_exumacao IS NULL
    `, [numeracao.codigo_cliente, numeracao.codigo_estacao, numeracao.codigo_bloco, numeracao.codigo_sub_bloco,
        numeracao.numero_inicio, numeracao.numero_fim]);

    if (parseInt(sepultamentosCheck.rows[0].count) > 0) {
      return res.status(400).json({
        error: `Não é possível deletar numeração pois há ${sepultamentosCheck.rows[0].count} sepultamento(s) ativo(s): ${sepultamentosCheck.rows[0].nomes}. Exume os sepultamentos primeiro.`
      });
    }

    // CORREÇÃO: Deletar numeração FISICAMENTE (DELETE real)
    // Isso resolve o problema de constraint única ao recriar ranges
    const result = await query(`
      DELETE FROM numeracoes_gavetas WHERE id = $1 RETURNING *
    `, [id]);

    // CORREÇÃO: Deletar gavetas do range FISICAMENTE (DELETE real)
    // Usar função SQL que já foi corrigida anteriormente
    await query(`
      SELECT deletar_gavetas_range($1, $2, $3, $4, $5, $6) as resultado
    `, [numeracao.codigo_cliente, numeracao.codigo_estacao, numeracao.codigo_bloco, numeracao.codigo_sub_bloco,
        numeracao.numero_inicio, numeracao.numero_fim]);

    console.log('✅ Numeração deletada com sucesso:', result.rows[0]);
    console.log(`✅ Gavetas desativadas: ${numeracao.numero_fim - numeracao.numero_inicio + 1} gavetas`);

    res.json({ message: 'Numeração de gavetas deletada com sucesso' });
  } catch (error) {
    console.error('❌ Erro ao deletar numeração de gavetas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Listar numerações de gavetas por sub-bloco
router.get('/sub-blocos/:sub_bloco_id/numeracoes', async (req, res) => {
  try {
    const { sub_bloco_id } = req.params;

    console.log('📋 Listando numerações do sub-bloco:', sub_bloco_id);

    // Buscar sub-bloco para obter códigos
    const subBlocoResult = await query(`
      SELECT codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco FROM sub_blocos WHERE id = $1 AND ativo = true
    `, [sub_bloco_id]);

    if (subBlocoResult.rows.length === 0) {
      return res.status(404).json({ error: 'Sub-bloco não encontrado' });
    }

    const { codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco } = subBlocoResult.rows[0];

    // Buscar numerações usando os códigos
    const result = await query(`
      SELECT
        ng.*,
        (ng.numero_fim - ng.numero_inicio + 1) as total_gavetas,
        COALESCE(
          (SELECT COUNT(*) FROM gavetas g
           WHERE g.codigo_cliente = ng.codigo_cliente
           AND g.codigo_estacao = ng.codigo_estacao
           AND g.codigo_bloco = ng.codigo_bloco
           AND g.codigo_sub_bloco = ng.codigo_sub_bloco
           AND g.numero_gaveta BETWEEN ng.numero_inicio AND ng.numero_fim
           AND g.disponivel = false AND g.ativo = true), 0
        ) as gavetas_ocupadas,
        COALESCE(
          (SELECT COUNT(*) FROM gavetas g
           WHERE g.codigo_cliente = ng.codigo_cliente
           AND g.codigo_estacao = ng.codigo_estacao
           AND g.codigo_bloco = ng.codigo_bloco
           AND g.codigo_sub_bloco = ng.codigo_sub_bloco
           AND g.numero_gaveta BETWEEN ng.numero_inicio AND ng.numero_fim
           AND g.disponivel = true AND g.ativo = true), 0
        ) as gavetas_disponiveis
      FROM numeracoes_gavetas ng
      WHERE ng.codigo_cliente = $1 AND ng.codigo_estacao = $2 AND ng.codigo_bloco = $3 AND ng.codigo_sub_bloco = $4 AND ng.ativo = true
      ORDER BY ng.numero_inicio
    `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco]);

    console.log('✅ Numerações encontradas:', result.rows.length);
    res.json(result.rows);
  } catch (error) {
    console.error('❌ Erro ao listar numerações por sub-bloco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Função para validar integridade antes de deleções
async function validateIntegrityBeforeDelete(tipo, dados) {
  try {
    console.log('🔍 Validando integridade para deleção:', { tipo, dados });

    switch (tipo) {
      case 'produto':
        // Verificar se há blocos associados
        const blocosCount = await query(`
          SELECT COUNT(*) as count FROM blocos
          WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND ativo = true
        `, [dados.codigo_cliente, dados.codigo_estacao]);

        if (parseInt(blocosCount.rows[0].count) > 0) {
          return {
            canDelete: false,
            message: `Não é possível deletar produto pois há ${blocosCount.rows[0].count} bloco(s) associado(s). Delete os blocos primeiro.`
          };
        }
        break;

      case 'bloco':
        // Verificar se há sub-blocos associados
        const subBlocosCount = await query(`
          SELECT COUNT(*) as count FROM sub_blocos
          WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 AND ativo = true
        `, [dados.codigo_cliente, dados.codigo_estacao, dados.codigo_bloco]);

        if (parseInt(subBlocosCount.rows[0].count) > 0) {
          return {
            canDelete: false,
            message: `Não é possível deletar bloco pois há ${subBlocosCount.rows[0].count} sub-bloco(s) associado(s). Delete os sub-blocos primeiro.`
          };
        }
        break;

      case 'sub_bloco':
        // Verificar se há numerações de gavetas associadas
        const numeracoesCount = await query(`
          SELECT COUNT(*) as count FROM numeracoes_gavetas
          WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 AND codigo_sub_bloco = $4 AND ativo = true
        `, [dados.codigo_cliente, dados.codigo_estacao, dados.codigo_bloco, dados.codigo_sub_bloco]);

        if (parseInt(numeracoesCount.rows[0].count) > 0) {
          return {
            canDelete: false,
            message: `Não é possível deletar sub-bloco pois há ${numeracoesCount.rows[0].count} numeração(ões) de gavetas associada(s). Delete as numerações primeiro.`
          };
        }

        // Verificar se há sepultamentos ativos
        const sepultamentosAtivos = await query(`
          SELECT COUNT(*) as count, STRING_AGG(s.nome_sepultado, ', ') as nomes
          FROM sepultamentos s
          WHERE s.codigo_cliente = $1 AND s.codigo_estacao = $2 AND s.codigo_bloco = $3 AND s.codigo_sub_bloco = $4
          AND s.ativo = true AND s.data_exumacao IS NULL
        `, [dados.codigo_cliente, dados.codigo_estacao, dados.codigo_bloco, dados.codigo_sub_bloco]);

        if (parseInt(sepultamentosAtivos.rows[0].count) > 0) {
          return {
            canDelete: false,
            message: `Não é possível deletar sub-bloco pois há ${sepultamentosAtivos.rows[0].count} sepultamento(s) ativo(s): ${sepultamentosAtivos.rows[0].nomes}. Exume os sepultamentos primeiro.`
          };
        }
        break;

      case 'numeracao_gavetas':
        // Verificar se há sepultamentos ativos no range
        const sepultamentosRange = await query(`
          SELECT COUNT(*) as count, STRING_AGG(s.nome_sepultado, ', ') as nomes
          FROM sepultamentos s
          WHERE s.codigo_cliente = $1 AND s.codigo_estacao = $2 AND s.codigo_bloco = $3 AND s.codigo_sub_bloco = $4
          AND s.numero_gaveta BETWEEN $5 AND $6 AND s.ativo = true AND s.data_exumacao IS NULL
        `, [dados.codigo_cliente, dados.codigo_estacao, dados.codigo_bloco, dados.codigo_sub_bloco,
            dados.numero_inicio, dados.numero_fim]);

        if (parseInt(sepultamentosRange.rows[0].count) > 0) {
          return {
            canDelete: false,
            message: `Não é possível deletar numeração pois há ${sepultamentosRange.rows[0].count} sepultamento(s) ativo(s) no range ${dados.numero_inicio}-${dados.numero_fim}: ${sepultamentosRange.rows[0].nomes}. Exume os sepultamentos primeiro.`
          };
        }
        break;
    }

    return { canDelete: true };

  } catch (error) {
    console.error('❌ Erro ao validar integridade:', error);
    throw error;
  }
}

// Função para sincronizar gavetas com numerações
async function syncGavetasWithNumeracoes(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco) {
  try {
    console.log('🔄 Sincronizando gavetas com numerações:', {
      codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco
    });

    // Buscar todas as numerações ativas para o sub-bloco
    const numeracoes = await query(`
      SELECT numero_inicio, numero_fim FROM numeracoes_gavetas
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 AND codigo_sub_bloco = $4 AND ativo = true
      ORDER BY numero_inicio
    `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco]);

    console.log('📋 Numerações encontradas:', numeracoes.rows.length);

    // Criar/atualizar gavetas para cada numeração
    for (const numeracao of numeracoes.rows) {
      for (let numero = numeracao.numero_inicio; numero <= numeracao.numero_fim; numero++) {
        await query(`
          INSERT INTO gavetas (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta, disponivel, ativo)
          VALUES ($1, $2, $3, $4, $5, $6, $7)
          ON CONFLICT (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta)
          DO UPDATE SET ativo = true, disponivel = EXCLUDED.disponivel
        `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero, true, true]);
      }
    }

    console.log('✅ Gavetas sincronizadas com sucesso');

  } catch (error) {
    console.error('❌ Erro ao sincronizar gavetas:', error);
    throw error;
  }
}

// Listar gavetas por sub-bloco
router.get('/sub-blocos/:sub_bloco_id/gavetas', async (req, res) => {
  try {
    const { sub_bloco_id } = req.params;
    const { disponivel, page = 1, limit = 50 } = req.query;

    console.log('📋 Listando gavetas do sub-bloco:', sub_bloco_id);

    // Buscar sub-bloco para obter códigos
    const subBlocoResult = await query(`
      SELECT codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco FROM sub_blocos WHERE id = $1 AND ativo = true
    `, [sub_bloco_id]);

    if (subBlocoResult.rows.length === 0) {
      return res.status(404).json({ error: 'Sub-bloco não encontrado' });
    }

    const { codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco } = subBlocoResult.rows[0];

    // Construir filtros
    let whereClause = `
      WHERE g.codigo_cliente = $1 AND g.codigo_estacao = $2 AND g.codigo_bloco = $3 AND g.codigo_sub_bloco = $4 AND g.ativo = true
    `;
    let params = [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco];

    if (disponivel !== undefined) {
      params.push(disponivel === 'true');
      whereClause += ` AND g.disponivel = $${params.length}`;
    }

    // Paginação
    const offset = (parseInt(page) - 1) * parseInt(limit);
    params.push(parseInt(limit), offset);

    // Buscar gavetas com informações de sepultamento
    const result = await query(`
      SELECT
        g.*,
        s.nome_sepultado,
        s.data_sepultamento,
        s.data_exumacao,
        s.observacoes as observacao_sepultamento,
        CASE
          WHEN s.id IS NOT NULL AND s.data_exumacao IS NULL THEN 'ocupada'
          WHEN s.id IS NOT NULL AND s.data_exumacao IS NOT NULL THEN 'exumada'
          ELSE 'disponivel'
        END as status_gaveta
      FROM gavetas g
      LEFT JOIN sepultamentos s ON g.codigo_cliente = s.codigo_cliente
                                AND g.codigo_estacao = s.codigo_estacao
                                AND g.codigo_bloco = s.codigo_bloco
                                AND g.codigo_sub_bloco = s.codigo_sub_bloco
                                AND g.numero_gaveta = s.numero_gaveta
                                AND s.ativo = true
      ${whereClause}
      ORDER BY g.numero_gaveta
      LIMIT $${params.length - 1} OFFSET $${params.length}
    `, params);

    // Contar total para paginação
    const countResult = await query(`
      SELECT COUNT(*) as total FROM gavetas g ${whereClause}
    `, params.slice(0, -2));

    console.log('✅ Gavetas encontradas:', result.rows.length);

    res.json({
      gavetas: result.rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: parseInt(countResult.rows[0].total),
        pages: Math.ceil(parseInt(countResult.rows[0].total) / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('❌ Erro ao listar gavetas por sub-bloco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Listar gavetas por códigos (NOVA ROTA PARA MODAL DE SEPULTAMENTO)
router.get('/sub-blocos/:codigo_cliente/:codigo_estacao/:codigo_bloco/:codigo_sub_bloco/gavetas', async (req, res) => {
  try {
    const { codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco } = req.params;
    const { disponivel, page = 1, limit = 1000 } = req.query; // Aumentado limite para 1000

    console.log('📋 Listando gavetas por códigos:', {
      codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, disponivel
    });

    // Log específico para debug da gaveta 3241
    if (codigo_bloco === 'BL_004' && codigo_sub_bloco === 'SUB_003') {
      console.log('🔍 DEBUG: Buscando gavetas no bloco BL_004/SUB_003 (onde está a gaveta 3241)');
    }

    // Se não for admin, filtrar por código do cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      if (codigo_cliente !== req.user.codigo_cliente) {
        return res.status(403).json({ error: 'Acesso negado' });
      }
    }

    let whereClause = `WHERE g.codigo_cliente = $1 AND g.codigo_estacao = $2 AND g.codigo_bloco = $3 AND g.codigo_sub_bloco = $4 AND g.ativo = true`;
    let params = [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco];
    let paramCount = 4;

    // LÓGICA SIMPLIFICADA: Filtrar apenas por campo disponivel da tabela gavetas
    if (disponivel !== undefined && (disponivel === 'true' || disponivel === true)) {
      whereClause += ` AND g.disponivel = true`;
      console.log('🔍 Filtrando apenas gavetas com disponivel = true');
    }

    // CONSULTA SIMPLIFICADA: Buscar apenas gavetas com base no campo disponivel
    // Remover paginação para gavetas disponíveis para garantir que todas apareçam
    let queryText = `
      SELECT
        g.*,
        'disponivel' as status_gaveta
      FROM gavetas g
      ${whereClause}
      ORDER BY g.numero_gaveta
    `;

    // Só aplicar paginação se não estiver filtrando por disponível
    if (disponivel === undefined || disponivel === 'false' || disponivel === false) {
      const offset = (page - 1) * limit;
      paramCount++;
      const limitParam = paramCount;
      paramCount++;
      const offsetParam = paramCount;
      params.push(parseInt(limit), parseInt(offset));
      queryText += ` LIMIT $${limitParam} OFFSET $${offsetParam}`;
    }

    const result = await query(queryText, params);

    console.log(`✅ Gavetas encontradas: ${result.rows.length}`);
    console.log(`🔍 Filtro aplicado - disponivel: ${disponivel}`);

    // Log específico para gaveta 2838
    const gaveta2838 = result.rows.find(g => g.numero_gaveta === 2838 || g.numero_gaveta === '2838');
    if (gaveta2838) {
      console.log('🎯 Gaveta 2838 encontrada:', {
        numero_gaveta: gaveta2838.numero_gaveta,
        disponivel: gaveta2838.disponivel,
        ativo: gaveta2838.ativo,
        status_gaveta: gaveta2838.status_gaveta
      });
    }

    // Log específico para gaveta 3241
    const gaveta3241 = result.rows.find(g => g.numero_gaveta === 3241 || g.numero_gaveta === '3241');
    if (gaveta3241) {
      console.log('🎯 Gaveta 3241 encontrada:', {
        numero_gaveta: gaveta3241.numero_gaveta,
        disponivel: gaveta3241.disponivel,
        ativo: gaveta3241.ativo,
        status_gaveta: gaveta3241.status_gaveta
      });
    } else if (codigo_bloco === 'BL_004' && codigo_sub_bloco === 'SUB_003') {
      console.log('❌ Gaveta 3241 NÃO encontrada na consulta do bloco BL_004/SUB_003');
      // Log das gavetas que começam com 32 para debug
      const gavetas32 = result.rows.filter(g =>
        g.numero_gaveta.toString().startsWith('32')
      );
      console.log('🔍 Gavetas que começam com 32:', gavetas32.map(g => ({
        numero: g.numero_gaveta,
        disponivel: g.disponivel,
        ativo: g.ativo
      })));
    }

    res.json(result.rows);
  } catch (error) {
    console.error('❌ Erro ao listar gavetas por códigos:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Buscar gaveta específica
router.get('/gavetas/:codigo_cliente/:codigo_estacao/:codigo_bloco/:codigo_sub_bloco/:numero_gaveta', async (req, res) => {
  try {
    const { codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta } = req.params;

    console.log('🔍 Buscando gaveta específica:', {
      codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta
    });

    const result = await query(`
      SELECT
        g.*,
        s.nome_sepultado,
        s.data_sepultamento,
        s.data_exumacao,
        s.observacoes as observacao_sepultamento,
        s.responsavel_sepultamento,
        s.responsavel_exumacao,
        CASE
          WHEN s.id IS NOT NULL AND s.data_exumacao IS NULL THEN 'ocupada'
          WHEN s.id IS NOT NULL AND s.data_exumacao IS NOT NULL THEN 'exumada'
          ELSE 'disponivel'
        END as status_gaveta
      FROM gavetas g
      LEFT JOIN sepultamentos s ON g.codigo_cliente = s.codigo_cliente
                                AND g.codigo_estacao = s.codigo_estacao
                                AND g.codigo_bloco = s.codigo_bloco
                                AND g.codigo_sub_bloco = s.codigo_sub_bloco
                                AND g.numero_gaveta = s.numero_gaveta
                                AND s.ativo = true
      WHERE g.codigo_cliente = $1 AND g.codigo_estacao = $2 AND g.codigo_bloco = $3
        AND g.codigo_sub_bloco = $4 AND g.numero_gaveta = $5 AND g.ativo = true
    `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Gaveta não encontrada' });
    }

    console.log('✅ Gaveta encontrada:', result.rows[0]);
    res.json(result.rows[0]);
  } catch (error) {
    console.error('❌ Erro ao buscar gaveta específica:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar status de gaveta (disponível/indisponível)
router.put('/gavetas/:codigo_cliente/:codigo_estacao/:codigo_bloco/:codigo_sub_bloco/:numero_gaveta', async (req, res) => {
  try {
    const { codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta } = req.params;
    const { disponivel, observacao } = req.body;

    console.log('🔄 Atualizando status da gaveta:', {
      codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta, disponivel
    });

    // Verificar se há sepultamento ativo antes de marcar como disponível
    if (disponivel === true) {
      const sepultamentoAtivo = await query(`
        SELECT COUNT(*) as count FROM sepultamentos
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3
          AND codigo_sub_bloco = $4 AND numero_gaveta = $5
          AND ativo = true AND data_exumacao IS NULL
      `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta]);

      if (parseInt(sepultamentoAtivo.rows[0].count) > 0) {
        return res.status(400).json({
          error: 'Não é possível marcar gaveta como disponível pois há sepultamento ativo. Exume o sepultamento primeiro.'
        });
      }
    }

    const result = await query(`
      UPDATE gavetas
      SET disponivel = $1, observacao = $2, updated_at = CURRENT_TIMESTAMP
      WHERE codigo_cliente = $3 AND codigo_estacao = $4 AND codigo_bloco = $5
        AND codigo_sub_bloco = $6 AND numero_gaveta = $7
      RETURNING *
    `, [disponivel, observacao, codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Gaveta não encontrada' });
    }

    console.log('✅ Status da gaveta atualizado:', result.rows[0]);
    res.json(result.rows[0]);
  } catch (error) {
    console.error('❌ Erro ao atualizar status da gaveta:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Ativar/Inativar produto (apenas admin)
router.patch('/:id/toggle-status', async (req, res) => {
  try {
    const { id } = req.params;

    console.log('🔐 MIDDLEWARE AUTH - Usuário autenticado:', {
      id: req.user?.id,
      email: req.user?.email,
      tipo: req.user?.tipo_usuario,
      codigo_cliente: req.user?.codigo_cliente
    });

    // Verificar se é admin
    if (!req.user) {
      console.log('❌ Usuário não autenticado no toggle-status');
      return res.status(401).json({ error: 'Usuário não autenticado' });
    }

    if (req.user.tipo_usuario !== 'admin') {
      console.log('❌ Acesso negado - usuário não é admin:', req.user.tipo_usuario);
      return res.status(403).json({ error: 'Acesso negado. Apenas administradores.' });
    }

    console.log('✅ Usuário autorizado para toggle-status:', req.user.email);

    console.log(`🔄 TOGGLE PRODUTO - Iniciando processo para ID: ${id}`);
    console.log(`📊 Request Info:`, {
      method: req.method,
      url: req.url,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date().toISOString()
    });

    // Buscar dados atuais do produto
    const produtoAtual = await query(
      'SELECT * FROM produtos WHERE id = $1',
      [id]
    );

    if (produtoAtual.rows.length === 0) {
      return res.status(404).json({ error: 'Produto não encontrado' });
    }

    const produto = produtoAtual.rows[0];
    const novoStatus = !produto.ativo;

    console.log(`📊 Produto ${produto.denominacao}: ${produto.ativo ? 'ATIVO' : 'INATIVO'} → ${novoStatus ? 'ATIVO' : 'INATIVO'}`);

    // Verificar dados subsequentes para relatório de integridade
    const dadosSubsequentes = await verificarDadosSubsequentes(produto);

    console.log('📋 Verificação de integridade dos dados subsequentes:');
    console.log(`   - Blocos: ${dadosSubsequentes.blocos}`);
    console.log(`   - Sub-blocos: ${dadosSubsequentes.subBlocos}`);
    console.log(`   - Gavetas: ${dadosSubsequentes.gavetas}`);
    console.log(`   - Sepultamentos: ${dadosSubsequentes.sepultamentos}`);
    console.log(`   - Sepultamentos ativos: ${dadosSubsequentes.sepultamentosAtivos}`);

    // VALIDAÇÃO CRÍTICA: Verificar dependências antes da inativação
    if (produto.ativo && !novoStatus) {
      console.log('🔍 Validando dependências para INATIVAÇÃO do produto...');

      // Verificar sepultamentos ativos (crítico)
      if (dadosSubsequentes.sepultamentosAtivos > 0) {
        console.log(`⚠️ AVISO CRÍTICO: ${dadosSubsequentes.sepultamentosAtivos} sepultamentos ativos serão preservados`);

        // Buscar detalhes dos sepultamentos ativos
        const sepultamentosDetalhes = await query(`
          SELECT nome_sepultado, numero_gaveta, data_sepultamento
          FROM sepultamentos
          WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND exumado = false AND ativo = true
          ORDER BY data_sepultamento DESC
          LIMIT 5
        `, [produto.codigo_cliente, produto.codigo_estacao]);

        console.log('📋 Sepultamentos ativos que serão preservados:');
        sepultamentosDetalhes.rows.forEach(s => {
          console.log(`   - ${s.nome_sepultado} (Gaveta ${s.numero_gaveta}) - ${s.data_sepultamento}`);
        });
      }

      // Verificar gavetas ocupadas
      const gavetasOcupadas = await query(`
        SELECT COUNT(*) as total
        FROM gavetas
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND disponivel = false AND ativo = true
      `, [produto.codigo_cliente, produto.codigo_estacao]);

      if (parseInt(gavetasOcupadas.rows[0].total) > 0) {
        console.log(`⚠️ AVISO: ${gavetasOcupadas.rows[0].total} gavetas ocupadas serão mantidas intactas`);
      }

      // Verificar se há processos em andamento (futuro)
      // Esta validação pode ser expandida para verificar outros processos críticos

      console.log('✅ Validação de dependências concluída - inativação é segura');
    } else if (!produto.ativo && novoStatus) {
      console.log('🔍 Validando ATIVAÇÃO do produto...');

      // Verificar se dados relacionados ainda existem
      if (dadosSubsequentes.blocos === 0 && dadosSubsequentes.subBlocos === 0) {
        console.log('⚠️ AVISO: Produto não possui blocos ou sub-blocos. Será necessário recadastrar estrutura.');
      }

      console.log('✅ Validação de ativação concluída');
    }

    // IMPORTANTE: Não alteramos nenhum dado subsequente, apenas o status do produto
    console.log('⚠️ GARANTIA DE INTEGRIDADE: Todos os dados subsequentes serão mantidos intactos');

    // Validação final de integridade
    const validacaoIntegridade = await validarIntegridadeCompleta(produto);
    if (!validacaoIntegridade.valido) {
      return res.status(400).json({
        error: 'Falha na validação de integridade: ' + validacaoIntegridade.erro
      });
    }

    // Atualizar status do produto
    const result = await query(`
      UPDATE produtos
      SET ativo = $1, updated_at = CURRENT_TIMESTAMP
      WHERE id = $2
      RETURNING *
    `, [novoStatus, id]);

    const produtoAtualizado = result.rows[0];

    // Log detalhado da ação com informações completas de integridade
    const logDetalhado = {
      acao: novoStatus ? 'ATIVACAO' : 'INATIVACAO',
      produto: {
        id: produto.id,
        denominacao: produto.denominacao,
        codigo_cliente: produto.codigo_cliente,
        codigo_estacao: produto.codigo_estacao
      },
      status_anterior: produto.ativo,
      status_novo: novoStatus,
      dados_preservados: dadosSubsequentes,
      timestamp: new Date().toISOString(),
      usuario: {
        id: req.user.id,
        email: req.user.email,
        tipo: req.user.tipo_usuario
      },
      garantias_integridade: [
        `${dadosSubsequentes.blocos} blocos mantidos intactos`,
        `${dadosSubsequentes.subBlocos} sub-blocos mantidos intactos`,
        `${dadosSubsequentes.gavetas} gavetas mantidas intactas`,
        `${dadosSubsequentes.sepultamentos} sepultamentos mantidos intactos`,
        `${dadosSubsequentes.sepultamentosAtivos} sepultamentos ativos preservados`
      ]
    };

    await logAction(
      req.user.id,
      'UPDATE',
      'produtos',
      produtoAtualizado.id,
      {
        ativo: produto.ativo,
        dados_subsequentes: dadosSubsequentes,
        detalhes_completos: logDetalhado
      },
      {
        ativo: novoStatus,
        dados_subsequentes_mantidos: dadosSubsequentes,
        detalhes_completos: logDetalhado
      },
      req.ip,
      req.get('User-Agent'),
      {
        codigo_cliente: produto.codigo_cliente,
        codigo_estacao: produto.codigo_estacao,
        log_detalhado: logDetalhado
      },
      `TOGGLE PRODUTO: ${produto.denominacao} ${novoStatus ? 'ATIVADO' : 'INATIVADO'} por ${req.user.email}. INTEGRIDADE 100% GARANTIDA: ${dadosSubsequentes.blocos} blocos, ${dadosSubsequentes.subBlocos} sub-blocos, ${dadosSubsequentes.gavetas} gavetas, ${dadosSubsequentes.sepultamentos} sepultamentos (${dadosSubsequentes.sepultamentosAtivos} ativos) PRESERVADOS. Timestamp: ${logDetalhado.timestamp}`
    );

    console.log('📋 LOG DETALHADO REGISTRADO:', JSON.stringify(logDetalhado, null, 2));

    console.log(`✅ TOGGLE PRODUTO CONCLUÍDO COM SUCESSO!`);
    console.log(`📊 Resumo da operação:`, {
      produto_id: produto.id,
      produto_nome: produto.denominacao,
      status_anterior: produto.ativo,
      status_novo: novoStatus,
      dados_preservados: dadosSubsequentes,
      usuario: req.user.email,
      timestamp: new Date().toISOString(),
      duracao_processo: `${Date.now() - Date.now()}ms` // Placeholder para timing
    });

    res.json({
      message: `Produto ${novoStatus ? 'ativado' : 'inativado'} com sucesso`,
      produto: produtoAtualizado,
      integridade: {
        dados_preservados: dadosSubsequentes,
        garantia: 'Todos os dados subsequentes foram mantidos intactos'
      }
    });

  } catch (error) {
    console.error('❌ Erro ao alterar status do produto:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Função auxiliar para verificar dados subsequentes
async function verificarDadosSubsequentes(produto) {
  try {
    // Contar blocos
    const blocosResult = await query(`
      SELECT COUNT(*) as total
      FROM blocos
      WHERE codigo_cliente = $1 AND codigo_estacao = $2
    `, [produto.codigo_cliente, produto.codigo_estacao]);

    // Contar sub-blocos
    const subBlocosResult = await query(`
      SELECT COUNT(*) as total
      FROM sub_blocos sb
      JOIN blocos b ON sb.codigo_cliente = b.codigo_cliente
        AND sb.codigo_estacao = b.codigo_estacao
        AND sb.codigo_bloco = b.codigo_bloco
      WHERE b.codigo_cliente = $1 AND b.codigo_estacao = $2
    `, [produto.codigo_cliente, produto.codigo_estacao]);

    // Contar gavetas
    const gavetasResult = await query(`
      SELECT COUNT(*) as total
      FROM gavetas
      WHERE codigo_cliente = $1 AND codigo_estacao = $2
    `, [produto.codigo_cliente, produto.codigo_estacao]);

    // Contar sepultamentos (total)
    const sepultamentosResult = await query(`
      SELECT COUNT(*) as total
      FROM sepultamentos
      WHERE codigo_cliente = $1 AND codigo_estacao = $2
    `, [produto.codigo_cliente, produto.codigo_estacao]);

    // Contar sepultamentos ativos (não exumados)
    const sepultamentosAtivosResult = await query(`
      SELECT COUNT(*) as total
      FROM sepultamentos
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND exumado = false
    `, [produto.codigo_cliente, produto.codigo_estacao]);

    return {
      blocos: parseInt(blocosResult.rows[0].total) || 0,
      subBlocos: parseInt(subBlocosResult.rows[0].total) || 0,
      gavetas: parseInt(gavetasResult.rows[0].total) || 0,
      sepultamentos: parseInt(sepultamentosResult.rows[0].total) || 0,
      sepultamentosAtivos: parseInt(sepultamentosAtivosResult.rows[0].total) || 0
    };
  } catch (error) {
    console.error('❌ Erro ao verificar dados subsequentes:', error);
    return {
      blocos: 0,
      subBlocos: 0,
      gavetas: 0,
      sepultamentos: 0,
      sepultamentosAtivos: 0
    };
  }
}

// Função para validação completa de integridade
async function validarIntegridadeCompleta(produto) {
  try {
    console.log('🔍 Iniciando validação completa de integridade...');

    // 1. Verificar consistência de blocos
    const blocosInconsistentes = await query(`
      SELECT COUNT(*) as total
      FROM blocos b
      LEFT JOIN produtos p ON b.codigo_cliente = p.codigo_cliente AND b.codigo_estacao = p.codigo_estacao
      WHERE b.codigo_cliente = $1 AND b.codigo_estacao = $2 AND p.id IS NULL
    `, [produto.codigo_cliente, produto.codigo_estacao]);

    if (parseInt(blocosInconsistentes.rows[0].total) > 0) {
      return {
        valido: false,
        erro: `Encontrados ${blocosInconsistentes.rows[0].total} blocos órfãos sem produto associado`
      };
    }

    // 2. Verificar consistência de sub-blocos
    const subBlocosInconsistentes = await query(`
      SELECT COUNT(*) as total
      FROM sub_blocos sb
      LEFT JOIN blocos b ON sb.codigo_cliente = b.codigo_cliente
        AND sb.codigo_estacao = b.codigo_estacao
        AND sb.codigo_bloco = b.codigo_bloco
      WHERE sb.codigo_cliente = $1 AND sb.codigo_estacao = $2 AND b.id IS NULL
    `, [produto.codigo_cliente, produto.codigo_estacao]);

    if (parseInt(subBlocosInconsistentes.rows[0].total) > 0) {
      return {
        valido: false,
        erro: `Encontrados ${subBlocosInconsistentes.rows[0].total} sub-blocos órfãos sem bloco associado`
      };
    }

    // 3. Verificar consistência de gavetas
    const gavetasInconsistentes = await query(`
      SELECT COUNT(*) as total
      FROM gavetas g
      LEFT JOIN sub_blocos sb ON g.codigo_cliente = sb.codigo_cliente
        AND g.codigo_estacao = sb.codigo_estacao
        AND g.codigo_bloco = sb.codigo_bloco
        AND g.codigo_sub_bloco = sb.codigo_sub_bloco
      WHERE g.codigo_cliente = $1 AND g.codigo_estacao = $2 AND sb.id IS NULL
    `, [produto.codigo_cliente, produto.codigo_estacao]);

    if (parseInt(gavetasInconsistentes.rows[0].total) > 0) {
      return {
        valido: false,
        erro: `Encontradas ${gavetasInconsistentes.rows[0].total} gavetas órfãs sem sub-bloco associado`
      };
    }

    // 4. Verificar consistência de sepultamentos
    const sepultamentosInconsistentes = await query(`
      SELECT COUNT(*) as total
      FROM sepultamentos s
      LEFT JOIN gavetas g ON s.codigo_cliente = g.codigo_cliente
        AND s.codigo_estacao = g.codigo_estacao
        AND s.codigo_bloco = g.codigo_bloco
        AND s.codigo_sub_bloco = g.codigo_sub_bloco
        AND s.numero_gaveta = g.numero_gaveta
      WHERE s.codigo_cliente = $1 AND s.codigo_estacao = $2 AND g.id IS NULL AND s.ativo = true
    `, [produto.codigo_cliente, produto.codigo_estacao]);

    if (parseInt(sepultamentosInconsistentes.rows[0].total) > 0) {
      return {
        valido: false,
        erro: `Encontrados ${sepultamentosInconsistentes.rows[0].total} sepultamentos órfãos sem gaveta associada`
      };
    }

    console.log('✅ Validação de integridade completa aprovada');
    return { valido: true };

  } catch (error) {
    console.error('❌ Erro na validação de integridade:', error);
    return {
      valido: false,
      erro: 'Erro interno na validação de integridade: ' + error.message
    };
  }
}

// Função para validar dependências críticas
async function validarDependenciasCriticas(produto, novoStatus) {
  try {
    console.log('🔍 Validando dependências críticas...');

    const validacoes = [];

    if (produto.ativo && !novoStatus) {
      // Inativando produto - verificações críticas

      // 1. Verificar sepultamentos ativos
      const sepultamentosAtivos = await query(`
        SELECT COUNT(*) as total, STRING_AGG(nome_sepultado, ', ') as nomes
        FROM sepultamentos
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND exumado = false AND ativo = true
      `, [produto.codigo_cliente, produto.codigo_estacao]);

      validacoes.push({
        tipo: 'sepultamentos_ativos',
        quantidade: parseInt(sepultamentosAtivos.rows[0].total),
        detalhes: sepultamentosAtivos.rows[0].nomes,
        critico: false, // Não impede inativação, mas deve ser preservado
        acao: 'preservar'
      });

      // 2. Verificar gavetas ocupadas
      const gavetasOcupadas = await query(`
        SELECT COUNT(*) as total
        FROM gavetas
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND disponivel = false AND ativo = true
      `, [produto.codigo_cliente, produto.codigo_estacao]);

      validacoes.push({
        tipo: 'gavetas_ocupadas',
        quantidade: parseInt(gavetasOcupadas.rows[0].total),
        critico: false, // Não impede inativação
        acao: 'preservar'
      });

      // 3. Verificar processos em andamento (futuro)
      // Aqui podem ser adicionadas outras validações críticas

    } else if (!produto.ativo && novoStatus) {
      // Ativando produto - verificações de consistência

      // Verificar se estrutura básica existe
      const estruturaBasica = await query(`
        SELECT
          (SELECT COUNT(*) FROM blocos WHERE codigo_cliente = $1 AND codigo_estacao = $2) as blocos,
          (SELECT COUNT(*) FROM sub_blocos sb JOIN blocos b ON sb.codigo_cliente = b.codigo_cliente AND sb.codigo_estacao = b.codigo_estacao AND sb.codigo_bloco = b.codigo_bloco WHERE b.codigo_cliente = $1 AND b.codigo_estacao = $2) as sub_blocos
      `, [produto.codigo_cliente, produto.codigo_estacao]);

      validacoes.push({
        tipo: 'estrutura_basica',
        blocos: parseInt(estruturaBasica.rows[0].blocos),
        sub_blocos: parseInt(estruturaBasica.rows[0].sub_blocos),
        critico: false, // Pode ativar mesmo sem estrutura
        acao: 'informar'
      });
    }

    return {
      valido: true, // Por enquanto, sempre permite (apenas preserva dados)
      validacoes: validacoes,
      avisos: validacoes.filter(v => v.quantidade > 0 || v.blocos > 0),
      bloqueios: validacoes.filter(v => v.critico)
    };

  } catch (error) {
    console.error('❌ Erro na validação de dependências:', error);
    return {
      valido: false,
      erro: 'Erro interno na validação: ' + error.message
    };
  }
}

module.exports = router;
