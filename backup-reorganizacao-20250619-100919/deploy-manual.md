# 🚀 DEPLOY MANUAL NA VPS

## Comandos para executar na VPS

Conecte na VPS e execute os comandos abaixo:

```bash
# 1. Conectar na VPS
ssh vscode@************
# Senha: nbr5410!

# 2. Navegar para o projeto
cd /home/<USER>/portalevo/portalcliente

# 3. Atualizar código
git pull origin master
# Username: MauricioFilh
# Password: ****************************************

# 4. Verificar se sudo funciona agora
sudo docker info

# 5. Se sudo funcionar, executar deploy
sudo docker swarm init --advertise-addr $(hostname -I | awk '{print $1}') || true

# 6. Criar rede se não existir
sudo docker network create --driver overlay --attachable redeinterna || true

# 7. Parar stack anterior
sudo docker stack rm portal-evolution || true

# 8. Aguardar remoção
sleep 15

# 9. Build backend
sudo docker build -f Dockerfile.backend -t portal-evolution-backend:latest .

# 10. Build frontend
sudo docker build -f Dockerfile.frontend -t portal-evolution-frontend:latest .

# 11. Deploy stack
sudo docker stack deploy -c docker-compose.prod.yml portal-evolution

# 12. Verificar status
sudo docker stack services portal-evolution

# 13. Aguardar e testar
sleep 60
curl -I https://portal.evo-eden.site
```

## Se sudo não funcionar

Execute estes comandos para verificar:

```bash
# Verificar grupos do usuário
groups
id

# Verificar se está no sudoers
sudo -l

# Se não funcionar, use o script existente
bash deploy-traefik.sh
```

## Verificação final

```bash
# Verificar serviços
sudo docker stack services portal-evolution

# Verificar logs
sudo docker service logs portal-evolution_portal_backend
sudo docker service logs portal-evolution_portal_frontend

# Testar aplicação
curl -I https://portal.evo-eden.site
```
