import React, { useState, useEffect } from 'react';
import { Button } from '@mui/material';
import styled from 'styled-components';
import Modal from './Modal';
import { produtoService } from '../services/api';

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const StyledFormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const Label = styled.label`
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
`;

const Input = styled.input`
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  &:disabled {
    background-color: #f9fafb;
    color: #6b7280;
  }
`;

const TextArea = styled.textarea`
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  min-height: 100px;
  resize: vertical;
  font-family: inherit;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
`;

const StyledRangeContainer = styled.div`
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
`;

const RangeHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const RangeTitle = styled.h4`
  margin: 0;
  color: #374151;
  font-size: 0.875rem;
`;

const RemoveButton = styled.button`
  background: #ef4444;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.75rem;

  &:hover {
    background: #dc2626;
  }
`;

const RangeInputs = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
`;

const AddRangeButton = styled.button`
  background: #10b981;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  margin-bottom: 16px;

  &:hover {
    background: #059669;
  }
`;

const StyledButtonGroup = styled.div`
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
`;

const StyledButton = styled.button`
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;

  &.primary {
    background: #3b82f6;
    color: white;

    &:hover {
      background: #2563eb;
    }

    &:disabled {
      background: #9ca3af;
      cursor: not-allowed;
    }
  }

  &.secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;

    &:hover {
      background: #e5e7eb;
    }
  }

  &.danger {
    background: #ef4444;
    color: white;

    &:hover {
      background: #dc2626;
    }

    &:disabled {
      background: #9ca3af;
      cursor: not-allowed;
    }
  }
`;

const ErrorMessage = styled.div`
  background: #fef2f2;
  color: #dc2626;
  padding: 12px;
  border-radius: 8px;
  font-size: 0.875rem;
`;

const InfoText = styled.div`
  background: #f0f9ff;
  color: #0369a1;
  padding: 12px;
  border-radius: 8px;
  font-size: 0.875rem;
  margin-bottom: 16px;
`;

const SubBlocoModal = ({ isOpen, onClose, subBloco, blocoId, onSuccess }) => {
  const [formData, setFormData] = useState({
    codigo_sub_bloco: '',
    nome: '',
    descricao: ''
  });
  const [ranges, setRanges] = useState([{ inicio: '', fim: '' }]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (isOpen) {
      if (subBloco) {
        setFormData({
          codigo_sub_bloco: subBloco.codigo_sub_bloco || '',
          nome: subBloco.nome || '',
          descricao: subBloco.descricao || ''
        });
        // Para edição, carregar ranges existentes se disponíveis
        if (subBloco.ranges_gavetas) {
          const rangesExistentes = subBloco.ranges_gavetas.split(', ').map(range => {
            if (range.includes('-')) {
              const [inicio, fim] = range.split('-');
              return { inicio, fim };
            } else {
              return { inicio: range, fim: range };
            }
          });
          setRanges(rangesExistentes);
        } else {
          setRanges([{ inicio: '', fim: '' }]);
        }
      } else {
        // Para novos sub-blocos, gerar código automático
        const generateCodigo = async () => {
          try {
            // Buscar sub-blocos existentes para gerar próximo código
            const response = await produtoService.listarSubBlocos({ bloco_id: blocoId });
            const subBlocosExistentes = response.data;

            // Extrair números dos códigos existentes
            const numeros = subBlocosExistentes
              .map(sb => {
                const match = sb.codigo_sub_bloco.match(/SUB_(\d+)/);
                return match ? parseInt(match[1]) : 0;
              })
              .filter(num => num > 0);

            // Encontrar próximo número disponível
            const proximoNumero = numeros.length > 0 ? Math.max(...numeros) + 1 : 1;
            const novoCodigo = `SUB_${proximoNumero.toString().padStart(3, '0')}`;

            setFormData({
              codigo_sub_bloco: novoCodigo,
              nome: '',
              descricao: ''
            });
          } catch (error) {
            console.error('Erro ao gerar código automático:', error);
            // Fallback para código baseado em timestamp
            const timestamp = Date.now().toString().slice(-3);
            setFormData({
              codigo_sub_bloco: `SUB_${timestamp}`,
              nome: '',
              descricao: ''
            });
          }
        };

        generateCodigo();
        setRanges([{ inicio: '', fim: '' }]);
      }
      setError('');
    }
  }, [isOpen, subBloco]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleRangeChange = (index, field, value) => {
    const newRanges = [...ranges];
    newRanges[index][field] = value;
    setRanges(newRanges);
  };

  const addRange = () => {
    setRanges([...ranges, { inicio: '', fim: '' }]);
  };

  const removeRange = (index) => {
    if (ranges.length > 1) {
      setRanges(ranges.filter((_, i) => i !== index));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      if (subBloco) {
        // EDIÇÃO: Incluir ranges
        const dados = {
          ...formData,
          numeracao_gavetas: ranges.filter(r => r.inicio && r.fim).map(r => ({
            inicio: parseInt(r.inicio),
            fim: parseInt(r.fim)
          }))
        };
        await produtoService.atualizarSubBloco(subBloco.id, dados);
        alert('Sub-bloco atualizado com sucesso!');
      } else {
        // CRIAÇÃO: SEM ranges (serão gerenciados depois)
        const dados = {
          ...formData
          // NÃO incluir numeracao_gavetas na criação
        };
        await produtoService.criarSubBloco(blocoId, dados);
        alert('Sub-bloco criado com sucesso!\n\nUse o botão "Gerenciar Ranges" para definir as numerações das gavetas.');
      }

      onSuccess();
      onClose();
    } catch (error) {
      console.error('Erro ao salvar sub-bloco:', error);
      setError(error.response?.data?.error || 'Erro ao salvar sub-bloco');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!subBloco || !window.confirm('Tem certeza que deseja deletar este sub-bloco? Esta ação não pode ser desfeita.')) {
      return;
    }

    setLoading(true);
    setError('');

    try {
      await produtoService.deletarSubBloco(subBloco.id);
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Erro ao deletar sub-bloco:', error);
      setError(error.response?.data?.error || 'Erro ao deletar sub-bloco');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={subBloco ? 'Editar Sub-bloco' : 'Novo Sub-bloco'}
      maxWidth="700px"
    >
      <Form onSubmit={handleSubmit}>
        <StyledFormGroup>
          <Label>Código do Sub-bloco *</Label>
          <Input
            type="text"
            name="codigo_sub_bloco"
            value={formData.codigo_sub_bloco}
            onChange={handleChange}
            required
            placeholder="Ex: SB01"
            disabled={!!subBloco}
          />
        </StyledFormGroup>

        <StyledFormGroup>
          <Label>Denominação *</Label>
          <Input
            type="text"
            name="nome"
            value={formData.nome}
            onChange={handleChange}
            required
            placeholder="Ex: Sub-bloco Principal"
          />
        </StyledFormGroup>

        <StyledFormGroup>
          <Label>Observações</Label>
          <TextArea
            name="descricao"
            value={formData.descricao}
            onChange={handleChange}
            placeholder="Detalhes sobre o sub-bloco..."
          />
        </StyledFormGroup>

        {/* RANGES APENAS PARA EDIÇÃO - NÃO PARA CRIAÇÃO */}
        {subBloco && (
          <StyledFormGroup>
            <Label>Numeração das Gavetas</Label>
            <InfoText>
              Edite os ranges de numeração das gavetas. ATENÇÃO: Alterar ranges irá recriar todas as gavetas. Certifique-se de que não há sepultamentos ativos.
            </InfoText>

              {ranges.map((range, index) => (
                <StyledRangeContainer key={index}>
                  <RangeHeader>
                    <RangeTitle>Range {index + 1}</RangeTitle>
                    {ranges.length > 1 && (
                      <RemoveButton onClick={() => removeRange(index)}>
                        Remover
                      </RemoveButton>
                    )}
                  </RangeHeader>
                  <RangeInputs>
                    <div>
                      <Label>Gaveta Inicial</Label>
                      <Input
                        type="number"
                        min="1"
                        value={range.inicio}
                        onChange={(e) => handleRangeChange(index, 'inicio', e.target.value)}
                        placeholder="Ex: 1"
                      />
                    </div>
                    <div>
                      <Label>Gaveta Final</Label>
                      <Input
                        type="number"
                        min="1"
                        value={range.fim}
                        onChange={(e) => handleRangeChange(index, 'fim', e.target.value)}
                        placeholder="Ex: 20"
                      />
                    </div>
                  </RangeInputs>
                </StyledRangeContainer>
              ))}

              <AddRangeButton type="button" onClick={addRange}>
                + Adicionar Range
              </AddRangeButton>
            </StyledFormGroup>
        )}

        {/* INFORMAÇÃO PARA NOVOS SUB-BLOCOS */}
        {!subBloco && (
          <InfoText>
            📝 Após criar o sub-bloco, use o botão "Gerenciar Ranges" para definir as numerações das gavetas.
          </InfoText>
        )}

        {error && <ErrorMessage>{error}</ErrorMessage>}

        <StyledButtonGroup>
          <StyledButton type="button" className="secondary" onClick={onClose}>
            Cancelar
          </StyledButton>
          {subBloco && (
            <StyledButton
              type="button"
              className="danger"
              onClick={handleDelete}
              disabled={loading}
            >
              {loading ? 'Deletando...' : 'Deletar'}
            </StyledButton>
          )}
          <StyledButton type="submit" className="primary" disabled={loading}>
            {loading ? 'Salvando...' : (subBloco ? 'Atualizar' : 'Criar')}
          </StyledButton>
        </StyledButtonGroup>
      </Form>
    </Modal>
  );
};

export default SubBlocoModal;
