#!/bin/bash

# Script para corrigir imports do Button em todos os arquivos

echo "🔧 Corrigindo imports do Button..."

# Lista de arquivos que usam Button mas podem não ter import
files_to_check=(
    "portalcliente/src/components/UsuarioModal.jsx"
    "portalcliente/src/components/ClienteModal.jsx"
    "portalcliente/src/components/BlocoModal.jsx"
    "portalcliente/src/components/TransferenciaGavetasModal.jsx"
    "portalcliente/src/components/SubBlocoModal.jsx"
    "portalcliente/src/components/TesteLoginSimples.jsx"
    "portalcliente/src/components/TesteSenhas.jsx"
)

for file in "${files_to_check[@]}"; do
    if [ -f "$file" ]; then
        echo "Verificando $file..."
        
        # Verifica se usa Button mas não tem import
        if grep -q "<Button" "$file" && ! grep -q "import.*Button" "$file"; then
            echo "  ❌ Arquivo $file usa Button sem import - corrigindo..."
            
            # Adiciona import do Button na linha após os imports do React
            sed -i '/import React/a import { Button } from '\''@mui/material'\'';' "$file"
            echo "  ✅ Import adicionado em $file"
        else
            echo "  ✅ Arquivo $file já tem import correto ou não usa Button"
        fi
    else
        echo "  ⚠️  Arquivo $file não encontrado"
    fi
done

echo ""
echo "🎯 Verificação final..."

# Verifica se ainda há arquivos com problema
problem_files=$(grep -r "<Button" portalcliente/src --include="*.jsx" --include="*.js" -l | xargs -I {} sh -c 'if ! grep -q "import.*Button" "{}"; then echo "{}"; fi')

if [ -z "$problem_files" ]; then
    echo "✅ Todos os arquivos que usam Button têm import correto!"
else
    echo "❌ Ainda há arquivos com problema:"
    echo "$problem_files"
fi

echo ""
echo "🚀 Correção de imports do Button concluída!"
