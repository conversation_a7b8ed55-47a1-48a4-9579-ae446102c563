import React from 'react';
import { Box, Typography, Container } from '@mui/material';
import logoSemFundo from '../assets/logo_sem_fundo_branco.png';

const Footer = () => {
  return (
    <Box
      component="footer"
      sx={{
        backgroundColor: '#f8fafc',
        borderTop: '1px solid #e2e8f0',
        py: 3,
        mt: 'auto',
      }}
    >
      <Container maxWidth="lg">
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', md: 'row' },
            alignItems: 'center',
            justifyContent: 'space-between',
            gap: 2,
          }}
        >
          {/* Logo e informações da empresa */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <img 
              src={logoSemFundo} 
              alt="Evolution Tecnologia Funerária" 
              style={{
                height: '40px',
                width: 'auto',
              }}
            />
            <Box>
              <Typography variant="body2" sx={{ fontWeight: 600, color: '#374151' }}>
                Evolution Tecnologia Funerária
              </Typography>
              <Typography variant="caption" sx={{ color: '#6b7280' }}>
                Soluções tecnológicas para o setor funerário
              </Typography>
            </Box>
          </Box>

          {/* Copyright */}
          <Typography variant="caption" sx={{ color: '#6b7280', textAlign: 'center' }}>
            © {new Date().getFullYear()} Evolution Tecnologia Funerária. Todos os direitos reservados.
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default Footer;
