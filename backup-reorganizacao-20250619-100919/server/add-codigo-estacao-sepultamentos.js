const { Pool } = require('pg');

// Configuração do banco de dados
const pool = new Pool({
  host: '************',
  port: 5432,
  database: 'dbetens',
  user: 'postgres',
  password: 'ab3780bd73ee4e2804d566ce6fd96209',
  ssl: false,
});

async function addCodigoEstacao() {
  try {
    console.log('🔧 ADICIONANDO COLUNA CODIGO_ESTACAO À TABELA SEPULTAMENTOS\n');
    
    // Verificar se a coluna já existe
    console.log('1. 🔍 Verificando se a coluna codigo_estacao já existe...');
    const checkColumn = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'sepultamentos' 
      AND column_name = 'codigo_estacao'
    `);
    
    if (checkColumn.rows.length > 0) {
      console.log('   ✅ Coluna codigo_estacao já existe');
    } else {
      console.log('   ❌ Coluna codigo_estacao não existe, criando...');
      
      // Adicionar coluna codigo_estacao
      await pool.query(`
        ALTER TABLE sepultamentos 
        ADD COLUMN codigo_estacao VARCHAR(50)
      `);
      
      console.log('   ✅ Coluna codigo_estacao adicionada com sucesso');
    }
    
    // Verificar se há sepultamentos sem codigo_estacao
    console.log('\n2. 📊 Verificando sepultamentos sem codigo_estacao...');
    const sepultamentosSemEstacao = await pool.query(`
      SELECT COUNT(*) as total
      FROM sepultamentos 
      WHERE codigo_estacao IS NULL OR codigo_estacao = ''
    `);
    
    const totalSemEstacao = parseInt(sepultamentosSemEstacao.rows[0].total);
    console.log(`   📋 Encontrados ${totalSemEstacao} sepultamentos sem codigo_estacao`);
    
    if (totalSemEstacao > 0) {
      console.log('\n3. 🔄 Atualizando sepultamentos existentes com codigo_estacao...');
      
      // Buscar sepultamentos que precisam ser atualizados
      const sepultamentosParaAtualizar = await pool.query(`
        SELECT s.id, s.codigo_cliente, s.gaveta_id, g.sub_bloco_id, sb.bloco_id, b.produto_id, p.codigo_estacao
        FROM sepultamentos s
        JOIN gavetas g ON s.gaveta_id = g.id
        JOIN sub_blocos sb ON g.sub_bloco_id = sb.id
        JOIN blocos b ON sb.bloco_id = b.id
        JOIN produtos p ON b.produto_id = p.id
        WHERE s.codigo_estacao IS NULL OR s.codigo_estacao = ''
        ORDER BY s.id
      `);
      
      console.log(`   📝 Atualizando ${sepultamentosParaAtualizar.rows.length} registros...`);
      
      let atualizados = 0;
      for (const sepultamento of sepultamentosParaAtualizar.rows) {
        try {
          await pool.query(`
            UPDATE sepultamentos 
            SET codigo_estacao = $1 
            WHERE id = $2
          `, [sepultamento.codigo_estacao, sepultamento.id]);
          
          atualizados++;
          
          if (atualizados % 10 === 0) {
            console.log(`   📊 Atualizados: ${atualizados}/${sepultamentosParaAtualizar.rows.length}`);
          }
        } catch (error) {
          console.log(`   ⚠️  Erro ao atualizar sepultamento ID ${sepultamento.id}:`, error.message);
        }
      }
      
      console.log(`   ✅ ${atualizados} sepultamentos atualizados com codigo_estacao`);
    }
    
    // Adicionar foreign key se não existir
    console.log('\n4. 🔗 Verificando foreign key para codigo_estacao...');
    const checkForeignKey = await pool.query(`
      SELECT constraint_name
      FROM information_schema.table_constraints 
      WHERE table_name = 'sepultamentos' 
      AND constraint_type = 'FOREIGN KEY'
      AND constraint_name LIKE '%codigo_estacao%'
    `);
    
    if (checkForeignKey.rows.length === 0) {
      console.log('   ❌ Foreign key não existe, criando...');
      try {
        await pool.query(`
          ALTER TABLE sepultamentos 
          ADD CONSTRAINT fk_sepultamentos_codigo_estacao 
          FOREIGN KEY (codigo_estacao) REFERENCES produtos(codigo_estacao)
        `);
        console.log('   ✅ Foreign key criada com sucesso');
      } catch (error) {
        console.log('   ⚠️  Erro ao criar foreign key:', error.message);
        console.log('   💡 Isso pode ser normal se houver dados inconsistentes');
      }
    } else {
      console.log('   ✅ Foreign key já existe');
    }
    
    // Verificar resultado final
    console.log('\n5. ✅ Verificando resultado final...');
    const finalCheck = await pool.query(`
      SELECT 
        COUNT(*) as total_sepultamentos,
        COUNT(CASE WHEN codigo_estacao IS NOT NULL AND codigo_estacao != '' THEN 1 END) as com_codigo_estacao
      FROM sepultamentos
    `);
    
    const { total_sepultamentos, com_codigo_estacao } = finalCheck.rows[0];
    console.log(`   📊 Total de sepultamentos: ${total_sepultamentos}`);
    console.log(`   📝 Com codigo_estacao: ${com_codigo_estacao}`);
    
    if (total_sepultamentos === com_codigo_estacao) {
      console.log('   🎉 Todos os sepultamentos têm codigo_estacao!');
    } else {
      console.log(`   ⚠️  ${total_sepultamentos - com_codigo_estacao} sepultamentos ainda sem codigo_estacao`);
    }
    
    // Mostrar estrutura final
    console.log('\n6. 📋 Estrutura final da tabela sepultamentos:');
    const estruturaFinal = await pool.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'sepultamentos' 
      AND column_name IN ('codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco')
      ORDER BY column_name
    `);
    
    console.log('   Colunas obrigatórias:');
    estruturaFinal.rows.forEach((col, index) => {
      console.log(`   ${index + 1}. ✅ ${col.column_name} (${col.data_type}) - ${col.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'}`);
    });
    
    console.log('\n🎯 ATUALIZAÇÃO CONCLUÍDA COM SUCESSO!');
    
  } catch (error) {
    console.error('❌ Erro ao adicionar codigo_estacao:', error);
    process.exit(1);
  } finally {
    await pool.end();
    console.log('🔌 Conexão encerrada');
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  addCodigoEstacao();
}

module.exports = { addCodigoEstacao };
