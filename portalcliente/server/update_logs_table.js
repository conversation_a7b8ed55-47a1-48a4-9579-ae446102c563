const { Pool } = require('pg');
require('dotenv').config();

// Configuração do banco de dados
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'portal_evolution',
  password: process.env.DB_PASSWORD || 'postgres',
  port: process.env.DB_PORT || 5432,
});

async function updateLogsTable() {
  const client = await pool.connect();
  
  try {
    console.log('🔄 Atualizando tabela de logs para incluir referências hierárquicas...');
    
    // Verificar se as colunas já existem
    const checkColumns = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'logs_auditoria' 
      AND column_name IN ('codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco', 'descricao')
      AND table_schema = 'public'
    `);
    
    const existingColumns = checkColumns.rows.map(row => row.column_name);
    console.log('📋 Colunas existentes:', existingColumns);
    
    // Adicionar colunas que não existem
    const columnsToAdd = [
      { name: 'codigo_cliente', type: 'VARCHAR(50)' },
      { name: 'codigo_estacao', type: 'VARCHAR(50)' },
      { name: 'codigo_bloco', type: 'VARCHAR(50)' },
      { name: 'codigo_sub_bloco', type: 'VARCHAR(50)' },
      { name: 'descricao', type: 'TEXT' }
    ];
    
    for (const column of columnsToAdd) {
      if (!existingColumns.includes(column.name)) {
        console.log(`➕ Adicionando coluna: ${column.name}`);
        await client.query(`ALTER TABLE logs_auditoria ADD COLUMN ${column.name} ${column.type}`);
      } else {
        console.log(`✅ Coluna já existe: ${column.name}`);
      }
    }
    
    // Verificar se a foreign key já existe
    const checkFK = await client.query(`
      SELECT constraint_name 
      FROM information_schema.table_constraints 
      WHERE table_name = 'logs_auditoria' 
      AND constraint_type = 'FOREIGN KEY'
      AND constraint_name = 'logs_auditoria_codigo_cliente_fkey'
    `);
    
    if (checkFK.rows.length === 0) {
      console.log('🔗 Adicionando foreign key para codigo_cliente...');
      await client.query(`
        ALTER TABLE logs_auditoria 
        ADD CONSTRAINT logs_auditoria_codigo_cliente_fkey 
        FOREIGN KEY (codigo_cliente) REFERENCES clientes(codigo_cliente)
      `);
    } else {
      console.log('✅ Foreign key já existe');
    }
    
    // Criar índices se não existirem
    const indexes = [
      { name: 'idx_logs_codigo_cliente', columns: 'codigo_cliente' },
      { name: 'idx_logs_codigo_estacao', columns: 'codigo_cliente, codigo_estacao' },
      { name: 'idx_logs_codigo_bloco', columns: 'codigo_cliente, codigo_estacao, codigo_bloco' },
      { name: 'idx_logs_codigo_sub_bloco', columns: 'codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco' },
      { name: 'idx_logs_acao', columns: 'acao' },
      { name: 'idx_logs_tabela', columns: 'tabela_afetada' }
    ];
    
    for (const index of indexes) {
      const checkIndex = await client.query(`
        SELECT indexname 
        FROM pg_indexes 
        WHERE tablename = 'logs_auditoria' 
        AND indexname = '${index.name}'
      `);
      
      if (checkIndex.rows.length === 0) {
        console.log(`📊 Criando índice: ${index.name}`);
        await client.query(`CREATE INDEX ${index.name} ON logs_auditoria(${index.columns})`);
      } else {
        console.log(`✅ Índice já existe: ${index.name}`);
      }
    }
    
    // Tentar popular as referências hierárquicas dos logs existentes
    console.log('🔄 Populando referências hierárquicas dos logs existentes...');
    
    // Atualizar logs de produtos
    await client.query(`
      UPDATE logs_auditoria 
      SET codigo_cliente = (dados_novos->>'codigo_cliente'),
          codigo_estacao = (dados_novos->>'codigo_estacao')
      WHERE tabela_afetada = 'produtos' 
      AND codigo_cliente IS NULL 
      AND dados_novos->>'codigo_cliente' IS NOT NULL
    `);
    
    // Atualizar logs de blocos
    await client.query(`
      UPDATE logs_auditoria 
      SET codigo_cliente = (dados_novos->>'codigo_cliente'),
          codigo_estacao = (dados_novos->>'codigo_estacao'),
          codigo_bloco = (dados_novos->>'codigo_bloco')
      WHERE tabela_afetada = 'blocos' 
      AND codigo_cliente IS NULL 
      AND dados_novos->>'codigo_cliente' IS NOT NULL
    `);
    
    // Atualizar logs de sub_blocos
    await client.query(`
      UPDATE logs_auditoria 
      SET codigo_cliente = (dados_novos->>'codigo_cliente'),
          codigo_estacao = (dados_novos->>'codigo_estacao'),
          codigo_bloco = (dados_novos->>'codigo_bloco'),
          codigo_sub_bloco = (dados_novos->>'codigo_sub_bloco')
      WHERE tabela_afetada = 'sub_blocos' 
      AND codigo_cliente IS NULL 
      AND dados_novos->>'codigo_cliente' IS NOT NULL
    `);
    
    // Atualizar logs de sepultamentos
    await client.query(`
      UPDATE logs_auditoria 
      SET codigo_cliente = (dados_novos->>'codigo_cliente'),
          codigo_estacao = (dados_novos->>'codigo_estacao'),
          codigo_bloco = (dados_novos->>'codigo_bloco'),
          codigo_sub_bloco = (dados_novos->>'codigo_sub_bloco')
      WHERE tabela_afetada = 'sepultamentos' 
      AND codigo_cliente IS NULL 
      AND dados_novos->>'codigo_cliente' IS NOT NULL
    `);
    
    // Verificar resultados
    const stats = await client.query(`
      SELECT 
        tabela_afetada,
        COUNT(*) as total,
        COUNT(codigo_cliente) as com_codigo_cliente,
        COUNT(codigo_estacao) as com_codigo_estacao,
        COUNT(codigo_bloco) as com_codigo_bloco,
        COUNT(codigo_sub_bloco) as com_codigo_sub_bloco
      FROM logs_auditoria 
      GROUP BY tabela_afetada
      ORDER BY tabela_afetada
    `);
    
    console.log('\n📊 Estatísticas dos logs após atualização:');
    stats.rows.forEach(row => {
      console.log(`   ${row.tabela_afetada}:`);
      console.log(`     Total: ${row.total}`);
      console.log(`     Com codigo_cliente: ${row.com_codigo_cliente}`);
      console.log(`     Com codigo_estacao: ${row.com_codigo_estacao}`);
      console.log(`     Com codigo_bloco: ${row.com_codigo_bloco}`);
      console.log(`     Com codigo_sub_bloco: ${row.com_codigo_sub_bloco}`);
    });
    
    console.log('\n✅ Tabela de logs atualizada com sucesso!');
    console.log('🔧 Agora todas as tabelas têm referências hierárquicas apropriadas.');
    console.log('📝 Logs futuros incluirão automaticamente as referências por códigos.');
    
  } catch (error) {
    console.error('❌ Erro ao atualizar tabela de logs:', error);
    process.exit(1);
  } finally {
    client.release();
    await pool.end();
  }
}

// Executar apenas se chamado diretamente
if (require.main === module) {
  updateLogsTable().catch(error => {
    console.error('❌ Erro fatal:', error);
    process.exit(1);
  });
}

module.exports = { updateLogsTable };
