#!/bin/bash

# ===================================
# SCRIPT DE REORGANIZAÇÃO PARA PRODUÇÃO
# ===================================
# Reorganiza estrutura sem alterar funcionalidade
# Mantém sistema funcionando durante processo

set -e

echo "🚀 ====================================="
echo "🚀 REORGANIZAÇÃO PARA PRODUÇÃO"
echo "🚀 Portal Evolution"
echo "🚀 ====================================="

# Verificar se está no diretório correto
if [ ! -f "docker-compose.prod.yml" ]; then
    echo "❌ Erro: Execute este script no diretório portalcliente"
    exit 1
fi

# Função para log com timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# 1. BACKUP COMPLETO
log "💾 Criando backup completo..."
BACKUP_DIR="../backup-reorganizacao-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$BACKUP_DIR"
cp -r . "$BACKUP_DIR/"
log "✅ Backup criado em: $BACKUP_DIR"

# 2. CRIAR NOVA ESTRUTURA DE DIRETÓRIOS
log "📁 Criando nova estrutura de diretórios..."

# Criar diretórios principais
mkdir -p .github/workflows
mkdir -p docs/api
mkdir -p docs/deploy
mkdir -p scripts/deploy
mkdir -p scripts/maintenance
mkdir -p docker/production
mkdir -p docker/development
mkdir -p config/environments
mkdir -p database/migrations
mkdir -p database/seeds
mkdir -p deploy/production
mkdir -p deploy/staging

log "✅ Estrutura de diretórios criada"

# 3. MOVER ARQUIVOS PARA LOCAIS CORRETOS
log "📦 Reorganizando arquivos..."

# Mover Dockerfiles
if [ -f "Dockerfile.backend" ]; then
    mv Dockerfile.backend docker/production/
    log "✅ Dockerfile.backend movido"
fi

if [ -f "Dockerfile.frontend" ]; then
    mv Dockerfile.frontend docker/production/
    log "✅ Dockerfile.frontend movido"
fi

if [ -f "Dockerfile.logs" ]; then
    mv Dockerfile.logs docker/production/
    log "✅ Dockerfile.logs movido"
fi

# Mover docker-compose para deploy
if [ -f "docker-compose.prod.yml" ]; then
    cp docker-compose.prod.yml deploy/production/
    log "✅ docker-compose.prod.yml copiado para deploy"
fi

# Mover scripts de deploy
mv deploy-*.sh scripts/deploy/ 2>/dev/null || true
mv *.ps1 scripts/deploy/ 2>/dev/null || true
mv atualizar.sh scripts/maintenance/ 2>/dev/null || true
mv monitor.sh scripts/maintenance/ 2>/dev/null || true
mv validate-setup.sh scripts/maintenance/ 2>/dev/null || true

log "✅ Scripts movidos"

# Mover documentação
mv docs/* docs/api/ 2>/dev/null || true
mv *.md docs/ 2>/dev/null || true

# Mover configurações
mv configuracao.env config/environments/production.env 2>/dev/null || true
mv configuracao.env.example config/environments/.env.example 2>/dev/null || true

log "✅ Configurações organizadas"

# 4. LIMPAR ARQUIVOS DESNECESSÁRIOS
log "🧹 Removendo arquivos desnecessários..."

# Remover arquivos de teste da raiz
rm -f test-*.js 2>/dev/null || true
rm -f debug-*.js 2>/dev/null || true
rm -f fix-*.js 2>/dev/null || true
rm -f check-*.js 2>/dev/null || true
rm -f *test*.js 2>/dev/null || true
rm -f test.html 2>/dev/null || true

# Remover arquivos temporários
rm -f *.log 2>/dev/null || true
rm -f *.tmp 2>/dev/null || true

log "✅ Arquivos desnecessários removidos"

# 5. CRIAR .GITIGNORE ADEQUADO
log "📝 Criando .gitignore..."
cat > .gitignore << 'EOF'
# Dependencies
node_modules/
*/node_modules/

# Production builds
dist/
build/

# Environment variables
.env
.env.local
.env.production
.env.staging
config/environments/production.env
config/environments/staging.env

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore

# Backup files
backup-*/
*.backup

# Test files
test-*.js
debug-*.js
fix-*.js

# Temporary files
*.tmp
*.temp
EOF

log "✅ .gitignore criado"

# 6. CRIAR .DOCKERIGNORE
log "📝 Criando .dockerignore..."
cat > .dockerignore << 'EOF'
node_modules/
npm-debug.log
.git
.gitignore
README.md
.env
.nyc_output
coverage
.nyc_output
.vscode
.idea
*.swp
*.swo
*~
.DS_Store
Thumbs.db
test-*
debug-*
fix-*
*.log
*.tmp
backup-*
EOF

log "✅ .dockerignore criado"

# 7. CRIAR ARQUIVO DE CONFIGURAÇÃO DE AMBIENTE
log "📝 Criando template de configuração..."
cat > config/environments/.env.example << 'EOF'
# ===================================
# PORTAL EVOLUTION - CONFIGURAÇÃO
# ===================================

# Ambiente
NODE_ENV=production

# Aplicação
PORT=3001
JWT_SECRET=your_jwt_secret_here

# Banco de Dados
DB_HOST=postgres_postgres
DB_PORT=5432
DB_NAME=dbetens
DB_USER=postgres
DB_PASSWORD=your_db_password_here

# Email
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password_here

# Docker
BACKEND_MEMORY=512m
FRONTEND_MEMORY=256m

# Domínio
DOMAIN=portal.evo-eden.site
EOF

log "✅ Template de configuração criado"

# 8. ATUALIZAR REFERÊNCIAS NOS DOCKERFILES
log "🔧 Atualizando referências nos Dockerfiles..."

# Atualizar docker-compose para usar novos caminhos
if [ -f "deploy/production/docker-compose.prod.yml" ]; then
    sed -i 's|Dockerfile.backend|docker/production/Dockerfile.backend|g' deploy/production/docker-compose.prod.yml
    sed -i 's|Dockerfile.frontend|docker/production/Dockerfile.frontend|g' deploy/production/docker-compose.prod.yml
    sed -i 's|Dockerfile.logs|docker/production/Dockerfile.logs|g' deploy/production/docker-compose.prod.yml
    log "✅ docker-compose.prod.yml atualizado"
fi

# 9. CRIAR SCRIPT DE BUILD OTIMIZADO
log "📝 Criando script de build..."
cat > scripts/deploy/build-production.sh << 'EOF'
#!/bin/bash

# Build script para produção
set -e

echo "🔨 Iniciando build para produção..."

# Gerar timestamp único
TIMESTAMP=$(date +%s)
echo "🏷️ Timestamp: $TIMESTAMP"

# Build backend
echo "🔨 Building backend..."
docker build -f docker/production/Dockerfile.backend -t portal-evolution-backend:$TIMESTAMP .

# Build frontend  
echo "🔨 Building frontend..."
docker build -f docker/production/Dockerfile.frontend -t portal-evolution-frontend:$TIMESTAMP .

# Build logs
echo "🔨 Building logs..."
docker build -f docker/production/Dockerfile.logs -t portal-evolution-logs:$TIMESTAMP .

# Atualizar docker-compose com novas imagens
echo "📝 Atualizando docker-compose..."
sed -i "s/portal-evolution-backend:[0-9]*/portal-evolution-backend:$TIMESTAMP/g" deploy/production/docker-compose.prod.yml
sed -i "s/portal-evolution-frontend:[0-9]*/portal-evolution-frontend:$TIMESTAMP/g" deploy/production/docker-compose.prod.yml
sed -i "s/portal-evolution-logs:[0-9]*/portal-evolution-logs:$TIMESTAMP/g" deploy/production/docker-compose.prod.yml

echo "✅ Build concluído com timestamp: $TIMESTAMP"
EOF

chmod +x scripts/deploy/build-production.sh
log "✅ Script de build criado"

# 10. CRIAR SCRIPT DE DEPLOY
log "📝 Criando script de deploy..."
cat > scripts/deploy/deploy-production.sh << 'EOF'
#!/bin/bash

# Deploy script para produção
set -e

echo "🚀 Iniciando deploy para produção..."

# Verificar se está no diretório correto
if [ ! -f "deploy/production/docker-compose.prod.yml" ]; then
    echo "❌ Erro: docker-compose.prod.yml não encontrado"
    exit 1
fi

# Parar stack atual
echo "🛑 Parando stack atual..."
docker stack rm portal-evolution || true
sleep 30

# Deploy nova versão
echo "🚀 Fazendo deploy..."
docker stack deploy -c deploy/production/docker-compose.prod.yml portal-evolution

# Aguardar inicialização
echo "⏳ Aguardando inicialização..."
sleep 60

# Verificar status
echo "🔍 Verificando status..."
docker stack services portal-evolution

echo "✅ Deploy concluído!"
EOF

chmod +x scripts/deploy/deploy-production.sh
log "✅ Script de deploy criado"

# 11. VERIFICAR SISTEMA AINDA FUNCIONANDO
log "🔍 Verificando se sistema ainda está funcionando..."
if curl -s https://portal.evo-eden.site/api/health > /dev/null; then
    log "✅ Sistema ainda funcionando corretamente"
else
    log "⚠️ Sistema pode estar com problemas - verificar manualmente"
fi

# 12. CRIAR README ATUALIZADO
log "📝 Criando README atualizado..."
cat > README.md << 'EOF'
# 🚀 Portal Evolution

Sistema de Gestão de Sepultamentos - ETENs e Ossuários

## 📁 Estrutura do Projeto

```
portalevo/
├── .github/workflows/      # GitHub Actions
├── docs/                   # Documentação
├── scripts/               # Scripts de deploy e manutenção
├── docker/                # Arquivos Docker
├── config/                # Configurações
├── frontend/              # Aplicação React (src/)
├── backend/               # API Node.js (server/)
├── database/              # Scripts de banco
└── deploy/                # Arquivos de deploy
```

## 🚀 Deploy Rápido

```bash
# Build
./scripts/deploy/build-production.sh

# Deploy
./scripts/deploy/deploy-production.sh
```

## 🔧 Configuração

1. Copie o arquivo de configuração:
```bash
cp config/environments/.env.example config/environments/production.env
```

2. Edite as variáveis de ambiente
3. Execute o deploy

## 📊 Monitoramento

- **API Health:** https://portal.evo-eden.site/api/health
- **Logs:** https://logs.portal.evo-eden.site
- **Sistema:** https://portal.evo-eden.site

## 🛠️ Manutenção

```bash
# Verificar status
docker stack services portal-evolution

# Ver logs
docker service logs portal-evolution_portal_backend

# Atualizar sistema
./scripts/maintenance/atualizar.sh
```
EOF

log "✅ README atualizado"

log "🎉 ====================================="
log "🎉 REORGANIZAÇÃO CONCLUÍDA!"
log "🎉 ====================================="
log "✅ Estrutura reorganizada"
log "✅ Arquivos movidos para locais corretos"
log "✅ Scripts de deploy criados"
log "✅ Configurações organizadas"
log "✅ Sistema ainda funcionando"
log "🎉 ====================================="

echo ""
echo "📋 PRÓXIMOS PASSOS:"
echo "1. Revisar arquivos em config/environments/"
echo "2. Testar build: ./scripts/deploy/build-production.sh"
echo "3. Testar deploy: ./scripts/deploy/deploy-production.sh"
echo "4. Configurar Git repository"
echo "5. Implementar CI/CD"
echo ""
echo "💾 Backup disponível em: $BACKUP_DIR"
