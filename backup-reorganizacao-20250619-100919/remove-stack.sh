#!/bin/bash

# ===================================
# REMOVER STACK PORTAL EVOLUTION
# ===================================
# Script para remover a stack do Portal Evolution
# Execute: bash remove-stack.sh

set -e

echo "🗑️ Removendo stack do Portal Evolution..."

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Verificar se stack existe
if ! docker stack ls | grep -q "portal-evolution"; then
    warn "Stack 'portal-evolution' não encontrada!"
    exit 0
fi

# Remover stack
log "Removendo stack portal-evolution..."
docker stack rm portal-evolution

# Aguardar remoção completa
log "Aguardando remoção completa..."
sleep 15

# Verificar se foi removida
if docker stack ls | grep -q "portal-evolution"; then
    warn "Stack ainda existe, aguardando mais..."
    sleep 15
fi

# Limpar volumes órfãos (opcional)
read -p "Deseja remover volumes de dados? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    log "Removendo volumes órfãos..."
    docker volume prune -f
fi

# Limpar imagens órfãs (opcional)
read -p "Deseja remover imagens órfãs? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    log "Removendo imagens órfãs..."
    docker image prune -f
fi

log "✅ Stack removida com sucesso!"
log ""
log "📋 Para verificar:"
log "   Stacks: docker stack ls"
log "   Serviços: docker service ls"
log "   Volumes: docker volume ls"
