import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Alert,
  CircularProgress,
  Chip,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Visibility as VisibilityIcon,
  ToggleOn as ToggleOnIcon,
  ToggleOff as ToggleOffIcon,
  Business as BusinessIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
} from '@mui/icons-material';
import { clienteService } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import { useAsyncOperation } from '../contexts/LoadingContext';
import Modal from '../components/Modal';
import ClienteModal from '../components/ClienteModal';
import {
  StandardContainer,
  StandardButton,
  StandardTable,
  TableActions,
  StatusChip,
  ContentSection,
} from '../components/common';

// Todos os styled-components removidos - usando Material-UI

const ClientesPage = () => {
  const [clientes, setClientes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedCliente, setSelectedCliente] = useState(null);
  const [showClienteModal, setShowClienteModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const { canAccessClientes } = useAuth();
  const { executeAsync } = useAsyncOperation();

  useEffect(() => {
    if (canAccessClientes()) {
      loadClientes();
    }
  }, [canAccessClientes]);

  const loadClientes = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await clienteService.listar();
      setClientes(response.data);
    } catch (error) {
      console.error('Erro ao carregar clientes:', error);
      setError('Erro ao carregar clientes');
    } finally {
      setLoading(false);
    }
  };

  const handleAddCliente = () => {
    setSelectedCliente(null);
    setShowClienteModal(true);
  };

  const handleEditCliente = (cliente) => {
    setSelectedCliente(cliente);
    setShowClienteModal(true);
  };

  const handleViewDetails = (cliente) => {
    setSelectedCliente(cliente);
    setShowDetailModal(true);
  };

  const handleToggleStatus = async (cliente) => {
    const action = cliente.ativo ? 'inativar' : 'ativar';
    if (window.confirm(`Tem certeza que deseja ${action} o cliente "${cliente.nome_fantasia}"?`)) {
      try {
        await clienteService.atualizar(cliente.codigo_cliente, { 
          ...cliente, 
          ativo: !cliente.ativo 
        });
        loadClientes();
      } catch (error) {
        console.error('Erro ao alterar status do cliente:', error);
        alert('Erro ao alterar status do cliente');
      }
    }
  };

  if (!canAccessClientes()) {
    return (
      <StandardContainer title="Gestão de Clientes" subtitle="Gerenciamento de clientes">
        <Alert severity="error" sx={{ textAlign: 'center' }}>
          Acesso negado. Apenas administradores podem acessar esta página.
        </Alert>
      </StandardContainer>
    );
  }

  if (loading) {
    return (
      <StandardContainer title="Gestão de Clientes" subtitle="Gerenciamento de clientes">
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
          <CircularProgress size={40} />
        </Box>
      </StandardContainer>
    );
  }

  if (error) {
    return (
      <StandardContainer title="Gestão de Clientes" subtitle="Gerenciamento de clientes">
        <Alert severity="error" sx={{ textAlign: 'center' }}>
          {error}
        </Alert>
      </StandardContainer>
    );
  }

  return (
    <StandardContainer
      title="Gestão de Clientes"
      subtitle="Gerenciamento de clientes"
      headerAction={
        <StandardButton
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddCliente}
        >
          Novo Cliente
        </StandardButton>
      }
    >
      {clientes.length === 0 ? (
        <StandardCard sx={{ textAlign: 'center', py: 8 }}>
          <Typography variant="h1" sx={{ fontSize: '3rem', mb: 2 }}>👥</Typography>
          <Typography variant="h5" sx={{ mb: 1, color: 'text.primary' }}>
            Nenhum cliente encontrado
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Comece criando o primeiro cliente.
          </Typography>
        </StandardCard>
      ) : (
        <ContentSection>
          <StandardTable
            columns={[
              {
                id: 'codigo_cliente',
                label: 'Código',
                minWidth: 100,
                render: (value) => (
                  <Typography variant="body2" fontWeight="medium" color="primary.main">
                    {value}
                  </Typography>
                ),
              },
              {
                id: 'cnpj',
                label: 'CNPJ',
                minWidth: 150,
                render: (value) => (
                  <Typography variant="body2" fontFamily="monospace">
                    {value}
                  </Typography>
                ),
              },
              {
                id: 'nome_fantasia',
                label: 'Nome Fantasia',
                minWidth: 200,
                render: (value) => (
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <BusinessIcon sx={{ mr: 1, color: 'text.secondary', fontSize: 20 }} />
                    <Typography variant="body2" fontWeight="medium">
                      {value}
                    </Typography>
                  </Box>
                ),
              },
              {
                id: 'cidade',
                label: 'Cidade',
                minWidth: 150,
              },
              {
                id: 'email',
                label: 'Email',
                minWidth: 200,
                render: (value) => value ? (
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <EmailIcon sx={{ mr: 1, color: 'text.secondary', fontSize: 16 }} />
                    <Typography variant="body2">
                      {value}
                    </Typography>
                  </Box>
                ) : '-',
              },
              {
                id: 'telefone',
                label: 'Telefone',
                minWidth: 130,
                render: (value) => value ? (
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <PhoneIcon sx={{ mr: 1, color: 'text.secondary', fontSize: 16 }} />
                    <Typography variant="body2">
                      {value}
                    </Typography>
                  </Box>
                ) : '-',
              },
              {
                id: 'ativo',
                label: 'Status',
                minWidth: 100,
                render: (value) => (
                  <StatusChip
                    status={value ? 'Ativo' : 'Inativo'}
                    color={value ? 'success' : 'error'}
                  />
                ),
              },
              {
                id: 'actions',
                label: 'Ações',
                minWidth: 150,
                align: 'center',
                render: (_, row) => {
                  const actions = [
                    {
                      icon: <VisibilityIcon />,
                      tooltip: 'Ver Detalhes',
                      onClick: () => handleViewDetails(row),
                      color: 'primary',
                    },
                    {
                      icon: <EditIcon />,
                      tooltip: 'Editar',
                      onClick: () => handleEditCliente(row),
                      color: 'warning',
                    },
                    {
                      icon: row.ativo ? <ToggleOffIcon /> : <ToggleOnIcon />,
                      tooltip: row.ativo ? 'Desativar' : 'Ativar',
                      onClick: () => handleToggleStatus(row),
                      color: row.ativo ? 'error' : 'success',
                    },
                  ];

                  return <TableActions actions={actions} />;
                },
              },
            ]}
            data={clientes}
            loading={false}
            emptyMessage="Nenhum cliente encontrado"
            onRowClick={handleViewDetails}
          />
        </ContentSection>
      )}

      {/* Modal de Cliente */}
      <ClienteModal
        isOpen={showClienteModal}
        onClose={() => setShowClienteModal(false)}
        cliente={selectedCliente}
        onSuccess={loadClientes}
      />

      {/* Modal de Detalhes */}
      <Modal
        isOpen={showDetailModal}
        onClose={() => setShowDetailModal(false)}
        title={`Detalhes: ${selectedCliente?.nome_fantasia}`}
        maxWidth="800px"
      >
        {selectedCliente && (
          <Box sx={{ p: 2 }}>
            <Typography variant="body1" sx={{ mb: 1 }}>
              <strong>Código:</strong> {selectedCliente.codigo_cliente}
            </Typography>
            <Typography variant="body1" sx={{ mb: 1 }}>
              <strong>CNPJ:</strong> {selectedCliente.cnpj}
            </Typography>
            <Typography variant="body1" sx={{ mb: 1 }}>
              <strong>Nome Fantasia:</strong> {selectedCliente.nome_fantasia}
            </Typography>
            <Typography variant="body1" sx={{ mb: 1 }}>
              <strong>Razão Social:</strong> {selectedCliente.razao_social}
            </Typography>
            <Typography variant="body1" sx={{ mb: 1 }}>
              <strong>Endereço:</strong> {selectedCliente.logradouro}, {selectedCliente.numero}
            </Typography>
            <Typography variant="body1" sx={{ mb: 1 }}>
              <strong>Cidade:</strong> {selectedCliente.cidade} - {selectedCliente.estado}
            </Typography>
            <Typography variant="body1" sx={{ mb: 1 }}>
              <strong>Status:</strong>
              <Chip
                label={selectedCliente.ativo ? 'Ativo' : 'Inativo'}
                color={selectedCliente.ativo ? 'success' : 'error'}
                size="small"
                sx={{ ml: 1 }}
              />
            </Typography>
          </Box>
        )}
      </Modal>
    </StandardContainer>
  );
};

export default ClientesPage;
