#!/bin/bash

# Script para testar a gaveta 2838 no ambiente de desenvolvimento
# Portal Evolution - Ultrathink

echo "🔍 TESTE DA GAVETA 2838 - AMBIENTE DE DESENVOLVIMENTO"
echo "====================================================="
echo ""

# Verificar se a gaveta 2838 existe no banco
echo "📋 ETAPA 1: Verificando gaveta 2838 no banco de dados..."
GAVETA_EXISTS=$(docker exec postgres_postgres.1.uu8nl39h8411mub7s40whrk03 psql -U postgres -d dbetens -t -c "SELECT COUNT(*) FROM gavetas WHERE numero_gaveta = 2838 AND codigo_cliente = 'ETEN_003';")

if [ "$GAVETA_EXISTS" -eq 1 ]; then
    echo "✅ Gaveta 2838 encontrada no banco de dados"
else
    echo "❌ Gaveta 2838 NÃO encontrada no banco de dados"
    exit 1
fi

# Verificar status da gaveta
echo ""
echo "📋 ETAPA 2: Verificando status da gaveta 2838..."
GAVETA_STATUS=$(docker exec postgres_postgres.1.uu8nl39h8411mub7s40whrk03 psql -U postgres -d dbetens -t -c "
SELECT 
    CASE
        WHEN s.id IS NOT NULL AND s.data_exumacao IS NULL THEN 'ocupada'
        WHEN s.id IS NOT NULL AND s.data_exumacao IS NOT NULL THEN 'exumada'
        ELSE 'disponivel'
    END as status_gaveta
FROM gavetas g
LEFT JOIN sepultamentos s ON g.codigo_cliente = s.codigo_cliente
                          AND g.codigo_estacao = s.codigo_estacao
                          AND g.codigo_bloco = s.codigo_bloco
                          AND g.codigo_sub_bloco = s.codigo_sub_bloco
                          AND g.numero_gaveta = s.numero_gaveta
                          AND s.ativo = true
                          AND s.data_exumacao IS NULL
WHERE g.numero_gaveta = 2838 AND g.codigo_cliente = 'ETEN_003';
")

echo "Status da gaveta 2838: $GAVETA_STATUS"

if [[ "$GAVETA_STATUS" == *"disponivel"* ]]; then
    echo "✅ Gaveta 2838 está DISPONÍVEL"
else
    echo "❌ Gaveta 2838 está OCUPADA ou EXUMADA"
fi

# Verificar estrutura completa
echo ""
echo "📋 ETAPA 3: Verificando estrutura completa..."
echo "Cliente: ETEN_003"
echo "Estação: EST_001"
echo "Bloco: BLO_003"
echo "Sub-bloco: SUB_003"
echo "Gaveta: 2838"

# Verificar se todos os componentes existem
CLIENTE_EXISTS=$(docker exec postgres_postgres.1.uu8nl39h8411mub7s40whrk03 psql -U postgres -d dbetens -t -c "SELECT COUNT(*) FROM clientes WHERE codigo_cliente = 'ETEN_003';")
PRODUTO_EXISTS=$(docker exec postgres_postgres.1.uu8nl39h8411mub7s40whrk03 psql -U postgres -d dbetens -t -c "SELECT COUNT(*) FROM produtos WHERE codigo_cliente = 'ETEN_003' AND codigo_estacao = 'EST_001';")
BLOCO_EXISTS=$(docker exec postgres_postgres.1.uu8nl39h8411mub7s40whrk03 psql -U postgres -d dbetens -t -c "SELECT COUNT(*) FROM blocos WHERE codigo_cliente = 'ETEN_003' AND codigo_estacao = 'EST_001' AND codigo_bloco = 'BLO_003';")
SUBBLOCO_EXISTS=$(docker exec postgres_postgres.1.uu8nl39h8411mub7s40whrk03 psql -U postgres -d dbetens -t -c "SELECT COUNT(*) FROM sub_blocos WHERE codigo_cliente = 'ETEN_003' AND codigo_estacao = 'EST_001' AND codigo_bloco = 'BLO_003' AND codigo_sub_bloco = 'SUB_003';")

echo ""
echo "📊 RESULTADOS DA VERIFICAÇÃO:"
echo "Cliente ETEN_003: $([ "$CLIENTE_EXISTS" -eq 1 ] && echo "✅ EXISTE" || echo "❌ NÃO EXISTE")"
echo "Produto EST_001: $([ "$PRODUTO_EXISTS" -eq 1 ] && echo "✅ EXISTE" || echo "❌ NÃO EXISTE")"
echo "Bloco BLO_003: $([ "$BLOCO_EXISTS" -eq 1 ] && echo "✅ EXISTE" || echo "❌ NÃO EXISTE")"
echo "Sub-bloco SUB_003: $([ "$SUBBLOCO_EXISTS" -eq 1 ] && echo "✅ EXISTE" || echo "❌ NÃO EXISTE")"
echo "Gaveta 2838: $([ "$GAVETA_EXISTS" -eq 1 ] && echo "✅ EXISTE" || echo "❌ NÃO EXISTE")"

echo ""
echo "🎯 RESUMO:"
if [ "$CLIENTE_EXISTS" -eq 1 ] && [ "$PRODUTO_EXISTS" -eq 1 ] && [ "$BLOCO_EXISTS" -eq 1 ] && [ "$SUBBLOCO_EXISTS" -eq 1 ] && [ "$GAVETA_EXISTS" -eq 1 ]; then
    echo "✅ ESTRUTURA COMPLETA - Gaveta 2838 deve aparecer no frontend"
    echo ""
    echo "🌐 Teste no navegador:"
    echo "1. Acesse: https://portaldev.evo-eden.site"
    echo "2. Faça login"
    echo "3. Vá em 'Novo Sepultamento'"
    echo "4. Selecione cliente: CEMITÉRIO MUNICIPAL DE ITAPEVI (ETEN 3) - ETEN_003"
    echo "5. Selecione bloco: BLOCO 03 - LÓCULOS 2601 A 2924"
    echo "6. A gaveta 2838 deve aparecer na lista"
else
    echo "❌ ESTRUTURA INCOMPLETA - Verifique os componentes faltantes"
fi

echo ""
echo "🔧 Ambiente de desenvolvimento: https://portaldev.evo-eden.site"
echo "🌐 Ambiente de produção: https://portal.evo-eden.site"
echo ""
