const express = require('express');
const { query } = require('../database/connection');
const { logAction } = require('../utils/logger');

const router = express.Router();

// Middleware para verificar se é admin
const requireAdmin = (req, res, next) => {
  if (req.user.tipo_usuario !== 'admin') {
    return res.status(403).json({ error: 'Acesso negado. Apenas administradores.' });
  }
  next();
};

// Listar clientes (apenas admin)
router.get('/', requireAdmin, async (req, res) => {
  try {
    const result = await query(`
      SELECT
        id,
        codigo_cliente,
        cnpj,
        nome_fantasia,
        razao_social,
        cep,
        logradouro,
        numero,
        complemento,
        bairro,
        cidade,
        estado,
        ativo,
        created_at,
        updated_at
      FROM clientes
      WHERE ativo = true
      ORDER BY nome_fantasia
    `);

    res.json(result.rows);
  } catch (error) {
    console.error('Erro ao listar clientes:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Buscar cliente por código
router.get('/:codigo', requireAdmin, async (req, res) => {
  try {
    const { codigo } = req.params;
    
    const result = await query(`
      SELECT * FROM clientes 
      WHERE codigo_cliente = $1 AND ativo = true
    `, [codigo]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Cliente não encontrado' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Erro ao buscar cliente:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar novo cliente (apenas admin)
router.post('/', requireAdmin, async (req, res) => {
  try {
    const {
      codigo_cliente,
      cnpj,
      nome_fantasia,
      razao_social,
      cep,
      logradouro,
      numero,
      complemento,
      bairro,
      cidade,
      estado
    } = req.body;

    if (!codigo_cliente || !cnpj || !nome_fantasia || !razao_social) {
      return res.status(400).json({
        error: 'Código do cliente, CNPJ, nome fantasia e razão social são obrigatórios'
      });
    }

    // Verificar se o código já existe
    const existingClient = await query(
      'SELECT id FROM clientes WHERE codigo_cliente = $1',
      [codigo_cliente]
    );

    if (existingClient.rows.length > 0) {
      return res.status(400).json({ error: 'Código do cliente já existe' });
    }

    // Verificar se o CNPJ já existe
    const existingCnpj = await query(
      'SELECT id FROM clientes WHERE cnpj = $1',
      [cnpj]
    );

    if (existingCnpj.rows.length > 0) {
      return res.status(400).json({ error: 'CNPJ já cadastrado' });
    }

    const result = await query(`
      INSERT INTO clientes (
        codigo_cliente, cnpj, nome_fantasia, razao_social,
        cep, logradouro, numero, complemento, bairro, cidade, estado, nome
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $3)
      RETURNING *
    `, [
      codigo_cliente, cnpj, nome_fantasia, razao_social,
      cep, logradouro, numero, complemento, bairro, cidade, estado
    ]);

    const novoCliente = result.rows[0];

    // Registrar log
    await logAction(
      req.user.id,
      'CREATE',
      'clientes',
      novoCliente.id,
      null,
      novoCliente,
      req.ip,
      req.get('User-Agent')
    );

    res.status(201).json({
      message: 'Cliente cadastrado com sucesso',
      cliente: novoCliente
    });

  } catch (error) {
    console.error('Erro ao criar cliente:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar cliente (apenas admin)
router.put('/:codigo', requireAdmin, async (req, res) => {
  try {
    const { codigo } = req.params;
    const {
      cnpj,
      nome_fantasia,
      razao_social,
      cep,
      logradouro,
      numero,
      complemento,
      bairro,
      cidade,
      estado,
      ativo
    } = req.body;

    // Buscar dados anteriores
    const dadosAnteriores = await query(
      'SELECT * FROM clientes WHERE codigo_cliente = $1',
      [codigo]
    );

    if (dadosAnteriores.rows.length === 0) {
      return res.status(404).json({ error: 'Cliente não encontrado' });
    }

    // Verificar se o CNPJ já existe em outro cliente
    if (cnpj && cnpj !== dadosAnteriores.rows[0].cnpj) {
      const existingCnpj = await query(
        'SELECT id FROM clientes WHERE cnpj = $1 AND codigo_cliente != $2',
        [cnpj, codigo]
      );

      if (existingCnpj.rows.length > 0) {
        return res.status(400).json({ error: 'CNPJ já cadastrado para outro cliente' });
      }
    }

    const result = await query(`
      UPDATE clientes
      SET
        cnpj = $1,
        nome_fantasia = $2,
        razao_social = $3,
        cep = $4,
        logradouro = $5,
        numero = $6,
        complemento = $7,
        bairro = $8,
        cidade = $9,
        estado = $10,
        ativo = $11,
        updated_at = CURRENT_TIMESTAMP
      WHERE codigo_cliente = $12
      RETURNING *
    `, [
      cnpj, nome_fantasia, razao_social, cep, logradouro,
      numero, complemento, bairro, cidade, estado, ativo, codigo
    ]);

    const clienteAtualizado = result.rows[0];

    // Registrar log
    await logAction(
      req.user.id,
      'UPDATE',
      'clientes',
      clienteAtualizado.id,
      dadosAnteriores.rows[0],
      clienteAtualizado,
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      message: 'Cliente atualizado com sucesso',
      cliente: clienteAtualizado
    });

  } catch (error) {
    console.error('Erro ao atualizar cliente:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
