const { Pool } = require('pg');

// Configuração do banco de dados
const pool = new Pool({
  user: 'postgres',
  host: 'postgres',
  database: 'dbetens',
  password: 'ab3780bd73ee4e2804d566ce6fd96209',
  port: 5432,
});

async function executarTransferencia() {
  console.log('🚀 INICIANDO TRANSFERÊNCIA DE GAVETAS ITV_001');
  console.log('=' .repeat(60));
  
  // Parâmetros da transferência conforme arquivo Transferencia_Gavetas.md
  const transferParams = {
    codigo_cliente: 'ITV_001',
    codigo_estacao: 'ETEN_002',
    codigo_bloco: 'BL_001',
    sub_origem: 'SUB_002',
    sub_destino: 'SUB_003',
    gavetas: [
      1279, 1280, 1281, 1282, 1283, 1284, 1285,
      1303, 1304, 1305, 1306, 1307, 1308, 1309,
      1327, 1328, 1329, 1330, 1331, 1332,
      1350, 1351, 1352, 1353, 1354, 1355, 1356,
      1374, 1375, 1376, 1377, 1378, 1379, 1380,
      1398, 1399, 1400, 1401, 1402, 1403,
      1421, 1422, 1423, 1424, 1425, 1426, 1427
    ],
    range_remover: { inicio: 1279, fim: 1444 }
  };

  try {
    // ETAPA 1: Verificações de segurança
    console.log('🔍 ETAPA 1: Verificações de segurança...');
    
    const subBlocoOrigem = await pool.query(`
      SELECT * FROM sub_blocos 
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 AND codigo_sub_bloco = $4
    `, [transferParams.codigo_cliente, transferParams.codigo_estacao, transferParams.codigo_bloco, transferParams.sub_origem]);

    const subBlocoDestino = await pool.query(`
      SELECT * FROM sub_blocos 
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 AND codigo_sub_bloco = $4
    `, [transferParams.codigo_cliente, transferParams.codigo_estacao, transferParams.codigo_bloco, transferParams.sub_destino]);

    if (subBlocoOrigem.rows.length === 0) {
      throw new Error(`Sub-bloco origem ${transferParams.sub_origem} não encontrado`);
    }

    if (subBlocoDestino.rows.length === 0) {
      throw new Error(`Sub-bloco destino ${transferParams.sub_destino} não encontrado`);
    }

    console.log('✅ Sub-blocos verificados');
    console.log(`   - Origem: ${subBlocoOrigem.rows[0].denominacao}`);
    console.log(`   - Destino: ${subBlocoDestino.rows[0].denominacao}`);

    // ETAPA 2: Verificar sepultamentos existentes
    console.log('\n🔍 ETAPA 2: Verificando sepultamentos nas gavetas...');
    
    const sepultamentosExistentes = await pool.query(`
      SELECT numero_gaveta, nome_sepultado, data_sepultamento, data_exumacao,
             CASE WHEN data_exumacao IS NULL THEN 'ATIVO' ELSE 'EXUMADO' END as status
      FROM sepultamentos 
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 AND codigo_sub_bloco = $4
      AND numero_gaveta = ANY($5) AND ativo = true
      ORDER BY numero_gaveta
    `, [transferParams.codigo_cliente, transferParams.codigo_estacao, transferParams.codigo_bloco, 
        transferParams.sub_origem, transferParams.gavetas]);

    console.log(`📊 Encontrados ${sepultamentosExistentes.rows.length} sepultamentos nas gavetas`);
    
    // Mostrar alguns exemplos
    if (sepultamentosExistentes.rows.length > 0) {
      console.log('   Exemplos:');
      sepultamentosExistentes.rows.slice(0, 5).forEach(sep => {
        console.log(`   - Gaveta ${sep.numero_gaveta}: ${sep.nome_sepultado} (${sep.status})`);
      });
      if (sepultamentosExistentes.rows.length > 5) {
        console.log(`   ... e mais ${sepultamentosExistentes.rows.length - 5} sepultamentos`);
      }
    }

    // ETAPA 3: Executar transferência em transação
    console.log('\n🔄 ETAPA 3: Executando transferência...');
    
    await pool.query('BEGIN');

    try {
      // 3.1: Transferir sepultamentos
      console.log('   3.1: Transferindo sepultamentos...');
      const resultSepultamentos = await pool.query(`
        UPDATE sepultamentos 
        SET codigo_sub_bloco = $1, updated_at = CURRENT_TIMESTAMP
        WHERE codigo_cliente = $2 AND codigo_estacao = $3 AND codigo_bloco = $4 
        AND codigo_sub_bloco = $5 AND numero_gaveta = ANY($6)
      `, [transferParams.sub_destino, transferParams.codigo_cliente, transferParams.codigo_estacao, 
          transferParams.codigo_bloco, transferParams.sub_origem, transferParams.gavetas]);

      console.log(`   ✅ ${resultSepultamentos.rowCount} sepultamentos transferidos`);

      // 3.2: Transferir gavetas
      console.log('   3.2: Transferindo gavetas...');
      const resultGavetas = await pool.query(`
        UPDATE gavetas 
        SET codigo_sub_bloco = $1, updated_at = CURRENT_TIMESTAMP
        WHERE codigo_cliente = $2 AND codigo_estacao = $3 AND codigo_bloco = $4 
        AND codigo_sub_bloco = $5 AND numero_gaveta = ANY($6)
      `, [transferParams.sub_destino, transferParams.codigo_cliente, transferParams.codigo_estacao, 
          transferParams.codigo_bloco, transferParams.sub_origem, transferParams.gavetas]);

      console.log(`   ✅ ${resultGavetas.rowCount} gavetas transferidas`);

      // 3.3: Remover range 1279-1444 do SUB_002
      console.log('   3.3: Removendo range do SUB_002...');
      const resultRange = await pool.query(`
        DELETE FROM numeracoes_gavetas 
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 
        AND codigo_sub_bloco = $4 AND numero_inicio = $5 AND numero_fim = $6
      `, [transferParams.codigo_cliente, transferParams.codigo_estacao, transferParams.codigo_bloco, 
          transferParams.sub_origem, transferParams.range_remover.inicio, transferParams.range_remover.fim]);

      console.log(`   ✅ ${resultRange.rowCount} ranges removidos do SUB_002`);

      await pool.query('COMMIT');
      console.log('\n✅ TRANSFERÊNCIA CONCLUÍDA COM SUCESSO!');

      // ETAPA 4: Verificações finais
      console.log('\n🔍 ETAPA 4: Verificações finais...');
      
      const verificacaoFinal = await pool.query(`
        SELECT 
          'Gavetas no SUB_003' as tipo,
          COUNT(*) as quantidade
        FROM gavetas 
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 
        AND codigo_sub_bloco = $4 AND numero_gaveta = ANY($5)
        
        UNION ALL
        
        SELECT 
          'Gavetas restantes no SUB_002' as tipo,
          COUNT(*) as quantidade
        FROM gavetas 
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 
        AND codigo_sub_bloco = $6 AND numero_gaveta = ANY($5)
        
        UNION ALL
        
        SELECT 
          'Sepultamentos no SUB_003' as tipo,
          COUNT(*) as quantidade
        FROM sepultamentos 
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 
        AND codigo_sub_bloco = $4 AND numero_gaveta = ANY($5) AND ativo = true
      `, [transferParams.codigo_cliente, transferParams.codigo_estacao, transferParams.codigo_bloco, 
          transferParams.sub_destino, transferParams.gavetas, transferParams.sub_origem]);

      console.log('\n📊 RESULTADO FINAL:');
      verificacaoFinal.rows.forEach(row => {
        console.log(`   - ${row.tipo}: ${row.quantidade}`);
      });

      console.log('\n🎉 TRANSFERÊNCIA DE GAVETAS ITV_001 EXECUTADA COM SUCESSO!');
      console.log('=' .repeat(60));

    } catch (error) {
      await pool.query('ROLLBACK');
      console.error('\n❌ Erro na transferência - Rollback executado:', error.message);
      throw error;
    }

  } catch (error) {
    console.error('\n❌ Erro na transferência de gavetas ITV_001:', error.message);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Executar a transferência
executarTransferencia();
