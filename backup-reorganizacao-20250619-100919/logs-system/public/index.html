<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portal Evolution - Sistema de Logs</title>
    <script src="/socket.io/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        .header h1 {
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .container {
            max-width: 1400px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .service-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .service-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .service-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status.running {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .logs-container {
            background: #1a1a1a;
            border-radius: 8px;
            height: 300px;
            overflow-y: auto;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
        }
        
        .log-entry {
            margin-bottom: 0.5rem;
            padding: 0.25rem;
            border-radius: 4px;
        }
        
        .log-entry.error {
            background: rgba(220, 53, 69, 0.1);
            color: #ff6b6b;
        }
        
        .log-entry.warning {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
        }
        
        .log-entry.info {
            color: #17a2b8;
        }
        
        .log-entry.debug {
            color: #6c757d;
        }
        
        .timestamp {
            color: #6c757d;
            font-size: 0.75rem;
        }
        
        .controls {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .auto-refresh {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Portal Evolution - Sistema de Logs</h1>
    </div>
    
    <div class="container">
        <div class="controls">
            <button class="btn btn-primary" onclick="refreshAllLogs()">🔄 Atualizar Todos</button>
            <button class="btn btn-secondary" onclick="clearAllLogs()">🗑️ Limpar Logs</button>
            <div class="auto-refresh">
                <input type="checkbox" id="autoRefresh" checked>
                <label for="autoRefresh">Auto-refresh (2s)</label>
            </div>
        </div>
        
        <div class="services-grid" id="servicesGrid">
            <!-- Serviços serão carregados dinamicamente -->
        </div>
    </div>

    <script>
        const socket = io();
        const services = ['backend', 'frontend', 'postgres', 'traefik'];
        let autoRefreshInterval;
        
        // Inicializar
        document.addEventListener('DOMContentLoaded', () => {
            createServiceCards();
            loadAllLogs();
            startAutoRefresh();
        });
        
        function createServiceCards() {
            const grid = document.getElementById('servicesGrid');
            grid.innerHTML = '';
            
            services.forEach(service => {
                const card = document.createElement('div');
                card.className = 'service-card';
                card.innerHTML = `
                    <div class="service-header">
                        <div class="service-name">${getServiceIcon(service)} ${service.toUpperCase()}</div>
                        <div class="status" id="status-${service}">Carregando...</div>
                    </div>
                    <div class="logs-container" id="logs-${service}">
                        <div class="loading"></div>
                    </div>
                `;
                grid.appendChild(card);
            });
        }
        
        function getServiceIcon(service) {
            const icons = {
                backend: '🔧',
                frontend: '🎨',
                postgres: '🗄️',
                traefik: '🌐'
            };
            return icons[service] || '📦';
        }
        
        async function loadLogs(service) {
            try {
                const response = await fetch(`/api/logs/${service}?lines=50`);
                const data = await response.json();
                
                const container = document.getElementById(`logs-${service}`);
                container.innerHTML = '';
                
                data.logs.forEach(log => {
                    const entry = document.createElement('div');
                    entry.className = `log-entry ${log.level}`;
                    entry.innerHTML = `
                        <div class="timestamp">${formatTimestamp(log.timestamp)}</div>
                        <div>${escapeHtml(log.message)}</div>
                    `;
                    container.appendChild(entry);
                });
                
                container.scrollTop = container.scrollHeight;
                
                // Atualizar status
                updateServiceStatus(service, 'running');
                
            } catch (error) {
                console.error(`Erro ao carregar logs do ${service}:`, error);
                updateServiceStatus(service, 'error');
            }
        }
        
        function updateServiceStatus(service, status) {
            const statusEl = document.getElementById(`status-${service}`);
            statusEl.textContent = status === 'running' ? '✅ Online' : '❌ Erro';
            statusEl.className = `status ${status}`;
        }
        
        function formatTimestamp(timestamp) {
            try {
                return new Date(timestamp).toLocaleString('pt-BR');
            } catch {
                return timestamp;
            }
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        function loadAllLogs() {
            services.forEach(service => {
                loadLogs(service);
            });
        }
        
        function refreshAllLogs() {
            loadAllLogs();
        }
        
        function clearAllLogs() {
            services.forEach(service => {
                const container = document.getElementById(`logs-${service}`);
                container.innerHTML = '<div style="color: #6c757d;">Logs limpos...</div>';
            });
        }
        
        function startAutoRefresh() {
            const checkbox = document.getElementById('autoRefresh');
            
            function toggleAutoRefresh() {
                if (checkbox.checked) {
                    autoRefreshInterval = setInterval(loadAllLogs, 2000);
                } else {
                    clearInterval(autoRefreshInterval);
                }
            }
            
            checkbox.addEventListener('change', toggleAutoRefresh);
            toggleAutoRefresh();
        }
        
        // WebSocket para logs em tempo real
        socket.on('logs', (data) => {
            const container = document.getElementById(`logs-${data.service}`);
            if (container) {
                data.logs.forEach(log => {
                    const entry = document.createElement('div');
                    entry.className = `log-entry ${log.level}`;
                    entry.innerHTML = `
                        <div class="timestamp">${formatTimestamp(log.timestamp)}</div>
                        <div>${escapeHtml(log.message)}</div>
                    `;
                    container.appendChild(entry);
                });
                container.scrollTop = container.scrollHeight;
            }
        });
    </script>
</body>
</html>
