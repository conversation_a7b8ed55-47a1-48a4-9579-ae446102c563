import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { gavetaService } from '../services/api';

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: ${props => props.show ? 1 : 0};
  visibility: ${props => props.show ? 'visible' : 'hidden'};
  transition: all 0.3s ease;
`;

const ModalContent = styled.div`
  background: white;
  border-radius: 16px;
  padding: 32px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  transform: ${props => props.show ? 'scale(1)' : 'scale(0.95)'};
  transition: all 0.3s ease;
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
`;

const ModalTitle = styled.h2`
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6b7280;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;

  &:hover {
    background: #f3f4f6;
    color: #374151;
  }
`;

const GavetaInfo = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
`;

const InfoItem = styled.div`
  background: #f9fafb;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
`;

const InfoLabel = styled.div`
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 4px;
`;

const InfoValue = styled.div`
  font-size: 1rem;
  font-weight: 500;
  color: #1f2937;
`;

const StatusBadge = styled.span`
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  background: ${props => props.disponivel ? '#f0fdf4' : '#fef2f2'};
  color: ${props => props.disponivel ? '#059669' : '#dc2626'};
  border: 1px solid ${props => props.disponivel ? '#bbf7d0' : '#fecaca'};
`;

const HistoricoSection = styled.div`
  margin-top: 24px;
`;

const SectionTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
`;

const HistoricoList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const HistoricoItem = styled.div`
  background: #f9fafb;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid ${props => props.exumado ? '#dc2626' : '#059669'};
`;

const HistoricoHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
`;

const SepultadoNome = styled.div`
  font-weight: 500;
  color: #1f2937;
`;

const DataSepultamento = styled.div`
  font-size: 0.875rem;
  color: #6b7280;
`;

const HistoricoDetalhes = styled.div`
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.4;
`;

const LoadingSpinner = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  
  &::after {
    content: '';
    width: 30px;
    height: 30px;
    border: 3px solid #e5e7eb;
    border-top: 3px solid #059669;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 32px;
  color: #6b7280;
`;

const GavetaModal = ({ gaveta, show, onClose }) => {
  const [historico, setHistorico] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (show && gaveta) {
      loadHistorico();
    }
  }, [show, gaveta]);

  const loadHistorico = async () => {
    try {
      setLoading(true);
      const response = await gavetaService.buscarHistorico(gaveta.id);
      setHistorico(response.data);
    } catch (error) {
      console.error('Erro ao carregar histórico:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!gaveta) return null;

  return (
    <ModalOverlay show={show} onClick={handleOverlayClick}>
      <ModalContent show={show}>
        <ModalHeader>
          <ModalTitle>Gaveta {gaveta.numero_gaveta}</ModalTitle>
          <CloseButton onClick={onClose}>×</CloseButton>
        </ModalHeader>

        <GavetaInfo>
          <InfoItem>
            <InfoLabel>Status</InfoLabel>
            <InfoValue>
              <StatusBadge disponivel={gaveta.disponivel}>
                {gaveta.disponivel ? 'Disponível' : 'Ocupada'}
              </StatusBadge>
            </InfoValue>
          </InfoItem>

          <InfoItem>
            <InfoLabel>Posição</InfoLabel>
            <InfoValue>X: {gaveta.posicao_x}, Y: {gaveta.posicao_y}</InfoValue>
          </InfoItem>

          <InfoItem>
            <InfoLabel>Altura Especial</InfoLabel>
            <InfoValue>{gaveta.altura_especial}x</InfoValue>
          </InfoItem>

          <InfoItem>
            <InfoLabel>Código do Cliente</InfoLabel>
            <InfoValue>{gaveta.codigo_cliente}</InfoValue>
          </InfoItem>

          <InfoItem>
            <InfoLabel>Bloco</InfoLabel>
            <InfoValue>{gaveta.codigo_bloco}</InfoValue>
          </InfoItem>

          <InfoItem>
            <InfoLabel>Sub-bloco</InfoLabel>
            <InfoValue>{gaveta.codigo_sub_bloco}</InfoValue>
          </InfoItem>
        </GavetaInfo>

        {gaveta.nome_sepultado && (
          <InfoItem style={{ marginBottom: '24px' }}>
            <InfoLabel>Sepultado Atual</InfoLabel>
            <InfoValue>{gaveta.nome_sepultado}</InfoValue>
            {gaveta.data_sepultamento && (
              <div style={{ fontSize: '0.875rem', color: '#6b7280', marginTop: '4px' }}>
                Sepultamento: {formatDate(gaveta.data_sepultamento)}
              </div>
            )}
            {gaveta.observacoes && (
              <div style={{ fontSize: '0.875rem', color: '#6b7280', marginTop: '4px' }}>
                {gaveta.observacoes}
              </div>
            )}
          </InfoItem>
        )}

        <HistoricoSection>
          <SectionTitle>Histórico de Sepultamentos</SectionTitle>
          
          {loading ? (
            <LoadingSpinner />
          ) : historico.length === 0 ? (
            <EmptyState>
              <div style={{ fontSize: '1.5rem', marginBottom: '8px' }}>📋</div>
              <div>Nenhum histórico encontrado</div>
            </EmptyState>
          ) : (
            <HistoricoList>
              {historico.map((item) => (
                <HistoricoItem key={item.id} exumado={!!item.data_exumacao}>
                  <HistoricoHeader>
                    <SepultadoNome>{item.nome_sepultado}</SepultadoNome>
                    <DataSepultamento>
                      {formatDate(item.data_sepultamento)}
                    </DataSepultamento>
                  </HistoricoHeader>
                  
                  <HistoricoDetalhes>
                    <div><strong>Cliente:</strong> {item.nome_cliente}</div>
                    <div><strong>Posição:</strong> {item.posicao}</div>
                    {item.localizacao && (
                      <div><strong>Localização:</strong> {item.localizacao}</div>
                    )}
                    {item.data_exumacao && (
                      <div style={{ color: '#dc2626' }}>
                        <strong>Exumado em:</strong> {formatDate(item.data_exumacao)}
                      </div>
                    )}
                    {item.observacoes && (
                      <div style={{ marginTop: '8px' }}>
                        <strong>Observações:</strong> {item.observacoes}
                      </div>
                    )}
                  </HistoricoDetalhes>
                </HistoricoItem>
              ))}
            </HistoricoList>
          )}
        </HistoricoSection>
      </ModalContent>
    </ModalOverlay>
  );
};

export default GavetaModal;
