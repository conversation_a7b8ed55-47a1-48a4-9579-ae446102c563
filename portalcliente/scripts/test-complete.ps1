Write-Host "=== TESTE COMPLETO DO PORTAL EVOLUTION ===" -ForegroundColor Green
Write-Host ""

# Função para testar uma URL
function Test-Url {
    param($url, $description)
    try {
        $response = Invoke-WebRequest -Uri $url -UseBasicParsing -TimeoutSec 5
        Write-Host "   ✅ $description (Status: $($response.StatusCode))" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "   ❌ $description - Erro: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Função para testar API
function Test-Api {
    param($url, $description, $headers = @{})
    try {
        $response = Invoke-RestMethod -Uri $url -Headers $headers -TimeoutSec 5
        Write-Host "   ✅ $description" -ForegroundColor Green
        return $response
    } catch {
        Write-Host "   ❌ $description - Erro: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

Write-Host "1. Verificando Serviços..." -ForegroundColor Yellow
$frontendOk = Test-Url "http://localhost:5173" "Frontend (React + Vite)"
$backendOk = Test-Url "http://localhost:3001/api/health" "Backend (Node.js + Express)"

if (-not $frontendOk -or -not $backendOk) {
    Write-Host ""
    Write-Host "❌ Serviços não estão funcionando corretamente!" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "2. Testando Autenticação..." -ForegroundColor Yellow

# Login Admin
$loginAdmin = Test-Api "http://localhost:3001/api/auth/login" "Login Admin" @{} -Method POST -Body '{"email":"admin","senha":"adminnbr5410!"}' -ContentType "application/json"
if ($loginAdmin) {
    $tokenAdmin = $loginAdmin.token
    $headersAdmin = @{ Authorization = "Bearer $tokenAdmin" }
}

# Login Cliente
$loginCliente = Test-Api "http://localhost:3001/api/auth/login" "Login Cliente" @{} -Method POST -Body '{"email":"<EMAIL>","senha":"cliente123"}' -ContentType "application/json"
if ($loginCliente) {
    $tokenCliente = $loginCliente.token
    $headersCliente = @{ Authorization = "Bearer $tokenCliente" }
}

Write-Host ""
Write-Host "3. Testando APIs do Admin..." -ForegroundColor Yellow

if ($headersAdmin) {
    $produtos = Test-Api "http://localhost:3001/api/produtos" "Listar Produtos" $headersAdmin
    $estatisticas = Test-Api "http://localhost:3001/api/produtos/estatisticas" "Estatísticas de Produtos" $headersAdmin
    $usuarios = Test-Api "http://localhost:3001/api/usuarios" "Listar Usuários" $headersAdmin
    $clientes = Test-Api "http://localhost:3001/api/clientes" "Listar Clientes" $headersAdmin
    $gavetas = Test-Api "http://localhost:3001/api/gavetas?sub_bloco_id=1" "Listar Gavetas" $headersAdmin
    $sepultamentos = Test-Api "http://localhost:3001/api/sepultamentos" "Listar Sepultamentos" $headersAdmin
    $logs = Test-Api "http://localhost:3001/api/logs" "Listar Logs" $headersAdmin
    
    if ($produtos) {
        Write-Host "      📊 Produtos encontrados: $($produtos.Count)" -ForegroundColor Cyan
    }
    if ($estatisticas) {
        Write-Host "      📈 Estatísticas de produtos: $($estatisticas.Count)" -ForegroundColor Cyan
    }
    if ($usuarios) {
        Write-Host "      👥 Usuários cadastrados: $($usuarios.Count)" -ForegroundColor Cyan
    }
}

Write-Host ""
Write-Host "4. Testando APIs do Cliente..." -ForegroundColor Yellow

if ($headersCliente) {
    $produtosCliente = Test-Api "http://localhost:3001/api/produtos" "Produtos do Cliente" $headersCliente
    $gavetasCliente = Test-Api "http://localhost:3001/api/gavetas?sub_bloco_id=1" "Gavetas do Cliente" $headersCliente
    
    if ($produtosCliente) {
        Write-Host "      🏢 Produtos do cliente: $($produtosCliente.Count)" -ForegroundColor Cyan
    }
}

Write-Host ""
Write-Host "5. Testando Estrutura Hierárquica..." -ForegroundColor Yellow

if ($headersAdmin -and $produtos -and $produtos.Count -gt 0) {
    $produto = $produtos[0]
    $blocos = Test-Api "http://localhost:3001/api/produtos/$($produto.id)/blocos" "Blocos do Produto $($produto.nome)" $headersAdmin
    
    if ($blocos -and $blocos.Count -gt 0) {
        $bloco = $blocos[0]
        $subBlocos = Test-Api "http://localhost:3001/api/produtos/blocos/$($bloco.id)/sub-blocos" "Sub-blocos do Bloco $($bloco.codigo_bloco)" $headersAdmin
        
        if ($subBlocos -and $subBlocos.Count -gt 0) {
            $subBloco = $subBlocos[0]
            $gavetasSubBloco = Test-Api "http://localhost:3001/api/gavetas?sub_bloco_id=$($subBloco.id)" "Gavetas do Sub-bloco $($subBloco.codigo_sub_bloco)" $headersAdmin
            
            if ($gavetasSubBloco) {
                Write-Host "      🗃️ Gavetas no sub-bloco: $($gavetasSubBloco.Count)" -ForegroundColor Cyan
                
                # Testar histórico de uma gaveta
                if ($gavetasSubBloco.Count -gt 0) {
                    $gaveta = $gavetasSubBloco[0]
                    $historico = Test-Api "http://localhost:3001/api/gavetas/$($gaveta.id)/historico" "Histórico da Gaveta $($gaveta.numero_gaveta)" $headersAdmin
                    if ($historico) {
                        Write-Host "      📋 Registros no histórico: $($historico.Count)" -ForegroundColor Cyan
                    }
                }
            }
        }
    }
}

Write-Host ""
Write-Host "6. Resumo dos Testes..." -ForegroundColor Yellow

$totalTestes = 0
$testesPassaram = 0

# Contar testes
if ($frontendOk) { $testesPassaram++ }; $totalTestes++
if ($backendOk) { $testesPassaram++ }; $totalTestes++
if ($loginAdmin) { $testesPassaram++ }; $totalTestes++
if ($loginCliente) { $testesPassaram++ }; $totalTestes++
if ($produtos) { $testesPassaram++ }; $totalTestes++
if ($estatisticas) { $testesPassaram++ }; $totalTestes++
if ($usuarios) { $testesPassaram++ }; $totalTestes++

$percentualSucesso = [math]::Round(($testesPassaram / $totalTestes) * 100, 1)

Write-Host ""
Write-Host "📊 RESULTADO DOS TESTES:" -ForegroundColor White
Write-Host "   Testes executados: $totalTestes" -ForegroundColor Cyan
Write-Host "   Testes aprovados: $testesPassaram" -ForegroundColor Green
Write-Host "   Taxa de sucesso: $percentualSucesso%" -ForegroundColor $(if ($percentualSucesso -ge 80) { "Green" } else { "Yellow" })

Write-Host ""
if ($percentualSucesso -ge 80) {
    Write-Host "🎉 PORTAL EVOLUTION ESTÁ FUNCIONANDO PERFEITAMENTE!" -ForegroundColor Green
} else {
    Write-Host "⚠️ PORTAL EVOLUTION TEM ALGUNS PROBLEMAS" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🌐 ACESSO AO SISTEMA:" -ForegroundColor Cyan
Write-Host "   Frontend: http://localhost:5173" -ForegroundColor White
Write-Host "   Backend:  http://localhost:3001/api" -ForegroundColor White
Write-Host ""
Write-Host "CREDENCIAIS DE TESTE:" -ForegroundColor Cyan
Write-Host "   Admin:    admin / adminnbr5410!" -ForegroundColor White
Write-Host "   Cliente:  <EMAIL> / cliente123" -ForegroundColor White
