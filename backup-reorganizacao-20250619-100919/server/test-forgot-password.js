const axios = require('axios');

async function testForgotPassword() {
  try {
    console.log('🧪 TESTANDO FUNCIONALIDADE "ESQUECI MINHA SENHA"\n');
    
    // Teste 1: <PERSON><PERSON> v<PERSON>lido (admin)
    console.log('1. 📧 Testando com email admin...');
    try {
      const response1 = await axios.post('http://localhost:3001/api/auth/forgot-password', {
        email: 'admin'
      });
      
      console.log('✅ Resposta:', response1.data);
    } catch (error) {
      console.log('❌ Erro:', error.response?.data || error.message);
    }
    
    console.log('\n' + '='.repeat(50) + '\n');
    
    // Teste 2: Email v<PERSON>lid<PERSON> (<EMAIL>)
    console.log('2. 📧 Testando <NAME_EMAIL>...');
    try {
      const response2 = await axios.post('http://localhost:3001/api/auth/forgot-password', {
        email: 'ma<PERSON><PERSON><PERSON><PERSON><PERSON>@evolutionbr.tech'
      });
      
      console.log('✅ Resposta:', response2.data);
    } catch (error) {
      console.log('❌ Erro:', error.response?.data || error.message);
    }
    
    console.log('\n' + '='.repeat(50) + '\n');
    
    // Teste 3: Email válido (<EMAIL>)
    console.log('3. 📧 Testando <NAME_EMAIL>...');
    try {
      const response3 = await axios.post('http://localhost:3001/api/auth/forgot-password', {
        email: '<EMAIL>'
      });
      
      console.log('✅ Resposta:', response3.data);
    } catch (error) {
      console.log('❌ Erro:', error.response?.data || error.message);
    }
    
    console.log('\n' + '='.repeat(50) + '\n');
    
    // Teste 4: Email inválido
    console.log('4. 📧 Testando com email inexistente...');
    try {
      const response4 = await axios.post('http://localhost:3001/api/auth/forgot-password', {
        email: '<EMAIL>'
      });
      
      console.log('✅ Resposta:', response4.data);
    } catch (error) {
      console.log('❌ Erro (esperado):', error.response?.data || error.message);
    }
    
    console.log('\n' + '='.repeat(50) + '\n');
    
    // Teste 5: Email vazio
    console.log('5. 📧 Testando com email vazio...');
    try {
      const response5 = await axios.post('http://localhost:3001/api/auth/forgot-password', {
        email: ''
      });
      
      console.log('✅ Resposta:', response5.data);
    } catch (error) {
      console.log('❌ Erro (esperado):', error.response?.data || error.message);
    }
    
    console.log('\n🎯 TESTE CONCLUÍDO!');
    console.log('\n📧 Verifique o console do backend para logs de envio de email.');
    console.log('📬 Se configurado com Gmail, verifique a caixa de entrada dos emails testados.');
    
  } catch (error) {
    console.error('❌ Erro geral no teste:', error.message);
  }
}

// Executar teste
testForgotPassword();
