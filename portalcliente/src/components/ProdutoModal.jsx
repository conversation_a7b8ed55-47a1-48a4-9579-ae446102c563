import React, { useState, useEffect } from 'react';
import {
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Box,
  Alert,
} from '@mui/material';
import Modal from './Modal';
import { produtoService, clienteService } from '../services/api';
import { StandardButton } from './common';

// Todos os styled-components removidos - usando Material-UI

const ProdutoModal = ({ isOpen, onClose, produto = null, onSuccess }) => {
  const [formData, setFormData] = useState({
    codigo_cliente: '',
    codigo_estacao: '',
    meses_para_exumar: 24,
    denominacao: '',
    observacao: ''
  });
  const [clientes, setClientes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (isOpen) {
      loadClientes();
      if (produto) {
        setFormData({
          codigo_cliente: produto.codigo_cliente || '',
          codigo_estacao: produto.codigo_estacao || '',
          meses_para_exumar: produto.meses_para_exumar || 24,
          denominacao: produto.denominacao || '',
          observacao: produto.observacao || ''
        });
      } else {
        setFormData({
          codigo_cliente: '',
          codigo_estacao: '',
          meses_para_exumar: 24,
          denominacao: '',
          observacao: ''
        });
      }
      setError('');
    }
  }, [isOpen, produto]);

  const loadClientes = async () => {
    try {
      const response = await clienteService.listar();
      setClientes(response.data);
    } catch (error) {
      console.error('Erro ao carregar clientes:', error);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;

    // Formatação automática para código da estação
    let formattedValue = value;
    if (name === 'codigo_estacao') {
      formattedValue = value.toUpperCase();
    }

    setFormData(prev => ({
      ...prev,
      [name]: formattedValue
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    console.log('📤 Enviando dados do produto:', formData);

    try {
      let response;
      if (produto) {
        console.log('🔄 Atualizando produto existente:', {
          id: produto.id,
          codigo_cliente: produto.codigo_cliente,
          codigo_estacao: produto.codigo_estacao
        });
        response = await produtoService.atualizar(produto.codigo_cliente, produto.codigo_estacao, formData);
        alert('Produto atualizado com sucesso!');
      } else {
        console.log('➕ Criando novo produto');
        response = await produtoService.criar(formData);
        alert('Produto criado com sucesso!');
      }

      console.log('✅ Produto salvo com sucesso:', response.data);
      onSuccess();
      onClose();
    } catch (error) {
      console.error('❌ Erro ao salvar produto:', error);
      console.error('📋 Detalhes do erro:', error.response?.data);

      if (error.response?.status === 400) {
        const errorData = error.response.data;

        if (errorData.dependencias || errorData.acao_necessaria) {
          // Erro de validação hierárquica
          let message = errorData.error;

          if (errorData.detalhes) {
            message += `\n\n${errorData.detalhes}`;
          }

          if (errorData.acao_necessaria) {
            message += `\n\n${errorData.acao_necessaria}`;
          }

          if (errorData.blocos_encontrados) {
            message += `\n\nBlocos encontrados: ${errorData.blocos_encontrados}`;
          }

          setError(message);
        } else {
          setError(errorData.error || 'Dados inválidos para salvar produto');
        }
      } else if (error.response?.status === 403) {
        setError('Acesso negado. Apenas administradores podem editar produtos.');
      } else if (error.response?.status === 404) {
        setError('Produto não encontrado. Pode ter sido removido por outro usuário.');
      } else {
        setError(error.response?.data?.error || 'Erro ao salvar produto');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={produto ? 'Editar Produto' : 'Novo Produto'}
      maxWidth="600px"
    >
      <Box component="form" onSubmit={handleSubmit} sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
        <FormControl fullWidth required>
          <InputLabel>Cliente</InputLabel>
          <Select
            name="codigo_cliente"
            value={formData.codigo_cliente}
            label="Cliente"
            onChange={handleChange}
            disabled={!!produto}
          >
            <MenuItem value="">Selecione um cliente</MenuItem>
            {clientes.map(cliente => (
              <MenuItem key={cliente.codigo_cliente} value={cliente.codigo_cliente}>
                {cliente.nome_fantasia} ({cliente.codigo_cliente})
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <TextField
          fullWidth
          required
          label="Código da Estação"
          name="codigo_estacao"
          value={formData.codigo_estacao}
          onChange={handleChange}
          placeholder="Ex: ETEN_001"
          disabled={!!produto}
        />

        <TextField
          fullWidth
          required
          type="number"
          label="Meses para Exumar"
          name="meses_para_exumar"
          value={formData.meses_para_exumar}
          onChange={handleChange}
          inputProps={{ min: 1, max: 120 }}
          placeholder="Ex: 24"
        />

        <TextField
          fullWidth
          required
          label="Denominação"
          name="denominacao"
          value={formData.denominacao}
          onChange={handleChange}
          placeholder="Ex: ESTAÇÃO DE TRATAMENTO 001"
        />

        <TextField
          fullWidth
          multiline
          rows={3}
          label="Observação"
          name="observacao"
          value={formData.observacao}
          onChange={handleChange}
          placeholder="Detalhes gerais da estação..."
        />

        {error && (
          <Alert severity="error">
            {error}
          </Alert>
        )}

        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 2 }}>
          <StandardButton
            variant="outlined"
            onClick={onClose}
          >
            Cancelar
          </StandardButton>
          <StandardButton
            type="submit"
            variant="contained"
            disabled={loading}
          >
            {loading ? 'Salvando...' : (produto ? 'Atualizar' : 'Criar')}
          </StandardButton>
        </Box>
      </Box>
    </Modal>
  );
};

export default ProdutoModal;
