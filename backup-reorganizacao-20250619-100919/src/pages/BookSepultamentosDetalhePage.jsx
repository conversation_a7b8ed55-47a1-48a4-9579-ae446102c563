import React, { useState, useEffect } from 'react';
import { useParams, useLocation, useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Skeleton,
  Alert,
  Breadcrumbs,
  Link
} from '@mui/material';
import { EditButton, DeleteButton, ExumarButton, AddButton, CancelButton, StandardButton } from '../components/common/StandardButtons';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  RemoveCircle as ExumarIcon,
  ArrowBack as ArrowBackIcon,
  Visibility as VisibilityIcon,
  Home as HomeIcon,
  Business as BusinessIcon
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { sepultamentoService } from '../services/api';
import BookSepultamentoModal from '../components/BookSepultamentoModal';

const BookSepultamentosDetalhePage = () => {
  const { codigoEstacao } = useParams();
  const location = useLocation();
  const navigate = useNavigate();
  const { user, canAddEditExhumeSepultamentos, canDeleteSepultamentos } = useAuth();
  
  const [produto] = useState(location.state?.produto || {});
  const [sepultamentos, setSepultamentos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedSepultamento, setSelectedSepultamento] = useState(null);
  const [showSepultamentoModal, setShowSepultamentoModal] = useState(false);
  const [showExumarDialog, setShowExumarDialog] = useState(false);
  const [exumarData, setExumarData] = useState('');
  const [exumarObservacoes, setExumarObservacoes] = useState('');

  useEffect(() => {
    loadSepultamentos();
  }, [codigoEstacao]);

  const loadSepultamentos = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await sepultamentoService.listar({
        codigo_estacao: codigoEstacao,
        ativo: true
      });
      
      setSepultamentos(response.data || []);
    } catch (error) {
      console.error('Erro ao carregar sepultamentos:', error);
      setError('Erro ao carregar sepultamentos');
    } finally {
      setLoading(false);
    }
  };

  const handleAddSepultamento = () => {
    setSelectedSepultamento(null);
    setShowSepultamentoModal(true);
  };

  const handleEditSepultamento = (sepultamento) => {
    setSelectedSepultamento(sepultamento);
    setShowSepultamentoModal(true);
  };

  const handleExumarClick = (sepultamento) => {
    setSelectedSepultamento(sepultamento);
    setExumarData(new Date().toISOString().split('T')[0]);
    setExumarObservacoes('');
    setShowExumarDialog(true);
  };

  const handleExumarConfirm = async () => {
    try {
      await sepultamentoService.exumar(selectedSepultamento.id, {
        data_exumacao: exumarData,
        observacoes_exumacao: exumarObservacoes || 'Exumação realizada via Book de Sepultamentos'
      });
      
      setShowExumarDialog(false);
      loadSepultamentos();
    } catch (error) {
      console.error('Erro ao exumar sepultamento:', error);
      alert('Erro ao exumar sepultamento: ' + (error.response?.data?.error || error.message));
    }
  };

  const handleDeleteSepultamento = async (sepultamento) => {
    // Verificar se o sepultamento já foi exumado e se o usuário não é admin
    if (sepultamento.status_exumacao && !isAdmin()) {
      alert('Não é possível deletar sepultamentos que já foram exumados');
      return;
    }

    if (!window.confirm(`Tem certeza que deseja DELETAR PERMANENTEMENTE o sepultamento de ${sepultamento.nome_sepultado}?\n\nEsta ação não pode ser desfeita e removerá o registro do banco de dados.`)) {
      return;
    }

    try {
      await sepultamentoService.deletar(sepultamento.id);
      loadSepultamentos();
      alert('Sepultamento deletado permanentemente com sucesso!');
    } catch (error) {
      console.error('Erro ao deletar sepultamento:', error);
      alert('Erro ao deletar sepultamento: ' + (error.response?.data?.error || error.message));
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const getStatusColor = (statusExumacao) => {
    return statusExumacao ? 'error' : 'success';
  };

  const getStatusText = (statusExumacao) => {
    return statusExumacao ? 'Exumado' : 'Sepultado';
  };

  const TableSkeleton = () => (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            {['Nome', 'Localização', 'Data', 'Status', 'Ações'].map((header) => (
              <TableCell key={header}>
                <Skeleton variant="text" width="80%" />
              </TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {[1, 2, 3, 4, 5].map((row) => (
            <TableRow key={row}>
              {[1, 2, 3, 4, 5].map((cell) => (
                <TableCell key={cell}>
                  <Skeleton variant="text" width="90%" />
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 3 }}>
        <Link 
          color="inherit" 
          href="#" 
          onClick={() => navigate('/dashboard')}
          sx={{ display: 'flex', alignItems: 'center' }}
        >
          <HomeIcon sx={{ mr: 0.5, fontSize: 16 }} />
          Início
        </Link>
        <Link 
          color="inherit" 
          href="#" 
          onClick={() => navigate('/dashboard/book-sepultamentos')}
          sx={{ display: 'flex', alignItems: 'center' }}
        >
          <BusinessIcon sx={{ mr: 0.5, fontSize: 16 }} />
          Book de Sepultamentos
        </Link>
        <Typography color="text.primary">
          {produto.denominacao || produto.nome || codigoEstacao}
        </Typography>
      </Breadcrumbs>

      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 4 }}>
        <Box>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <IconButton 
              onClick={() => navigate('/dashboard/book-sepultamentos')}
              sx={{ mr: 1 }}
            >
              <ArrowBackIcon />
            </IconButton>
            <Typography variant="h4" component="h1">
              {produto.denominacao || produto.nome || `Produto ${codigoEstacao}`}
            </Typography>
          </Box>
          <Typography variant="body1" color="text.secondary">
            Gerencie os sepultamentos deste produto
          </Typography>
        </Box>
        
        {canAddEditExhumeSepultamentos() && (
          <AddButton
            startIcon={<AddIcon />}
            onClick={handleAddSepultamento}
          >
            Adicionar Novo Sepultamento
          </AddButton>
        )}
      </Box>

      {/* Conteúdo */}
      {loading ? (
        <TableSkeleton />
      ) : error ? (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      ) : sepultamentos.length === 0 ? (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h6" gutterBottom>
            Nenhum sepultamento encontrado
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Este produto ainda não possui sepultamentos registrados.
          </Typography>
          {canAddEditExhumeSepultamentos() && (
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAddSepultamento}
            >
              Adicionar Primeiro Sepultamento
            </Button>
          )}
        </Paper>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Nome do Sepultado</TableCell>
                <TableCell>Localização</TableCell>
                <TableCell>Número da Gaveta</TableCell>
                <TableCell>Data Sepultamento</TableCell>
                <TableCell>Horário</TableCell>
                <TableCell>Status</TableCell>
                <TableCell align="center">Ações</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {sepultamentos.map((sepultamento) => {
                const localizacao = sepultamento.denominacao_bloco
                  ? `${sepultamento.denominacao_bloco} → Gaveta ${sepultamento.numero_gaveta}`
                  : `Gaveta ${sepultamento.numero_gaveta}`;

                return (
                  <TableRow key={sepultamento.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium">
                        {sepultamento.nome_sepultado}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="text.secondary">
                        {localizacao}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium" color="primary.main">
                        {sepultamento.numero_gaveta || '-'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {formatDate(sepultamento.data_sepultamento)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {sepultamento.horario_sepultamento || '-'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={getStatusText(sepultamento.status_exumacao)}
                        color={getStatusColor(sepultamento.status_exumacao)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, alignItems: 'center' }}>
                        {/* Botão Editar - Desabilitado se exumado e usuário não é admin */}
                        <EditButton
                          size="small"
                          startIcon={<EditIcon />}
                          onClick={() => handleEditSepultamento(sepultamento)}
                          disabled={sepultamento.status_exumacao && !isAdmin()}
                          sx={{ minWidth: '80px', fontSize: '0.75rem' }}
                        >
                          Editar
                        </EditButton>

                        {/* Botão Exumar - Apenas se não foi exumado */}
                        {!sepultamento.status_exumacao && (
                          <ExumarButton
                            size="small"
                            startIcon={<ExumarIcon />}
                            onClick={() => handleExumarClick(sepultamento)}
                            sx={{ minWidth: '80px', fontSize: '0.75rem' }}
                          >
                            Exumar
                          </ExumarButton>
                        )}

                        {/* Botão Deletar - Sempre disponível para clientes se não exumado, sempre para admin */}
                        {(!sepultamento.status_exumacao || isAdmin()) && (
                          <DeleteButton
                            size="small"
                            startIcon={<DeleteIcon />}
                            onClick={() => handleDeleteSepultamento(sepultamento)}
                            sx={{ minWidth: '80px', fontSize: '0.75rem' }}
                          >
                            Deletar
                          </DeleteButton>
                        )}
                      </Box>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Modal de Sepultamento */}
      <BookSepultamentoModal
        isOpen={showSepultamentoModal}
        onClose={() => {
          setShowSepultamentoModal(false);
          setSelectedSepultamento(null);
        }}
        sepultamento={selectedSepultamento}
        produto={produto}
        onSuccess={loadSepultamentos}
      />

      {/* Dialog de Exumação */}
      <Dialog open={showExumarDialog} onClose={() => setShowExumarDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Confirmar Exumação</DialogTitle>
        <DialogContent>
          <Typography variant="body1" sx={{ mb: 3 }}>
            Tem certeza que deseja exumar o sepultamento de <strong>{selectedSepultamento?.nome_sepultado}</strong>?
          </Typography>
          
          <TextField
            label="Data da Exumação"
            type="date"
            value={exumarData}
            onChange={(e) => setExumarData(e.target.value)}
            fullWidth
            sx={{ mb: 2 }}
            InputLabelProps={{ shrink: true }}
            required
          />
          
          <TextField
            label="Observações (opcional)"
            multiline
            rows={3}
            value={exumarObservacoes}
            onChange={(e) => setExumarObservacoes(e.target.value)}
            fullWidth
            placeholder="Observações sobre a exumação..."
          />
        </DialogContent>
        <DialogActions>
          <CancelButton onClick={() => setShowExumarDialog(false)}>
            Cancelar
          </CancelButton>
          <ExumarButton
            onClick={handleExumarConfirm}
            variant="contained"
            disabled={!exumarData}
          >
            Confirmar Exumação
          </ExumarButton>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default BookSepultamentosDetalhePage;
