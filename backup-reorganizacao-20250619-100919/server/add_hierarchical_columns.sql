-- SCRIPT PARA ADICIONAR COLUNAS HIERÁRQUICAS ÀS TABELAS EXISTENTES
-- <PERSON><PERSON> script adiciona as colunas de referência hierárquica sem quebrar a estrutura atual

BEGIN;

-- =====================================================
-- 1. TABELA PRODUTOS
-- Adicionar colunas que faltam (algumas já existem)
-- =====================================================

-- Verificar e adicionar codigo_estacao se não existir (já existe)
-- ALTER TABLE produtos ADD COLUMN IF NOT EXISTS codigo_estacao VARCHAR(50);

-- Verificar e adicionar denominacao se não existir (já existe)
-- ALTER TABLE produtos ADD COLUMN IF NOT EXISTS denominacao VARCHAR(255);

-- Verificar e adicionar meses_para_exumar se não existir (já existe)
-- ALTER TABLE produtos ADD COLUMN IF NOT EXISTS meses_para_exumar INTEGER DEFAULT 24;

-- Verificar e adicionar observacao se não existir (já existe)
-- ALTER TABLE produtos ADD COLUMN IF NOT EXISTS observacao TEXT;

-- =====================================================
-- 2. TABELA BLOCOS
-- Adicionar colunas hierárquicas: codigo_cliente, codigo_estacao
-- =====================================================

-- Adicionar codigo_cliente
ALTER TABLE blocos ADD COLUMN IF NOT EXISTS codigo_cliente VARCHAR(50);

-- Adicionar codigo_estacao  
ALTER TABLE blocos ADD COLUMN IF NOT EXISTS codigo_estacao VARCHAR(50);

-- Adicionar denominacao (renomear nome para denominacao se necessário)
ALTER TABLE blocos ADD COLUMN IF NOT EXISTS denominacao VARCHAR(255);

-- Atualizar dados existentes na tabela blocos
UPDATE blocos 
SET codigo_cliente = p.codigo_cliente,
    codigo_estacao = p.codigo_estacao,
    denominacao = COALESCE(blocos.denominacao, blocos.nome)
FROM produtos p 
WHERE blocos.produto_id = p.id 
AND blocos.codigo_cliente IS NULL;

-- =====================================================
-- 3. TABELA SUB_BLOCOS  
-- Adicionar colunas hierárquicas: codigo_cliente, codigo_estacao, codigo_bloco
-- =====================================================

-- Adicionar codigo_cliente
ALTER TABLE sub_blocos ADD COLUMN IF NOT EXISTS codigo_cliente VARCHAR(50);

-- Adicionar codigo_estacao
ALTER TABLE sub_blocos ADD COLUMN IF NOT EXISTS codigo_estacao VARCHAR(50);

-- Adicionar codigo_bloco
ALTER TABLE sub_blocos ADD COLUMN IF NOT EXISTS codigo_bloco VARCHAR(50);

-- Adicionar denominacao (renomear nome para denominacao se necessário)
ALTER TABLE sub_blocos ADD COLUMN IF NOT EXISTS denominacao VARCHAR(255);

-- Atualizar dados existentes na tabela sub_blocos
UPDATE sub_blocos 
SET codigo_cliente = b.codigo_cliente,
    codigo_estacao = b.codigo_estacao,
    codigo_bloco = b.codigo_bloco,
    denominacao = COALESCE(sub_blocos.denominacao, sub_blocos.nome)
FROM blocos b 
WHERE sub_blocos.bloco_id = b.id 
AND sub_blocos.codigo_cliente IS NULL;

-- =====================================================
-- 4. TABELA GAVETAS
-- Adicionar colunas hierárquicas: codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco
-- =====================================================

-- Adicionar codigo_cliente
ALTER TABLE gavetas ADD COLUMN IF NOT EXISTS codigo_cliente VARCHAR(50);

-- Adicionar codigo_estacao
ALTER TABLE gavetas ADD COLUMN IF NOT EXISTS codigo_estacao VARCHAR(50);

-- Adicionar codigo_bloco
ALTER TABLE gavetas ADD COLUMN IF NOT EXISTS codigo_bloco VARCHAR(50);

-- Adicionar codigo_sub_bloco
ALTER TABLE gavetas ADD COLUMN IF NOT EXISTS codigo_sub_bloco VARCHAR(50);

-- Atualizar dados existentes na tabela gavetas
UPDATE gavetas 
SET codigo_cliente = sb.codigo_cliente,
    codigo_estacao = sb.codigo_estacao,
    codigo_bloco = sb.codigo_bloco,
    codigo_sub_bloco = sb.codigo_sub_bloco
FROM sub_blocos sb 
WHERE gavetas.sub_bloco_id = sb.id 
AND gavetas.codigo_cliente IS NULL;

-- =====================================================
-- 5. TABELA NUMERACOES_GAVETAS
-- Adicionar colunas hierárquicas: codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco
-- =====================================================

-- Adicionar codigo_cliente
ALTER TABLE numeracoes_gavetas ADD COLUMN IF NOT EXISTS codigo_cliente VARCHAR(50);

-- Adicionar codigo_estacao
ALTER TABLE numeracoes_gavetas ADD COLUMN IF NOT EXISTS codigo_estacao VARCHAR(50);

-- Adicionar codigo_bloco
ALTER TABLE numeracoes_gavetas ADD COLUMN IF NOT EXISTS codigo_bloco VARCHAR(50);

-- Adicionar codigo_sub_bloco
ALTER TABLE numeracoes_gavetas ADD COLUMN IF NOT EXISTS codigo_sub_bloco VARCHAR(50);

-- Atualizar dados existentes na tabela numeracoes_gavetas
UPDATE numeracoes_gavetas 
SET codigo_cliente = sb.codigo_cliente,
    codigo_estacao = sb.codigo_estacao,
    codigo_bloco = sb.codigo_bloco,
    codigo_sub_bloco = sb.codigo_sub_bloco
FROM sub_blocos sb 
WHERE numeracoes_gavetas.sub_bloco_id = sb.id 
AND numeracoes_gavetas.codigo_cliente IS NULL;

-- =====================================================
-- 6. TABELA SEPULTAMENTOS
-- Verificar e adicionar colunas que faltam (algumas já existem)
-- =====================================================

-- codigo_cliente já existe
-- codigo_estacao já existe  
-- codigo_bloco já existe
-- codigo_sub_bloco já existe
-- numero_gaveta já existe

-- Verificar se falta alguma e atualizar dados se necessário
UPDATE sepultamentos 
SET codigo_estacao = g.codigo_estacao
FROM gavetas g 
WHERE sepultamentos.gaveta_id = g.id 
AND sepultamentos.codigo_estacao IS NULL;

-- =====================================================
-- 7. TABELA LOGS_AUDITORIA
-- Adicionar colunas hierárquicas para contexto
-- =====================================================

-- Adicionar codigo_cliente
ALTER TABLE logs_auditoria ADD COLUMN IF NOT EXISTS codigo_cliente VARCHAR(50);

-- Adicionar codigo_estacao
ALTER TABLE logs_auditoria ADD COLUMN IF NOT EXISTS codigo_estacao VARCHAR(50);

-- Adicionar codigo_bloco
ALTER TABLE logs_auditoria ADD COLUMN IF NOT EXISTS codigo_bloco VARCHAR(50);

-- Adicionar codigo_sub_bloco
ALTER TABLE logs_auditoria ADD COLUMN IF NOT EXISTS codigo_sub_bloco VARCHAR(50);

-- Adicionar descricao
ALTER TABLE logs_auditoria ADD COLUMN IF NOT EXISTS descricao TEXT;

-- =====================================================
-- 8. CRIAR ÍNDICES PARA AS NOVAS COLUNAS
-- =====================================================

-- Índices para blocos
CREATE INDEX IF NOT EXISTS idx_blocos_codigo_cliente ON blocos(codigo_cliente);
CREATE INDEX IF NOT EXISTS idx_blocos_codigo_estacao ON blocos(codigo_cliente, codigo_estacao);
CREATE INDEX IF NOT EXISTS idx_blocos_codigo_bloco ON blocos(codigo_cliente, codigo_estacao, codigo_bloco);

-- Índices para sub_blocos
CREATE INDEX IF NOT EXISTS idx_sub_blocos_codigo_cliente ON sub_blocos(codigo_cliente);
CREATE INDEX IF NOT EXISTS idx_sub_blocos_codigo_estacao ON sub_blocos(codigo_cliente, codigo_estacao);
CREATE INDEX IF NOT EXISTS idx_sub_blocos_codigo_bloco ON sub_blocos(codigo_cliente, codigo_estacao, codigo_bloco);
CREATE INDEX IF NOT EXISTS idx_sub_blocos_codigo_sub_bloco ON sub_blocos(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco);

-- Índices para gavetas
CREATE INDEX IF NOT EXISTS idx_gavetas_codigo_cliente ON gavetas(codigo_cliente);
CREATE INDEX IF NOT EXISTS idx_gavetas_codigo_estacao ON gavetas(codigo_cliente, codigo_estacao);
CREATE INDEX IF NOT EXISTS idx_gavetas_codigo_bloco ON gavetas(codigo_cliente, codigo_estacao, codigo_bloco);
CREATE INDEX IF NOT EXISTS idx_gavetas_codigo_sub_bloco ON gavetas(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco);
CREATE INDEX IF NOT EXISTS idx_gavetas_numero ON gavetas(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta);

-- Índices para numeracoes_gavetas
CREATE INDEX IF NOT EXISTS idx_numeracoes_codigo_cliente ON numeracoes_gavetas(codigo_cliente);
CREATE INDEX IF NOT EXISTS idx_numeracoes_codigo_estacao ON numeracoes_gavetas(codigo_cliente, codigo_estacao);
CREATE INDEX IF NOT EXISTS idx_numeracoes_codigo_bloco ON numeracoes_gavetas(codigo_cliente, codigo_estacao, codigo_bloco);
CREATE INDEX IF NOT EXISTS idx_numeracoes_codigo_sub_bloco ON numeracoes_gavetas(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco);

-- Índices para sepultamentos (alguns já existem)
CREATE INDEX IF NOT EXISTS idx_sepultamentos_codigo_estacao ON sepultamentos(codigo_cliente, codigo_estacao);
CREATE INDEX IF NOT EXISTS idx_sepultamentos_codigo_bloco ON sepultamentos(codigo_cliente, codigo_estacao, codigo_bloco);
CREATE INDEX IF NOT EXISTS idx_sepultamentos_codigo_sub_bloco ON sepultamentos(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco);
CREATE INDEX IF NOT EXISTS idx_sepultamentos_numero_gaveta ON sepultamentos(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta);

-- Índices para logs_auditoria
CREATE INDEX IF NOT EXISTS idx_logs_codigo_cliente ON logs_auditoria(codigo_cliente);
CREATE INDEX IF NOT EXISTS idx_logs_codigo_estacao ON logs_auditoria(codigo_cliente, codigo_estacao);
CREATE INDEX IF NOT EXISTS idx_logs_codigo_bloco ON logs_auditoria(codigo_cliente, codigo_estacao, codigo_bloco);
CREATE INDEX IF NOT EXISTS idx_logs_codigo_sub_bloco ON logs_auditoria(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco);
CREATE INDEX IF NOT EXISTS idx_logs_acao ON logs_auditoria(acao);
CREATE INDEX IF NOT EXISTS idx_logs_tabela ON logs_auditoria(tabela_afetada);

-- =====================================================
-- 9. ADICIONAR FOREIGN KEYS PARA AS NOVAS COLUNAS (OPCIONAL)
-- =====================================================

-- Foreign key para blocos -> clientes
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'blocos_codigo_cliente_fkey'
    ) THEN
        ALTER TABLE blocos 
        ADD CONSTRAINT blocos_codigo_cliente_fkey 
        FOREIGN KEY (codigo_cliente) REFERENCES clientes(codigo_cliente);
    END IF;
END $$;

-- Foreign key para sub_blocos -> clientes
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'sub_blocos_codigo_cliente_fkey'
    ) THEN
        ALTER TABLE sub_blocos 
        ADD CONSTRAINT sub_blocos_codigo_cliente_fkey 
        FOREIGN KEY (codigo_cliente) REFERENCES clientes(codigo_cliente);
    END IF;
END $$;

-- Foreign key para gavetas -> clientes
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'gavetas_codigo_cliente_fkey'
    ) THEN
        ALTER TABLE gavetas 
        ADD CONSTRAINT gavetas_codigo_cliente_fkey 
        FOREIGN KEY (codigo_cliente) REFERENCES clientes(codigo_cliente);
    END IF;
END $$;

-- Foreign key para numeracoes_gavetas -> clientes
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'numeracoes_gavetas_codigo_cliente_fkey'
    ) THEN
        ALTER TABLE numeracoes_gavetas 
        ADD CONSTRAINT numeracoes_gavetas_codigo_cliente_fkey 
        FOREIGN KEY (codigo_cliente) REFERENCES clientes(codigo_cliente);
    END IF;
END $$;

-- Foreign key para logs_auditoria -> clientes
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'logs_auditoria_codigo_cliente_fkey'
    ) THEN
        ALTER TABLE logs_auditoria 
        ADD CONSTRAINT logs_auditoria_codigo_cliente_fkey 
        FOREIGN KEY (codigo_cliente) REFERENCES clientes(codigo_cliente);
    END IF;
END $$;

COMMIT;

-- =====================================================
-- 10. VERIFICAÇÃO FINAL
-- =====================================================

-- Verificar se as colunas foram adicionadas
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name IN ('blocos', 'sub_blocos', 'gavetas', 'numeracoes_gavetas', 'sepultamentos', 'logs_auditoria')
AND column_name IN ('codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco')
ORDER BY table_name, column_name;

-- Verificar dados populados
SELECT 'blocos' as tabela, 
       COUNT(*) as total, 
       COUNT(codigo_cliente) as com_codigo_cliente,
       COUNT(codigo_estacao) as com_codigo_estacao
FROM blocos
UNION ALL
SELECT 'sub_blocos' as tabela, 
       COUNT(*) as total, 
       COUNT(codigo_cliente) as com_codigo_cliente,
       COUNT(codigo_estacao) as com_codigo_estacao
FROM sub_blocos
UNION ALL
SELECT 'gavetas' as tabela, 
       COUNT(*) as total, 
       COUNT(codigo_cliente) as com_codigo_cliente,
       COUNT(codigo_estacao) as com_codigo_estacao
FROM gavetas
UNION ALL
SELECT 'numeracoes_gavetas' as tabela, 
       COUNT(*) as total, 
       COUNT(codigo_cliente) as com_codigo_cliente,
       COUNT(codigo_estacao) as com_codigo_estacao
FROM numeracoes_gavetas;
