const { Pool } = require('pg');

// Configuração direta do banco de dados
const pool = new Pool({
  user: 'postgres',
  host: '************',
  database: 'dbetens',
  password: 'ab3780bd73ee4e2804d566ce6fd96209',
  port: 5432,
});

async function successTest() {
  console.log('🎯 TESTE DE SUCESSO - Colunas Hierárquicas');
  console.log('=' .repeat(50));

  const client = await pool.connect();
  
  try {
    // Gerar códigos únicos
    const timestamp = Math.floor(Date.now() / 1000);
    const testData = {
      codigo_cliente: `T${timestamp}`.slice(0, 10),
      cnpj: '12.345.678/0001-99',
      nome_fantasia: 'Teste Sucesso',
      codigo_estacao: `E${timestamp}`.slice(0, 10),
      denominacao_produto: 'Estação Sucesso',
      codigo_bloco: `B${timestamp}`.slice(0, 10),
      nome_bloco: 'Bloco Sucesso',
      codigo_sub_bloco: `S${timestamp}`.slice(0, 10),
      nome_sub_bloco: 'Sub-bloco Sucesso'
    };
    
    console.log(`📋 Códigos únicos gerados:`);
    console.log(`   Cliente: ${testData.codigo_cliente}`);
    console.log(`   Produto: ${testData.codigo_estacao}`);
    console.log(`   Bloco: ${testData.codigo_bloco}`);
    console.log(`   Sub-bloco: ${testData.codigo_sub_bloco}`);
    
    await client.query('BEGIN');
    
    // 1. Criar cliente
    console.log('\n👤 1. Criando cliente...');
    await client.query(`
      INSERT INTO clientes (codigo_cliente, cnpj, nome_fantasia, razao_social, nome)
      VALUES ($1, $2, $3, $4, $5)
    `, [testData.codigo_cliente, testData.cnpj, testData.nome_fantasia, testData.nome_fantasia, testData.nome_fantasia]);
    console.log('   ✅ Cliente criado');
    
    // 2. Criar produto
    console.log('\n📦 2. Criando produto...');
    await client.query(`
      INSERT INTO produtos (codigo_cliente, codigo_estacao, denominacao, meses_para_exumar, nome, tipo)
      VALUES ($1, $2, $3, 24, $4, 'ETEN')
    `, [testData.codigo_cliente, testData.codigo_estacao, testData.denominacao_produto, testData.denominacao_produto]);
    console.log('   ✅ Produto criado');
    
    // Buscar ID do produto
    const produtoResult = await client.query(`
      SELECT id FROM produtos WHERE codigo_cliente = $1 AND codigo_estacao = $2
    `, [testData.codigo_cliente, testData.codigo_estacao]);
    const produtoId = produtoResult.rows[0].id;
    
    // 3. Criar bloco COM colunas hierárquicas
    console.log('\n🧱 3. Criando bloco com colunas hierárquicas...');
    await client.query(`
      INSERT INTO blocos (produto_id, codigo_bloco, nome, codigo_cliente, codigo_estacao, denominacao)
      VALUES ($1, $2, $3, $4, $5, $6)
    `, [produtoId, testData.codigo_bloco, testData.nome_bloco, testData.codigo_cliente, testData.codigo_estacao, testData.nome_bloco]);
    console.log('   ✅ Bloco criado com referências hierárquicas');
    
    // Buscar ID do bloco
    const blocoResult = await client.query(`
      SELECT id FROM blocos WHERE produto_id = $1 AND codigo_bloco = $2
    `, [produtoId, testData.codigo_bloco]);
    const blocoId = blocoResult.rows[0].id;
    
    // 4. Criar sub-bloco COM colunas hierárquicas
    console.log('\n🔲 4. Criando sub-bloco com colunas hierárquicas...');
    await client.query(`
      INSERT INTO sub_blocos (bloco_id, codigo_sub_bloco, nome, codigo_cliente, codigo_estacao, codigo_bloco, denominacao)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
    `, [blocoId, testData.codigo_sub_bloco, testData.nome_sub_bloco, testData.codigo_cliente, testData.codigo_estacao, testData.codigo_bloco, testData.nome_sub_bloco]);
    console.log('   ✅ Sub-bloco criado com referências hierárquicas');
    
    // Buscar ID do sub-bloco
    const subBlocoResult = await client.query(`
      SELECT id FROM sub_blocos WHERE bloco_id = $1 AND codigo_sub_bloco = $2
    `, [blocoId, testData.codigo_sub_bloco]);
    const subBlocoId = subBlocoResult.rows[0].id;
    
    // 5. Criar gavetas COM colunas hierárquicas
    console.log('\n📊 5. Criando gavetas com colunas hierárquicas...');
    for (let i = 1; i <= 10; i++) {
      await client.query(`
        INSERT INTO gavetas (sub_bloco_id, numero_gaveta, posicao_x, posicao_y, codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco)
        VALUES ($1, $2, 1, 1, $3, $4, $5, $6)
      `, [subBlocoId, i, testData.codigo_cliente, testData.codigo_estacao, testData.codigo_bloco, testData.codigo_sub_bloco]);
    }
    console.log('   ✅ 10 gavetas criadas com referências hierárquicas');
    
    // 6. Criar 5 sepultamentos COM colunas hierárquicas
    console.log('\n⚰️  6. Criando 5 sepultamentos com colunas hierárquicas...');
    const sepultados = [
      { nome: 'João Silva Sucesso', gaveta: 1, data: '2024-01-15' },
      { nome: 'Maria Santos Sucesso', gaveta: 2, data: '2024-01-20' },
      { nome: 'Pedro Oliveira Sucesso', gaveta: 3, data: '2024-01-25' },
      { nome: 'Ana Costa Sucesso', gaveta: 4, data: '2024-02-01' },
      { nome: 'Carlos Lima Sucesso', gaveta: 5, data: '2024-02-05' }
    ];
    
    for (const sepultado of sepultados) {
      // Buscar ID da gaveta
      const gavetaResult = await client.query(`
        SELECT id FROM gavetas WHERE sub_bloco_id = $1 AND numero_gaveta = $2
      `, [subBlocoId, sepultado.gaveta]);
      
      if (gavetaResult.rows.length > 0) {
        const gavetaId = gavetaResult.rows[0].id;
        
        await client.query(`
          INSERT INTO sepultamentos (
            gaveta_id, nome_sepultado, codigo_cliente, codigo_bloco, codigo_sub_bloco, 
            numero_gaveta, data_sepultamento, codigo_estacao, horario_sepultamento
          )
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, '14:00')
        `, [
          gavetaId, 
          sepultado.nome, 
          testData.codigo_cliente, 
          testData.codigo_bloco, 
          testData.codigo_sub_bloco, 
          sepultado.gaveta, 
          sepultado.data,
          testData.codigo_estacao
        ]);
        
        // Marcar gaveta como ocupada
        await client.query(`UPDATE gavetas SET disponivel = false WHERE id = $1`, [gavetaId]);
        
        console.log(`   ✅ ${sepultado.nome} - Gaveta ${sepultado.gaveta}`);
      }
    }
    
    await client.query('COMMIT');
    console.log('\n🎉 Todos os dados criados com sucesso!');
    
    // 7. VERIFICAR DADOS USANDO CONSULTAS HIERÁRQUICAS
    console.log('\n🔍 7. VERIFICANDO COM CONSULTAS POR CÓDIGOS:');
    
    // Consulta direta por códigos (SEM JOINs por ID)
    const directQuery = await client.query(`
      SELECT 
        g.codigo_cliente,
        g.codigo_estacao,
        g.codigo_bloco,
        g.codigo_sub_bloco,
        g.numero_gaveta,
        g.disponivel,
        s.nome_sepultado,
        s.data_sepultamento
      FROM gavetas g
      LEFT JOIN sepultamentos s ON g.codigo_cliente = s.codigo_cliente 
                                AND g.codigo_estacao = s.codigo_estacao 
                                AND g.codigo_bloco = s.codigo_bloco 
                                AND g.codigo_sub_bloco = s.codigo_sub_bloco 
                                AND g.numero_gaveta = s.numero_gaveta
                                AND s.ativo = true
      WHERE g.codigo_cliente = $1 
      AND g.codigo_estacao = $2
      AND g.codigo_bloco = $3
      AND g.codigo_sub_bloco = $4
      ORDER BY g.numero_gaveta
    `, [testData.codigo_cliente, testData.codigo_estacao, testData.codigo_bloco, testData.codigo_sub_bloco]);
    
    console.log(`   📊 Gavetas encontradas: ${directQuery.rows.length}`);
    directQuery.rows.forEach(row => {
      const status = row.disponivel ? '🟢 Disponível' : '🔴 Ocupada';
      const sepultado = row.nome_sepultado ? ` - ${row.nome_sepultado}` : '';
      console.log(`      ${status} Gaveta ${row.numero_gaveta}${sepultado}`);
      console.log(`         📍 ${row.codigo_cliente}/${row.codigo_estacao}/${row.codigo_bloco}/${row.codigo_sub_bloco}`);
    });
    
    // 8. TESTE DE IMPORTAÇÃO USANDO APENAS CÓDIGOS
    console.log('\n📥 8. TESTE DE IMPORTAÇÃO POR CÓDIGOS:');
    
    // Simular dados vindos de sistema externo
    const dadosExternos = {
      codigo_cliente: testData.codigo_cliente,
      codigo_estacao: testData.codigo_estacao,
      codigo_bloco: testData.codigo_bloco,
      codigo_sub_bloco: testData.codigo_sub_bloco,
      numero_gaveta: 6,
      nome_sepultado: 'Importado Via Códigos',
      data_sepultamento: '2024-03-01'
    };
    
    // Buscar gaveta usando APENAS códigos hierárquicos
    const gavetaImportacao = await client.query(`
      SELECT id, disponivel FROM gavetas 
      WHERE codigo_cliente = $1 
      AND codigo_estacao = $2
      AND codigo_bloco = $3
      AND codigo_sub_bloco = $4
      AND numero_gaveta = $5
    `, [
      dadosExternos.codigo_cliente,
      dadosExternos.codigo_estacao,
      dadosExternos.codigo_bloco,
      dadosExternos.codigo_sub_bloco,
      dadosExternos.numero_gaveta
    ]);
    
    if (gavetaImportacao.rows.length > 0) {
      const gaveta = gavetaImportacao.rows[0];
      
      await client.query(`
        INSERT INTO sepultamentos (
          gaveta_id, nome_sepultado, codigo_cliente, codigo_estacao, codigo_bloco, 
          codigo_sub_bloco, numero_gaveta, data_sepultamento, horario_sepultamento
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, '16:00')
      `, [
        gaveta.id,
        dadosExternos.nome_sepultado,
        dadosExternos.codigo_cliente,
        dadosExternos.codigo_estacao,
        dadosExternos.codigo_bloco,
        dadosExternos.codigo_sub_bloco,
        dadosExternos.numero_gaveta,
        dadosExternos.data_sepultamento
      ]);
      
      await client.query(`UPDATE gavetas SET disponivel = false WHERE id = $1`, [gaveta.id]);
      
      console.log(`   ✅ SUCESSO! Sepultamento importado usando códigos:`);
      console.log(`      Nome: ${dadosExternos.nome_sepultado}`);
      console.log(`      Localização: ${dadosExternos.codigo_cliente}/${dadosExternos.codigo_estacao}/${dadosExternos.codigo_bloco}/${dadosExternos.codigo_sub_bloco}/Gaveta-${dadosExternos.numero_gaveta}`);
    }
    
    // 9. VERIFICAÇÃO FINAL
    console.log('\n📊 9. VERIFICAÇÃO FINAL:');
    
    const finalCheck = await client.query(`
      SELECT 
        codigo_cliente,
        codigo_estacao,
        codigo_bloco,
        codigo_sub_bloco,
        COUNT(*) as total_gavetas,
        COUNT(CASE WHEN disponivel = false THEN 1 END) as gavetas_ocupadas
      FROM gavetas
      WHERE codigo_cliente = $1 
      GROUP BY codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco
    `, [testData.codigo_cliente]);
    
    if (finalCheck.rows.length > 0) {
      const stat = finalCheck.rows[0];
      console.log(`   📈 ${stat.codigo_cliente}/${stat.codigo_estacao}/${stat.codigo_bloco}/${stat.codigo_sub_bloco}:`);
      console.log(`      Total: ${stat.total_gavetas} gavetas`);
      console.log(`      Ocupadas: ${stat.gavetas_ocupadas} gavetas`);
    }
    
    console.log('\n🎉 TESTE DE SUCESSO CONCLUÍDO!');
    console.log('=' .repeat(50));
    console.log('✅ COLUNAS HIERÁRQUICAS FUNCIONANDO!');
    console.log('✅ CONSULTAS POR CÓDIGOS OPERACIONAIS!');
    console.log('✅ IMPORTAÇÃO POR CÓDIGOS TESTADA!');
    console.log('✅ SISTEMA PRONTO PARA DADOS EXTERNOS!');
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Erro:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

// Executar teste
if (require.main === module) {
  successTest().catch(error => {
    console.error('❌ Erro fatal:', error);
    process.exit(1);
  });
}

module.exports = { successTest };
