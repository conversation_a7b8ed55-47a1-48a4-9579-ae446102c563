const express = require('express');
const { query } = require('../database/connection');

const router = express.Router();

// Listar gavetas - Nova estrutura baseada em códigos
router.get('/', async (req, res) => {
  try {
    const {
      sub_bloco_id,
      codigo_cliente,
      codigo_estacao,
      codigo_bloco,
      codigo_sub_bloco,
      disponivel
    } = req.query;

    let whereClause = 'WHERE g.ativo = true';
    let params = [];
    let paramCount = 0;

    // Filtrar por códigos se fornecidos
    if (codigo_cliente) {
      paramCount++;
      whereClause += ` AND g.codigo_cliente = $${paramCount}`;
      params.push(codigo_cliente);
    }

    if (codigo_estacao) {
      paramCount++;
      whereClause += ` AND g.codigo_estacao = $${paramCount}`;
      params.push(codigo_estacao);
    }

    if (codigo_bloco) {
      paramCount++;
      whereClause += ` AND g.codigo_bloco = $${paramCount}`;
      params.push(codigo_bloco);
    }

    if (codigo_sub_bloco) {
      paramCount++;
      whereClause += ` AND g.codigo_sub_bloco = $${paramCount}`;
      params.push(codigo_sub_bloco);
    }

    // Filtrar por disponibilidade se especificado
    if (disponivel !== undefined) {
      paramCount++;
      whereClause += ` AND g.disponivel = $${paramCount}`;
      params.push(disponivel === 'true' || disponivel === true);
    }

    // Filtro de sub_bloco_id para compatibilidade com código antigo
    if (sub_bloco_id) {
      paramCount++;
      whereClause += ` AND g.sub_bloco_id = $${paramCount}`;
      params.push(sub_bloco_id);
    }

    // Se não for admin, filtrar por código do cliente do usuário
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente && !codigo_cliente) {
      paramCount++;
      whereClause += ` AND g.codigo_cliente = $${paramCount}`;
      params.push(req.user.codigo_cliente);
    }

    const result = await query(`
      SELECT
        g.*,
        s.id as sepultamento_id,
        s.nome_sepultado,
        s.data_sepultamento,
        s.status_exumacao,
        s.exumado_em
      FROM gavetas g
      LEFT JOIN sepultamentos s ON g.codigo_cliente = s.codigo_cliente
        AND g.codigo_estacao = s.codigo_estacao
        AND g.codigo_bloco = s.codigo_bloco
        AND g.codigo_sub_bloco = s.codigo_sub_bloco
        AND g.numero_gaveta = s.numero_gaveta
        AND s.ativo = true
        AND s.status_exumacao = false
      ${whereClause}
      ORDER BY g.numero_gaveta
    `, params);

    res.json(result.rows);
  } catch (error) {
    console.error('Erro ao listar gavetas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Listar gavetas por bloco
router.get('/bloco', async (req, res) => {
  try {
    const { bloco_id, disponivel } = req.query;

    if (!bloco_id) {
      return res.status(400).json({ error: 'bloco_id é obrigatório' });
    }

    let whereClause = 'WHERE b.id = $1 AND g.ativo = true';
    let params = [bloco_id];
    let paramCount = 1;

    // Se não for admin, filtrar por código do cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      paramCount++;
      whereClause += ` AND p.codigo_cliente = $${paramCount}`;
      params.push(req.user.codigo_cliente);
    }

    // Filtrar por disponibilidade se especificado
    if (disponivel !== undefined) {
      paramCount++;
      whereClause += ` AND g.disponivel = $${paramCount}`;
      params.push(disponivel === 'true' || disponivel === true);
    }

    const result = await query(`
      SELECT
        g.*,
        s.id as sepultamento_id,
        s.nome_sepultado,
        s.data_sepultamento,
        s.data_exumacao,
        s.posicao,
        s.observacoes,
        sb.codigo_sub_bloco,
        sb.nome as sub_bloco_denominacao,
        b.codigo_bloco,
        b.nome as bloco_denominacao,
        p.codigo_cliente,
        p.codigo_estacao,
        CONCAT(b.nome, ' - Sub Bloco ', sb.codigo_sub_bloco, ' - Gaveta ', g.numero_gaveta) as localizacao_completa
      FROM gavetas g
      JOIN sub_blocos sb ON g.sub_bloco_id = sb.id
      JOIN blocos b ON sb.bloco_id = b.id
      JOIN produtos p ON b.produto_id = p.id
      LEFT JOIN sepultamentos s ON g.id = s.gaveta_id AND s.ativo = true AND s.data_exumacao IS NULL
      ${whereClause}
      ORDER BY sb.codigo_sub_bloco, g.numero_gaveta
    `, params);

    res.json(result.rows);
  } catch (error) {
    console.error('Erro ao listar gavetas por bloco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Buscar gaveta por ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const result = await query(`
      SELECT 
        g.*,
        s.id as sepultamento_id,
        s.nome_sepultado,
        s.data_sepultamento,
        s.data_exumacao,
        s.posicao,
        s.observacoes,
        sb.codigo_sub_bloco,
        b.codigo_bloco,
        p.codigo_cliente
      FROM gavetas g
      LEFT JOIN sub_blocos sb ON g.sub_bloco_id = sb.id
      LEFT JOIN blocos b ON sb.bloco_id = b.id
      LEFT JOIN produtos p ON b.produto_id = p.id
      LEFT JOIN sepultamentos s ON g.id = s.gaveta_id AND s.ativo = true AND s.data_exumacao IS NULL
      WHERE g.id = $1
    `, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Gaveta não encontrada' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Erro ao buscar gaveta:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Buscar histórico de uma gaveta
router.get('/:id/historico', async (req, res) => {
  try {
    const { id } = req.params;
    
    const result = await query(`
      SELECT 
        s.*,
        c.nome as nome_cliente
      FROM sepultamentos s
      LEFT JOIN clientes c ON s.codigo_cliente = c.codigo_cliente
      WHERE s.gaveta_id = $1 AND s.ativo = true
      ORDER BY s.data_sepultamento DESC
    `, [id]);

    res.json(result.rows);
  } catch (error) {
    console.error('Erro ao buscar histórico da gaveta:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
