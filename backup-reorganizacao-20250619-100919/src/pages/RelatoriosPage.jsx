import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Paper,
  Box,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  Card,
  CardContent,
  Chip,
  Divider,
  CircularProgress
} from '@mui/material';
import {
  Assessment as ReportIcon,
  DateRange as DateIcon,
  Business as BusinessIcon,
  TrendingUp as TrendingIcon,
  Person as PersonIcon,
  CalendarToday as CalendarIcon,
  PictureAsPdf as PdfIcon
} from '@mui/icons-material';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { useAuth } from '../contexts/AuthContext';
import { produtoService, sepultamentoService, gavetaService } from '../services/api';

const RelatoriosPage = () => {
  const { user, isClient } = useAuth();
  const [produtos, setProdutos] = useState([]);
  const [selectedProduto, setSelectedProduto] = useState('');
  const [dataInicio, setDataInicio] = useState('');
  const [dataFim, setDataFim] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [relatorioData, setRelatorioData] = useState(null);

  useEffect(() => {
    loadProdutos();
  }, []);

  const loadProdutos = async () => {
    try {
      const response = await produtoService.listar();
      setProdutos(response.data || []);
    } catch (error) {
      console.error('Erro ao carregar produtos:', error);
      setError('Erro ao carregar produtos');
    }
  };

  const handleGerarRelatorio = async () => {
    if (!selectedProduto || !dataInicio || !dataFim) {
      setError('Por favor, selecione o produto e as datas de início e fim');
      return;
    }

    if (new Date(dataInicio) > new Date(dataFim)) {
      setError('A data de início deve ser anterior à data de fim');
      return;
    }

    try {
      setLoading(true);
      setError('');

      // Buscar produto selecionado para obter códigos
      const produtoResponse = await produtoService.buscarCompleto(selectedProduto);
      const produto = produtoResponse.data.produto; // Corrigido: acessar produto dentro da resposta

      console.log('🔍 Produto encontrado:', produto);

      // CORREÇÃO DEFINITIVA: Buscar sepultamentos usando Data Fim +1 para garantir inclusão do último dia
      const dataFimMais1 = new Date(dataFim);
      dataFimMais1.setDate(dataFimMais1.getDate() + 1);
      const dataFimMais1Str = dataFimMais1.toISOString().split('T')[0];

      console.log('🔧 CORREÇÃO DEFINITIVA - Buscando dados do backend:', {
        dataInicio: dataInicio,
        dataFimOriginal: dataFim,
        dataFimMais1: dataFimMais1Str,
        observacao: 'Backend receberá Data Fim +1 para garantir inclusão do último dia'
      });

      const response = await sepultamentoService.listar({
        codigo_cliente: produto.codigo_cliente,
        codigo_estacao: produto.codigo_estacao,
        data_inicio: dataInicio,
        data_fim: dataFimMais1Str // Usar Data Fim +1 para busca no backend
      });

      const sepultamentos = response.data || [];
      console.log('🔍 Sepultamentos do período encontrados:', sepultamentos.length);
      console.log('🔍 Primeiros 3 sepultamentos:', sepultamentos.slice(0, 3));

      // Buscar dados de gavetas para cálculos de ocupação
      const gavetasResponse = await gavetaService.listarPorProduto({
        codigo_cliente: produto.codigo_cliente,
        codigo_estacao: produto.codigo_estacao
      });

      const gavetas = gavetasResponse.data || [];
      console.log('🔍 Gavetas encontradas:', gavetas.length);

      // Buscar TODOS os sepultamentos ativos (não exumados) até a data fim para cálculo de ocupação
      const sepultamentosAtivosResponse = await sepultamentoService.listar({
        codigo_cliente: produto.codigo_cliente,
        codigo_estacao: produto.codigo_estacao,
        data_fim: dataFim, // Apenas até a data fim
        incluir_todos_ate_data_fim: true // Flag para incluir todos até a data fim
      });

      const sepultamentosAtivos = sepultamentosAtivosResponse.data || [];
      console.log('🔍 Sepultamentos ativos até data fim:', sepultamentosAtivos.length);

      // Processar dados para o relatório com novos cálculos
      const relatorio = processarDadosRelatorio(sepultamentos, gavetas, sepultamentosAtivos, produto, dataInicio, dataFim);
      setRelatorioData(relatorio);

    } catch (error) {
      console.error('Erro ao gerar relatório:', error);
      setError('Erro ao gerar relatório');
    } finally {
      setLoading(false);
    }
  };

  const processarDadosRelatorio = (sepultamentos, gavetas, sepultamentosAtivos, produto, inicio, fim) => {
    // Trabalhar apenas com strings de data para evitar problemas de fuso horário
    const dataInicioStr = inicio; // Formato YYYY-MM-DD
    const dataFimStr = fim; // Formato YYYY-MM-DD

    // Criar objetos Date apenas para cálculos de diferença
    const dataInicioObj = new Date(inicio + 'T00:00:00');
    const dataFimObj = new Date(fim + 'T00:00:00');

    // Calcular quantidade de dias no período ORIGINAL (para exibição)
    const diffTime = Math.abs(dataFimObj - dataInicioObj);
    const diffDaysOriginal = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 para incluir o último dia

    // NOVA LÓGICA: Criar variável "Data Corrigida" = "Data de Fim" + 1 dia
    const dataCorrigida = new Date(dataFimStr);
    dataCorrigida.setDate(dataCorrigida.getDate() + 1); // Data Corrigida = Data Fim + 1 dia
    const dataCorrigidaStr = dataCorrigida.toISOString().split('T')[0]; // Formato YYYY-MM-DD

    // NOVA LÓGICA: Criar array de dias incluindo Data Fim +1 para ambas as tabelas
    const diasPeriodo = [];
    const [anoInicio, mesInicio, diaInicio] = dataInicioStr.split('-').map(Number);
    const [anoCorrigida, mesCorrigida, diaCorrigida] = dataCorrigidaStr.split('-').map(Number);

    let currentDate = new Date(anoInicio, mesInicio - 1, diaInicio); // Mês é 0-indexado
    const endDateComDataFimMais1 = new Date(anoCorrigida, mesCorrigida - 1, diaCorrigida); // Data Fim +1

    while (currentDate <= endDateComDataFimMais1) {
      const year = currentDate.getFullYear();
      const month = String(currentDate.getMonth() + 1).padStart(2, '0');
      const day = String(currentDate.getDate()).padStart(2, '0');
      diasPeriodo.push(`${year}-${month}-${day}`);
      currentDate.setDate(currentDate.getDate() + 1);
    }

    console.log('🔧 Período solicitado (CORREÇÃO DEFINITIVA - BACKEND COM DATA FIM +1):', {
      inicio: dataInicioStr,
      fim: dataFimStr,
      dataCorrigida: dataCorrigidaStr,
      inicioFormatado: dataInicioObj.toLocaleDateString('pt-BR'),
      fimFormatado: dataFimObj.toLocaleDateString('pt-BR'),
      dataCorrigidaFormatada: dataCorrigida.toLocaleDateString('pt-BR'),
      totalDiasComDataFimMais1: diasPeriodo.length,
      diasPeriodo: diasPeriodo.slice(0, 5).concat(['...', diasPeriodo[diasPeriodo.length - 1]]),
      dataInicioCompleta: new Date(dataInicioStr + 'T00:00:00').toISOString(),
      dataFimCompleta: new Date(dataFimStr + 'T23:59:59').toISOString(),
      dataCorrigidaCompleta: new Date(dataCorrigidaStr + 'T23:59:59').toISOString(),
      observacao: 'CORREÇÃO DEFINITIVA: Backend busca com Data Fim +1, tabelas incluem Data Fim +1, Resumo por Dia esconde último registro'
    });

    // NOVA LÓGICA ESPECÍFICA: Garantir que o último dia seja sempre incluído - CORREÇÃO DEFINITIVA
    const sepultamentosFiltrados = sepultamentos.filter(sep => {
      const dataSepultamento = new Date(sep.data_sepultamento);

      // Criar data de início (00:00:00 do dia inicial)
      const dataInicioCompleta = new Date(dataInicioStr + 'T00:00:00');

      // NOVA LÓGICA: Usar Data Corrigida para garantir inclusão do último dia
      const dataCorrigidaCompleta = new Date(dataCorrigidaStr + 'T23:59:59');

      const isInRange = dataSepultamento >= dataInicioCompleta && dataSepultamento <= dataCorrigidaCompleta;

      // Debug específico para o último dia - CORREÇÃO DEFINITIVA APLICADA
      if (sep.data_sepultamento && (sep.data_sepultamento.includes('2023-03-31') || sep.data_sepultamento.includes('2023-04-01'))) {
        console.log('🔧 DEBUG COM CORREÇÃO DEFINITIVA - BACKEND COM DATA FIM +1:', {
          nome: sep.nome_sepultado,
          dataSepultamento: sep.data_sepultamento,
          dataSepultamentoObj: dataSepultamento.toISOString(),
          dataInicioCompleta: dataInicioCompleta.toISOString(),
          dataCorrigidaCompleta: dataCorrigidaCompleta.toISOString(),
          isInRange: isInRange,
          comparacao1: dataSepultamento >= dataInicioCompleta,
          comparacao2: dataSepultamento <= dataCorrigidaCompleta,
          observacao: 'CORREÇÃO DEFINITIVA: Backend busca com Data Fim +1, frontend filtra com Data Corrigida'
        });
      }

      return isInRange;
    });

    // LÓGICA ADICIONAL: Forçar inclusão de sepultamentos do último dia se não foram incluídos
    const sepultamentosUltimoDia = sepultamentos.filter(sep => {
      const dataSep = new Date(sep.data_sepultamento);
      const dataFimCompleta = new Date(dataFimStr + 'T23:59:59');
      const dataFimInicio = new Date(dataFimStr + 'T00:00:00');
      return dataSep >= dataFimInicio && dataSep <= dataFimCompleta;
    });

    // Adicionar sepultamentos do último dia que não foram incluídos
    sepultamentosUltimoDia.forEach(sep => {
      if (!sepultamentosFiltrados.find(s => s.id === sep.id)) {
        sepultamentosFiltrados.push(sep);
        console.log('🔧 FORÇANDO INCLUSÃO DO ÚLTIMO DIA:', {
          nome: sep.nome_sepultado,
          data: sep.data_sepultamento,
          observacao: 'Sepultamento do último dia adicionado forçadamente'
        });
      }
    });

    // Agrupar sepultamentos por dia - CORREÇÃO APLICADA: Evitar problemas de timezone
    const sepultamentosPorDia = {};
    sepultamentosFiltrados.forEach(sep => {
      // Extrair data sem problemas de timezone
      const dataSepultamento = new Date(sep.data_sepultamento);
      const year = dataSepultamento.getFullYear();
      const month = String(dataSepultamento.getMonth() + 1).padStart(2, '0');
      const day = String(dataSepultamento.getDate()).padStart(2, '0');
      const dataSep = `${year}-${month}-${day}`;

      if (!sepultamentosPorDia[dataSep]) {
        sepultamentosPorDia[dataSep] = [];
      }
      sepultamentosPorDia[dataSep].push(sep);
    });

    console.log('🔍 Debug filtros de data (COM HORÁRIO COMPLETO):', {
      sepultamentosTotais: sepultamentos.length,
      sepultamentosFiltrados: sepultamentosFiltrados.length,
      primeiroSepultamentoFiltrado: sepultamentosFiltrados[0]?.data_sepultamento,
      ultimoSepultamentoFiltrado: sepultamentosFiltrados[sepultamentosFiltrados.length - 1]?.data_sepultamento,
      diasComSepultamentos: Object.keys(sepultamentosPorDia).sort(),
      sepultamentosUltimoDia: sepultamentosPorDia[dataFimStr]?.length || 0,
      sepultamentosUltimoDiaDetalhes: sepultamentosPorDia[dataFimStr]?.map(s => ({
        nome: s.nome_sepultado,
        data: s.data_sepultamento,
        horario: s.horario_sepultamento
      })) || []
    });

    // Criar visão geral por dia - CORREÇÃO APLICADA: diasPeriodo inclui Data Corrigida
    const visaoGeralCompleta = diasPeriodo.map(diaStr => {
      const sepultamentosDia = sepultamentosPorDia[diaStr] || [];

      // Converter string para Date apenas para formatação
      const [ano, mes, dia] = diaStr.split('-').map(Number);
      const dataObj = new Date(ano, mes - 1, dia);

      return {
        data: diaStr,
        dataFormatada: dataObj.toLocaleDateString('pt-BR'),
        quantidade: sepultamentosDia.length,
        sepultamentos: sepultamentosDia
      };
    });

    // NOVA LÓGICA: Tabela "Resumo por Dia" esconde último registro (Data Fim +1)
    // Tabela "Todos os Sepultamentos" mantém todos os dados incluindo Data Fim +1
    // Requisito: Dados até Data Fim +1, mas Resumo por Dia esconde último dia
    const visaoGeral = visaoGeralCompleta.slice(0, -1); // Remove último elemento (Data Fim +1) da tabela Resumo por Dia

    console.log('🔧 Debug visão geral (CORREÇÃO DEFINITIVA - BACKEND COM DATA FIM +1):', {
      totalDiasVisaoGeralCompleta: visaoGeralCompleta.length,
      totalDiasVisaoGeralTabela: visaoGeral.length,
      primeiroDiaVisaoGeral: visaoGeral[0]?.data,
      ultimoDiaVisaoGeral: visaoGeral[visaoGeral.length - 1]?.data,
      ultimoDiaOcultoResumo: visaoGeralCompleta[visaoGeralCompleta.length - 1]?.data,
      periodoUsuario: `${dataInicioStr} até ${dataFimStr}`,
      periodoTabelas: `${dataInicioStr} até ${dataCorrigidaStr}`,
      diasComQuantidade: visaoGeral.filter(d => d.quantidade > 0).map(d => ({ data: d.data, dataFormatada: d.dataFormatada, quantidade: d.quantidade })),
      totalSepultamentosVisaoGeral: visaoGeral.reduce((sum, d) => sum + d.quantidade, 0),
      totalSepultamentosCompletos: visaoGeralCompleta.reduce((sum, d) => sum + d.quantidade, 0),
      sepultamentosPorDiaKeys: Object.keys(sepultamentosPorDia).sort(),
      observacao: 'CORREÇÃO DEFINITIVA APLICADA: Backend busca com Data Fim +1, dados até Data Fim +1, Resumo por Dia esconde último registro'
    });

    // Ordenar sepultamentos por data crescente - MANTÉM TODOS OS DADOS (incluindo Data Fim +1)
    const todosSepultamentos = sepultamentosFiltrados.sort((a, b) =>
      new Date(a.data_sepultamento) - new Date(b.data_sepultamento)
    );

    // Criar variável específica para tabela "Todos os Sepultamentos" com dados completos
    const visaoGeralCompleta_TodosSepultamentos = visaoGeralCompleta; // Mantém todos os dados incluindo Data Fim +1

    // CORREÇÃO FINAL: Indicadores usam apenas dias originais, dados usam +1 dia
    const totalSepultamentosPeriodo = sepultamentosFiltrados.length;
    const taxaSepultamentosPorDia = diffDaysOriginal > 0 ? (totalSepultamentosPeriodo / diffDaysOriginal).toFixed(2) : 0;

    // CORREÇÃO COMPLETA: Debug específico para mostrar diferença entre período exibido e cálculos
    console.log('🎯 CORREÇÃO COMPLETA - Diferença entre período exibido e cálculos:', {
      periodoExibido: {
        inicio: dataInicioStr,
        fim: dataFimStr,
        diasOriginais: diffDaysOriginal,
        textoCompleto: `${dataInicioObj.toLocaleDateString('pt-BR')} a ${dataFimObj.toLocaleDateString('pt-BR')} - ${diffDaysOriginal} dias`
      },
      calculosIndicadores: {
        inicio: dataInicioStr,
        fim: dataFimStr,
        diasOriginais: diffDaysOriginal,
        textoCompleto: `${dataInicioObj.toLocaleDateString('pt-BR')} a ${dataFimObj.toLocaleDateString('pt-BR')} - ${diffDaysOriginal} dias (para indicadores)`
      },
      indicadores: {
        totalSepultamentos: totalSepultamentosPeriodo,
        taxaSepultamentosPorDia: `${totalSepultamentosPeriodo} ÷ ${diffDaysOriginal} = ${taxaSepultamentosPorDia}`
      },
      observacao: 'CORREÇÃO DEFINITIVA IMPLEMENTADA: Backend busca com Data Fim +1, dados até Data Fim +1, Resumo por Dia esconde último registro'
    });

    // NOVOS CÁLCULOS DE GAVETAS CORRIGIDOS
    const totalGavetas = gavetas.length;

    // Gavetas ocupadas = Total de sepultamentos ativos (não exumados) até Data Corrigida para consistência
    const sepultamentosAtivosAteFim = sepultamentosAtivos.filter(sep => {
      const dataSepultamento = new Date(sep.data_sepultamento);

      // NOVA LÓGICA ESPECÍFICA: Usar Data Corrigida para consistência com filtro de dados
      const dataCorrigidaCompleta = new Date(dataCorrigidaStr + 'T23:59:59');

      return dataSepultamento <= dataCorrigidaCompleta && !sep.status_exumacao && sep.ativo !== false;
    });

    const gavetasOcupadas = sepultamentosAtivosAteFim.length;

    // Gavetas disponíveis = Total de gavetas - Gavetas ocupadas
    const gavetasDisponiveis = totalGavetas - gavetasOcupadas;

    // Taxa de ocupação = (Gavetas ocupadas / Total de gavetas) * 100
    const taxaOcupacao = totalGavetas > 0 ? ((gavetasOcupadas / totalGavetas) * 100).toFixed(1) : 0;

    return {
      visaoGeral, // Resumo por Dia (esconde último registro)
      visaoGeralCompleta, // Dados completos incluindo Data Fim +1
      todosSepultamentos, // Todos os sepultamentos (incluindo Data Fim +1)
      totalSepultamentos: totalSepultamentosPeriodo, // Apenas do período
      totalSepultamentosPeriodo, // Novo campo específico
      taxaSepultamentosPorDia, // Novo cálculo (usa diffDaysOriginal)
      diffDays: diffDaysOriginal, // Quantidade de dias ORIGINAL (para exibição e indicadores)
      // Novos dados de gavetas
      totalGavetas,
      gavetasOcupadas,
      gavetasDisponiveis,
      taxaOcupacao,
      produto: {
        denominacao: produto?.denominacao || produto?.nome || 'Produto não identificado',
        nome_completo: produto?.denominacao || produto?.nome || 'Produto não identificado',
        codigo_cliente: produto?.codigo_cliente,
        codigo_estacao: produto?.codigo_estacao,
        // Debug: incluir dados brutos para verificação
        _debug_produto: produto
      },
      periodo: {
        inicio: dataInicioObj.toLocaleDateString('pt-BR'),
        fim: dataFimObj.toLocaleDateString('pt-BR'),
        // CORREÇÃO COMPLETA: Período exibido usa datas originais (conforme inserido pelo usuário)
        textoCompleto: `${dataInicioObj.toLocaleDateString('pt-BR')} a ${dataFimObj.toLocaleDateString('pt-BR')} - Total de ${diffDaysOriginal} Dias`
      }
    };
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const formatTime = (timeString) => {
    if (!timeString) return '-';
    return timeString.substring(0, 5); // HH:MM
  };

  const handleEmitirPDF = async () => {
    if (!relatorioData) return;

    try {
      // Criar PDF com texto nativo para melhor qualidade
      const pdf = new jsPDF('p', 'mm', 'a4');
      const pageWidth = 210; // A4 width in mm
      const pageHeight = 297; // A4 height in mm
      let currentY = 40; // Posição Y inicial (após cabeçalho)
      let pageNumber = 1;

      // Função para adicionar cabeçalho e rodapé otimizada
      const addHeaderFooter = (pdf, pageNum, totalPages) => {
        // Cabeçalho
        pdf.setFontSize(10);
        pdf.setTextColor(100, 100, 100);
        pdf.text('Portal Evolution - Sistema de Gestão de Sepultamentos', 20, 15);
        pdf.text(`${relatorioData.produto?.nome_completo || 'N/A'}`, 20, 25);

        // Linha separadora do cabeçalho
        pdf.setDrawColor(25, 118, 210);
        pdf.setLineWidth(0.5);
        pdf.line(20, 30, 190, 30);

        // Rodapé
        pdf.setFontSize(8);
        pdf.setTextColor(150, 150, 150);
        const footerY = 285;

        // Linha separadora do rodapé
        pdf.line(20, footerY - 5, 190, footerY - 5);

        // Informações do rodapé
        pdf.text(`Página ${pageNum}`, 20, footerY);
        pdf.text(`Gerado em ${new Date().toLocaleDateString('pt-BR')} às ${new Date().toLocaleTimeString('pt-BR')}`, 105, footerY, { align: 'center' });
        pdf.text(`${relatorioData.periodo.textoCompleto}`, 190, footerY, { align: 'right' });
      };

      // Carregar logo para marca d'água
      const logoImg = new Image();
      logoImg.crossOrigin = 'anonymous';

      const generatePDFContent = () => {
        // Adicionar cabeçalho e rodapé na primeira página
        addHeaderFooter(pdf, pageNumber, 1);

        // Título principal
        pdf.setFontSize(20);
        pdf.setTextColor(25, 118, 210);
        pdf.text('Relatório de Sepultamentos', 105, currentY, { align: 'center' });
        currentY += 15;

        // Informações do produto e período
        pdf.setFontSize(12);
        pdf.setTextColor(0, 0, 0);
        pdf.text(`Produto: ${relatorioData.produto?.nome_completo || 'N/A'}`, 20, currentY);
        currentY += 8;
        pdf.text(`Período: ${relatorioData.periodo.textoCompleto}`, 20, currentY);
        currentY += 15;

        // Linha separadora
        pdf.setDrawColor(25, 118, 210);
        pdf.setLineWidth(1);
        pdf.line(20, currentY, 190, currentY);
        currentY += 15;

        // Título do Resumo Executivo
        pdf.setFontSize(16);
        pdf.setTextColor(25, 118, 210);
        pdf.text('Resumo Executivo', 20, currentY);
        currentY += 15;

        // Criar grid de resumo executivo com retângulos
        const resumoData = [
          { valor: relatorioData.totalSepultamentosPeriodo, label: 'Total de Sepultamentos no Período' },
          { valor: relatorioData.taxaSepultamentosPorDia, label: 'Taxa de Sepultamentos por Dia' },
          { valor: `${relatorioData.taxaOcupacao}%`, label: 'Taxa de Ocupação Final' },
          { valor: relatorioData.totalGavetas, label: 'Total de Gavetas' },
          { valor: relatorioData.gavetasOcupadas, label: 'Gavetas Ocupadas' },
          { valor: relatorioData.gavetasDisponiveis, label: 'Gavetas Disponíveis' }
        ];

        // Desenhar grid 3x2
        let gridX = 20;
        let gridY = currentY;
        const boxWidth = 56;
        const boxHeight = 25;
        const spacing = 5;

        resumoData.forEach((item, index) => {
          const col = index % 3;
          const row = Math.floor(index / 3);
          const x = gridX + col * (boxWidth + spacing);
          const y = gridY + row * (boxHeight + spacing + 5);

          // Desenhar retângulo de fundo
          pdf.setFillColor(245, 245, 245);
          pdf.rect(x, y, boxWidth, boxHeight, 'F');

          // Desenhar borda
          pdf.setDrawColor(200, 200, 200);
          pdf.rect(x, y, boxWidth, boxHeight);

          // Valor principal
          pdf.setFontSize(14);
          pdf.setTextColor(25, 118, 210);
          pdf.text(String(item.valor), x + boxWidth/2, y + 12, { align: 'center' });

          // Label
          pdf.setFontSize(8);
          pdf.setTextColor(100, 100, 100);
          const lines = pdf.splitTextToSize(item.label, boxWidth - 4);
          pdf.text(lines, x + boxWidth/2, y + 18, { align: 'center' });
        });

        currentY += 85; // Espaço após o grid - AUMENTADO para melhor separação visual
      };

      // Função para desenhar tabela "Resumo por Dia"
      const drawResumoPorDiaTable = () => {
        // Título da seção
        pdf.setFontSize(16);
        pdf.setTextColor(25, 118, 210);
        pdf.text('Resumo por Dia', 20, currentY);
        currentY += 5;

        // Linha separadora do título
        pdf.setDrawColor(25, 118, 210);
        pdf.setLineWidth(0.5);
        pdf.line(20, currentY, 190, currentY);
        currentY += 15;

        // Configurações da tabela
        const tableWidth = 170; // Largura total da tabela
        const colWidths = [51, 34, 85]; // Larguras das colunas: Data, Quantidade, Status
        const rowHeight = 8;
        const headerHeight = 12;

        let tableX = 20;
        let tableY = currentY;

        // Desenhar cabeçalho da tabela
        pdf.setFillColor(25, 118, 210);
        pdf.rect(tableX, tableY, tableWidth, headerHeight, 'F');

        // Texto do cabeçalho
        pdf.setFontSize(10);
        pdf.setTextColor(255, 255, 255);
        pdf.text('Data', tableX + colWidths[0]/2, tableY + 8, { align: 'center' });
        pdf.text('Quantidade', tableX + colWidths[0] + colWidths[1]/2, tableY + 8, { align: 'center' });
        pdf.text('Status', tableX + colWidths[0] + colWidths[1] + colWidths[2]/2, tableY + 8, { align: 'center' });

        tableY += headerHeight;

        // Desenhar linhas de dados
        pdf.setTextColor(0, 0, 0);
        pdf.setFontSize(9);

        relatorioData.visaoGeral.forEach((dia, index) => {
          // Verificar se precisa de nova página
          if (tableY + rowHeight > 270) {
            pdf.addPage();
            pageNumber++;
            addHeaderFooter(pdf, pageNumber, 1);
            tableY = 45;

            // Repetir cabeçalho na nova página
            pdf.setFillColor(25, 118, 210);
            pdf.rect(tableX, tableY, tableWidth, headerHeight, 'F');
            pdf.setFontSize(10);
            pdf.setTextColor(255, 255, 255);
            pdf.text('Data', tableX + colWidths[0]/2, tableY + 8, { align: 'center' });
            pdf.text('Quantidade', tableX + colWidths[0] + colWidths[1]/2, tableY + 8, { align: 'center' });
            pdf.text('Status', tableX + colWidths[0] + colWidths[1] + colWidths[2]/2, tableY + 8, { align: 'center' });
            tableY += headerHeight;
            pdf.setTextColor(0, 0, 0);
            pdf.setFontSize(9);
          }

          // Alternar cor de fundo das linhas
          if (index % 2 === 0) {
            pdf.setFillColor(249, 249, 249);
            pdf.rect(tableX, tableY, tableWidth, rowHeight, 'F');
          }

          // Desenhar bordas das células
          pdf.setDrawColor(221, 221, 221);
          pdf.rect(tableX, tableY, colWidths[0], rowHeight);
          pdf.rect(tableX + colWidths[0], tableY, colWidths[1], rowHeight);
          pdf.rect(tableX + colWidths[0] + colWidths[1], tableY, colWidths[2], rowHeight);

          // Conteúdo das células
          pdf.text(dia.dataFormatada, tableX + colWidths[0]/2, tableY + 5, { align: 'center' });

          // Quantidade em azul e negrito
          pdf.setTextColor(25, 118, 210);
          pdf.setFont(undefined, 'bold');
          pdf.text(String(dia.quantidade), tableX + colWidths[0] + colWidths[1]/2, tableY + 5, { align: 'center' });

          // Status
          pdf.setTextColor(0, 0, 0);
          pdf.setFont(undefined, 'normal');
          const status = dia.quantidade > 0 ? `${dia.quantidade} sepultamento(s)` : 'Nenhum registro';
          pdf.text(status, tableX + colWidths[0] + colWidths[1] + colWidths[2]/2, tableY + 5, { align: 'center' });

          tableY += rowHeight;
        });

        currentY = tableY + 20;
      };

      // Função para desenhar tabela "Todos os Sepultamentos" com nomes completos
      const drawSepultamentosTable = () => {
        // Nova página para a tabela de sepultamentos
        pdf.addPage();
        pageNumber++;
        addHeaderFooter(pdf, pageNumber, 1);

        let tableY = 45;

        // Título da seção
        pdf.setFontSize(16);
        pdf.setTextColor(25, 118, 210);
        pdf.text(`Todos os Sepultamentos do Período (${relatorioData.todosSepultamentos.length})`, 20, tableY);
        tableY += 5;

        // Linha separadora do título
        pdf.setDrawColor(25, 118, 210);
        pdf.setLineWidth(0.5);
        pdf.line(20, tableY, 190, tableY);
        tableY += 15;

        // Configurações da tabela com larguras otimizadas
        const tableWidth = 170;
        const colWidths = [25, 15, 70, 25, 20, 15]; // Data, Hora, Nome, Bloco, Gaveta, Status
        const baseRowHeight = 8;
        const headerHeight = 12;

        let tableX = 20;

        // Desenhar cabeçalho da tabela
        pdf.setFillColor(25, 118, 210);
        pdf.rect(tableX, tableY, tableWidth, headerHeight, 'F');

        // Texto do cabeçalho
        pdf.setFontSize(9);
        pdf.setTextColor(255, 255, 255);
        let headerX = tableX;
        pdf.text('Data', headerX + colWidths[0]/2, tableY + 8, { align: 'center' });
        headerX += colWidths[0];
        pdf.text('Hora', headerX + colWidths[1]/2, tableY + 8, { align: 'center' });
        headerX += colWidths[1];
        pdf.text('Nome do Sepultado', headerX + colWidths[2]/2, tableY + 8, { align: 'center' });
        headerX += colWidths[2];
        pdf.text('Bloco', headerX + colWidths[3]/2, tableY + 8, { align: 'center' });
        headerX += colWidths[3];
        pdf.text('Gaveta', headerX + colWidths[4]/2, tableY + 8, { align: 'center' });
        headerX += colWidths[4];
        pdf.text('Status', headerX + colWidths[5]/2, tableY + 8, { align: 'center' });

        tableY += headerHeight;

        // Desenhar linhas de dados
        pdf.setTextColor(0, 0, 0);
        pdf.setFontSize(8);

        relatorioData.todosSepultamentos.forEach((sep, index) => {
          // Calcular altura necessária para o nome (com quebra de linha)
          const nomeCompleto = sep.nome_sepultado || 'N/A';
          const nomeLines = pdf.splitTextToSize(nomeCompleto, colWidths[2] - 2);
          const blocoCompleto = sep.denominacao_bloco || sep.codigo_bloco || 'N/A';
          const blocoLines = pdf.splitTextToSize(blocoCompleto, colWidths[3] - 2);

          const maxLines = Math.max(nomeLines.length, blocoLines.length, 1);
          const rowHeight = Math.max(baseRowHeight, maxLines * 4 + 4);

          // Verificar se precisa de nova página
          if (tableY + rowHeight > 270) {
            pdf.addPage();
            pageNumber++;
            addHeaderFooter(pdf, pageNumber, 1);
            tableY = 45;

            // Repetir cabeçalho na nova página
            pdf.setFillColor(25, 118, 210);
            pdf.rect(tableX, tableY, tableWidth, headerHeight, 'F');
            pdf.setFontSize(9);
            pdf.setTextColor(255, 255, 255);
            let headerX = tableX;
            pdf.text('Data', headerX + colWidths[0]/2, tableY + 8, { align: 'center' });
            headerX += colWidths[0];
            pdf.text('Hora', headerX + colWidths[1]/2, tableY + 8, { align: 'center' });
            headerX += colWidths[1];
            pdf.text('Nome do Sepultado', headerX + colWidths[2]/2, tableY + 8, { align: 'center' });
            headerX += colWidths[2];
            pdf.text('Bloco', headerX + colWidths[3]/2, tableY + 8, { align: 'center' });
            headerX += colWidths[3];
            pdf.text('Gaveta', headerX + colWidths[4]/2, tableY + 8, { align: 'center' });
            headerX += colWidths[4];
            pdf.text('Status', headerX + colWidths[5]/2, tableY + 8, { align: 'center' });
            tableY += headerHeight;
            pdf.setTextColor(0, 0, 0);
            pdf.setFontSize(8);
          }

          // Alternar cor de fundo das linhas
          if (index % 2 === 0) {
            pdf.setFillColor(249, 249, 249);
            pdf.rect(tableX, tableY, tableWidth, rowHeight, 'F');
          }

          // Desenhar bordas das células
          pdf.setDrawColor(221, 221, 221);
          let cellX = tableX;
          for (let i = 0; i < colWidths.length; i++) {
            pdf.rect(cellX, tableY, colWidths[i], rowHeight);
            cellX += colWidths[i];
          }

          // Conteúdo das células
          cellX = tableX;
          const cellY = tableY + 5;

          // Data
          pdf.text(formatDate(sep.data_sepultamento), cellX + colWidths[0]/2, cellY, { align: 'center' });
          cellX += colWidths[0];

          // Hora
          pdf.text(formatTime(sep.horario_sepultamento) || '-', cellX + colWidths[1]/2, cellY, { align: 'center' });
          cellX += colWidths[1];

          // Nome completo com quebra de linha
          pdf.text(nomeLines, cellX + 1, cellY);
          cellX += colWidths[2];

          // Bloco completo com quebra de linha
          pdf.text(blocoLines, cellX + 1, cellY);
          cellX += colWidths[3];

          // Gaveta
          pdf.text(String(sep.numero_gaveta || 'N/A'), cellX + colWidths[4]/2, cellY, { align: 'center' });
          cellX += colWidths[4];

          // Status
          pdf.text(sep.status_exumacao ? 'Exumado' : 'Ativo', cellX + colWidths[5]/2, cellY, { align: 'center' });

          tableY += rowHeight;
        });
      };

      // Função principal para gerar o PDF
      const generateCompletePDF = () => {
        // Gerar primeira página com resumo
        generatePDFContent();

        // Gerar tabela resumo por dia
        drawResumoPorDiaTable();

        // Gerar tabela de sepultamentos se houver dados
        if (relatorioData.todosSepultamentos.length > 0) {
          drawSepultamentosTable();
        }

        // Adicionar marca d'água em todas as páginas
        const totalPages = pdf.getNumberOfPages();
        for (let i = 1; i <= totalPages; i++) {
          pdf.setPage(i);

          // Adicionar marca d'água
          pdf.setGState(new pdf.GState({opacity: 0.1}));
          pdf.addImage(logoImg, 'PNG', 52.5, 100, 105, 105); // Centralizado
          pdf.setGState(new pdf.GState({opacity: 1}));
        }

        // Salvar PDF
        const fileName = `relatorio_sepultamentos_${relatorioData.produto?.denominacao?.replace(/[^a-zA-Z0-9]/g, '_') || 'produto'}_${new Date().toISOString().split('T')[0]}.pdf`;
        pdf.save(fileName);
      };

      // Carregar logo e gerar PDF
      logoImg.onload = () => {
        generateCompletePDF();
      };

      logoImg.onerror = () => {
        // Se não conseguir carregar a logo, gerar PDF sem marca d'água
        console.warn('Não foi possível carregar a logo para marca d\'água');
        generateCompletePDF();
      };

      // Tentar carregar a logo
      logoImg.src = '/logo_sem_fundo_branco.png';

    } catch (error) {
      console.error('Erro ao gerar PDF:', error);
      alert('Erro ao gerar PDF. Tente novamente.');
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <ReportIcon sx={{ mr: 2, color: 'primary.main', fontSize: 32 }} />
          <Typography variant="h4" component="h1" gutterBottom>
            Relatórios de Sepultamentos
          </Typography>
        </Box>
        <Typography variant="body1" color="text.secondary">
          Gere relatórios detalhados dos sepultamentos por produto e período
        </Typography>
      </Box>

      {/* Filtros */}
      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
          <DateIcon sx={{ mr: 1 }} />
          Filtros do Relatório
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth required>
              <InputLabel>Produto</InputLabel>
              <Select
                value={selectedProduto}
                onChange={(e) => setSelectedProduto(e.target.value)}
                label="Produto"
                MenuProps={{
                  PaperProps: {
                    sx: {
                      maxHeight: 300,
                      '& .MuiMenuItem-root': {
                        fontSize: '1rem',
                        padding: '12px 16px',
                        minHeight: '48px'
                      }
                    }
                  }
                }}
              >
                <MenuItem value="">
                  <em>Selecione um produto</em>
                </MenuItem>
                {produtos.map(produto => (
                  <MenuItem key={produto.id} value={produto.id}>
                    {produto.denominacao || produto.nome}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              required
              type="date"
              label="Data de Início"
              value={dataInicio}
              onChange={(e) => setDataInicio(e.target.value)}
              InputLabelProps={{ shrink: true }}
            />
          </Grid>
          
          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              required
              type="date"
              label="Data de Fim"
              value={dataFim}
              onChange={(e) => setDataFim(e.target.value)}
              InputLabelProps={{ shrink: true }}
            />
          </Grid>
          
          <Grid item xs={12} md={2}>
            <Button
              fullWidth
              variant="contained"
              onClick={handleGerarRelatorio}
              disabled={loading}
              sx={{ height: '56px' }}
            >
              {loading ? <CircularProgress size={24} /> : 'Gerar Relatório'}
            </Button>
          </Grid>

          {/* Botão Emitir PDF - só aparece após gerar relatório */}
          {relatorioData && (
            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="outlined"
                color="secondary"
                startIcon={<PdfIcon />}
                onClick={handleEmitirPDF}
                sx={{ height: '56px' }}
              >
                Emitir PDF
              </Button>
            </Grid>
          )}
        </Grid>

        {error && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {error}
          </Alert>
        )}
      </Paper>

      {/* Resultados do Relatório */}
      {relatorioData && (
        <>
          {/* Resumo */}
          <Card sx={{ mb: 4 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                <TrendingIcon sx={{ mr: 1 }} />
                Resumo do Período
              </Typography>
              
              <Grid container spacing={2}>
                {/* Primeira linha - Dados do período */}
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2, backgroundColor: 'primary.light', borderRadius: 1 }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'white' }}>
                      {relatorioData.totalSepultamentosPeriodo}
                    </Typography>
                    <Typography variant="body2" sx={{ color: 'white' }}>
                      Total de Sepultamentos Apenas no Período
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2, backgroundColor: 'success.light', borderRadius: 1 }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'white' }}>
                      {relatorioData.taxaSepultamentosPorDia}
                    </Typography>
                    <Typography variant="body2" sx={{ color: 'white' }}>
                      Taxa de Sepultamentos no Período (por dia)
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2, backgroundColor: 'warning.light', borderRadius: 1 }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'white' }}>
                      {relatorioData.taxaOcupacao}%
                    </Typography>
                    <Typography variant="body2" sx={{ color: 'white' }}>
                      Taxa de Ocupação ao Final deste Período
                    </Typography>
                  </Box>
                </Grid>

                {/* Segunda linha - Dados de gavetas */}
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2, backgroundColor: 'secondary.light', borderRadius: 1 }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'white' }}>
                      {relatorioData.totalGavetas}
                    </Typography>
                    <Typography variant="body2" sx={{ color: 'white' }}>
                      Total de Gavetas Cadastradas
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2, backgroundColor: 'error.light', borderRadius: 1 }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'white' }}>
                      {relatorioData.gavetasOcupadas}
                    </Typography>
                    <Typography variant="body2" sx={{ color: 'white' }}>
                      Total de Gavetas Ocupadas
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2, backgroundColor: 'success.main', borderRadius: 1 }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'white' }}>
                      {relatorioData.gavetasDisponiveis}
                    </Typography>
                    <Typography variant="body2" sx={{ color: 'white' }}>
                      Total de Gavetas Disponíveis
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2, backgroundColor: 'primary.main', borderRadius: 1 }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'white' }}>
                      {relatorioData.produto?.nome_completo || 'N/A'}
                    </Typography>
                    <Typography variant="body2" sx={{ color: 'white' }}>
                      Produto Filtrado
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Container para as duas listas com largura igual */}
          <Grid container spacing={4}>
            {/* Visão Geral por Dia */}
            <Grid item xs={12} lg={6}>
              <Paper sx={{ p: 3, height: 'fit-content' }}>
                <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
                  <CalendarIcon sx={{ mr: 1 }} />
                  Visão Geral - Sepultamentos por Dia
                </Typography>

                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell><strong>Data</strong></TableCell>
                        <TableCell align="center"><strong>Quantidade</strong></TableCell>
                        <TableCell><strong>Status</strong></TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {relatorioData.visaoGeral.map((dia, index) => (
                        <TableRow key={index} hover>
                          <TableCell>{dia.dataFormatada}</TableCell>
                          <TableCell align="center">
                            <Chip
                              label={dia.quantidade}
                              color={dia.quantidade > 0 ? 'primary' : 'default'}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            {dia.quantidade > 0 ? (
                              <Chip label={`${dia.quantidade} sepultamento(s)`} color="success" size="small" />
                            ) : (
                              <Chip label="Nenhum registro efetuado neste dia" color="default" size="small" />
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>
            </Grid>

            {/* Todos os Sepultamentos */}
            <Grid item xs={12} lg={6}>
              {relatorioData.todosSepultamentos.length > 0 ? (
                <Paper sx={{ p: 3, height: 'fit-content' }}>
                  <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
                    <PersonIcon sx={{ mr: 1 }} />
                    Todos os Sepultamentos do Período ({relatorioData.todosSepultamentos.length})
                  </Typography>

                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell><strong>Data</strong></TableCell>
                          <TableCell><strong>Hora</strong></TableCell>
                          <TableCell><strong>Nome do Sepultado</strong></TableCell>
                          <TableCell><strong>Bloco</strong></TableCell>
                          <TableCell><strong>Gaveta</strong></TableCell>
                          <TableCell><strong>Status</strong></TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {relatorioData.todosSepultamentos.map((sepultamento, index) => (
                          <TableRow key={index} hover>
                            <TableCell>{formatDate(sepultamento.data_sepultamento)}</TableCell>
                            <TableCell>{formatTime(sepultamento.horario_sepultamento)}</TableCell>
                            <TableCell>
                              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                {sepultamento.nome_sepultado}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Typography variant="body2">
                                {sepultamento.denominacao_bloco || sepultamento.codigo_bloco || '-'}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Chip
                                label={`Gaveta ${sepultamento.numero_gaveta || 'N/A'}`}
                                size="small"
                                color="primary"
                              />
                            </TableCell>
                            <TableCell>
                              <Chip
                                label={sepultamento.status_exumacao ? 'Exumado' : 'Ativo'}
                                color={sepultamento.status_exumacao ? 'default' : 'success'}
                                size="small"
                              />
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Paper>
              ) : (
                <Paper sx={{ p: 3, height: 'fit-content' }}>
                  <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
                    <PersonIcon sx={{ mr: 1 }} />
                    Sepultamentos do Período
                  </Typography>
                  <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                    Nenhum sepultamento encontrado no período selecionado.
                  </Typography>
                </Paper>
              )}
            </Grid>
          </Grid>
        </>
      )}
    </Container>
  );
};

export default RelatoriosPage;
