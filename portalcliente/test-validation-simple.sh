#!/bin/bash

# Teste simplificado de validação
echo "🧪 TESTE SIMPLIFICADO DE VALIDAÇÃO"
echo "=================================="

# Cores
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log_test() {
    local status=$1
    local message=$2
    if [ "$status" = "PASS" ]; then
        echo -e "${GREEN}✅ PASS${NC}: $message"
    elif [ "$status" = "FAIL" ]; then
        echo -e "${RED}❌ FAIL${NC}: $message"
    else
        echo -e "${BLUE}ℹ️  INFO${NC}: $message"
    fi
}

# Teste 1: API Health
echo ""
log_test "INFO" "TESTE 1: Verificando API..."
response=$(curl -s https://portal.evo-eden.site/api/health)
status=$(echo "$response" | jq -r '.status // empty')

if [ "$status" = "OK" ]; then
    log_test "PASS" "API funcionando"
else
    log_test "FAIL" "API com problemas"
fi

# Teste 2: Validação no Banco
echo ""
log_test "INFO" "TESTE 2: Validação de email duplicado no banco..."

# Limpar dados de teste
docker exec -i $(docker ps -q --filter "name=postgres") psql -U postgres -d dbetens -c "
DELETE FROM usuarios WHERE email = '<EMAIL>';
" > /dev/null 2>&1

# Criar usuário
result1=$(docker exec -i $(docker ps -q --filter "name=postgres") psql -U postgres -d dbetens -c "
INSERT INTO usuarios (email, senha, tipo_usuario, nome, ativo) 
VALUES ('<EMAIL>', 'senha123', 'cliente', 'Usuário Teste', true);
SELECT 'SUCESSO' as resultado;
" 2>/dev/null | grep SUCESSO)

if [ "$result1" = " SUCESSO" ]; then
    log_test "PASS" "Usuário criado com sucesso"
else
    log_test "FAIL" "Erro ao criar usuário inicial"
fi

# Tentar criar duplicado
result2=$(docker exec -i $(docker ps -q --filter "name=postgres") psql -U postgres -d dbetens -c "
INSERT INTO usuarios (email, senha, tipo_usuario, nome, ativo) 
VALUES ('<EMAIL>', 'senha456', 'cliente', 'Usuário Duplicado', true);
" 2>&1 | grep "duplicate key")

if [ -n "$result2" ]; then
    log_test "PASS" "Validação de email duplicado funcionando no banco"
else
    log_test "FAIL" "Validação de email duplicado não funcionou"
fi

# Teste 3: Verificar estrutura de templates
echo ""
log_test "INFO" "TESTE 3: Verificando templates de email..."

# Verificar se as funções existem no código
if grep -q "enviarEmailBoasVindas" server/routes/auth.js; then
    log_test "PASS" "Função enviarEmailBoasVindas encontrada"
else
    log_test "FAIL" "Função enviarEmailBoasVindas não encontrada"
fi

if grep -q "Bem-vindo ao Portal Evolution" server/routes/auth.js; then
    log_test "PASS" "Template de boas-vindas implementado"
else
    log_test "FAIL" "Template de boas-vindas não encontrado"
fi

if grep -q "Tipo de Usuário" server/routes/auth.js; then
    log_test "FAIL" "Campo 'Tipo de Usuário' ainda presente (deveria ter sido removido)"
else
    log_test "PASS" "Campo 'Tipo de Usuário' removido corretamente"
fi

# Teste 4: Verificar frontend
echo ""
log_test "INFO" "TESTE 4: Verificando tratamento de erro no frontend..."

if grep -q "email-duplicado" src/components/UsuarioModal.jsx; then
    log_test "PASS" "Tratamento de erro de email duplicado implementado no frontend"
else
    log_test "FAIL" "Tratamento de erro não encontrado no frontend"
fi

if grep -q "suggestion.*forgot_password" src/components/UsuarioModal.jsx; then
    log_test "PASS" "Verificação de sugestão 'forgot_password' implementada"
else
    log_test "FAIL" "Verificação de sugestão não encontrada"
fi

# Teste 5: Verificar deploy
echo ""
log_test "INFO" "TESTE 5: Verificando deploy..."

# Verificar se o frontend está acessível
frontend_status=$(curl -s -o /dev/null -w "%{http_code}" https://portal.evo-eden.site/)
if [ "$frontend_status" = "200" ]; then
    log_test "PASS" "Frontend acessível"
else
    log_test "FAIL" "Frontend com problemas (HTTP $frontend_status)"
fi

# Verificar se o backend está respondendo
backend_status=$(curl -s -o /dev/null -w "%{http_code}" https://portal.evo-eden.site/api/health)
if [ "$backend_status" = "200" ]; then
    log_test "PASS" "Backend acessível"
else
    log_test "FAIL" "Backend com problemas (HTTP $backend_status)"
fi

# Limpeza
docker exec -i $(docker ps -q --filter "name=postgres") psql -U postgres -d dbetens -c "
DELETE FROM usuarios WHERE email = '<EMAIL>';
DELETE FROM usuarios WHERE email = '<EMAIL>';
" > /dev/null 2>&1

echo ""
echo "=================================="
log_test "INFO" "TESTES CONCLUÍDOS"
echo "=================================="
echo ""
echo "✅ Funcionalidades implementadas:"
echo "   • Verificação de email duplicado"
echo "   • Template de boas-vindas personalizado"
echo "   • Remoção do campo 'Tipo de Usuário'"
echo "   • Tratamento de erro no frontend"
echo "   • Deploy em produção"
echo ""
echo "🌐 Sistema disponível em: https://portal.evo-eden.site"
echo "📧 Emails de boas-vindas serão enviados automaticamente"
echo ""
