#!/bin/bash

# 🧪 SCRIPT DE VALIDAÇÃO COMPLETA - PORTAL EVOLUTION
# Valida se o erro 405 foi corrigido e tudo está funcionando

echo "🧪 VALIDAÇÃO COMPLETA DO PORTAL EVOLUTION"
echo "========================================"

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log colorido
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Contador de testes
TOTAL_TESTS=0
PASSED_TESTS=0

# Função para executar teste
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_pattern="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    log_info "Teste $TOTAL_TESTS: $test_name"
    
    result=$(eval "$test_command" 2>/dev/null)
    
    if echo "$result" | grep -q "$expected_pattern"; then
        log_success "PASSOU: $test_name"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        log_error "FALHOU: $test_name"
        echo "  Resultado: $result"
        return 1
    fi
}

echo ""
log_info "Iniciando validação dos serviços..."

# TESTE 1: Verificar se os serviços estão rodando
run_test "Serviços Docker Swarm" \
    "docker stack services portal-evolution | grep '1/1'" \
    "portal-evolution_portal_backend.*1/1"

# TESTE 2: Testar Health da API
run_test "API Health Check" \
    "curl -s https://portal.evo-eden.site/api/health" \
    '"status":"OK"'

# TESTE 3: Testar Frontend (página principal)
run_test "Frontend Carregando" \
    "curl -s -o /dev/null -w '%{http_code}' https://portal.evo-eden.site" \
    "200"

# TESTE 4: Testar Login (corrigir erro 405)
run_test "Login API (Erro 405 Corrigido)" \
    "curl -s -X POST https://portal.evo-eden.site/api/auth/login -H 'Content-Type: application/json' -d '{\"email\":\"<EMAIL>\",\"password\":\"54321\"}'" \
    '"message":"Login realizado com sucesso"'

# TESTE 5: Testar endpoint de usuários (autenticado)
TOKEN=$(curl -s -X POST https://portal.evo-eden.site/api/auth/login -H "Content-Type: application/json" -d '{"email":"<EMAIL>","password":"54321"}' | grep -o '"token":"[^"]*"' | cut -d'"' -f4)

if [ ! -z "$TOKEN" ]; then
    run_test "API Usuários (Autenticada)" \
        "curl -s -H 'Authorization: Bearer $TOKEN' https://portal.evo-eden.site/api/usuarios" \
        '"id":'
else
    log_error "Não foi possível obter token para teste autenticado"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
fi

# TESTE 6: Verificar se não há mais erro 405
run_test "Verificar Ausência de Erro 405" \
    "curl -s -X POST https://portal.evo-eden.site/api/auth/login -H 'Content-Type: application/json' -d '{}' -w '%{http_code}'" \
    "400"  # Deve retornar 400 (bad request) e não 405 (method not allowed)

echo ""
echo "📊 RESUMO DA VALIDAÇÃO"
echo "====================="
log_info "Total de testes: $TOTAL_TESTS"
log_success "Testes aprovados: $PASSED_TESTS"

if [ $PASSED_TESTS -eq $TOTAL_TESTS ]; then
    echo ""
    log_success "🎉 TODOS OS TESTES PASSARAM!"
    log_success "✅ O erro 405 foi corrigido com sucesso"
    log_success "✅ Frontend e Backend estão funcionando"
    log_success "✅ API de login está operacional"
    echo ""
    log_info "Portal disponível em: https://portal.evo-eden.site"
    log_info "API disponível em: https://portal.evo-eden.site/api"
    echo ""
    log_info "Credenciais de teste:"
    echo "  Admin: <EMAIL> / adminnbr5410!"
    echo "  Cliente: <EMAIL> / 54321"
else
    echo ""
    log_error "❌ ALGUNS TESTES FALHARAM"
    log_warning "Verifique os logs dos serviços:"
    echo "  Backend: docker service logs portal-evolution_portal_backend"
    echo "  Frontend: docker service logs portal-evolution_portal_frontend"
fi

echo ""
log_info "Status dos serviços:"
docker stack services portal-evolution

echo ""
log_success "Validação concluída! 🧪"
