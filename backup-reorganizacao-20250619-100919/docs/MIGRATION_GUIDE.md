# Guia de Migração - Estrutura por Códigos

## 🎯 Objetivo

Esta migração reestrutura completamente o banco de dados PostgreSQL para usar **referências por códigos** ao invés de IDs, criando uma hierarquia clara e consistente.

## 📋 O que Mudou

### ❌ Estrutura Antiga (IDs)
```
produtos (id) → blocos (produto_id) → sub_blocos (bloco_id) → gavetas (sub_bloco_id) → sepultamentos (gaveta_id)
```

### ✅ Nova Estrutura (Códigos)
```
clientes (codigo_cliente)
├── produtos (codigo_cliente + codigo_estacao)
    ├── blocos (codigo_cliente + codigo_estacao + codigo_bloco)
        ├── sub_blocos (codigo_cliente + codigo_estacao + codigo_bloco + codigo_sub_bloco)
            ├── gavetas (codigo_cliente + codigo_estacao + codigo_bloco + codigo_sub_bloco + numero_gaveta)
            └── sepultamentos (codigo_cliente + codigo_estacao + codigo_bloco + codigo_sub_bloco + numero_gaveta)
```

## 🔧 Mudanças Técnicas

### Tabelas Reestruturadas

#### 1. **produtos**
- **Antes**: `id` (PK), `codigo_cliente` (FK), `codigo_estacao`
- **Depois**: `codigo_cliente + codigo_estacao` (PK composta)

#### 2. **blocos**
- **Antes**: `id` (PK), `produto_id` (FK), `codigo_bloco`
- **Depois**: `codigo_cliente + codigo_estacao + codigo_bloco` (PK composta)

#### 3. **sub_blocos**
- **Antes**: `id` (PK), `bloco_id` (FK), `codigo_sub_bloco`
- **Depois**: `codigo_cliente + codigo_estacao + codigo_bloco + codigo_sub_bloco` (PK composta)

#### 4. **gavetas**
- **Antes**: `id` (PK), `sub_bloco_id` (FK), `numero_gaveta`
- **Depois**: `codigo_cliente + codigo_estacao + codigo_bloco + codigo_sub_bloco + numero_gaveta` (PK composta)

#### 5. **sepultamentos**
- **Antes**: `id` (PK), `gaveta_id` (FK)
- **Depois**: `id` (PK), `codigo_cliente + codigo_estacao + codigo_bloco + codigo_sub_bloco + numero_gaveta` (FK composta)

#### 6. **logs_auditoria** ⭐ **NOVA FUNCIONALIDADE**
- **Antes**: `id` (PK), `usuario_id` (FK), dados básicos
- **Depois**: `id` (PK), `usuario_id` (FK), `codigo_cliente + codigo_estacao + codigo_bloco + codigo_sub_bloco` (contexto hierárquico)

## 🚀 Como Executar a Migração

### Pré-requisitos
1. ✅ Backup completo do banco de dados
2. ✅ Servidor parado
3. ✅ Acesso ao PostgreSQL
4. ✅ Node.js instalado

### Passo a Passo

#### 1. **Fazer Backup**
```bash
# Backup completo
pg_dump -h localhost -U postgres -d portal_evolution > backup_antes_migracao.sql

# Verificar backup
ls -la backup_antes_migracao.sql
```

#### 2. **Parar o Servidor**
```bash
# Parar servidor Node.js
# Ctrl+C ou fechar terminal
```

#### 3. **Executar Migração Completa** ⭐ **RECOMENDADO**
```bash
# Navegar para pasta do servidor
cd portalcliente/server

# Executar migração completa (inclui logs e todas as tabelas)
node complete_migration.js
```

**OU executar migração básica:**
```bash
# Executar apenas migração das tabelas principais
node migrate_to_codes.js

# Depois atualizar logs separadamente
node update_logs_table.js
```

#### 4. **Verificar Migração**
```bash
# O script mostrará:
# ✅ Status da migração
# 📊 Contagem de registros
# 🏗️ Estrutura das tabelas
```

#### 5. **Iniciar Servidor**
```bash
# Iniciar servidor com nova estrutura
npm start
```

## 📊 Verificação Pós-Migração

### Comandos SQL para Verificar

```sql
-- Verificar estrutura das tabelas
SELECT table_name, column_name, data_type 
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name IN ('produtos', 'blocos', 'sub_blocos', 'gavetas', 'sepultamentos')
ORDER BY table_name, ordinal_position;

-- Verificar chaves primárias
SELECT t.table_name, k.column_name
FROM information_schema.table_constraints t
JOIN information_schema.key_column_usage k ON k.constraint_name = t.constraint_name
WHERE t.constraint_type = 'PRIMARY KEY'
AND t.table_schema = 'public'
ORDER BY t.table_name, k.ordinal_position;

-- Verificar dados migrados
SELECT 'produtos' as tabela, COUNT(*) as registros FROM produtos
UNION ALL
SELECT 'blocos' as tabela, COUNT(*) as registros FROM blocos
UNION ALL
SELECT 'sub_blocos' as tabela, COUNT(*) as registros FROM sub_blocos
UNION ALL
SELECT 'gavetas' as tabela, COUNT(*) as registros FROM gavetas
UNION ALL
SELECT 'sepultamentos' as tabela, COUNT(*) as registros FROM sepultamentos;
```

## 🔄 Rollback (Se Necessário)

### Em Caso de Problemas

```bash
# 1. Parar servidor
# Ctrl+C

# 2. Restaurar backup
psql -h localhost -U postgres -d portal_evolution < backup_antes_migracao.sql

# 3. Usar código antigo
git checkout HEAD~1  # ou commit anterior

# 4. Iniciar servidor antigo
npm start
```

## 📝 Arquivos Modificados

### Backend
- ✅ `database/schema.sql` - Nova estrutura completa
- ✅ `database/migration_to_codes.sql` - Script de migração principal
- ✅ `routes/produtos_new.js` - Rotas por códigos
- ✅ `routes/sub_blocos_new.js` - Rotas por códigos
- ✅ `routes/sepultamentos_new.js` - Rotas por códigos
- ✅ `routes/dashboard_new.js` - Dashboard por códigos
- ✅ `index.js` - Rotas atualizadas
- ✅ `migrate_to_codes.js` - Script de migração principal
- ✅ `update_logs_table.js` - Script para atualizar logs ⭐ **NOVO**
- ✅ `complete_migration.js` - Script de migração completa ⭐ **NOVO**
- ✅ `utils/logger.js` - Logger com referências hierárquicas ⭐ **ATUALIZADO**

### Frontend
- 🔄 **Próximo passo**: Atualizar APIs no frontend

## 🗂️ Referências Hierárquicas por Tabela

### 📋 **Mapeamento Completo das Referências:**

| Tabela | Referências Hierárquicas | Propósito |
|--------|---------------------------|-----------|
| `clientes` | `codigo_cliente` (PK) | Base da hierarquia |
| `usuarios` | `codigo_cliente` | Associação ao cliente |
| `produtos` | `codigo_cliente` + `codigo_estacao` | Nível produto/estação |
| `blocos` | `codigo_cliente` + `codigo_estacao` + `codigo_bloco` | Nível bloco |
| `sub_blocos` | `codigo_cliente` + `codigo_estacao` + `codigo_bloco` + `codigo_sub_bloco` | Nível sub-bloco |
| `gavetas` | `codigo_cliente` + `codigo_estacao` + `codigo_bloco` + `codigo_sub_bloco` | Nível gaveta |
| `numeracoes_gavetas` | `codigo_cliente` + `codigo_estacao` + `codigo_bloco` + `codigo_sub_bloco` | Configuração de ranges |
| `sepultamentos` | `codigo_cliente` + `codigo_estacao` + `codigo_bloco` + `codigo_sub_bloco` | Localização do sepultamento |
| `logs_auditoria` | `codigo_cliente` + `codigo_estacao` + `codigo_bloco` + `codigo_sub_bloco` | Contexto da ação |

### 🎯 **Benefícios para Importação de Dados:**

✅ **Facilita importação** de dados de outras fontes
✅ **Elimina dependência** de IDs sequenciais
✅ **Permite relacionamento** direto por códigos
✅ **Mantém consistência** hierárquica
✅ **Simplifica consultas** entre sistemas
✅ **Melhora rastreabilidade** de ações

## ⚠️ Importante

1. **Backup Obrigatório**: Sempre faça backup antes da migração
2. **Ambiente de Teste**: Teste primeiro em ambiente de desenvolvimento
3. **Downtime**: O sistema ficará indisponível durante a migração
4. **Irreversível**: A migração modifica permanentemente a estrutura
5. **APIs**: O frontend precisará ser atualizado para usar as novas APIs
6. **Importação**: Todas as tabelas estão preparadas para importação por códigos ⭐ **NOVO**

## 🆘 Suporte

Em caso de problemas:

1. **Verificar logs**: `console.log` durante migração
2. **Verificar conexão**: PostgreSQL acessível
3. **Verificar permissões**: Usuário com privilégios
4. **Restaurar backup**: Se necessário
5. **Contatar suporte**: Com logs de erro

## ✅ Checklist Final

- [ ] Backup realizado
- [ ] Migração executada sem erros
- [ ] Dados verificados
- [ ] Servidor iniciado
- [ ] APIs funcionando
- [ ] Frontend atualizado
- [ ] Testes realizados

---

**🎉 Parabéns!** Seu sistema agora usa a nova estrutura hierárquica por códigos, proporcionando maior clareza e consistência nas referências entre tabelas.
