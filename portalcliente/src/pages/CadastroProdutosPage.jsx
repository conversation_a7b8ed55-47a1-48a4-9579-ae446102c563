import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Add as AddIcon } from '@mui/icons-material';
import { produtoService, clienteService, sepultamentoService } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import Modal from '../components/Modal';
import ProdutoModal from '../components/ProdutoModal';
import { StandardButton } from '../components/common';

const PageContainer = styled.div`
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
`;

const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  flex-wrap: wrap;
  gap: 16px;
`;

const PageTitle = styled.h1`
  margin: 0;
  color: #1f2937;
  font-size: 2rem;
  font-weight: 600;
`;

const FilterContainer = styled.div`
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
`;

const FilterSelect = styled.select`
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  min-width: 200px;
`;



const ProductsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
  margin-top: 24px;
`;

const ProductCard = styled.div`
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
`;

const ProductHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
`;

const ProductTitle = styled.h3`
  margin: 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
`;

const ProductCode = styled.span`
  background: #e0f2fe;
  color: #0369a1;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
`;

const ProductInfo = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 16px;
`;

const InfoItem = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

const InfoLabel = styled.span`
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
`;

const InfoValue = styled.span`
  font-size: 0.875rem;
  color: #1f2937;
  font-weight: 500;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 8px;
  margin-top: 16px;
`;

const ActionButton = styled.button`
  flex: 1;
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #f3f4f6;
  }

  &.primary {
    background: #1e3a8a;
    color: white;
    border-color: #1e3a8a;

    &:hover {
      background: #1e40af;
    }
  }

  &.danger {
    background: #dc2626;
    color: white;
    border-color: #dc2626;

    &:hover {
      background: #ef4444;
    }
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 48px;
  color: #6b7280;
`;

const LoadingSpinner = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 1.125rem;
  color: #6b7280;
`;

const SepultamentosModal = styled.div`
  max-height: 400px;
  overflow-y: auto;
`;

const SepultamentoItem = styled.div`
  padding: 12px;
  border-bottom: 1px solid #e5e7eb;
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 12px;
  align-items: center;
  font-size: 0.875rem;

  &:last-child {
    border-bottom: none;
  }
`;

const CadastroProdutosPage = () => {
  const [produtos, setProdutos] = useState([]);
  const [clientes, setClientes] = useState([]);
  const [filtroCliente, setFiltroCliente] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showProdutoModal, setShowProdutoModal] = useState(false);
  const [selectedProduto, setSelectedProduto] = useState(null);
  const [showSepultamentosModal, setShowSepultamentosModal] = useState(false);
  const [sepultamentosProduto, setSepultamentosProduto] = useState([]);
  const { user } = useAuth();

  useEffect(() => {
    loadClientes();
    loadProdutos();
  }, []);

  useEffect(() => {
    loadProdutos();
  }, [filtroCliente]);

  const loadClientes = async () => {
    try {
      const response = await clienteService.listar();
      setClientes(response.data);
    } catch (error) {
      console.error('Erro ao carregar clientes:', error);
    }
  };

  const loadProdutos = async () => {
    try {
      setLoading(true);
      // Usar a rota de estatísticas que já calcula ocupação e total de gavetas
      const response = await produtoService.estatisticas();

      // Filtrar por cliente se necessário
      let produtosFiltrados = response.data;
      if (filtroCliente) {
        produtosFiltrados = response.data.filter(produto => produto.codigo_cliente === filtroCliente);
      }

      setProdutos(produtosFiltrados);
    } catch (error) {
      console.error('Erro ao carregar produtos:', error);
      setError('Erro ao carregar produtos');
    } finally {
      setLoading(false);
    }
  };

  const handleAddProduto = () => {
    setSelectedProduto(null);
    setShowProdutoModal(true);
  };

  const handleEditProduto = (produto) => {
    setSelectedProduto(produto);
    setShowProdutoModal(true);
  };

  const handleDeleteProduto = async (produto) => {
    // CONFIRMAÇÃO MELHORADA conforme requisitos
    const confirmMessage = `⚠️ CONFIRMAÇÃO DE DELEÇÃO ⚠️

Tem certeza que deseja deletar o produto "${produto.denominacao}"?

📋 VALIDAÇÕES AUTOMÁTICAS:
• Não é possível deletar se houver blocos associados
• Não é possível deletar se houver sub-blocos associados
• Não é possível deletar se houver gavetas associadas
• Não é possível deletar se houver sepultamentos ativos

Esta ação NÃO pode ser desfeita!

Confirma a deleção?`;

    if (!window.confirm(confirmMessage)) {
      return;
    }

    try {
      console.log('🗑️ Iniciando deleção do produto:', produto.id, produto.denominacao);
      await produtoService.deletar(produto.id);
      console.log('✅ Produto deletado com sucesso');
      loadProdutos();
      alert('✅ Produto deletado com sucesso!');
    } catch (error) {
      console.error('❌ Erro ao deletar produto:', error);

      // TRATAMENTO MELHORADO DE ERROS
      if (error.response?.status === 400) {
        // Erro de validação hierárquica
        alert(`❌ Não é possível deletar o produto:\n\n${error.response.data.error}`);
      } else if (error.response?.status === 403) {
        // Erro de permissão
        alert('❌ Acesso negado. Apenas administradores podem deletar produtos.');
      } else if (error.response?.status === 404) {
        // Produto não encontrado
        alert('❌ Produto não encontrado.');
        loadProdutos(); // Recarregar lista
      } else {
        // Erro interno ou outros
        alert(`❌ Erro ao deletar produto:\n\n${error.response?.data?.error || error.message || 'Erro interno do servidor'}`);
      }
    }
  };

  const handleViewSepultamentos = async (produto) => {
    try {
      console.log('🔍 Carregando sepultamentos para produto:', produto);
      const response = await sepultamentoService.listar({
        codigo_estacao: produto.codigo_estacao,
        status_exumacao: false,
        ativo: true
      });
      setSepultamentosProduto(response.data || []);
      setSelectedProduto(produto);
      setShowSepultamentosModal(true);
    } catch (error) {
      console.error('❌ Erro ao carregar sepultamentos:', error);
      alert('Erro ao carregar sepultamentos do produto');
    }
  };

  const handleViewDetalhes = async (produto) => {
    try {
      console.log('🔍 Carregando detalhes completos do produto:', produto);
      const response = await produtoService.buscarCompleto(produto.id);

      console.log('📊 Detalhes encontrados:', response.data);

      if (response.data && response.data.blocos) {
        let detalhesInfo = `PRODUTO: ${produto.denominacao}\n`;
        detalhesInfo += `ESTAÇÃO: ${produto.codigo_estacao}\n\n`;

        response.data.blocos.forEach(bloco => {
          detalhesInfo += `📦 BLOCO: ${bloco.nome} (${bloco.codigo_bloco})\n`;

          if (bloco.sub_blocos && bloco.sub_blocos.length > 0) {
            bloco.sub_blocos.forEach(subBloco => {
              detalhesInfo += `  📋 Sub-bloco: ${subBloco.nome} (${subBloco.codigo_sub_bloco})\n`;
              detalhesInfo += `     Gavetas: ${subBloco.total_gavetas || 0}\n`;
              if (subBloco.ranges_gavetas) {
                detalhesInfo += `     Numeração: ${subBloco.ranges_gavetas}\n`;
              }
            });
          } else {
            detalhesInfo += `     Nenhum sub-bloco cadastrado\n`;
          }
          detalhesInfo += `\n`;
        });

        alert(detalhesInfo);
      } else {
        alert(`Nenhum detalhe encontrado para o produto "${produto.denominacao}"`);
      }
    } catch (error) {
      console.error('Erro ao carregar detalhes do produto:', error);
      alert('Erro ao carregar detalhes do produto');
    }
  };

  if (loading) {
    return (
      <PageContainer>
        <LoadingSpinner>Carregando produtos...</LoadingSpinner>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <PageHeader>
        <PageTitle>Cadastro de Produtos</PageTitle>
        <FilterContainer>
          <FilterSelect
            value={filtroCliente}
            onChange={(e) => setFiltroCliente(e.target.value)}
          >
            <option value="">Todos os clientes</option>
            {clientes.map(cliente => (
              <option key={cliente.codigo_cliente} value={cliente.codigo_cliente}>
                {cliente.nome_fantasia} ({cliente.codigo_cliente})
              </option>
            ))}
          </FilterSelect>
          <StandardButton
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAddProduto}
          >
            Novo Produto
          </StandardButton>
        </FilterContainer>
      </PageHeader>

      {error && (
        <div style={{ color: '#dc2626', textAlign: 'center', padding: '24px' }}>
          {error}
        </div>
      )}

      {produtos.length === 0 ? (
        <EmptyState>
          <div style={{ fontSize: '2rem', marginBottom: '16px' }}>🏢</div>
          <div>Nenhum produto encontrado</div>
          {filtroCliente && (
            <div style={{ marginTop: '8px', fontSize: '0.875rem' }}>
              Tente remover o filtro de cliente
            </div>
          )}
        </EmptyState>
      ) : (
        <ProductsGrid>
          {produtos.map((produto) => (
            <ProductCard key={produto.id}>
              <ProductHeader>
                <ProductTitle>{produto.denominacao}</ProductTitle>
                <ProductCode>{produto.codigo_estacao}</ProductCode>
              </ProductHeader>

              <ProductInfo>
                <InfoItem>
                  <InfoLabel>Cliente</InfoLabel>
                  <InfoValue>{produto.nome_cliente}</InfoValue>
                </InfoItem>
                <InfoItem>
                  <InfoLabel>Meses para Exumar</InfoLabel>
                  <InfoValue>{produto.meses_para_exumar} meses</InfoValue>
                </InfoItem>
                <InfoItem>
                  <InfoLabel>Ocupação</InfoLabel>
                  <InfoValue>{produto.percentual_ocupacao || 0}%</InfoValue>
                </InfoItem>
                <InfoItem>
                  <InfoLabel>Gavetas</InfoLabel>
                  <InfoValue>{produto.gavetas_ocupadas || 0}/{produto.total_gavetas || 0}</InfoValue>
                </InfoItem>
              </ProductInfo>

              <ActionButtons>
                <ActionButton onClick={() => handleViewSepultamentos(produto)}>
                  Ver Sepultados
                </ActionButton>
                <ActionButton onClick={() => handleViewDetalhes(produto)}>
                  Ver Detalhes
                </ActionButton>
                <ActionButton
                  className="primary"
                  onClick={() => handleEditProduto(produto)}
                >
                  Editar
                </ActionButton>
                <ActionButton
                  className="danger"
                  onClick={() => handleDeleteProduto(produto)}
                >
                  Deletar
                </ActionButton>
              </ActionButtons>
            </ProductCard>
          ))}
        </ProductsGrid>
      )}

      {/* Modal de Produto */}
      <ProdutoModal
        isOpen={showProdutoModal}
        onClose={() => setShowProdutoModal(false)}
        produto={selectedProduto}
        onSuccess={loadProdutos}
      />

      {/* Modal de Sepultamentos */}
      <Modal
        isOpen={showSepultamentosModal}
        onClose={() => setShowSepultamentosModal(false)}
        title={`Sepultados - ${selectedProduto?.denominacao}`}
        maxWidth="800px"
      >
        <SepultamentosModal>
          {sepultamentosProduto.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '24px', color: '#6b7280' }}>
              Nenhum sepultamento encontrado neste produto
            </div>
          ) : (
            <>
              <div style={{ 
                display: 'grid', 
                gridTemplateColumns: '2fr 1fr 1fr 1fr',
                gap: '12px',
                padding: '12px',
                background: '#f9fafb',
                fontWeight: '600',
                fontSize: '0.875rem',
                color: '#374151'
              }}>
                <div>Nome do Sepultado</div>
                <div>Localização</div>
                <div>Data Sepultamento</div>
                <div>Horário</div>
              </div>
              {sepultamentosProduto.map((sepultamento) => {
                // Construir localização com denominação do bloco
                const localizacao = sepultamento.denominacao_bloco
                  ? `${sepultamento.denominacao_bloco} → Sub Bloco ${sepultamento.codigo_sub_bloco} → Gaveta ${sepultamento.numero_gaveta}`
                  : sepultamento.localizacao;

                return (
                  <SepultamentoItem key={sepultamento.id}>
                    <div>{sepultamento.nome_sepultado}</div>
                    <div>{localizacao}</div>
                    <div>
                      {sepultamento.data_sepultamento
                        ? new Date(sepultamento.data_sepultamento).toLocaleDateString('pt-BR')
                        : '-'
                      }
                    </div>
                    <div>{sepultamento.horario_sepultamento || '-'}</div>
                  </SepultamentoItem>
                );
              })}
            </>
          )}
        </SepultamentosModal>
      </Modal>
    </PageContainer>
  );
};

export default CadastroProdutosPage;
