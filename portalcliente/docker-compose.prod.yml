version: '3.8'

services:
  # Backend Node.js para Portal Evolution
  portal_backend:
    image: portal-evolution-backend:latest
    restart: unless-stopped
    environment:
      NODE_ENV: production
      DB_HOST: postgres_postgres
      DB_PORT: 5432
      DB_NAME: dbetens
      DB_USER: postgres
      DB_PASSWORD: ab3780bd73ee4e2804d566ce6fd96209
      JWT_SECRET: portal_evolution_jwt_secret_2025_secure
      PORT: 3001
      EMAIL_HOST: smtp.gmail.com
      EMAIL_PORT: 587
      EMAIL_USER: <EMAIL>
      EMAIL_PASS: jgvhevmyjpuucbhp

    networks:
      - redeinterna
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3001/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      replicas: 1
      resources:
        limits:
          memory: ${BACKEND_MEMORY:-512m}
        reservations:
          memory: 256M
      labels:
        - "traefik.enable=true"
        - "traefik.http.services.portal-backend.loadbalancer.server.port=3001"
        - "traefik.http.routers.portal-backend.rule=Host(`portal.evo-eden.site`) && PathPrefix(`/api`)"
        - "traefik.http.routers.portal-backend.entrypoints=websecure"
        - "traefik.http.routers.portal-backend.tls.certresolver=letsencryptresolver"

  # Frontend React + Nginx para Portal Evolution
  portal_frontend:
    image: portal-evolution-frontend:latest
    restart: unless-stopped
    depends_on:
      - portal_backend
    networks:
      - redeinterna
    # healthcheck:
    #   test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:80/"]
    #   interval: 30s
    #   timeout: 10s
    #   retries: 3
    #   start_period: 30s
    deploy:
      replicas: 1
      resources:
        limits:
          memory: ${FRONTEND_MEMORY:-256m}
        reservations:
          memory: 128M
      labels:
        - "traefik.enable=true"
        - "traefik.http.services.portal-frontend.loadbalancer.server.port=80"
        - "traefik.http.routers.portal-frontend.rule=Host(`portal.evo-eden.site`)"
        - "traefik.http.routers.portal-frontend.entrypoints=websecure"
        - "traefik.http.routers.portal-frontend.tls.certresolver=letsencryptresolver"
        - "traefik.http.routers.portal-frontend.priority=1"
        # Headers para forçar cache-busting
        - "traefik.http.middlewares.nocache.headers.customrequestheaders.Cache-Control=no-cache, no-store, must-revalidate"
        - "traefik.http.middlewares.nocache.headers.customrequestheaders.Pragma=no-cache"
        - "traefik.http.middlewares.nocache.headers.customrequestheaders.Expires=0"
        - "traefik.http.routers.portal-frontend.middlewares=nocache"

  # Sistema de Logs Web
  portal_logs:
    image: portal-evolution-logs:1750364964
    restart: unless-stopped
    networks:
      - redeinterna
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock:ro"
    environment:
      NODE_ENV: production
      PORT: 3003
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role == manager
      resources:
        limits:
          memory: 256m
        reservations:
          memory: 128M
      labels:
        - "traefik.enable=true"
        - "traefik.http.services.portal-logs.loadbalancer.server.port=3003"
        - "traefik.http.routers.portal-logs.rule=Host(`logs.portal.evo-eden.site`)"
        - "traefik.http.routers.portal-logs.entrypoints=websecure"
        - "traefik.http.routers.portal-logs.tls.certresolver=letsencryptresolver"

networks:
  redeinterna:
    external: true
