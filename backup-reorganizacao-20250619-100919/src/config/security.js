/**
 * Configurações de Segurança do Portal Evolution
 * Controla aspectos de segurança da aplicação
 */

// Configurações de segurança
export const SECURITY_CONFIG = {
  // Console
  DISABLE_CONSOLE: true, // Sempre desabilitar console
  DISABLE_DEVTOOLS: true, // Desabilitar DevTools em produção
  DISABLE_RIGHT_CLICK: true, // Desabilitar menu de contexto
  DISABLE_TEXT_SELECTION: true, // Desabilitar seleção de texto
  DISABLE_DRAG: true, // Desabilitar arrastar elementos
  
  // Teclas de atalho
  DISABLE_F12: true, // Desabilitar F12
  DISABLE_CTRL_SHIFT_I: true, // Desabilitar Ctrl+Shift+I
  DISABLE_CTRL_SHIFT_C: true, // Desabilitar Ctrl+Shift+C
  DISABLE_CTRL_SHIFT_J: true, // Desabilitar Ctrl+Shift+J
  DISABLE_CTRL_U: true, // Desabilitar Ctrl+U (view source)
  
  // Mensagens
  SHOW_SECURITY_WARNINGS: false, // Não mostrar avisos de segurança
  CLEAR_CONSOLE_ON_DEVTOOLS: true, // Limpar console quando DevTools abrir
  
  // Ambiente
  PRODUCTION_ONLY_RESTRICTIONS: false, // Aplicar restrições em todos os ambientes
};

// Função para verificar se está em produção
export const isProduction = () => {
  return process.env.NODE_ENV === 'production' || !process.env.NODE_ENV;
};

// Função para verificar se deve aplicar restrições
export const shouldApplyRestrictions = () => {
  return SECURITY_CONFIG.PRODUCTION_ONLY_RESTRICTIONS ? isProduction() : true;
};

// Mensagens de segurança (ocultas por padrão)
export const SECURITY_MESSAGES = {
  CONSOLE_DISABLED: 'Console desabilitado por segurança',
  DEVTOOLS_BLOCKED: 'DevTools bloqueado por segurança',
  RIGHT_CLICK_DISABLED: 'Menu de contexto desabilitado',
  SELECTION_DISABLED: 'Seleção de texto desabilitada',
  SHORTCUT_BLOCKED: 'Atalho de teclado bloqueado'
};

export default SECURITY_CONFIG;
