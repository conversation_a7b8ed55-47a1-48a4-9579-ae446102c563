/**
 * =====================================================
 * SERVIÇO DE VALIDAÇÃO DE RANGES DE GAVETAS
 * =====================================================
 * Implementa todas as regras de negócio para validação de ranges
 * conforme especificado em definindo_range.md
 */

const { query } = require('../database/connection');

class RangeValidationService {
    
    /**
     * Valida se um range de gavetas pode ser criado/atualizado
     * @param {Object} params - Parâmetros de validação
     * @returns {Object} - Resultado da validação
     */
    async validateRange(params) {
        const {
            codigo_cliente,
            codigo_estacao,
            codigo_bloco,
            codigo_sub_bloco,
            numero_inicio,
            numero_fim,
            excluir_id = null
        } = params;

        try {
            console.log('🔍 Validando range de gavetas:', params);

            // 1. Validar se o range é válido (início <= fim)
            if (numero_inicio > numero_fim) {
                return {
                    valid: false,
                    error: `Número de início (${numero_inicio}) não pode ser maior que número de fim (${numero_fim})`
                };
            }

            // 2. Verificar sobreposição com outros ranges
            const overlapResult = await query(`
                SELECT validar_range_gavetas($1, $2, $3, $4, $5, $6, $7) as is_valid
            `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_inicio, numero_fim, excluir_id]);

            if (!overlapResult.rows[0].is_valid) {
                // Buscar detalhes do conflito
                const conflictDetails = await this.getConflictDetails(
                    codigo_cliente, codigo_estacao, codigo_bloco, numero_inicio, numero_fim, excluir_id
                );
                
                return {
                    valid: false,
                    error: `Range de gavetas (${numero_inicio} a ${numero_fim}) conflita com ranges existentes`,
                    conflicts: conflictDetails
                };
            }

            // 3. Se for atualização, verificar gavetas ocupadas
            if (excluir_id) {
                const occupiedResult = await query(`
                    SELECT verificar_gavetas_ocupadas($1, $2, $3, $4, $5, $6) as occupied_count
                `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_inicio, numero_fim]);

                const occupiedCount = occupiedResult.rows[0].occupied_count;
                if (occupiedCount > 0) {
                    return {
                        valid: false,
                        error: `Não é possível alterar range pois há ${occupiedCount} gaveta(s) ocupada(s) no range ${numero_inicio} a ${numero_fim}`
                    };
                }
            }

            console.log('✅ Range validado com sucesso');
            return { valid: true };

        } catch (error) {
            console.error('❌ Erro na validação de range:', error);
            return {
                valid: false,
                error: 'Erro interno na validação de range'
            };
        }
    }

    /**
     * Busca detalhes dos conflitos de range
     */
    async getConflictDetails(codigo_cliente, codigo_estacao, codigo_bloco, numero_inicio, numero_fim, excluir_id) {
        try {
            const result = await query(`
                SELECT 
                    ng.codigo_sub_bloco,
                    ng.numero_inicio,
                    ng.numero_fim,
                    sb.denominacao as sub_bloco_nome
                FROM numeracoes_gavetas ng
                JOIN sub_blocos sb ON ng.codigo_cliente = sb.codigo_cliente 
                    AND ng.codigo_estacao = sb.codigo_estacao
                    AND ng.codigo_bloco = sb.codigo_bloco
                    AND ng.codigo_sub_bloco = sb.codigo_sub_bloco
                WHERE ng.codigo_cliente = $1
                  AND ng.codigo_estacao = $2
                  AND ng.codigo_bloco = $3
                  AND ng.ativo = true
                  AND ($4 IS NULL OR ng.id != $4)
                  AND (
                      ($5 BETWEEN ng.numero_inicio AND ng.numero_fim) OR
                      ($6 BETWEEN ng.numero_inicio AND ng.numero_fim) OR
                      (ng.numero_inicio BETWEEN $5 AND $6) OR
                      (ng.numero_fim BETWEEN $5 AND $6)
                  )
            `, [codigo_cliente, codigo_estacao, codigo_bloco, excluir_id, numero_inicio, numero_fim]);

            return result.rows;
        } catch (error) {
            console.error('❌ Erro ao buscar detalhes de conflito:', error);
            return [];
        }
    }

    /**
     * Sincroniza gavetas com as numerações
     */
    async syncGavetas(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco) {
        try {
            console.log('🔄 Sincronizando gavetas:', { codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco });

            await query(`
                SELECT sincronizar_gavetas($1, $2, $3, $4)
            `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco]);

            console.log('✅ Gavetas sincronizadas com sucesso');
            return { success: true };

        } catch (error) {
            console.error('❌ Erro na sincronização de gavetas:', error);
            throw error;
        }
    }

    /**
     * Valida se uma entidade pode ser deletada (validação hierárquica)
     */
    async validateDeletion(tipo, params) {
        try {
            console.log('🔍 Validando deleção hierárquica:', { tipo, params });

            const {
                codigo_cliente,
                codigo_estacao = null,
                codigo_bloco = null,
                codigo_sub_bloco = null,
                numero_gaveta = null
            } = params;

            const result = await query(`
                SELECT validar_delecao_hierarquica($1, $2, $3, $4, $5, $6) as erro
            `, [tipo, codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta]);

            const erro = result.rows[0].erro;
            
            if (erro) {
                return {
                    canDelete: false,
                    error: erro
                };
            }

            return { canDelete: true };

        } catch (error) {
            console.error('❌ Erro na validação de deleção:', error);
            return {
                canDelete: false,
                error: 'Erro interno na validação de deleção'
            };
        }
    }

    /**
     * Lista ranges de um sub-bloco com informações detalhadas (NOVA ARQUITETURA)
     */
    async listRanges(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco) {
        try {
            console.log('📋 Listando ranges com nova arquitetura:', {
                codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco
            });

            // Usar função SQL otimizada
            const result = await query(`
                SELECT * FROM listar_ranges_detalhados($1, $2, $3, $4)
            `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco]);

            console.log('✅ Ranges encontrados:', result.rows.length);
            return result.rows;
        } catch (error) {
            console.error('❌ Erro ao listar ranges:', error);
            throw error;
        }
    }

    /**
     * Calcula total de gavetas baseado nos ranges (NOVA ARQUITETURA)
     */
    async calcularTotalGavetasRanges(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco) {
        try {
            console.log('📊 Calculando total de gavetas por ranges:', {
                codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco
            });

            const result = await query(`
                SELECT calcular_total_gavetas_ranges($1, $2, $3, $4) as total_gavetas
            `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco]);

            const totalGavetas = result.rows[0].total_gavetas || 0;
            console.log('✅ Total de gavetas calculado:', totalGavetas);

            return {
                success: true,
                total_gavetas: totalGavetas,
                codigo_cliente,
                codigo_estacao,
                codigo_bloco,
                codigo_sub_bloco
            };

        } catch (error) {
            console.error('❌ Erro ao calcular total de gavetas:', error);
            return {
                success: false,
                error: 'Erro interno ao calcular gavetas'
            };
        }
    }

    /**
     * Cria um novo range de gavetas
     */
    async createRange(params) {
        const {
            codigo_cliente,
            codigo_estacao,
            codigo_bloco,
            codigo_sub_bloco,
            numero_inicio,
            numero_fim
        } = params;

        try {
            console.log('🔨 Criando novo range:', params);

            // Validar range
            const validation = await this.validateRange(params);
            if (!validation.valid) {
                return validation;
            }

            // Inserir range
            const result = await query(`
                INSERT INTO numeracoes_gavetas (
                    codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco,
                    numero_inicio, numero_fim, ativo
                ) VALUES ($1, $2, $3, $4, $5, $6, true)
                RETURNING *
            `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_inicio, numero_fim]);

            console.log('✅ Range criado com sucesso:', result.rows[0]);
            return {
                success: true,
                range: result.rows[0]
            };

        } catch (error) {
            console.error('❌ Erro ao criar range:', error);
            return {
                success: false,
                error: 'Erro interno ao criar range'
            };
        }
    }

    /**
     * Atualiza um range existente
     */
    async updateRange(id, params) {
        const {
            codigo_cliente,
            codigo_estacao,
            codigo_bloco,
            codigo_sub_bloco,
            numero_inicio,
            numero_fim
        } = params;

        try {
            console.log('🔄 Atualizando range:', { id, ...params });

            // Validar range (incluindo ID para excluir da validação)
            const validation = await this.validateRange({ ...params, excluir_id: id });
            if (!validation.valid) {
                return validation;
            }

            // Atualizar range
            const result = await query(`
                UPDATE numeracoes_gavetas 
                SET numero_inicio = $1, numero_fim = $2, updated_at = CURRENT_TIMESTAMP
                WHERE id = $3 AND codigo_cliente = $4 AND codigo_estacao = $5 
                  AND codigo_bloco = $6 AND codigo_sub_bloco = $7
                RETURNING *
            `, [numero_inicio, numero_fim, id, codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco]);

            if (result.rows.length === 0) {
                return {
                    success: false,
                    error: 'Range não encontrado'
                };
            }

            console.log('✅ Range atualizado com sucesso:', result.rows[0]);
            return {
                success: true,
                range: result.rows[0]
            };

        } catch (error) {
            console.error('❌ Erro ao atualizar range:', error);
            return {
                success: false,
                error: 'Erro interno ao atualizar range'
            };
        }
    }

    /**
     * Deleta um range de gavetas
     */
    async deleteRange(id, codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco) {
        try {
            console.log('🗑️ Deletando range:', { id, codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco });

            // Buscar range atual
            const currentRange = await query(`
                SELECT * FROM numeracoes_gavetas 
                WHERE id = $1 AND codigo_cliente = $2 AND codigo_estacao = $3 
                  AND codigo_bloco = $4 AND codigo_sub_bloco = $5
            `, [id, codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco]);

            if (currentRange.rows.length === 0) {
                return {
                    success: false,
                    error: 'Range não encontrado'
                };
            }

            const range = currentRange.rows[0];

            // Verificar se range pode ser deletado (CONFORME INSTRUCAO.MD)
            const canDeleteResult = await query(`
                SELECT pode_deletar_range($1, $2, $3, $4, $5, $6) as pode_deletar
            `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, range.numero_inicio, range.numero_fim]);

            const podeDeletear = canDeleteResult.rows[0].pode_deletar;
            if (!podeDeletear) {
                // Buscar contagem detalhada para feedback
                const occupiedResult = await query(`
                    SELECT COUNT(*) as occupied_count
                    FROM gavetas
                    WHERE codigo_cliente = $1 AND codigo_estacao = $2
                    AND codigo_bloco = $3 AND codigo_sub_bloco = $4
                    AND numero_gaveta BETWEEN $5 AND $6
                    AND disponivel = false AND ativo = true
                `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, range.numero_inicio, range.numero_fim]);

                const occupiedCount = occupiedResult.rows[0].occupied_count;
                return {
                    success: false,
                    error: `Não é possível deletar range pois há ${occupiedCount} gaveta(s) ocupada(s) no range ${range.numero_inicio} a ${range.numero_fim} (conforme instrucao.md)`
                };
            }

            // PASSO 1: Deletar gavetas do range primeiro (CONFORME INSTRUCAO.MD)
            const deleteGavetasResult = await query(`
                SELECT deletar_gavetas_range($1, $2, $3, $4, $5, $6) as resultado
            `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, range.numero_inicio, range.numero_fim]);

            console.log('🗑️ Resultado da deleção de gavetas:', deleteGavetasResult.rows[0].resultado);

            // PASSO 2: Deletar range FISICAMENTE (DELETE real)
            // CORREÇÃO: Isso resolve o problema de constraint única ao recriar ranges
            await query('SET session_replication_role = replica;');
            await query(`
                DELETE FROM numeracoes_gavetas
                WHERE id = $1
            `, [id]);
            await query('SET session_replication_role = DEFAULT;');

            console.log('✅ Range deletado com sucesso');
            return {
                success: true,
                gavetas_info: deleteGavetasResult.rows[0].resultado
            };

        } catch (error) {
            console.error('❌ Erro ao deletar range:', error);
            return {
                success: false,
                error: 'Erro interno ao deletar range'
            };
        }
    }

    /**
     * Conta gavetas baseado nos ranges (CONFORME INSTRUCAO.MD)
     */
    async contarGavetasPorRanges(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco) {
        try {
            console.log('📊 Contando gavetas por ranges conforme instrucao.md:', {
                codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco
            });

            const result = await query(`
                SELECT contar_gavetas_por_ranges($1, $2, $3, $4) as total_gavetas
            `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco]);

            const totalGavetas = result.rows[0].total_gavetas;
            console.log('✅ Total de gavetas contadas:', totalGavetas);

            return {
                success: true,
                total_gavetas: totalGavetas
            };

        } catch (error) {
            console.error('❌ Erro ao contar gavetas por ranges:', error);
            return {
                success: false,
                error: 'Erro interno ao contar gavetas'
            };
        }
    }
}

module.exports = new RangeValidationService();
