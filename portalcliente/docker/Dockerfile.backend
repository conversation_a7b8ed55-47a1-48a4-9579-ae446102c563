# ===================================
# DOCKERFILE BACKEND - PORTAL EVOLUTION
# ===================================
# Build Node.js seguindo padrão da VPS
# Para verificar build: docker build -f docker/Dockerfile.backend -t portal-backend .

FROM node:18-alpine

# Criar usuário não-root para segurança
# Para verificar usuário: docker exec portal-backend id nodejs
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# Definir diretório de trabalho
WORKDIR /app

# Copiar arquivos de dependências do servidor
# Para verificar dependências: cat server/package.json | grep dependencies
COPY server/package*.json ./

# Instalar dependências
# Para verificar instalação: npm list --depth=0
RUN npm ci --only=production && npm cache clean --force

# Copiar código do servidor
COPY server/ .

# Criar diretórios necessários
# Para verificar diretórios: docker exec portal-backend ls -la /app/
RUN mkdir -p logs uploads

# Alterar proprietário dos arquivos
# Para verificar permissões: docker exec portal-backend ls -la /app/
RUN chown -R nodejs:nodejs /app
USER nodejs

# Expor porta
EXPOSE 3001

# Health check
# Para testar: docker exec portal-backend curl http://localhost:3001/api/health
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3001/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Comando para iniciar
CMD ["node", "index.js"]

# ===================================
# INSTRUÇÕES DE BUILD E DEBUG
# ===================================

# Para fazer build:
# docker build -f docker/Dockerfile.backend -t portal-backend .

# Para executar localmente:
# docker run -p 3001:3001 -e DB_HOST=localhost portal-backend

# Para debug:
# docker run -it portal-backend sh

# Para verificar logs:
# docker logs portal-backend

# Para conectar no container:
# docker exec -it portal-backend sh
