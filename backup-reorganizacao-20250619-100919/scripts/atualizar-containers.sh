#!/bin/bash

# ===================================
# SCRIPT DE ATUALIZAÇÃO - PORTAL EVOLUTION
# ===================================
# Execute: bash scripts/atualizar-containers.sh
# Para verificar atualização: docker images | grep portal

set -e

echo "🔄 Atualizando Portal Evolution..."

# Cores para output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

# ===================================
# VERIFICAÇÕES INICIAIS
# ===================================
log "Verificando sistema..."

# Verificar se arquivo .env existe
if [[ ! -f ".env" ]]; then
    error "Arquivo .env não encontrado!"
fi

# Verificar se docker-compose está disponível
# Para instalar: curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
if ! command -v docker-compose &> /dev/null; then
    error "docker-compose não encontrado!"
fi

# ===================================
# BACKUP ANTES DA ATUALIZAÇÃO
# ===================================
log "Fazendo backup antes da atualização..."

# Executar script de backup
# Para verificar backup: ls -la dados/backups/
if [[ -f "scripts/backup-dados.sh" ]]; then
    bash scripts/backup-dados.sh
    log "✅ Backup concluído"
else
    warn "⚠️  Script de backup não encontrado, continuando sem backup"
fi

# ===================================
# ATUALIZAR CÓDIGO (SE USANDO GIT)
# ===================================
if [[ -d ".git" ]]; then
    log "Atualizando código via Git..."
    
    # Verificar se há alterações locais
    # Para verificar: git status --porcelain
    if [[ -n $(git status --porcelain) ]]; then
        warn "⚠️  Há alterações locais não commitadas"
        info "Fazendo stash das alterações..."
        git stash
    fi
    
    # Fazer pull das atualizações
    # Para verificar: git log --oneline -5
    if git pull origin master; then
        log "✅ Código atualizado via Git"
    else
        warn "⚠️  Erro ao fazer git pull, continuando com código atual"
    fi
else
    info "ℹ️  Não é um repositório Git, pulando atualização de código"
fi

# ===================================
# PARAR CONTAINERS ATUAIS
# ===================================
log "Parando containers atuais..."

# Verificar quais containers estão rodando
# Para verificar: docker ps | grep portal
RUNNING_CONTAINERS=$(docker ps --filter "name=portal-" --format "{{.Names}}" | wc -l)
if [[ $RUNNING_CONTAINERS -gt 0 ]]; then
    log "   Parando $RUNNING_CONTAINERS containers..."
    docker-compose -f portal-evolution.yaml down
    log "✅ Containers parados"
else
    info "ℹ️  Nenhum container rodando"
fi

# ===================================
# LIMPAR IMAGENS ANTIGAS
# ===================================
log "Limpando imagens antigas..."

# Remover imagens não utilizadas
# Para verificar: docker images | grep "<none>"
REMOVED_IMAGES=$(docker image prune -f | grep "Total reclaimed space" | awk '{print $4}' || echo "0B")
log "   Espaço liberado: $REMOVED_IMAGES"

# Remover imagens antigas do portal
# Para verificar: docker images | grep portal-evolution
OLD_IMAGES=$(docker images --filter "reference=portal-evolution/*" --filter "dangling=true" -q)
if [[ -n "$OLD_IMAGES" ]]; then
    docker rmi $OLD_IMAGES
    log "✅ Imagens antigas removidas"
fi

# ===================================
# RECONSTRUIR E INICIAR CONTAINERS
# ===================================
log "Reconstruindo containers..."

# Build das novas imagens
# Para verificar build: docker-compose -f portal-evolution.yaml build --no-cache
docker-compose -f portal-evolution.yaml build --no-cache

log "Iniciando containers atualizados..."

# Iniciar containers
# Para verificar: docker-compose -f portal-evolution.yaml ps
docker-compose -f portal-evolution.yaml up -d

# ===================================
# AGUARDAR E VERIFICAR SAÚDE
# ===================================
log "Aguardando containers iniciarem..."
sleep 30

# Verificar status dos containers
log "Verificando status dos containers..."
docker-compose -f portal-evolution.yaml ps

# Verificar saúde dos serviços
log "Verificando saúde dos serviços..."

# Aguardar um pouco mais para os serviços estabilizarem
sleep 20

# Verificar banco de dados
# Para testar: docker exec portal-database pg_isready -U portal_user
if docker exec portal-database pg_isready -U portal_user > /dev/null 2>&1; then
    log "✅ Banco de dados: OK"
else
    warn "❌ Banco de dados: ERRO"
fi

# Verificar backend
# Para testar: curl http://localhost:3001/api/health
if curl -f -s http://localhost:3001/api/health > /dev/null; then
    log "✅ Backend: OK"
else
    warn "❌ Backend: ERRO"
fi

# Verificar frontend
# Para testar: curl http://localhost/health
if curl -f -s http://localhost/health > /dev/null; then
    log "✅ Frontend: OK"
else
    warn "❌ Frontend: ERRO"
fi

# ===================================
# LIMPEZA FINAL
# ===================================
log "Fazendo limpeza final..."

# Remover containers parados
# Para verificar: docker ps -a | grep Exited
docker container prune -f > /dev/null

# Remover volumes não utilizados
# Para verificar: docker volume ls | grep -v portal
docker volume prune -f > /dev/null

log "✅ Limpeza concluída"

# ===================================
# RESUMO DA ATUALIZAÇÃO
# ===================================
log "📊 Resumo da atualização:"

# Verificar versões das imagens
# Para verificar: docker images | grep portal-evolution
FRONTEND_IMAGE=$(docker images --format "table {{.Repository}}:{{.Tag}}\t{{.CreatedAt}}" | grep portal-evolution/frontend | head -1)
BACKEND_IMAGE=$(docker images --format "table {{.Repository}}:{{.Tag}}\t{{.CreatedAt}}" | grep portal-evolution/backend | head -1)

if [[ -n "$FRONTEND_IMAGE" ]]; then
    log "   🖥️  Frontend: $FRONTEND_IMAGE"
fi

if [[ -n "$BACKEND_IMAGE" ]]; then
    log "   ⚙️  Backend: $BACKEND_IMAGE"
fi

# Verificar uso de recursos
# Para verificar: docker stats --no-stream
log "   💾 Uso de memória:"
docker stats --no-stream --format "table {{.Container}}\t{{.MemUsage}}" | grep portal

# Verificar espaço em disco
DISK_USAGE=$(df -h . | tail -1 | awk '{print $5}')
log "   💿 Uso do disco: $DISK_USAGE"

log ""
log "🎉 Atualização concluída com sucesso!"
log ""
log "📋 Para verificar:"
log "   Status: docker-compose -f portal-evolution.yaml ps"
log "   Logs: docker-compose -f portal-evolution.yaml logs -f"
log "   Saúde: curl http://localhost/health && curl http://localhost/api/health"

# Obter IP para acesso
VPS_IP=$(curl -s ifconfig.me 2>/dev/null || echo "SEU_IP")
log ""
log "🌐 Acesso: http://$VPS_IP"
