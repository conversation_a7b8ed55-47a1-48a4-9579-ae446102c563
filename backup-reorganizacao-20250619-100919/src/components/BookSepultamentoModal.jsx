import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  Typography,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Divider,
  IconButton
} from '@mui/material';
import {
  Close as CloseIcon,
  Person as PersonIcon,
  CalendarToday as CalendarIcon,
  AccessTime as TimeIcon,
  LocationOn as LocationIcon
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { sepultamentoService, produtoService } from '../services/api';

const BookSepultamentoModal = ({ 
  isOpen, 
  onClose, 
  sepultamento = null, 
  produto = null,
  onSuccess 
}) => {
  const [formData, setFormData] = useState({
    nome_sepultado: '',
    data_sepultamento: '',
    horario_sepultamento: '',
    observacoes: '',
    bloco_id: '',
    gaveta_id: ''
  });
  const [blocos, setBlocos] = useState([]);
  const [gavetas, setGavetas] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const { user, getCodigoCliente } = useAuth();

  useEffect(() => {
    if (isOpen) {
      if (sepultamento) {
        // Modo edição
        setFormData({
          nome_sepultado: sepultamento.nome_sepultado || '',
          data_sepultamento: sepultamento.data_sepultamento ? sepultamento.data_sepultamento.split('T')[0] : '',
          horario_sepultamento: sepultamento.horario_sepultamento || '',
          observacoes: sepultamento.observacoes || '',
          bloco_id: sepultamento.bloco_id || '',
          gaveta_id: sepultamento.gaveta_id || ''
        });
      } else {
        // Modo criação
        const now = new Date();
        const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;

        setFormData({
          nome_sepultado: '',
          data_sepultamento: new Date().toISOString().split('T')[0],
          horario_sepultamento: currentTime,
          observacoes: '',
          bloco_id: '',
          gaveta_id: ''
        });
      }
      setError('');
      
      // Carregar blocos se temos produto
      if (produto) {
        loadBlocos();
      }
    }
  }, [isOpen, sepultamento, produto]);

  useEffect(() => {
    if (formData.bloco_id) {
      loadGavetas(formData.bloco_id);
    } else {
      setGavetas([]);
      setFormData(prev => ({ ...prev, gaveta_id: '' }));
    }
  }, [formData.bloco_id]);

  const loadBlocos = async () => {
    try {
      if (!produto?.codigo_cliente || !produto?.codigo_estacao) {
        console.error('Produto sem códigos necessários:', produto);
        return;
      }

      const clienteCode = getCodigoCliente() || produto.codigo_cliente;
      const response = await produtoService.listarBlocos(clienteCode, produto.codigo_estacao);
      setBlocos(response.data || []);
    } catch (error) {
      console.error('Erro ao carregar blocos:', error);
      setError('Erro ao carregar blocos disponíveis');
    }
  };

  const loadGavetas = async (blocoId) => {
    try {
      const bloco = blocos.find(b => b.id == blocoId);
      if (!bloco || !produto) {
        return;
      }

      const clienteCode = getCodigoCliente() || produto.codigo_cliente;
      
      // Buscar sub-blocos do bloco
      const subBlocosResponse = await produtoService.listarSubBlocos(
        clienteCode,
        produto.codigo_estacao,
        bloco.codigo_bloco
      );

      // Carregar gavetas de todos os sub-blocos
      let todasGavetas = [];
      for (const subBloco of subBlocosResponse.data || []) {
        try {
          const gavetasResponse = await produtoService.listarGavetas(
            clienteCode,
            produto.codigo_estacao,
            bloco.codigo_bloco,
            subBloco.codigo_sub_bloco
          );

          // Filtrar apenas gavetas disponíveis (sem sepultamento ativo)
          const gavetasDisponiveis = (gavetasResponse.data || []).filter(g => 
            g.disponivel && !g.nome_sepultado
          );
          
          // Adicionar informação do sub-bloco para cada gaveta
          const gavetasComSubBloco = gavetasDisponiveis.map(gaveta => ({
            ...gaveta,
            sub_bloco_nome: subBloco.denominacao || subBloco.nome,
            sub_bloco_codigo: subBloco.codigo_sub_bloco
          }));
          
          todasGavetas = [...todasGavetas, ...gavetasComSubBloco];
        } catch (error) {
          console.error(`Erro ao carregar gavetas do sub-bloco ${subBloco.codigo_sub_bloco}:`, error);
        }
      }

      setGavetas(todasGavetas);
    } catch (error) {
      console.error('Erro ao carregar gavetas:', error);
      setError('Erro ao carregar gavetas disponíveis');
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;

    // Formatação especial para horário
    if (name === 'horario_sepultamento') {
      // Remove caracteres não numéricos
      const numericValue = value.replace(/\D/g, '');

      // Aplica formatação HH:MM
      let formattedValue = '';
      if (numericValue.length >= 1) {
        formattedValue = numericValue.substring(0, 2);
        if (numericValue.length >= 3) {
          formattedValue += ':' + numericValue.substring(2, 4);
        }
      }

      // Validação básica de horário
      if (formattedValue.length === 5) {
        const [hours, minutes] = formattedValue.split(':');
        if (parseInt(hours) > 23 || parseInt(minutes) > 59) {
          return; // Não atualiza se horário inválido
        }
      }

      setFormData(prev => ({
        ...prev,
        [name]: formattedValue
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.nome_sepultado || !formData.data_sepultamento || !formData.gaveta_id) {
      setError('Por favor, preencha todos os campos obrigatórios');
      return;
    }

    try {
      setLoading(true);
      setError('');

      const gaveta = gavetas.find(g => g.id == formData.gaveta_id);
      const bloco = blocos.find(b => b.id == formData.bloco_id);
      
      if (!gaveta || !bloco) {
        setError('Gaveta ou bloco não encontrado');
        return;
      }

      const dadosEnvio = {
        nome_sepultado: formData.nome_sepultado,
        data_sepultamento: formData.data_sepultamento,
        horario_sepultamento: formData.horario_sepultamento,
        observacoes: formData.observacoes,
        gaveta_id: gaveta.id,
        codigo_cliente: produto.codigo_cliente,
        codigo_estacao: produto.codigo_estacao,
        codigo_bloco: bloco.codigo_bloco,
        codigo_sub_bloco: gaveta.codigo_sub_bloco,
        numero_gaveta: gaveta.numero_gaveta,
        posicao: 1
      };

      if (sepultamento) {
        // Atualizar sepultamento existente
        await sepultamentoService.atualizar(sepultamento.id, dadosEnvio);
      } else {
        // Criar novo sepultamento
        await sepultamentoService.criar(dadosEnvio);
      }

      onSuccess();
      onClose();
    } catch (error) {
      console.error('Erro ao salvar sepultamento:', error);
      setError(error.response?.data?.error || 'Erro ao salvar sepultamento');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog 
      open={isOpen} 
      onClose={onClose} 
      maxWidth="md" 
      fullWidth
      PaperProps={{
        sx: { borderRadius: 2 }
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6" component="div">
            {sepultamento ? 'Editar Sepultamento' : 'Novo Sepultamento'}
          </Typography>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
        {produto && (
          <Typography variant="body2" color="text.secondary">
            {produto.denominacao || produto.nome} - {produto.codigo_estacao}
          </Typography>
        )}
      </DialogTitle>

      <form onSubmit={handleSubmit}>
        <DialogContent sx={{ pt: 2 }}>
          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {/* Informações do Sepultado */}
          <Box sx={{ mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <PersonIcon sx={{ mr: 1, color: 'primary.main' }} />
              <Typography variant="h6">Informações do Sepultado</Typography>
            </Box>
            
            <TextField
              name="nome_sepultado"
              label="Nome do Sepultado"
              value={formData.nome_sepultado}
              onChange={handleChange}
              fullWidth
              required
              placeholder="Nome completo do sepultado"
              sx={{ mb: 2 }}
            />

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="data_sepultamento"
                  label="Data do Sepultamento"
                  type="date"
                  value={formData.data_sepultamento}
                  onChange={handleChange}
                  fullWidth
                  required
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="horario_sepultamento"
                  label="Horário do Sepultamento"
                  value={formData.horario_sepultamento}
                  onChange={handleChange}
                  fullWidth
                  placeholder="HH:MM"
                  inputProps={{
                    maxLength: 5,
                    pattern: "[0-9]{2}:[0-9]{2}"
                  }}
                  InputProps={{
                    startAdornment: <TimeIcon sx={{ mr: 1, color: 'text.secondary' }} />
                  }}
                  helperText="Formato: HH:MM (ex: 14:30)"
                />
              </Grid>
            </Grid>
          </Box>

          <Divider sx={{ my: 3 }} />

          {/* Localização */}
          <Box sx={{ mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <LocationIcon sx={{ mr: 1, color: 'primary.main' }} />
              <Typography variant="h6">Localização</Typography>
            </Box>

            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth required>
                  <InputLabel sx={{ fontSize: '1.2rem', fontWeight: 600, color: '#1976d2' }}>Bloco</InputLabel>
                  <Select
                    name="bloco_id"
                    value={formData.bloco_id}
                    onChange={handleChange}
                    label="Bloco"
                    MenuProps={{
                      PaperProps: {
                        sx: {
                          maxHeight: 400,
                          '& .MuiMenuItem-root': {
                            fontSize: '1.1rem',
                            padding: '16px 20px',
                            minHeight: '56px',
                            fontWeight: 500,
                            '&:hover': {
                              backgroundColor: '#f0f9ff'
                            }
                          }
                        }
                      }
                    }}
                    sx={{
                      minHeight: '80px',
                      '& .MuiSelect-select': {
                        padding: '20px 16px',
                        fontSize: '1.2rem',
                        minHeight: '60px',
                        display: 'flex',
                        alignItems: 'center'
                      },
                      '& .MuiInputLabel-root': {
                        fontSize: '1.2rem',
                        fontWeight: 600
                      },
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '12px'
                      }
                    }}
                  >
                    <MenuItem value="">
                      <em>Selecione um bloco</em>
                    </MenuItem>
                    {blocos.map(bloco => (
                      <MenuItem key={bloco.id} value={bloco.id}>
                        {bloco.denominacao || bloco.nome}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth required disabled={!formData.bloco_id || gavetas.length === 0}>
                  <InputLabel sx={{ fontSize: '1.2rem', fontWeight: 600, color: '#1976d2' }}>Gaveta</InputLabel>
                  <Select
                    name="gaveta_id"
                    value={formData.gaveta_id}
                    onChange={handleChange}
                    label="Gaveta"
                    MenuProps={{
                      PaperProps: {
                        sx: {
                          maxHeight: 400,
                          '& .MuiMenuItem-root': {
                            fontSize: '1.1rem',
                            padding: '16px 20px',
                            minHeight: '56px',
                            fontWeight: 500,
                            '&:hover': {
                              backgroundColor: '#f0f9ff'
                            }
                          }
                        }
                      }
                    }}
                    sx={{
                      minHeight: '80px',
                      '& .MuiSelect-select': {
                        padding: '20px 16px',
                        fontSize: '1.2rem',
                        minHeight: '60px',
                        display: 'flex',
                        alignItems: 'center'
                      },
                      '& .MuiInputLabel-root': {
                        fontSize: '1.2rem',
                        fontWeight: 600
                      },
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '12px'
                      }
                    }}
                  >
                    <MenuItem value="">
                      <em>Selecione uma gaveta</em>
                    </MenuItem>
                    {gavetas.map(gaveta => (
                      <MenuItem key={gaveta.id} value={gaveta.id}>
                        Gaveta {gaveta.numero_gaveta}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            </Grid>

            {formData.bloco_id && gavetas.length === 0 && (
              <Alert severity="warning" sx={{ mt: 2 }}>
                Nenhuma gaveta disponível neste bloco.
              </Alert>
            )}
          </Box>

          <Divider sx={{ my: 3 }} />

          {/* Observações */}
          <TextField
            name="observacoes"
            label="Observações"
            value={formData.observacoes}
            onChange={handleChange}
            fullWidth
            multiline
            rows={3}
            placeholder="Observações adicionais sobre o sepultamento..."
          />
        </DialogContent>

        <DialogActions sx={{ p: 3, pt: 2 }}>
          <Button onClick={onClose} disabled={loading}>
            Cancelar
          </Button>
          <Button 
            type="submit" 
            variant="contained" 
            disabled={loading}
            sx={{ textTransform: 'none', fontWeight: 600 }}
          >
            {loading ? 'Salvando...' : (sepultamento ? 'Atualizar' : 'Cadastrar')}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default BookSepultamentoModal;
