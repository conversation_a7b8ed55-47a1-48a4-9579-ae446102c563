# ===================================
# PORTAL EVOLUTION - TESTE DO SISTEMA
# ===================================
# Script para testar todo o sistema
# Execute: .\test-system.ps1

param(
    [switch]$Quick,      # Teste rápido
    [switch]$Full,       # Teste completo
    [switch]$SkipSSH     # Pular teste SSH
)

# Função para log
function Write-Log {
    param([string]$Message, [string]$Color = "Green")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] $Message" -ForegroundColor $Color
}

function Write-Info {
    param([string]$Message)
    Write-Log $Message "Cyan"
}

function Write-Success {
    param([string]$Message)
    Write-Log $Message "Green"
}

function Write-Warning {
    param([string]$Message)
    Write-Log $Message "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-Log $Message "Red"
}

function Test-Command {
    param([string]$Command, [string]$Name)
    try {
        $result = Invoke-Expression "$Command 2>$null"
        if ($LASTEXITCODE -eq 0) {
            Write-Success "✅ $Name: OK"
            return $true
        } else {
            Write-Warning "⚠️ $Name: Não encontrado"
            return $false
        }
    } catch {
        Write-Warning "⚠️ $Name: Erro - $_"
        return $false
    }
}

# Definir diretório do script
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $scriptPath

Write-Host ""
Write-Host "🧪 PORTAL EVOLUTION - TESTE DO SISTEMA" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan
Write-Host ""

$testResults = @{}

# Teste 1: Estrutura de arquivos
Write-Info "1. Testando estrutura de arquivos..."

$requiredFiles = @(
    "start-app.ps1",
    "quick-setup.ps1", 
    "sync-to-vps.ps1",
    "deploy-auto.sh",
    "test-system.ps1",
    "configuracao.env.example",
    "docker-compose.prod.yml",
    "Dockerfile.backend",
    "Dockerfile.frontend",
    "package.json",
    "server/package.json",
    "server/index.js",
    "src/App.jsx",
    "src/main.jsx"
)

$missingFiles = @()
foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "   ✅ $file" -ForegroundColor Green
    } else {
        Write-Host "   ❌ $file" -ForegroundColor Red
        $missingFiles += $file
    }
}

if ($missingFiles.Count -eq 0) {
    Write-Success "✅ Estrutura de arquivos: OK"
    $testResults["files"] = $true
} else {
    Write-Error "❌ Arquivos faltando: $($missingFiles -join ', ')"
    $testResults["files"] = $false
}

Write-Host ""

# Teste 2: Dependências do sistema
Write-Info "2. Testando dependências do sistema..."

$testResults["git"] = Test-Command "git --version" "Git"
$testResults["node"] = Test-Command "node --version" "Node.js"
$testResults["npm"] = Test-Command "npm --version" "NPM"

if (-not $SkipSSH) {
    $testResults["ssh"] = Test-Command "ssh -V" "SSH"
} else {
    Write-Warning "⚠️ SSH: Pulado"
    $testResults["ssh"] = $true
}

Write-Host ""

# Teste 3: Configuração
Write-Info "3. Testando configuração..."

if (Test-Path "configuracao.env") {
    Write-Success "✅ Arquivo configuracao.env existe"
    
    # Verificar conteúdo básico
    $config = Get-Content "configuracao.env" -Raw
    $requiredVars = @("VPS_HOST", "VPS_USER", "DOMAIN", "DB_NAME", "DB_USER", "JWT_SECRET")
    $missingVars = @()
    
    foreach ($var in $requiredVars) {
        if ($config -match "$var=") {
            Write-Host "   ✅ $var configurado" -ForegroundColor Green
        } else {
            Write-Host "   ❌ $var não encontrado" -ForegroundColor Red
            $missingVars += $var
        }
    }
    
    if ($missingVars.Count -eq 0) {
        Write-Success "✅ Configuração: OK"
        $testResults["config"] = $true
    } else {
        Write-Error "❌ Variáveis faltando: $($missingVars -join ', ')"
        $testResults["config"] = $false
    }
} else {
    Write-Warning "⚠️ Arquivo configuracao.env não existe"
    Write-Host "   Execute: .\quick-setup.ps1" -ForegroundColor Yellow
    $testResults["config"] = $false
}

Write-Host ""

# Teste 4: Scripts executáveis
Write-Info "4. Testando scripts..."

$scripts = @(
    "start-app.ps1",
    "quick-setup.ps1",
    "sync-to-vps.ps1",
    "test-system.ps1"
)

foreach ($script in $scripts) {
    if (Test-Path $script) {
        try {
            # Teste básico de sintaxe
            $null = Get-Command $script -ErrorAction Stop
            Write-Host "   ✅ $script: Sintaxe OK" -ForegroundColor Green
        } catch {
            Write-Host "   ❌ $script: Erro de sintaxe" -ForegroundColor Red
        }
    }
}

$testResults["scripts"] = $true
Write-Host ""

# Teste 5: Conectividade SSH (se configurado)
if (-not $SkipSSH -and $testResults["config"] -and $testResults["ssh"]) {
    Write-Info "5. Testando conectividade SSH..."
    
    try {
        # Carregar configuração
        $config = @{}
        Get-Content "configuracao.env" | ForEach-Object {
            if ($_ -match "^([^#][^=]+)=(.*)$") {
                $config[$matches[1].Trim()] = $matches[2].Trim()
            }
        }
        
        $vpsHost = $config["VPS_HOST"]
        $vpsUser = $config["VPS_USER"]
        
        if ($vpsHost -and $vpsUser) {
            Write-Host "   Testando: $vpsUser@$vpsHost" -ForegroundColor Gray
            
            $sshTest = ssh -o ConnectTimeout=10 -o BatchMode=yes $vpsUser@$vpsHost "echo 'SSH OK'" 2>$null
            
            if ($LASTEXITCODE -eq 0) {
                Write-Success "✅ SSH: Conectado com sucesso"
                $testResults["ssh_connectivity"] = $true
            } else {
                Write-Warning "⚠️ SSH: Não foi possível conectar"
                Write-Host "   Verifique chaves SSH e conectividade" -ForegroundColor Yellow
                $testResults["ssh_connectivity"] = $false
            }
        } else {
            Write-Warning "⚠️ SSH: Configuração incompleta"
            $testResults["ssh_connectivity"] = $false
        }
    } catch {
        Write-Warning "⚠️ SSH: Erro no teste - $_"
        $testResults["ssh_connectivity"] = $false
    }
} else {
    Write-Info "5. Teste SSH pulado"
    $testResults["ssh_connectivity"] = $true
}

Write-Host ""

# Teste 6: Dependências Node.js (se Node.js estiver disponível)
if ($testResults["node"]) {
    Write-Info "6. Testando dependências Node.js..."
    
    # Verificar package.json principal
    if (Test-Path "package.json") {
        try {
            $packageJson = Get-Content "package.json" | ConvertFrom-Json
            Write-Host "   ✅ package.json válido" -ForegroundColor Green
            
            # Verificar se node_modules existe
            if (Test-Path "node_modules") {
                Write-Host "   ✅ node_modules existe" -ForegroundColor Green
            } else {
                Write-Warning "   ⚠️ node_modules não existe - execute: npm install"
            }
        } catch {
            Write-Error "   ❌ package.json inválido"
        }
    }
    
    # Verificar package.json do servidor
    if (Test-Path "server/package.json") {
        try {
            $serverPackageJson = Get-Content "server/package.json" | ConvertFrom-Json
            Write-Host "   ✅ server/package.json válido" -ForegroundColor Green
            
            # Verificar se node_modules do servidor existe
            if (Test-Path "server/node_modules") {
                Write-Host "   ✅ server/node_modules existe" -ForegroundColor Green
            } else {
                Write-Warning "   ⚠️ server/node_modules não existe - execute: cd server && npm install"
            }
        } catch {
            Write-Error "   ❌ server/package.json inválido"
        }
    }
    
    $testResults["nodejs_deps"] = $true
} else {
    Write-Info "6. Teste Node.js pulado (Node.js não disponível)"
    $testResults["nodejs_deps"] = $true
}

Write-Host ""

# Resumo dos testes
Write-Info "📊 RESUMO DOS TESTES:"
Write-Host ""

$totalTests = $testResults.Count
$passedTests = ($testResults.Values | Where-Object { $_ -eq $true }).Count
$failedTests = $totalTests - $passedTests

foreach ($test in $testResults.GetEnumerator()) {
    $status = if ($test.Value) { "✅ PASSOU" } else { "❌ FALHOU" }
    $color = if ($test.Value) { "Green" } else { "Red" }
    Write-Host "   $($test.Key): $status" -ForegroundColor $color
}

Write-Host ""

if ($failedTests -eq 0) {
    Write-Success "🎉 TODOS OS TESTES PASSARAM! ($passedTests/$totalTests)"
    Write-Host ""
    Write-Host "✅ Sistema pronto para uso!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 PRÓXIMOS PASSOS:" -ForegroundColor Cyan
    Write-Host "   1. .\start-app.ps1 -Local    # Testar localmente" -ForegroundColor White
    Write-Host "   2. .\start-app.ps1 -Deploy   # Deploy na VPS" -ForegroundColor White
    Write-Host "   3. .\start-app.ps1           # Ambos" -ForegroundColor White
} else {
    Write-Warning "⚠️ ALGUNS TESTES FALHARAM ($failedTests/$totalTests)"
    Write-Host ""
    Write-Host "🔧 AÇÕES RECOMENDADAS:" -ForegroundColor Yellow
    
    if (-not $testResults["files"]) {
        Write-Host "   - Verificar se todos os arquivos estão presentes" -ForegroundColor Yellow
    }
    
    if (-not $testResults["config"]) {
        Write-Host "   - Executar: .\quick-setup.ps1" -ForegroundColor Yellow
    }
    
    if (-not $testResults["git"]) {
        Write-Host "   - Instalar Git: https://git-scm.com/" -ForegroundColor Yellow
    }
    
    if (-not $testResults["node"]) {
        Write-Host "   - Instalar Node.js: https://nodejs.org/" -ForegroundColor Yellow
    }
    
    if (-not $testResults["ssh"]) {
        Write-Host "   - Instalar OpenSSH" -ForegroundColor Yellow
    }
    
    if (-not $testResults["ssh_connectivity"]) {
        Write-Host "   - Configurar chaves SSH para a VPS" -ForegroundColor Yellow
    }
}

Write-Host ""
