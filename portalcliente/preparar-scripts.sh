#!/bin/bash

# Script para tornar todos os scripts executáveis
echo "🔧 Preparando scripts para execução..."

# Tornar scripts executáveis
chmod +x *.sh

echo "✅ Scripts preparados:"
echo "   - deploy.sh"
echo "   - atualizar.sh" 
echo "   - configurar-vps.sh"
echo "   - validar-deploy.sh"
echo "   - preparar-scripts.sh"

echo ""
echo "🎯 Agora você pode executar:"
echo "   bash validar-deploy.sh    # Validar configuração"
echo "   bash configurar-vps.sh    # Configurar VPS (na VPS)"
echo "   bash deploy.sh            # Fazer deploy"
echo "   bash atualizar.sh         # Atualizar sistema"

echo ""
echo "✅ Scripts prontos para uso!"
