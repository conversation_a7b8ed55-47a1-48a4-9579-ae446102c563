const express = require('express');
const { query } = require('../database/connection');
const { logSepultamento, logExumacao } = require('../utils/logger');

const router = express.Router();

// Listar sepultamentos
router.get('/', async (req, res) => {
  try {
    const {
      codigo_cliente,
      codigo_bloco,
      codigo_sub_bloco,
      codigo_estacao,
      produto_id,
      data_inicio,
      data_fim,
      ativo = 'true'
    } = req.query;
    
    let whereClause = 'WHERE s.ativo = $1';
    let params = [ativo === 'true'];
    let paramCount = 1;

    // Se não for admin, filtrar por código do cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      paramCount++;
      whereClause += ` AND s.codigo_cliente = $${paramCount}`;
      params.push(req.user.codigo_cliente);
    } else if (codigo_cliente) {
      paramCount++;
      whereClause += ` AND s.codigo_cliente = $${paramCount}`;
      params.push(codigo_cliente);
    }

    if (codigo_estacao) {
      paramCount++;
      whereClause += ` AND p.codigo_estacao = $${paramCount}`;
      params.push(codigo_estacao);
    }

    if (codigo_bloco) {
      paramCount++;
      whereClause += ` AND s.codigo_bloco = $${paramCount}`;
      params.push(codigo_bloco);
    }

    if (codigo_sub_bloco) {
      paramCount++;
      whereClause += ` AND s.codigo_sub_bloco = $${paramCount}`;
      params.push(codigo_sub_bloco);
    }

    // Filtros para relatórios
    if (produto_id) {
      paramCount++;
      whereClause += ` AND p.id = $${paramCount}`;
      params.push(produto_id);
    }

    if (data_inicio) {
      paramCount++;
      whereClause += ` AND s.data_sepultamento >= $${paramCount}`;
      params.push(data_inicio);
    }

    if (data_fim) {
      paramCount++;
      whereClause += ` AND s.data_sepultamento <= $${paramCount}`;
      params.push(data_fim);
    }

    const result = await query(`
      SELECT
        s.*,
        s.nome_sepultado as nome_falecido,
        s.horario_sepultamento as hora_sepultamento,
        s.data_exumacao IS NOT NULL as exumado,
        c.nome as nome_cliente,
        p.codigo_estacao,
        p.denominacao as produto_denominacao,
        b.denominacao as denominacao_bloco,
        b.nome as bloco_nome,
        sb.nome as sub_bloco_nome
      FROM sepultamentos s
      LEFT JOIN clientes c ON s.codigo_cliente = c.codigo_cliente
      LEFT JOIN produtos p ON s.codigo_cliente = p.codigo_cliente AND s.codigo_estacao = p.codigo_estacao
      LEFT JOIN blocos b ON s.codigo_cliente = b.codigo_cliente AND s.codigo_estacao = b.codigo_estacao AND s.codigo_bloco = b.codigo_bloco
      LEFT JOIN sub_blocos sb ON s.codigo_cliente = sb.codigo_cliente AND s.codigo_estacao = sb.codigo_estacao AND s.codigo_bloco = sb.codigo_bloco AND s.codigo_sub_bloco = sb.codigo_sub_bloco
      ${whereClause}
      ORDER BY s.data_sepultamento DESC
    `, params);

    res.json(result.rows);
  } catch (error) {
    console.error('Erro ao listar sepultamentos:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Listar sepultamentos por estação (código)
router.get('/estacao/:codigoEstacao', async (req, res) => {
  try {
    const { codigoEstacao } = req.params;

    let whereClause = 'WHERE s.codigo_estacao = $1 AND s.ativo = true';
    let params = [codigoEstacao];

    // Se não for admin, filtrar por código do cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      whereClause += ' AND s.codigo_cliente = $2';
      params.push(req.user.codigo_cliente);
    }

    const result = await query(`
      SELECT s.*,
             p.denominacao as produto_denominacao,
             p.codigo_estacao,
             b.denominacao as denominacao_bloco,
             b.codigo_bloco,
             sb.denominacao as sub_bloco_nome,
             sb.codigo_sub_bloco
      FROM sepultamentos s
      LEFT JOIN produtos p ON s.codigo_cliente = p.codigo_cliente AND s.codigo_estacao = p.codigo_estacao
      LEFT JOIN blocos b ON s.codigo_cliente = b.codigo_cliente AND s.codigo_estacao = b.codigo_estacao AND s.codigo_bloco = b.codigo_bloco
      LEFT JOIN sub_blocos sb ON s.codigo_cliente = sb.codigo_cliente AND s.codigo_estacao = sb.codigo_estacao AND s.codigo_bloco = sb.codigo_bloco AND s.codigo_sub_bloco = sb.codigo_sub_bloco
      ${whereClause}
      ORDER BY s.data_sepultamento DESC, s.nome_sepultado ASC
    `, params);

    res.json(result.rows);

  } catch (error) {
    console.error('Erro ao listar sepultamentos por estação:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Buscar sepultamento por ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    let whereClause = 'WHERE s.id = $1';
    let params = [id];

    // Se não for admin, filtrar por código do cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      whereClause += ' AND s.codigo_cliente = $2';
      params.push(req.user.codigo_cliente);
    }

    const result = await query(`
      SELECT 
        s.*,
        g.posicao_x,
        g.posicao_y,
        g.altura_especial,
        c.nome as nome_cliente
      FROM sepultamentos s
      LEFT JOIN gavetas g ON s.gaveta_id = g.id
      LEFT JOIN clientes c ON s.codigo_cliente = c.codigo_cliente
      ${whereClause}
    `, params);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Sepultamento não encontrado' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Erro ao buscar sepultamento:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar novo sepultamento
router.post('/', async (req, res) => {
  try {
    const {
      gaveta_id,
      nome_sepultado,
      data_sepultamento,
      horario_sepultamento,
      codigo_cliente,
      codigo_bloco,
      codigo_sub_bloco,
      numero_gaveta,
      posicao = 1,
      localizacao,
      observacoes
    } = req.body;

    // Validações
    if (!gaveta_id || !nome_sepultado || !codigo_cliente || !codigo_bloco || !codigo_sub_bloco || !numero_gaveta || !data_sepultamento) {
      return res.status(400).json({ error: 'Campos obrigatórios não preenchidos' });
    }

    // Se não for admin, usar o código do cliente do usuário logado
    const clienteCode = req.user.tipo_usuario === 'admin' ? codigo_cliente : req.user.codigo_cliente;

    // Verificar se a gaveta está disponível e buscar codigo_estacao
    const gavetaCheck = await query(`
      SELECT g.disponivel, p.codigo_estacao
      FROM gavetas g
      JOIN sub_blocos sb ON g.sub_bloco_id = sb.id
      JOIN blocos b ON sb.bloco_id = b.id
      JOIN produtos p ON b.produto_id = p.id
      WHERE g.id = $1
    `, [gaveta_id]);

    if (gavetaCheck.rows.length === 0) {
      return res.status(404).json({ error: 'Gaveta não encontrada' });
    }

    if (!gavetaCheck.rows[0].disponivel) {
      return res.status(400).json({ error: 'Gaveta não está disponível' });
    }

    // Obter codigo_estacao da gaveta
    const codigo_estacao = gavetaCheck.rows[0].codigo_estacao;

    // Inserir sepultamento
    const result = await query(`
      INSERT INTO sepultamentos
      (gaveta_id, nome_sepultado, data_sepultamento, horario_sepultamento, codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta, posicao, localizacao, observacoes)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
      RETURNING *
    `, [gaveta_id, nome_sepultado, data_sepultamento, horario_sepultamento, clienteCode, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta, posicao, localizacao, observacoes]);

    // Marcar gaveta como ocupada
    await query(
      'UPDATE gavetas SET disponivel = false WHERE id = $1',
      [gaveta_id]
    );

    const novoSepultamento = result.rows[0];

    // Registrar log e enviar webhook
    await logSepultamento(
      req.user.id,
      'CREATE',
      novoSepultamento,
      req.ip,
      req.get('User-Agent')
    );

    res.status(201).json({
      message: 'Sepultamento cadastrado com sucesso',
      sepultamento: novoSepultamento
    });

  } catch (error) {
    console.error('Erro ao criar sepultamento:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar sepultamento
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      nome_sepultado,
      data_sepultamento,
      horario_sepultamento,
      localizacao,
      observacoes
    } = req.body;

    // Buscar dados anteriores
    let whereClause = 'WHERE id = $1';
    let params = [id];

    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      whereClause += ' AND codigo_cliente = $2';
      params.push(req.user.codigo_cliente);
    }

    const dadosAnteriores = await query(`SELECT * FROM sepultamentos ${whereClause}`, params);

    if (dadosAnteriores.rows.length === 0) {
      return res.status(404).json({ error: 'Sepultamento não encontrado' });
    }

    // Atualizar sepultamento
    const result = await query(`
      UPDATE sepultamentos
      SET nome_sepultado = $1, data_sepultamento = $2, horario_sepultamento = $3, localizacao = $4, observacoes = $5, updated_at = CURRENT_TIMESTAMP
      WHERE id = $6
      RETURNING *
    `, [nome_sepultado, data_sepultamento, horario_sepultamento, localizacao, observacoes, id]);

    const sepultamentoAtualizado = result.rows[0];
    sepultamentoAtualizado.dadosAnteriores = dadosAnteriores.rows[0];

    // Registrar log e enviar webhook
    await logSepultamento(
      req.user.id,
      'EDIT',
      sepultamentoAtualizado,
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      message: 'Sepultamento atualizado com sucesso',
      sepultamento: sepultamentoAtualizado
    });

  } catch (error) {
    console.error('Erro ao atualizar sepultamento:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Exumar sepultamento
router.post('/:id/exumar', async (req, res) => {
  try {
    const { id } = req.params;
    const { data_exumacao, observacoes_exumacao } = req.body;

    if (!data_exumacao) {
      return res.status(400).json({ error: 'Data de exumação é obrigatória' });
    }

    // Buscar dados anteriores
    let whereClause = 'WHERE id = $1';
    let params = [id];

    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      whereClause += ' AND codigo_cliente = $2';
      params.push(req.user.codigo_cliente);
    }

    const dadosAnteriores = await query(`SELECT * FROM sepultamentos ${whereClause}`, params);

    if (dadosAnteriores.rows.length === 0) {
      return res.status(404).json({ error: 'Sepultamento não encontrado' });
    }

    if (dadosAnteriores.rows[0].data_exumacao) {
      return res.status(400).json({ error: 'Sepultamento já foi exumado' });
    }

    // Atualizar sepultamento com data de exumação
    const result = await query(`
      UPDATE sepultamentos 
      SET data_exumacao = $1, observacoes = COALESCE(observacoes, '') || CASE WHEN observacoes IS NOT NULL THEN E'\n' ELSE '' END || $2, updated_at = CURRENT_TIMESTAMP
      WHERE id = $3
      RETURNING *
    `, [data_exumacao, `Exumação: ${observacoes_exumacao || ''}`, id]);

    // Liberar gaveta
    await query(
      'UPDATE gavetas SET disponivel = true WHERE id = $1',
      [dadosAnteriores.rows[0].gaveta_id]
    );

    const sepultamentoExumado = result.rows[0];
    sepultamentoExumado.dadosAnteriores = dadosAnteriores.rows[0];

    // Registrar log e enviar webhook
    await logExumacao(
      req.user.id,
      sepultamentoExumado,
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      message: 'Exumação realizada com sucesso',
      sepultamento: sepultamentoExumado
    });

  } catch (error) {
    console.error('Erro ao exumar sepultamento:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar sepultamento (apenas admin e apenas se exumado)
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Apenas admin pode deletar
    if (req.user.tipo_usuario !== 'admin') {
      return res.status(403).json({ error: 'Apenas administradores podem deletar sepultamentos' });
    }

    // Buscar sepultamento
    const sepultamento = await query(
      'SELECT * FROM sepultamentos WHERE id = $1',
      [id]
    );

    if (sepultamento.rows.length === 0) {
      return res.status(404).json({ error: 'Sepultamento não encontrado' });
    }

    const sepultamentoData = sepultamento.rows[0];

    // Verificar se foi exumado
    if (!sepultamentoData.data_exumacao) {
      return res.status(400).json({ error: 'Apenas sepultamentos exumados podem ser deletados' });
    }

    // Deletar sepultamento
    await query('DELETE FROM sepultamentos WHERE id = $1', [id]);

    // Registrar log
    await logSepultamento(
      req.user.id,
      'DELETE',
      sepultamentoData,
      req.ip,
      req.get('User-Agent')
    );

    res.json({ message: 'Sepultamento deletado com sucesso' });

  } catch (error) {
    console.error('Erro ao deletar sepultamento:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
