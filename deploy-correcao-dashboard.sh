#!/bin/bash

# 🔧 SCRIPT DE DEPLOY - CORREÇÃO DASHBOARD ERROR 500
# Corrige erro 500 no endpoint /api/dashboard/stats

set -e  # Parar em caso de erro

echo "🔧 INICIANDO CORREÇÃO DO DASHBOARD - ERROR 500"
echo "=============================================="

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log colorido
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# PASSO 1: Parar serviços atuais
log_info "Parando serviços atuais..."
docker stack rm portal-evolution || true
log_warning "Aguardando 15 segundos para limpeza..."
sleep 15
log_success "Serviços parados"

# PASSO 2: Limpar imagens antigas
log_info "Limpando imagens antigas..."
docker image rm portal-evolution-backend:latest || true
docker image rm portal-evolution-frontend:latest || true
docker image prune -f
docker system prune -f
log_success "Limpeza concluída"

# PASSO 3: Construir nova imagem do backend com correções
log_info "Construindo nova imagem do backend..."
cd portalcliente
docker build -f docker/Dockerfile.backend -t portal-evolution-backend:latest . --no-cache
log_success "Backend reconstruído com correções"

# PASSO 4: Construir nova imagem do frontend
log_info "Construindo nova imagem do frontend..."
docker build -f Dockerfile -t portal-evolution-frontend:latest . --no-cache
log_success "Frontend reconstruído"

# PASSO 5: Deploy do stack
log_info "Fazendo deploy do stack corrigido..."
docker stack deploy -c docker-compose.prod.yml portal-evolution
log_success "Stack deployado"

# PASSO 6: Aguardar inicialização
log_warning "Aguardando 60 segundos para inicialização..."
sleep 60

# PASSO 7: Verificar status dos serviços
log_info "Verificando status dos serviços..."
docker stack services portal-evolution

# PASSO 8: Testes específicos do dashboard
log_info "Testando correção do dashboard..."

# Testar Health
log_info "1. Testando API Health..."
if curl -s -f https://portal.evo-eden.site/api/health > /dev/null; then
    log_success "API Health OK"
else
    log_warning "API Health não respondeu"
fi

# Testar Login
log_info "2. Testando login..."
TOKEN=$(curl -s -X POST https://portal.evo-eden.site/api/auth/login \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"54321"}' | jq -r '.token' 2>/dev/null || echo "")

if [ ! -z "$TOKEN" ] && [ "$TOKEN" != "null" ]; then
    log_success "Login OK - Token obtido"
    
    # Testar Dashboard sem parâmetros
    log_info "3. Testando dashboard sem parâmetros..."
    if curl -s -H "Authorization: Bearer $TOKEN" https://portal.evo-eden.site/api/dashboard/stats | grep -q "total_sepultamentos"; then
        log_success "Dashboard sem parâmetros OK"
    else
        log_error "Dashboard sem parâmetros falhou"
    fi
    
    # Testar Dashboard com parâmetros (que causava erro 500)
    log_info "4. Testando dashboard com cliente_id (correção do erro 500)..."
    RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" https://portal.evo-eden.site/api/dashboard/stats?cliente_id=17)
    if echo "$RESPONSE" | grep -q "total_sepultamentos"; then
        log_success "Dashboard com cliente_id OK - ERRO 500 CORRIGIDO!"
    else
        log_error "Dashboard com cliente_id ainda falha:"
        echo "$RESPONSE"
    fi
    
else
    log_error "Falha no login - não foi possível testar dashboard"
fi

# PASSO 9: Resumo final
echo ""
echo "🎉 CORREÇÃO DO DASHBOARD CONCLUÍDA!"
echo "=================================="
log_success "Portal Principal: https://portal.evo-eden.site"
log_success "API Dashboard: https://portal.evo-eden.site/api/dashboard/stats"
echo ""
log_info "Correções implementadas:"
echo "  ✅ Validação de entrada melhorada"
echo "  ✅ Tratamento de erro específico"
echo "  ✅ Logs de debug adicionados"
echo "  ✅ Lógica de cliente_id corrigida"
echo ""

# Verificação final dos serviços
log_info "Status final dos serviços:"
docker stack services portal-evolution

echo ""
log_success "Deploy da correção concluído! 🔧"
