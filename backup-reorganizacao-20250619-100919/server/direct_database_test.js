const { Pool } = require('pg');

// Configuração direta do banco de dados
const pool = new Pool({
  user: 'postgres',
  host: '************',
  database: 'dbetens',
  password: 'ab3780bd73ee4e2804d566ce6fd96209',
  port: 5432,
});

async function directDatabaseTest() {
  console.log('🔍 TESTE DIRETO NO BANCO PostgreSQL');
  console.log('=' .repeat(60));
  console.log('Host: ************:5432');
  console.log('Database: dbetens');
  console.log('=' .repeat(60));

  const client = await pool.connect();
  
  try {
    // 1. Verificar estrutura atual das tabelas
    console.log('\n📋 1. VERIFICANDO ESTRUTURA ATUAL DAS TABELAS:');
    
    const tables = ['blocos', 'sub_blocos', 'gavetas', 'numeracoes_gavetas', 'sepultamentos'];
    
    for (const tableName of tables) {
      console.log(`\n🔍 Tabela: ${tableName.toUpperCase()}`);
      
      const result = await client.query(`
        SELECT column_name, data_type, is_nullable 
        FROM information_schema.columns 
        WHERE table_schema = 'public' AND table_name = $1
        ORDER BY ordinal_position
      `, [tableName]);
      
      console.log(`   Total de colunas: ${result.rows.length}`);
      
      // Verificar colunas hierárquicas
      const hierarchicalCols = ['codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco'];
      const foundCols = result.rows.filter(row => hierarchicalCols.includes(row.column_name));
      
      if (foundCols.length > 0) {
        console.log('   ✅ Colunas hierárquicas encontradas:');
        foundCols.forEach(col => {
          console.log(`      - ${col.column_name} (${col.data_type})`);
        });
      } else {
        console.log('   ❌ NENHUMA coluna hierárquica encontrada!');
      }
      
      // Mostrar todas as colunas para debug
      console.log('   📝 Todas as colunas:');
      result.rows.forEach(col => {
        console.log(`      - ${col.column_name} (${col.data_type})`);
      });
    }
    
    // 2. Se as colunas não existem, vamos criá-las AGORA
    console.log('\n🔧 2. ADICIONANDO COLUNAS HIERÁRQUICAS DIRETAMENTE:');
    
    await client.query('BEGIN');
    
    try {
      // Adicionar colunas na tabela blocos
      console.log('   📋 Adicionando colunas em BLOCOS...');
      await client.query('ALTER TABLE blocos ADD COLUMN IF NOT EXISTS codigo_cliente VARCHAR(50)');
      await client.query('ALTER TABLE blocos ADD COLUMN IF NOT EXISTS codigo_estacao VARCHAR(50)');
      await client.query('ALTER TABLE blocos ADD COLUMN IF NOT EXISTS denominacao VARCHAR(255)');
      
      // Adicionar colunas na tabela sub_blocos
      console.log('   📋 Adicionando colunas em SUB_BLOCOS...');
      await client.query('ALTER TABLE sub_blocos ADD COLUMN IF NOT EXISTS codigo_cliente VARCHAR(50)');
      await client.query('ALTER TABLE sub_blocos ADD COLUMN IF NOT EXISTS codigo_estacao VARCHAR(50)');
      await client.query('ALTER TABLE sub_blocos ADD COLUMN IF NOT EXISTS codigo_bloco VARCHAR(50)');
      await client.query('ALTER TABLE sub_blocos ADD COLUMN IF NOT EXISTS denominacao VARCHAR(255)');
      
      // Adicionar colunas na tabela gavetas
      console.log('   📋 Adicionando colunas em GAVETAS...');
      await client.query('ALTER TABLE gavetas ADD COLUMN IF NOT EXISTS codigo_cliente VARCHAR(50)');
      await client.query('ALTER TABLE gavetas ADD COLUMN IF NOT EXISTS codigo_estacao VARCHAR(50)');
      await client.query('ALTER TABLE gavetas ADD COLUMN IF NOT EXISTS codigo_bloco VARCHAR(50)');
      await client.query('ALTER TABLE gavetas ADD COLUMN IF NOT EXISTS codigo_sub_bloco VARCHAR(50)');
      
      // Adicionar colunas na tabela numeracoes_gavetas
      console.log('   📋 Adicionando colunas em NUMERACOES_GAVETAS...');
      await client.query('ALTER TABLE numeracoes_gavetas ADD COLUMN IF NOT EXISTS codigo_cliente VARCHAR(50)');
      await client.query('ALTER TABLE numeracoes_gavetas ADD COLUMN IF NOT EXISTS codigo_estacao VARCHAR(50)');
      await client.query('ALTER TABLE numeracoes_gavetas ADD COLUMN IF NOT EXISTS codigo_bloco VARCHAR(50)');
      await client.query('ALTER TABLE numeracoes_gavetas ADD COLUMN IF NOT EXISTS codigo_sub_bloco VARCHAR(50)');
      
      // Adicionar colunas na tabela logs_auditoria
      console.log('   📋 Adicionando colunas em LOGS_AUDITORIA...');
      await client.query('ALTER TABLE logs_auditoria ADD COLUMN IF NOT EXISTS codigo_cliente VARCHAR(50)');
      await client.query('ALTER TABLE logs_auditoria ADD COLUMN IF NOT EXISTS codigo_estacao VARCHAR(50)');
      await client.query('ALTER TABLE logs_auditoria ADD COLUMN IF NOT EXISTS codigo_bloco VARCHAR(50)');
      await client.query('ALTER TABLE logs_auditoria ADD COLUMN IF NOT EXISTS codigo_sub_bloco VARCHAR(50)');
      await client.query('ALTER TABLE logs_auditoria ADD COLUMN IF NOT EXISTS descricao TEXT');
      
      await client.query('COMMIT');
      console.log('   ✅ Colunas adicionadas com sucesso!');
      
    } catch (error) {
      await client.query('ROLLBACK');
      console.log(`   ❌ Erro ao adicionar colunas: ${error.message}`);
    }
    
    // 3. Verificar novamente se as colunas foram criadas
    console.log('\n🔍 3. VERIFICANDO COLUNAS APÓS ADIÇÃO:');
    
    for (const tableName of tables) {
      const result = await client.query(`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_schema = 'public' AND table_name = $1
        AND column_name IN ('codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco')
        ORDER BY column_name
      `, [tableName]);
      
      console.log(`   📋 ${tableName.toUpperCase()}: ${result.rows.length} colunas hierárquicas`);
      result.rows.forEach(row => {
        console.log(`      ✅ ${row.column_name}`);
      });
    }
    
    // 4. Agora vamos popular as colunas com dados existentes
    console.log('\n📊 4. POPULANDO COLUNAS COM DADOS EXISTENTES:');
    
    await client.query('BEGIN');
    
    try {
      // Popular blocos
      console.log('   📋 Populando BLOCOS...');
      const updateBlocos = await client.query(`
        UPDATE blocos 
        SET codigo_cliente = p.codigo_cliente,
            codigo_estacao = p.codigo_estacao,
            denominacao = COALESCE(blocos.denominacao, blocos.nome)
        FROM produtos p 
        WHERE blocos.produto_id = p.id 
        AND blocos.codigo_cliente IS NULL
      `);
      console.log(`      ✅ ${updateBlocos.rowCount} blocos atualizados`);
      
      // Popular sub_blocos
      console.log('   📋 Populando SUB_BLOCOS...');
      const updateSubBlocos = await client.query(`
        UPDATE sub_blocos 
        SET codigo_cliente = b.codigo_cliente,
            codigo_estacao = b.codigo_estacao,
            codigo_bloco = b.codigo_bloco,
            denominacao = COALESCE(sub_blocos.denominacao, sub_blocos.nome)
        FROM blocos b 
        WHERE sub_blocos.bloco_id = b.id 
        AND sub_blocos.codigo_cliente IS NULL
      `);
      console.log(`      ✅ ${updateSubBlocos.rowCount} sub-blocos atualizados`);
      
      // Popular gavetas
      console.log('   📋 Populando GAVETAS...');
      const updateGavetas = await client.query(`
        UPDATE gavetas 
        SET codigo_cliente = sb.codigo_cliente,
            codigo_estacao = sb.codigo_estacao,
            codigo_bloco = sb.codigo_bloco,
            codigo_sub_bloco = sb.codigo_sub_bloco
        FROM sub_blocos sb 
        WHERE gavetas.sub_bloco_id = sb.id 
        AND gavetas.codigo_cliente IS NULL
      `);
      console.log(`      ✅ ${updateGavetas.rowCount} gavetas atualizadas`);
      
      await client.query('COMMIT');
      console.log('   🎉 Dados populados com sucesso!');
      
    } catch (error) {
      await client.query('ROLLBACK');
      console.log(`   ❌ Erro ao popular dados: ${error.message}`);
    }
    
    // 5. TESTE COMPLETO: Criar dados do zero
    console.log('\n🧪 5. TESTE COMPLETO - CRIANDO DADOS DO ZERO:');
    
    await client.query('BEGIN');
    
    try {
      const testData = {
        codigo_cliente: 'TESTE_REAL_001',
        cnpj: '11.222.333/0001-44',
        nome_fantasia: 'Teste Real Hierárquico',
        codigo_estacao: 'EST_REAL_001',
        denominacao_produto: 'Estação Real de Teste',
        codigo_bloco: 'BL_REAL',
        nome_bloco: 'Bloco Real de Teste',
        codigo_sub_bloco: 'SB_REAL',
        nome_sub_bloco: 'Sub-bloco Real de Teste'
      };
      
      // 5.1. Criar cliente
      console.log(`   👤 Criando cliente: ${testData.codigo_cliente}`);
      await client.query(`
        INSERT INTO clientes (codigo_cliente, cnpj, nome_fantasia, razao_social, nome)
        VALUES ($1, $2, $3, $4, $5)
        ON CONFLICT (codigo_cliente) DO UPDATE SET nome_fantasia = EXCLUDED.nome_fantasia
      `, [testData.codigo_cliente, testData.cnpj, testData.nome_fantasia, testData.nome_fantasia, testData.nome_fantasia]);
      
      // 5.2. Criar produto
      console.log(`   📦 Criando produto: ${testData.codigo_estacao}`);
      await client.query(`
        INSERT INTO produtos (codigo_cliente, codigo_estacao, denominacao, meses_para_exumar, nome, tipo)
        VALUES ($1, $2, $3, 24, $4, 'ETEN')
        ON CONFLICT (codigo_cliente, codigo_estacao) DO UPDATE SET denominacao = EXCLUDED.denominacao
      `, [testData.codigo_cliente, testData.codigo_estacao, testData.denominacao_produto, testData.denominacao_produto]);
      
      // Buscar ID do produto
      const produtoResult = await client.query(`
        SELECT id FROM produtos WHERE codigo_cliente = $1 AND codigo_estacao = $2
      `, [testData.codigo_cliente, testData.codigo_estacao]);
      const produtoId = produtoResult.rows[0].id;
      
      // 5.3. Criar bloco COM colunas hierárquicas
      console.log(`   🧱 Criando bloco: ${testData.codigo_bloco}`);
      await client.query(`
        INSERT INTO blocos (produto_id, codigo_bloco, nome, codigo_cliente, codigo_estacao, denominacao)
        VALUES ($1, $2, $3, $4, $5, $6)
        ON CONFLICT (produto_id, codigo_bloco) DO UPDATE SET
          codigo_cliente = EXCLUDED.codigo_cliente,
          codigo_estacao = EXCLUDED.codigo_estacao,
          denominacao = EXCLUDED.denominacao
      `, [produtoId, testData.codigo_bloco, testData.nome_bloco, testData.codigo_cliente, testData.codigo_estacao, testData.nome_bloco]);
      
      // Buscar ID do bloco
      const blocoResult = await client.query(`
        SELECT id FROM blocos WHERE produto_id = $1 AND codigo_bloco = $2
      `, [produtoId, testData.codigo_bloco]);
      const blocoId = blocoResult.rows[0].id;
      
      // 5.4. Criar sub-bloco COM colunas hierárquicas
      console.log(`   🔲 Criando sub-bloco: ${testData.codigo_sub_bloco}`);
      await client.query(`
        INSERT INTO sub_blocos (bloco_id, codigo_sub_bloco, nome, codigo_cliente, codigo_estacao, codigo_bloco, denominacao)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        ON CONFLICT (bloco_id, codigo_sub_bloco) DO UPDATE SET
          codigo_cliente = EXCLUDED.codigo_cliente,
          codigo_estacao = EXCLUDED.codigo_estacao,
          codigo_bloco = EXCLUDED.codigo_bloco,
          denominacao = EXCLUDED.denominacao
      `, [blocoId, testData.codigo_sub_bloco, testData.nome_sub_bloco, testData.codigo_cliente, testData.codigo_estacao, testData.codigo_bloco, testData.nome_sub_bloco]);
      
      // Buscar ID do sub-bloco
      const subBlocoResult = await client.query(`
        SELECT id FROM sub_blocos WHERE bloco_id = $1 AND codigo_sub_bloco = $2
      `, [blocoId, testData.codigo_sub_bloco]);
      const subBlocoId = subBlocoResult.rows[0].id;
      
      // 5.5. Criar gavetas COM colunas hierárquicas
      console.log(`   📊 Criando gavetas 1-10...`);
      for (let i = 1; i <= 10; i++) {
        await client.query(`
          INSERT INTO gavetas (sub_bloco_id, numero_gaveta, posicao_x, posicao_y, codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco)
          VALUES ($1, $2, 1, 1, $3, $4, $5, $6)
          ON CONFLICT (sub_bloco_id, numero_gaveta) DO UPDATE SET
            codigo_cliente = EXCLUDED.codigo_cliente,
            codigo_estacao = EXCLUDED.codigo_estacao,
            codigo_bloco = EXCLUDED.codigo_bloco,
            codigo_sub_bloco = EXCLUDED.codigo_sub_bloco
        `, [subBlocoId, i, testData.codigo_cliente, testData.codigo_estacao, testData.codigo_bloco, testData.codigo_sub_bloco]);
      }
      
      // 5.6. Criar 5 sepultamentos COM colunas hierárquicas
      console.log(`   ⚰️  Criando 5 sepultamentos...`);
      const sepultados = [
        { nome: 'João Silva Real', gaveta: 1, data: '2024-01-15' },
        { nome: 'Maria Santos Real', gaveta: 2, data: '2024-01-20' },
        { nome: 'Pedro Oliveira Real', gaveta: 3, data: '2024-01-25' },
        { nome: 'Ana Costa Real', gaveta: 4, data: '2024-02-01' },
        { nome: 'Carlos Lima Real', gaveta: 5, data: '2024-02-05' }
      ];
      
      for (const sepultado of sepultados) {
        // Buscar ID da gaveta
        const gavetaResult = await client.query(`
          SELECT id FROM gavetas WHERE sub_bloco_id = $1 AND numero_gaveta = $2
        `, [subBlocoId, sepultado.gaveta]);
        
        if (gavetaResult.rows.length > 0) {
          const gavetaId = gavetaResult.rows[0].id;
          
          await client.query(`
            INSERT INTO sepultamentos (
              gaveta_id, nome_sepultado, codigo_cliente, codigo_bloco, codigo_sub_bloco, 
              numero_gaveta, data_sepultamento, codigo_estacao, horario_sepultamento
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, '14:00')
          `, [
            gavetaId, 
            sepultado.nome, 
            testData.codigo_cliente, 
            testData.codigo_bloco, 
            testData.codigo_sub_bloco, 
            sepultado.gaveta, 
            sepultado.data,
            testData.codigo_estacao
          ]);
          
          // Marcar gaveta como ocupada
          await client.query(`UPDATE gavetas SET disponivel = false WHERE id = $1`, [gavetaId]);
          
          console.log(`      ✅ ${sepultado.nome} - Gaveta ${sepultado.gaveta}`);
        }
      }
      
      await client.query('COMMIT');
      console.log('   🎉 Dados de teste criados com sucesso!');
      
      // 6. Verificar dados usando consultas hierárquicas
      console.log('\n🔍 6. VERIFICANDO COM CONSULTAS HIERÁRQUICAS:');
      
      // Consulta por códigos hierárquicos
      const verificacao = await client.query(`
        SELECT 
          g.codigo_cliente,
          g.codigo_estacao,
          g.codigo_bloco,
          g.codigo_sub_bloco,
          g.numero_gaveta,
          g.disponivel,
          s.nome_sepultado
        FROM gavetas g
        LEFT JOIN sepultamentos s ON g.codigo_cliente = s.codigo_cliente 
                                  AND g.codigo_estacao = s.codigo_estacao 
                                  AND g.codigo_bloco = s.codigo_bloco 
                                  AND g.codigo_sub_bloco = s.codigo_sub_bloco 
                                  AND g.numero_gaveta = s.numero_gaveta
                                  AND s.ativo = true
        WHERE g.codigo_cliente = $1 
        AND g.codigo_estacao = $2
        AND g.codigo_bloco = $3
        AND g.codigo_sub_bloco = $4
        ORDER BY g.numero_gaveta
      `, [testData.codigo_cliente, testData.codigo_estacao, testData.codigo_bloco, testData.codigo_sub_bloco]);
      
      console.log(`   📊 Gavetas encontradas: ${verificacao.rows.length}`);
      verificacao.rows.forEach(row => {
        const status = row.disponivel ? '🟢 Disponível' : '🔴 Ocupada';
        const sepultado = row.nome_sepultado ? ` - ${row.nome_sepultado}` : '';
        console.log(`      ${status} Gaveta ${row.numero_gaveta}${sepultado}`);
        console.log(`         ${row.codigo_cliente}/${row.codigo_estacao}/${row.codigo_bloco}/${row.codigo_sub_bloco}`);
      });
      
    } catch (error) {
      await client.query('ROLLBACK');
      console.log(`   ❌ Erro no teste: ${error.message}`);
      console.log(`   Stack: ${error.stack}`);
    }
    
    console.log('\n🎉 TESTE DIRETO FINALIZADO!');
    console.log('=' .repeat(60));
    console.log('✅ Colunas hierárquicas implementadas e testadas!');
    console.log('✅ Sistema pronto para importação de dados por códigos!');
    
  } catch (error) {
    console.error('❌ Erro na conexão:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Executar teste direto
if (require.main === module) {
  directDatabaseTest().catch(error => {
    console.error('❌ Erro fatal:', error);
    process.exit(1);
  });
}

module.exports = { directDatabaseTest };
