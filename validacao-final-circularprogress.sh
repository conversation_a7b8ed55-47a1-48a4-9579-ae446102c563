#!/bin/bash

# 🧪 VALIDAÇÃO FINAL - CORREÇÃO CIRCULARPROGRESS
# Validação completa das correções implementadas pelos 10 agentes

echo "🧪 VALIDAÇÃO FINAL - CORREÇÃO CIRCULARPROGRESS"
echo "=============================================="

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Função para log colorido
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_test() {
    echo -e "${PURPLE}🧪 $1${NC}"
}

# Contador de testes
TOTAL_TESTS=0
PASSED_TESTS=0

# Função para executar teste
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_pattern="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    log_test "Teste $TOTAL_TESTS: $test_name"
    
    result=$(eval "$test_command" 2>/dev/null)
    
    if echo "$result" | grep -q "$expected_pattern"; then
        log_success "PASSOU: $test_name"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        log_error "FALHOU: $test_name"
        echo "  Resultado: $result"
        return 1
    fi
}

echo ""
log_info "Iniciando validação final das correções..."

# TESTE 1: Verificar se os serviços estão rodando
run_test "Serviços Docker Swarm Ativos" \
    "docker stack services portal-evolution | grep '1/1'" \
    "portal-evolution_portal_backend.*1/1"

# TESTE 2: Testar Health da API
run_test "API Health Check" \
    "curl -s https://portal.evo-eden.site/api/health" \
    '"status":"OK"'

# TESTE 3: Testar Frontend (página principal)
run_test "Frontend Carregando" \
    "curl -s -o /dev/null -w '%{http_code}' https://portal.evo-eden.site" \
    "200"

# TESTE 4: Verificar importações nos arquivos corrigidos
run_test "UsuariosPage.jsx - CircularProgress Importado" \
    "grep -n 'CircularProgress' portalcliente/src/pages/UsuariosPage.jsx" \
    "CircularProgress"

run_test "ClientesPage.jsx - CircularProgress Importado" \
    "grep -n 'CircularProgress' portalcliente/src/pages/ClientesPage.jsx" \
    "CircularProgress"

run_test "LogsPage.jsx - CircularProgress Importado" \
    "grep -n 'CircularProgress' portalcliente/src/pages/LogsPage.jsx" \
    "CircularProgress"

# Obter token para testes autenticados
TOKEN=$(curl -s -X POST https://portal.evo-eden.site/api/auth/login -H "Content-Type: application/json" -d '{"email":"<EMAIL>","password":"adminnbr5410!"}' | jq -r '.token' 2>/dev/null || echo "")

if [ ! -z "$TOKEN" ] && [ "$TOKEN" != "null" ]; then
    log_info "Token admin obtido com sucesso para testes autenticados"
    
    # TESTE 7: API de usuários
    run_test "API de Usuários Funcionando" \
        "curl -s -H 'Authorization: Bearer $TOKEN' https://portal.evo-eden.site/api/usuarios" \
        '"email"'
    
    # TESTE 8: API de clientes
    run_test "API de Clientes Funcionando" \
        "curl -s -H 'Authorization: Bearer $TOKEN' https://portal.evo-eden.site/api/clientes" \
        '"nome_fantasia"'
    
    # TESTE 9: API de logs
    run_test "API de Logs Funcionando" \
        "curl -s -H 'Authorization: Bearer $TOKEN' https://portal.evo-eden.site/api/logs" \
        '"acao"'
    
    # TESTE 10: Verificar se não há erro 404 nas páginas específicas
    run_test "Página Usuários Sem Erro 404" \
        "curl -s -o /dev/null -w '%{http_code}' https://portal.evo-eden.site/dashboard/usuarios" \
        "200"
    
    run_test "Página Clientes Sem Erro 404" \
        "curl -s -o /dev/null -w '%{http_code}' https://portal.evo-eden.site/dashboard/clientes" \
        "200"
    
    run_test "Página Logs Sem Erro 404" \
        "curl -s -o /dev/null -w '%{http_code}' https://portal.evo-eden.site/dashboard/logs" \
        "200"
    
else
    log_error "Não foi possível obter token admin para testes autenticados"
    TOTAL_TESTS=$((TOTAL_TESTS + 6))
fi

# TESTE 13: Verificar logs do backend (sem erros críticos)
run_test "Logs do Backend Sem Erros Críticos" \
    "docker service logs portal-evolution_portal_backend --tail 10 | grep -v 'ERROR\\|FATAL\\|❌'" \
    "portal-evolution_portal_backend"

# TESTE 14: Verificar se frontend foi construído corretamente
run_test "Frontend Build Correto" \
    "docker images | grep portal-evolution-frontend" \
    "portal-evolution-frontend.*latest"

# TESTE 15: Verificar se backend foi construído corretamente
run_test "Backend Build Correto" \
    "docker images | grep portal-evolution-backend" \
    "portal-evolution-backend.*latest"

echo ""
echo "📊 RESUMO DA VALIDAÇÃO FINAL"
echo "============================"
log_info "Total de testes: $TOTAL_TESTS"
log_success "Testes aprovados: $PASSED_TESTS"

if [ $PASSED_TESTS -eq $TOTAL_TESTS ]; then
    echo ""
    log_success "🎉 TODOS OS TESTES PASSARAM!"
    log_success "✅ Erro 'CircularProgress is not defined' CORRIGIDO"
    log_success "✅ Importações Material-UI corrigidas em todos os arquivos"
    log_success "✅ Frontend e Backend reconstruídos com sucesso"
    log_success "✅ Todas as páginas funcionando (Usuários, Clientes, Logs)"
    log_success "✅ APIs funcionando perfeitamente"
    echo ""
    log_info "🌐 Páginas totalmente funcionais:"
    echo "  Portal: https://portal.evo-eden.site"
    echo "  Usuários: https://portal.evo-eden.site/dashboard/usuarios"
    echo "  Clientes: https://portal.evo-eden.site/dashboard/clientes"
    echo "  Logs: https://portal.evo-eden.site/dashboard/logs"
    echo ""
    log_info "🔑 Credenciais de teste:"
    echo "  Admin: <EMAIL> / adminnbr5410!"
    echo ""
    log_success "🤖 ORQUESTRAÇÃO DE 10 AGENTES BEM-SUCEDIDA!"
else
    echo ""
    log_error "❌ ALGUNS TESTES FALHARAM ($((TOTAL_TESTS - PASSED_TESTS)) de $TOTAL_TESTS)"
    log_warning "Verifique os logs dos serviços:"
    echo "  Backend: docker service logs portal-evolution_portal_backend"
    echo "  Frontend: docker service logs portal-evolution_portal_frontend"
fi

echo ""
log_info "Status atual dos serviços:"
docker stack services portal-evolution

echo ""
log_success "🧪 Validação final concluída!"
