const express = require('express');
const { query } = require('../database/connection');
const { logAction } = require('../utils/logger');

const router = express.Router();

// MIDDLEWARE DE DEBUG PARA TODAS AS ROTAS
router.use((req, res, next) => {
  console.log(`🔍 SEPULTAMENTOS ROUTE: ${req.method} ${req.path} - Params: ${JSON.stringify(req.params)} - Query: ${JSON.stringify(req.query)}`);
  next();
});

// ROTA SIMPLES DE TESTE
router.get('/teste-simples', (req, res) => {
  console.log('🚨 ROTA TESTE SIMPLES CHAMADA');
  res.json({
    message: 'Rota teste funcionando!',
    timestamp: new Date().toISOString()
  });
});

// ROTA DE TESTE PARA ESTAÇÃO
router.get('/test-estacao/:codigo_estacao', async (req, res) => {
  console.log(`🚨 ROTA DE TESTE CHAMADA: ${req.params.codigo_estacao}`);
  res.json({
    message: `Teste funcionando para estação ${req.params.codigo_estacao}`,
    timestamp: new Date().toISOString(),
    user: req.user ? req.user.email : 'não autenticado'
  });
});

// NOVA ROTA PARA ESTAÇÃO - REFATORADA
router.get('/por-estacao/:codigo_estacao', async (req, res) => {
  console.log(`🔥 NOVA ROTA ESTAÇÃO: ${req.params.codigo_estacao}`);

  try {
    const { codigo_estacao } = req.params;

    // Consulta direta sem filtros complexos para teste
    const result = await query(`
      SELECT
        id, nome_sepultado, data_sepultamento, codigo_estacao, codigo_bloco, numero_gaveta
      FROM sepultamentos
      WHERE ativo = true
        AND status_exumacao = false
        AND codigo_estacao = $1
      ORDER BY data_sepultamento DESC
      LIMIT 10
    `, [codigo_estacao]);

    console.log(`🔥 NOVA ROTA - Encontrados: ${result.rows.length} registros`);

    res.json({
      success: true,
      count: result.rows.length,
      data: result.rows,
      estacao: codigo_estacao
    });

  } catch (error) {
    console.error('🔥 NOVA ROTA - Erro:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      estacao: req.params.codigo_estacao
    });
  }
});

// Listar sepultamentos
router.get('/', async (req, res) => {
  try {
    const { codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta, estacao_only, status_exumacao, ativo, data_inicio, data_fim, incluir_todos_ate_data_fim } = req.query;
    console.log(`🔍 GET /sepultamentos - Params:`, { codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta, estacao_only, status_exumacao, ativo, data_inicio, data_fim, incluir_todos_ate_data_fim });
    
    let whereClause = 'WHERE s.ativo = true';
    let params = [];
    let paramCount = 0;

    // Se não for admin, filtrar por código do cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      paramCount++;
      whereClause += ` AND s.codigo_cliente = $${paramCount}`;
      params.push(req.user.codigo_cliente);
    } else if (codigo_cliente) {
      paramCount++;
      whereClause += ` AND s.codigo_cliente = $${paramCount}`;
      params.push(codigo_cliente);
    }

    if (codigo_estacao) {
      paramCount++;
      whereClause += ` AND s.codigo_estacao = $${paramCount}`;
      params.push(codigo_estacao);
    }

    if (codigo_bloco) {
      paramCount++;
      whereClause += ` AND s.codigo_bloco = $${paramCount}`;
      params.push(codigo_bloco);
    }

    if (codigo_sub_bloco) {
      paramCount++;
      whereClause += ` AND s.codigo_sub_bloco = $${paramCount}`;
      params.push(codigo_sub_bloco);
    }

    if (numero_gaveta) {
      paramCount++;
      whereClause += ` AND s.numero_gaveta = $${paramCount}`;
      params.push(numero_gaveta);
    }

    // Filtro por status de exumação
    if (status_exumacao !== undefined) {
      whereClause += ` AND s.status_exumacao = ${status_exumacao === 'true'}`;
      console.log(`🔍 Filtro status_exumacao aplicado: ${status_exumacao}`);
    }

    // Filtro por ativo
    if (ativo !== undefined) {
      whereClause += ` AND s.ativo = ${ativo === 'true'}`;
      console.log(`🔍 Filtro ativo aplicado: ${ativo}`);
    }

    // Filtros de data para relatórios
    if (incluir_todos_ate_data_fim === 'true') {
      // Para cálculo de ocupação: incluir todos até a data fim (sem filtro de início)
      if (data_fim) {
        paramCount++;
        whereClause += ` AND s.data_sepultamento <= $${paramCount}`;
        params.push(data_fim);
        console.log(`🔍 Filtro incluir_todos_ate_data_fim aplicado até: ${data_fim}`);
      }
    } else {
      // Para relatórios normais: filtro de período completo
      if (data_inicio) {
        paramCount++;
        whereClause += ` AND s.data_sepultamento >= $${paramCount}`;
        params.push(data_inicio);
        console.log(`🔍 Filtro data_inicio aplicado: ${data_inicio}`);
      }

      if (data_fim) {
        paramCount++;
        whereClause += ` AND s.data_sepultamento <= $${paramCount}`;
        params.push(data_fim);
        console.log(`🔍 Filtro data_fim aplicado: ${data_fim}`);
      }
    }

    // Se for filtro apenas por estação (para funcionalidade do produto)
    if (estacao_only === 'true' && codigo_estacao) {
      whereClause += ' AND s.status_exumacao = false';
      console.log(`🔍 Filtro por estação aplicado: ${codigo_estacao}`);

      // Consulta simplificada para estação
      const result = await query(`
        SELECT
          s.id,
          s.nome_sepultado,
          s.data_sepultamento,
          s.horario_sepultamento,
          s.status_exumacao,
          s.codigo_cliente,
          s.codigo_estacao,
          s.codigo_bloco,
          s.codigo_sub_bloco,
          s.numero_gaveta
        FROM sepultamentos s
        ${whereClause}
        ORDER BY s.data_sepultamento DESC
        LIMIT 50
      `, params);

      console.log(`✅ Encontrados ${result.rows.length} sepultamentos para estação ${codigo_estacao}`);
      return res.json(result.rows);
    }

    const result = await query(`
      SELECT
        s.*,
        p.denominacao as produto_denominacao,
        b.denominacao as denominacao_bloco,
        sb.denominacao as sub_bloco_denominacao,
        c.nome_fantasia as cliente_nome
      FROM sepultamentos s
      LEFT JOIN produtos p ON s.codigo_cliente = p.codigo_cliente AND s.codigo_estacao = p.codigo_estacao
      LEFT JOIN blocos b ON s.codigo_cliente = b.codigo_cliente AND s.codigo_estacao = b.codigo_estacao AND s.codigo_bloco = b.codigo_bloco
      LEFT JOIN sub_blocos sb ON s.codigo_cliente = sb.codigo_cliente AND s.codigo_estacao = sb.codigo_estacao AND s.codigo_bloco = sb.codigo_bloco AND s.codigo_sub_bloco = sb.codigo_sub_bloco
      LEFT JOIN clientes c ON s.codigo_cliente = c.codigo_cliente
      ${whereClause}
      ORDER BY s.data_sepultamento DESC, s.horario_sepultamento DESC
    `, params);

    res.json(result.rows);
  } catch (error) {
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Listar sepultamentos por produto
router.get('/produto/:produtoId', async (req, res) => {
  try {
    const { produtoId } = req.params;

    // Se não for admin, filtrar por código do cliente
    let whereClause = 'WHERE p.id = $1 AND s.ativo = true';
    let params = [produtoId];

    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      whereClause += ' AND s.codigo_cliente = $2';
      params.push(req.user.codigo_cliente);
    }

    const result = await query(`
      SELECT
        s.*,
        p.denominacao as produto_denominacao,
        p.codigo_estacao,
        b.denominacao as denominacao_bloco,
        b.codigo_bloco,
        sb.denominacao as sub_bloco_denominacao,
        sb.codigo_sub_bloco
      FROM sepultamentos s
      JOIN blocos b ON s.codigo_cliente = b.codigo_cliente AND s.codigo_estacao = b.codigo_estacao AND s.codigo_bloco = b.codigo_bloco
      JOIN produtos p ON b.codigo_cliente = p.codigo_cliente AND b.codigo_estacao = p.codigo_estacao AND b.produto_id = p.id
      JOIN sub_blocos sb ON s.codigo_cliente = sb.codigo_cliente AND s.codigo_estacao = sb.codigo_estacao AND s.codigo_bloco = sb.codigo_bloco AND s.codigo_sub_bloco = sb.codigo_sub_bloco
      ${whereClause}
      ORDER BY s.data_sepultamento DESC, s.nome_sepultado ASC
    `, params);

    res.json(result.rows);

  } catch (error) {
    console.error('Erro ao listar sepultamentos por produto:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// ROTA ESPECÍFICA PARA ESTAÇÃO - FUNCIONA INDEPENDENTE DO CACHE
router.get('/estacao/:codigo_estacao', async (req, res) => {
  console.log(`🔍 ROTA ESTAÇÃO CHAMADA: ${req.params.codigo_estacao}`);

  try {
    const { codigo_estacao } = req.params;
    console.log(`🔍 Buscando sepultamentos para estação: ${codigo_estacao}`);
    console.log(`👤 Usuário: ${req.user ? req.user.email : 'não autenticado'}`);

    let whereClause = 'WHERE s.ativo = true AND s.status_exumacao = false AND s.codigo_estacao = $1';
    let params = [codigo_estacao];
    let paramCount = 1;

    // Se não for admin, filtrar por código do cliente
    if (req.user && req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      paramCount++;
      whereClause += ` AND s.codigo_cliente = $${paramCount}`;
      params.push(req.user.codigo_cliente);
      console.log(`🔒 Filtro de cliente aplicado: ${req.user.codigo_cliente}`);
    }

    console.log(`📊 Executando consulta com whereClause: ${whereClause}`);
    console.log(`📊 Parâmetros: ${JSON.stringify(params)}`);

    // Consulta simplificada
    const result = await query(`
      SELECT
        s.id,
        s.nome_sepultado,
        s.data_sepultamento,
        s.horario_sepultamento,
        s.status_exumacao,
        s.codigo_cliente,
        s.codigo_estacao,
        s.codigo_bloco,
        s.codigo_sub_bloco,
        s.numero_gaveta,
        s.observacoes
      FROM sepultamentos s
      ${whereClause}
      ORDER BY s.data_sepultamento DESC, s.horario_sepultamento DESC
      LIMIT 50
    `, params);

    console.log(`✅ Encontrados ${result.rows.length} sepultamentos para estação ${codigo_estacao}`);
    res.json(result.rows);
  } catch (error) {
    console.error('❌ Erro ao listar sepultamentos por estação:', error);
    res.status(500).json({ error: 'Erro interno do servidor', details: error.message });
  }
});

// Buscar sepultamento por ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    let whereClause = 'WHERE s.id = $1';
    let params = [id];

    // Se não for admin, filtrar por código do cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      whereClause += ' AND s.codigo_cliente = $2';
      params.push(req.user.codigo_cliente);
    }

    const result = await query(`
      SELECT
        s.*,
        p.denominacao as produto_denominacao,
        b.denominacao as denominacao_bloco,
        sb.denominacao as sub_bloco_denominacao,
        c.nome_fantasia as cliente_nome
      FROM sepultamentos s
      LEFT JOIN produtos p ON s.codigo_cliente = p.codigo_cliente AND s.codigo_estacao = p.codigo_estacao
      LEFT JOIN blocos b ON s.codigo_cliente = b.codigo_cliente AND s.codigo_estacao = b.codigo_estacao AND s.codigo_bloco = b.codigo_bloco
      LEFT JOIN sub_blocos sb ON s.codigo_cliente = sb.codigo_cliente AND s.codigo_estacao = sb.codigo_estacao AND s.codigo_bloco = sb.codigo_bloco AND s.codigo_sub_bloco = sb.codigo_sub_bloco
      LEFT JOIN clientes c ON s.codigo_cliente = c.codigo_cliente
      ${whereClause}
    `, params);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Sepultamento não encontrado' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar novo sepultamento
router.post('/', async (req, res) => {
  try {
    const { 
      codigo_cliente, 
      codigo_estacao, 
      codigo_bloco, 
      codigo_sub_bloco, 
      numero_gaveta,
      nome_sepultado,
      data_sepultamento,
      horario_sepultamento,
      observacoes
    } = req.body;

    if (!codigo_cliente || !codigo_estacao || !codigo_bloco || !codigo_sub_bloco || !numero_gaveta || !nome_sepultado || !data_sepultamento || !horario_sepultamento) {
      return res.status(400).json({ error: 'Todos os campos obrigatórios devem ser preenchidos' });
    }

    // Se não for admin, verificar se pertence ao cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      if (req.user.codigo_cliente !== codigo_cliente) {
        return res.status(403).json({ error: 'Acesso negado' });
      }
    }

    // Verificar se a gaveta existe e está disponível
    const gaveta = await query(`
      SELECT disponivel FROM gavetas 
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 AND codigo_sub_bloco = $4 AND numero_gaveta = $5 AND ativo = true
    `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta]);

    if (gaveta.rows.length === 0) {
      return res.status(404).json({ error: 'Gaveta não encontrada' });
    }

    if (!gaveta.rows[0].disponivel) {
      return res.status(400).json({ error: 'Gaveta não está disponível' });
    }

    // Criar sepultamento
    const result = await query(`
      INSERT INTO sepultamentos (
        codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta,
        nome_sepultado, data_sepultamento, horario_sepultamento, observacoes, ativo
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING *
    `, [
      codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta,
      nome_sepultado, data_sepultamento, horario_sepultamento, observacoes || '', true
    ]);

    // Marcar gaveta como ocupada
    await query(`
      UPDATE gavetas 
      SET disponivel = false, updated_at = CURRENT_TIMESTAMP
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 AND codigo_sub_bloco = $4 AND numero_gaveta = $5
    `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta]);

    const novoSepultamento = result.rows[0];

    // Registrar log
    await logAction(
      req.user.id,
      'CREATE',
      'sepultamentos',
      novoSepultamento.id,
      null,
      novoSepultamento,
      req.ip,
      req.get('User-Agent')
    );

    res.status(201).json({
      message: 'Sepultamento cadastrado com sucesso',
      sepultamento: novoSepultamento
    });

  } catch (error) {
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar sepultamento
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      nome_sepultado,
      data_sepultamento,
      horario_sepultamento,
      observacoes,
      ativo
    } = req.body;

    // Buscar dados anteriores
    let whereClause = 'WHERE id = $1';
    let params = [id];

    // Se não for admin, filtrar por código do cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      whereClause += ' AND codigo_cliente = $2';
      params.push(req.user.codigo_cliente);
    }

    const dadosAnteriores = await query(`SELECT * FROM sepultamentos ${whereClause}`, params);

    if (dadosAnteriores.rows.length === 0) {
      return res.status(404).json({ error: 'Sepultamento não encontrado' });
    }

    const result = await query(`
      UPDATE sepultamentos
      SET nome_sepultado = $1, data_sepultamento = $2, horario_sepultamento = $3, observacoes = $4, ativo = $5, updated_at = CURRENT_TIMESTAMP
      WHERE id = $6
      RETURNING *
    `, [
      nome_sepultado || dadosAnteriores.rows[0].nome_sepultado,
      data_sepultamento || dadosAnteriores.rows[0].data_sepultamento,
      horario_sepultamento || dadosAnteriores.rows[0].horario_sepultamento,
      observacoes !== undefined ? observacoes : dadosAnteriores.rows[0].observacoes,
      ativo !== undefined ? ativo : dadosAnteriores.rows[0].ativo,
      id
    ]);

    const sepultamentoAtualizado = result.rows[0];

    // Registrar log
    await logAction(
      req.user.id,
      'EDIT',
      'sepultamentos',
      id,
      dadosAnteriores.rows[0],
      sepultamentoAtualizado,
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      message: 'Sepultamento atualizado com sucesso',
      sepultamento: sepultamentoAtualizado
    });

  } catch (error) {
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Exumar sepultamento (admin e cliente)
router.post('/:id/exumar', async (req, res) => {
  try {
    const { id } = req.params;

    console.log(`🔄 EXUMAR sepultamento ID: ${id} - Usuário: ${req.user?.email} (${req.user?.tipo_usuario})`);

    // Buscar sepultamento com filtro por cliente se necessário
    let whereClause = 'WHERE id = $1 AND ativo = true';
    let params = [id];

    // Se não for admin, filtrar por código do cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      whereClause += ' AND codigo_cliente = $2';
      params.push(req.user.codigo_cliente);
      console.log(`🔒 Filtro de cliente aplicado: ${req.user.codigo_cliente}`);
    }

    // Buscar sepultamento
    const sepultamento = await query(`SELECT * FROM sepultamentos ${whereClause}`, params);

    if (sepultamento.rows.length === 0) {
      console.log('❌ Sepultamento não encontrado');
      return res.status(404).json({ error: 'Sepultamento não encontrado' });
    }

    const dadosAnteriores = sepultamento.rows[0];
    console.log(`📋 Sepultamento encontrado: ${dadosAnteriores.nome_sepultado} - Já exumado: ${!!dadosAnteriores.status_exumacao}`);

    // Verificar se já foi exumado
    if (dadosAnteriores.status_exumacao) {
      console.log('❌ Sepultamento já foi exumado');
      return res.status(400).json({ error: 'Sepultamento já foi exumado' });
    }

    // Marcar como exumado
    const result = await query(`
      UPDATE sepultamentos
      SET status_exumacao = true, exumado_em = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `, [id]);

    // Liberar gaveta
    await query(`
      UPDATE gavetas 
      SET disponivel = true, updated_at = CURRENT_TIMESTAMP
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 AND codigo_sub_bloco = $4 AND numero_gaveta = $5
    `, [
      dadosAnteriores.codigo_cliente,
      dadosAnteriores.codigo_estacao,
      dadosAnteriores.codigo_bloco,
      dadosAnteriores.codigo_sub_bloco,
      dadosAnteriores.numero_gaveta
    ]);

    const sepultamentoExumado = result.rows[0];

    // Registrar log
    await logAction(
      req.user.id,
      'EXUMAR',
      'sepultamentos',
      id,
      dadosAnteriores,
      sepultamentoExumado,
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      message: 'Exumação realizada com sucesso',
      sepultamento: sepultamentoExumado
    });

  } catch (error) {
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar sepultamento (apenas admin e apenas se exumado)
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    console.log(`🗑️ DELETE sepultamento ID: ${id} - Usuário: ${req.user?.email} (${req.user?.tipo_usuario})`);

    // Buscar sepultamento primeiro para verificar permissões
    let whereClause = 'WHERE id = $1 AND ativo = true';
    let params = [id];

    // Se não for admin, filtrar por código do cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      whereClause += ' AND codigo_cliente = $2';
      params.push(req.user.codigo_cliente);
      console.log(`🔒 Filtro de cliente aplicado: ${req.user.codigo_cliente}`);
    }

    // Buscar sepultamento
    const sepultamento = await query(
      `SELECT * FROM sepultamentos ${whereClause}`,
      params
    );

    if (sepultamento.rows.length === 0) {
      console.log('❌ Sepultamento não encontrado');
      return res.status(404).json({ error: 'Sepultamento não encontrado' });
    }

    const sepultamentoData = sepultamento.rows[0];
    console.log(`📋 Sepultamento encontrado: ${sepultamentoData.nome_sepultado} - Exumado: ${!!sepultamentoData.status_exumacao}`);

    // Deletar sepultamento (hard delete - remove permanentemente)
    await query('DELETE FROM sepultamentos WHERE id = $1', [id]);

    // Liberar gaveta baseado nos códigos
    console.log(`🔓 Liberando gaveta: cliente=${sepultamentoData.codigo_cliente}, estacao=${sepultamentoData.codigo_estacao}, bloco=${sepultamentoData.codigo_bloco}, sub_bloco=${sepultamentoData.codigo_sub_bloco}, gaveta=${sepultamentoData.numero_gaveta}`);

    const gavetaUpdate = await query(`
      UPDATE gavetas
      SET disponivel = true, updated_at = CURRENT_TIMESTAMP
      WHERE codigo_cliente = $1
        AND codigo_estacao = $2
        AND codigo_bloco = $3
        AND codigo_sub_bloco = $4
        AND numero_gaveta = $5
    `, [
      sepultamentoData.codigo_cliente,
      sepultamentoData.codigo_estacao,
      sepultamentoData.codigo_bloco,
      sepultamentoData.codigo_sub_bloco,
      sepultamentoData.numero_gaveta
    ]);

    console.log(`✅ Gavetas liberadas: ${gavetaUpdate.rowCount}`);

    // Registrar log
    await logAction(
      req.user.id,
      'DELETE',
      'sepultamentos',
      id,
      sepultamentoData,
      { ...sepultamentoData, ativo: false },
      req.ip,
      req.get('User-Agent')
    );

    console.log(`✅ Sepultamento ${id} deletado com sucesso`);
    res.json({ message: 'Sepultamento deletado com sucesso' });

  } catch (error) {
    console.error('❌ Erro ao deletar sepultamento:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
