const { Pool } = require('pg');

// Configuração direta do banco de dados
const pool = new Pool({
  user: 'postgres',
  host: '************',
  database: 'dbetens',
  password: 'ab3780bd73ee4e2804d566ce6fd96209',
  port: 5432,
});

async function checkColumnsDirectly() {
  console.log('🔍 VERIFICAÇÃO DIRETA DAS COLUNAS HIERÁRQUICAS');
  console.log('=' .repeat(70));
  console.log('Banco: dbetens @ ************:5432');
  console.log('=' .repeat(70));

  const client = await pool.connect();
  
  try {
    const tables = ['blocos', 'sub_blocos', 'gavetas', 'numeracoes_gavetas', 'sepultamentos', 'logs_auditoria'];
    const expectedColumns = {
      'blocos': ['codigo_cliente', 'codigo_estacao', 'codigo_bloco'],
      'sub_blocos': ['codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco'],
      'gavetas': ['codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco'],
      'numeracoes_gavetas': ['codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco'],
      'sepultamentos': ['codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco'],
      'logs_auditoria': ['codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco']
    };
    
    let totalTablesComplete = 0;
    let totalHierarchicalColumns = 0;
    
    for (const tableName of tables) {
      console.log(`\n📋 TABELA: ${tableName.toUpperCase()}`);
      console.log('-'.repeat(50));
      
      // Verificar se a tabela existe
      const tableExists = await client.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = $1
        )
      `, [tableName]);
      
      if (!tableExists.rows[0].exists) {
        console.log(`   ❌ TABELA NÃO EXISTE!`);
        continue;
      }
      
      // Buscar todas as colunas da tabela
      const allColumnsResult = await client.query(`
        SELECT 
          column_name,
          data_type,
          is_nullable,
          character_maximum_length
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = $1
        ORDER BY ordinal_position
      `, [tableName]);
      
      console.log(`   📊 Total de colunas: ${allColumnsResult.rows.length}`);
      
      // Verificar colunas hierárquicas específicas
      const expected = expectedColumns[tableName] || [];
      const foundHierarchical = [];
      const missingHierarchical = [];
      
      for (const expectedCol of expected) {
        const found = allColumnsResult.rows.find(row => row.column_name === expectedCol);
        if (found) {
          foundHierarchical.push(found);
        } else {
          missingHierarchical.push(expectedCol);
        }
      }
      
      console.log(`   ✅ Colunas hierárquicas encontradas: ${foundHierarchical.length}/${expected.length}`);
      
      if (foundHierarchical.length > 0) {
        console.log(`   📝 Colunas hierárquicas presentes:`);
        foundHierarchical.forEach(col => {
          console.log(`      ✅ ${col.column_name} (${col.data_type}${col.character_maximum_length ? `(${col.character_maximum_length})` : ''})`);
        });
        totalHierarchicalColumns += foundHierarchical.length;
      }
      
      if (missingHierarchical.length > 0) {
        console.log(`   ❌ Colunas hierárquicas FALTANDO:`);
        missingHierarchical.forEach(col => {
          console.log(`      ❌ ${col}`);
        });
      } else {
        console.log(`   🎉 TABELA COMPLETA - Todas as colunas hierárquicas presentes!`);
        totalTablesComplete++;
      }
      
      // Mostrar algumas colunas existentes para contexto
      console.log(`   📋 Primeiras 10 colunas da tabela:`);
      allColumnsResult.rows.slice(0, 10).forEach((col, index) => {
        const isHierarchical = expected.includes(col.column_name);
        const marker = isHierarchical ? '🔹' : '  ';
        console.log(`      ${marker} ${col.column_name} (${col.data_type})`);
      });
      
      if (allColumnsResult.rows.length > 10) {
        console.log(`      ... e mais ${allColumnsResult.rows.length - 10} colunas`);
      }
    }
    
    // Resumo final
    console.log('\n' + '=' .repeat(70));
    console.log('📊 RESUMO FINAL');
    console.log('=' .repeat(70));
    console.log(`📋 Total de tabelas verificadas: ${tables.length}`);
    console.log(`✅ Tabelas com todas as colunas hierárquicas: ${totalTablesComplete}`);
    console.log(`❌ Tabelas com colunas faltando: ${tables.length - totalTablesComplete}`);
    console.log(`🔹 Total de colunas hierárquicas encontradas: ${totalHierarchicalColumns}`);
    
    if (totalTablesComplete === tables.length) {
      console.log('\n🎉 SUCESSO TOTAL! Todas as tabelas têm as colunas hierárquicas necessárias!');
    } else {
      console.log('\n⚠️  ATENÇÃO! Algumas tabelas ainda não têm todas as colunas hierárquicas.');
      console.log('💡 Execute o comando para adicionar as colunas faltantes.');
    }
    
    // Verificar dados de exemplo em uma tabela que deveria ter colunas hierárquicas
    console.log('\n🔍 VERIFICAÇÃO DE DADOS DE EXEMPLO:');
    console.log('-'.repeat(50));
    
    try {
      const sampleData = await client.query(`
        SELECT 
          codigo_cliente,
          codigo_estacao,
          codigo_bloco,
          codigo_sub_bloco,
          numero_gaveta
        FROM gavetas 
        WHERE codigo_cliente IS NOT NULL
        LIMIT 5
      `);
      
      if (sampleData.rows.length > 0) {
        console.log(`📊 Encontrados ${sampleData.rows.length} registros com dados hierárquicos em gavetas:`);
        sampleData.rows.forEach((row, index) => {
          console.log(`   ${index + 1}. ${row.codigo_cliente}/${row.codigo_estacao}/${row.codigo_bloco}/${row.codigo_sub_bloco}/Gaveta-${row.numero_gaveta}`);
        });
      } else {
        console.log(`⚠️  Nenhum registro encontrado com dados hierárquicos em gavetas.`);
        console.log(`💡 As colunas podem existir mas ainda não foram populadas com dados.`);
      }
    } catch (error) {
      console.log(`❌ Erro ao verificar dados de exemplo: ${error.message}`);
      console.log(`💡 Isso pode indicar que as colunas hierárquicas ainda não existem.`);
    }
    
  } catch (error) {
    console.error('❌ Erro durante verificação:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Executar verificação
if (require.main === module) {
  checkColumnsDirectly().catch(error => {
    console.error('❌ Erro fatal:', error);
    process.exit(1);
  });
}

module.exports = { checkColumnsDirectly };
