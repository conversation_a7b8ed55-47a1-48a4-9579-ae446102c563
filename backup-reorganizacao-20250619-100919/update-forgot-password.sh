#!/bin/bash

# ===================================
# SCRIPT DE ATUALIZAÇÃO - ESQUECI MINHA SENHA
# ===================================
# Atualiza sistema com correções para recuperação de senha
# Autor: Sistema de 12 Agentes IA
# Data: $(date)

set -e  # Parar em caso de erro

echo "🚀 ====================================="
echo "🚀 INICIANDO ATUALIZAÇÃO DO SISTEMA"
echo "🚀 Correção: Esqueci Minha Senha"
echo "🚀 ====================================="

# Verificar se está no diretório correto
if [ ! -f "docker-compose.prod.yml" ]; then
    echo "❌ Erro: Execute este script no diretório portalcliente"
    exit 1
fi

# Função para log com timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# 1. PARAR SERVIÇOS EXISTENTES
log "🛑 Parando serviços existentes..."
docker stack rm portal-evolution 2>/dev/null || true

# Aguardar remoção completa
log "⏳ Aguardando remoção completa dos serviços..."
sleep 30

# 2. REMOVER IMAGENS ANTIGAS
log "🗑️ Removendo imagens antigas..."
docker image rm portal-evolution-backend:1750092855 2>/dev/null || true
docker image rm portal-evolution-frontend:1750092923 2>/dev/null || true
docker image rm portal-evolution-logs:latest 2>/dev/null || true

# Limpar imagens órfãs
log "🧹 Limpando imagens órfãs..."
docker image prune -f

# 3. GERAR TIMESTAMP ÚNICO
TIMESTAMP=$(date +%s)
log "🏷️ Timestamp para novas imagens: $TIMESTAMP"

# 4. CONSTRUIR NOVA IMAGEM BACKEND
log "🔨 Construindo nova imagem do backend..."
docker build -f Dockerfile.backend -t portal-evolution-backend:$TIMESTAMP .

# 5. CONSTRUIR NOVA IMAGEM FRONTEND
log "🔨 Construindo nova imagem do frontend..."
docker build -f Dockerfile.frontend -t portal-evolution-frontend:$TIMESTAMP .

# 6. CONSTRUIR NOVA IMAGEM LOGS
log "🔨 Construindo nova imagem de logs..."
docker build -f Dockerfile.logs -t portal-evolution-logs:$TIMESTAMP .

# 7. ATUALIZAR DOCKER-COMPOSE COM NOVAS IMAGENS
log "📝 Atualizando docker-compose.prod.yml..."
sed -i "s/portal-evolution-backend:[0-9]*/portal-evolution-backend:$TIMESTAMP/g" docker-compose.prod.yml
sed -i "s/portal-evolution-frontend:[0-9]*/portal-evolution-frontend:$TIMESTAMP/g" docker-compose.prod.yml
sed -i "s/portal-evolution-logs:latest/portal-evolution-logs:$TIMESTAMP/g" docker-compose.prod.yml

# 8. VERIFICAR CONFIGURAÇÕES
log "🔍 Verificando configurações de email..."
if grep -q "<EMAIL>" docker-compose.prod.yml; then
    log "✅ Email configurado corretamente"
else
    log "❌ Erro: Email não configurado corretamente"
    exit 1
fi

if grep -q "jgvhevmyjpuucbhp" docker-compose.prod.yml; then
    log "✅ Senha de app configurada corretamente"
else
    log "❌ Erro: Senha de app não configurada corretamente"
    exit 1
fi

# 9. DEPLOY DO STACK ATUALIZADO
log "🚀 Fazendo deploy do stack atualizado..."
docker stack deploy -c docker-compose.prod.yml portal-evolution

# 10. AGUARDAR INICIALIZAÇÃO
log "⏳ Aguardando inicialização dos serviços..."
sleep 60

# 11. VERIFICAR STATUS DOS SERVIÇOS
log "🔍 Verificando status dos serviços..."
docker stack services portal-evolution

# 12. TESTAR CONECTIVIDADE
log "🧪 Testando conectividade dos serviços..."

# Testar backend
for i in {1..10}; do
    if curl -s -f http://localhost:3001/api/health > /dev/null; then
        log "✅ Backend respondendo corretamente"
        break
    else
        log "⏳ Aguardando backend... (tentativa $i/10)"
        sleep 10
    fi
done

# Testar frontend
for i in {1..10}; do
    if curl -s -f http://localhost:80/health > /dev/null; then
        log "✅ Frontend respondendo corretamente"
        break
    else
        log "⏳ Aguardando frontend... (tentativa $i/10)"
        sleep 10
    fi
done

# 13. VERIFICAR LOGS
log "📋 Verificando logs dos serviços..."
echo "--- LOGS BACKEND ---"
docker service logs portal-evolution_portal_backend --tail 20

echo "--- LOGS FRONTEND ---"
docker service logs portal-evolution_portal_frontend --tail 20

# 14. TESTE ESPECÍFICO DE EMAIL
log "📧 Testando configuração de email..."
echo "Para testar o sistema de recuperação de senha:"
echo "1. Acesse: https://portal.evo-eden.site/login"
echo "2. Clique em 'Esqueci minha senha'"
echo "3. Digite um email de usuário cadastrado"
echo "4. Verifique se o email é enviado corretamente"

# 15. LIMPEZA FINAL
log "🧹 Limpeza final..."
docker system prune -f

log "✅ ====================================="
log "✅ ATUALIZAÇÃO CONCLUÍDA COM SUCESSO!"
log "✅ ====================================="
log "✅ Correções aplicadas:"
log "✅ - Email configurado: <EMAIL>"
log "✅ - Senha de app configurada: jgvhevmyjpuucbhp"
log "✅ - URL do sistema corrigida: https://portal.evo-eden.site/login"
log "✅ - Logs de email melhorados"
log "✅ - Imagens Docker atualizadas: $TIMESTAMP"
log "✅ ====================================="

echo ""
echo "🎯 PRÓXIMOS PASSOS:"
echo "1. Teste a funcionalidade 'Esqueci minha senha'"
echo "2. Verifique os logs em caso de problemas"
echo "3. Monitore o sistema por alguns minutos"
echo ""
echo "📞 Em caso de problemas, verifique:"
echo "- docker stack services portal-evolution"
echo "- docker service logs portal-evolution_portal_backend"
echo "- docker service logs portal-evolution_portal_frontend"
