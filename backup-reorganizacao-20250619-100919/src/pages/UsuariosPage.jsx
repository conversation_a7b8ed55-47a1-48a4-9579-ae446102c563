import React, { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Box,
  Typography,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ToggleOn as ToggleOnIcon,
  ToggleOff as ToggleOffIcon,
} from '@mui/icons-material';
import { usuarioService, clienteService, produtoService } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import UsuarioModal from '../components/UsuarioModal';
import { StandardContainer, StandardButton, StandardCard } from '../components/common';

// Todos os styled-components removidos - usando Material-UI

const UsuariosPage = () => {
  const [usuarios, setUsuarios] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedUsuario, setSelectedUsuario] = useState(null);
  const [showUsuarioModal, setShowUsuarioModal] = useState(false);
  const { user } = useAuth();

  useEffect(() => {
    loadUsuarios();
  }, []);

  const loadUsuarios = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await usuarioService.listar();
      setUsuarios(response.data);
    } catch (error) {
      console.error('Erro ao carregar usuários:', error);
      setError('Erro ao carregar usuários');
    } finally {
      setLoading(false);
    }
  };

  const handleAddUser = () => {
    setSelectedUsuario(null);
    setShowUsuarioModal(true);
  };

  const handleEditUser = (usuario) => {
    setSelectedUsuario(usuario);
    setShowUsuarioModal(true);
  };

  const handleToggleStatus = async (usuario) => {
    try {
      await usuarioService.atualizar(usuario.id, {
        ...usuario,
        ativo: !usuario.ativo
      });
      loadUsuarios();
    } catch (error) {
      console.error('Erro ao alterar status:', error);
      alert('Erro ao alterar status do usuário');
    }
  };

  const handleDeleteUser = async (usuario) => {
    const confirmMessage = `⚠️ ATENÇÃO: EXCLUSÃO PERMANENTE ⚠️

Tem certeza que deseja DELETAR o usuário abaixo?

📋 DETALHES DO USUÁRIO:
• Nome: ${usuario.nome}
• Email: ${usuario.email}
• Tipo: ${usuario.tipo_usuario === 'admin' ? 'Administrador' : 'Cliente'}
• Cliente: ${usuario.codigo_cliente || 'N/A'}
• Status: ${usuario.ativo ? 'Ativo' : 'Inativo'}

⚠️ ESTA AÇÃO NÃO PODE SER DESFEITA!

Digite "CONFIRMAR" para prosseguir:`;

    const confirmation = prompt(confirmMessage);

    if (confirmation === 'CONFIRMAR') {
      try {
        await usuarioService.deletar(usuario.id);
        loadUsuarios();
        alert('✅ Usuário deletado com sucesso');
      } catch (error) {
        console.error('Erro ao deletar usuário:', error);
        alert('❌ ' + (error.response?.data?.error || 'Erro ao deletar usuário'));
      }
    } else if (confirmation !== null) {
      alert('❌ Exclusão cancelada. Digite exatamente "CONFIRMAR" para prosseguir.');
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  if (loading) {
    return (
      <StandardContainer title="Gestão de Usuários" subtitle="Gerenciamento de usuários">
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
          <CircularProgress size={40} />
        </Box>
      </StandardContainer>
    );
  }

  if (error) {
    return (
      <StandardContainer title="Gestão de Usuários" subtitle="Gerenciamento de usuários">
        <Alert severity="error" sx={{ textAlign: 'center' }}>
          {error}
        </Alert>
      </StandardContainer>
    );
  }

  return (
    <StandardContainer
      title="Gestão de Usuários"
      subtitle="Gerenciamento de usuários"
      headerAction={
        <StandardButton
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddUser}
        >
          Novo Usuário
        </StandardButton>
      }
    >
      {usuarios.length === 0 ? (
        <StandardCard sx={{ textAlign: 'center', py: 8 }}>
          <Typography variant="h1" sx={{ fontSize: '2rem', mb: 2 }}>👤</Typography>
          <Typography variant="h5" sx={{ mb: 1, color: 'text.primary' }}>
            Nenhum usuário encontrado
          </Typography>
        </StandardCard>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Nome</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Tipo</TableCell>
                <TableCell>Cliente</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Criado em</TableCell>
                <TableCell>Ações</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {usuarios.map((usuario) => (
                <TableRow key={usuario.id} hover>
                  <TableCell>{usuario.nome}</TableCell>
                  <TableCell>{usuario.email}</TableCell>
                  <TableCell>
                    <Chip
                      label={usuario.tipo_usuario === 'admin' ? 'Administrador' : 'Cliente'}
                      color={usuario.tipo_usuario === 'admin' ? 'primary' : 'success'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {usuario.codigo_cliente ? (
                      <Typography variant="body2">
                        {usuario.nome_cliente || usuario.codigo_cliente}
                      </Typography>
                    ) : (
                      <Typography variant="body2" color="text.secondary">-</Typography>
                    )}
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={usuario.ativo ? 'Ativo' : 'Inativo'}
                      color={usuario.ativo ? 'success' : 'error'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>{formatDate(usuario.created_at)}</TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <StandardButton
                        variant="outlined"
                        color="warning"
                        size="small"
                        startIcon={<EditIcon />}
                        onClick={() => handleEditUser(usuario)}
                      >
                        Editar
                      </StandardButton>
                      <StandardButton
                        variant="outlined"
                        color={usuario.ativo ? 'error' : 'success'}
                        size="small"
                        startIcon={usuario.ativo ? <ToggleOffIcon /> : <ToggleOnIcon />}
                        onClick={() => handleToggleStatus(usuario)}
                      >
                        {usuario.ativo ? 'Desativar' : 'Ativar'}
                      </StandardButton>
                      <StandardButton
                        variant="outlined"
                        color="error"
                        size="small"
                        startIcon={<DeleteIcon />}
                        onClick={() => handleDeleteUser(usuario)}
                      >
                        Deletar
                      </StandardButton>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Modal de Usuário */}
      <UsuarioModal
        isOpen={showUsuarioModal}
        onClose={() => setShowUsuarioModal(false)}
        usuario={selectedUsuario}
        onSuccess={loadUsuarios}
      />
    </StandardContainer>
  );
};

export default UsuariosPage;
