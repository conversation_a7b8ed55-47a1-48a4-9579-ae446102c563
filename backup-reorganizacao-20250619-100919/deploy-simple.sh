#!/bin/bash

# ===================================
# PORTAL EVOLUTION - DEPLOY SIMPLES
# ===================================
# Script para deploy direto na VPS
# Execute: bash deploy-simple.sh

set -e

echo "=== PORTAL EVOLUTION - DEPLOY AUTOMATICO ==="
echo "Iniciado em: $(date)"
echo ""

# Navegar para o diretorio do projeto
cd /home/<USER>/portalevo/portalcliente || {
    echo "ERRO: Diretorio /home/<USER>/portalevo/portalcliente nao encontrado!"
    exit 1
}

echo "Diretorio atual: $(pwd)"

# Atualizar codigo
echo "Atualizando codigo via git pull..."
git pull origin master || {
    echo "ERRO: Falha no git pull"
    exit 1
}

echo "Codigo atualizado com sucesso!"

# Verificar se arquivos necessarios existem
echo "Verificando arquivos necessarios..."
if [ ! -f "docker-compose.prod.yml" ]; then
    echo "ERRO: docker-compose.prod.yml nao encontrado!"
    exit 1
fi

echo "Arquivos necessarios encontrados!"

# Tornar scripts executaveis
chmod +x *.sh

# Verificar se Docker esta funcionando
echo "Verificando Docker..."
if ! sudo docker info > /dev/null 2>&1; then
    echo "ERRO: Docker nao esta funcionando"
    exit 1
fi

echo "Docker funcionando corretamente"

# Verificar se Docker Swarm esta ativo
echo "Verificando Docker Swarm..."
if ! sudo docker info | grep -q "Swarm: active"; then
    echo "Docker Swarm nao esta ativo. Inicializando..."
    sudo docker swarm init --advertise-addr $(hostname -I | awk '{print $1}') || true
fi

# Verificar se rede redeinterna existe
if ! sudo docker network ls | grep -q "redeinterna"; then
    echo "Rede 'redeinterna' nao encontrada. Criando..."
    sudo docker network create --driver overlay --attachable redeinterna || true
fi

# Parar stack se existir
echo "Removendo stack anterior (se existir)..."
sudo docker stack rm portal-evolution 2>/dev/null || true

# Aguardar remocao completa
echo "Aguardando remocao completa..."
sleep 15

# Limpar imagens antigas do portal
echo "Limpando imagens antigas do portal..."
sudo docker images | grep "portal-evolution" | awk '{print $3}' | xargs -r sudo docker rmi -f 2>/dev/null || true

# Build das imagens
echo "Construindo imagens..."

# Build backend
echo "Construindo backend..."
if sudo docker build -f Dockerfile.backend -t portal-evolution-backend:latest .; then
    echo "Backend construido com sucesso"
else
    echo "ERRO: Falha ao construir backend"
    exit 1
fi

# Build frontend
echo "Construindo frontend..."
if sudo docker build -f Dockerfile.frontend -t portal-evolution-frontend:latest .; then
    echo "Frontend construido com sucesso"
else
    echo "ERRO: Falha ao construir frontend"
    exit 1
fi

# Deploy da stack
echo "Fazendo deploy da stack..."
if sudo docker stack deploy -c docker-compose.prod.yml portal-evolution; then
    echo "Stack deployada com sucesso"
else
    echo "ERRO: Falha no deploy da stack"
    exit 1
fi

# Aguardar servicos iniciarem
echo "Aguardando servicos iniciarem..."
sleep 45

# Verificar status dos servicos
echo "Verificando status dos servicos..."
sudo docker stack services portal-evolution

echo ""
echo "=== DEPLOY CONCLUIDO ==="
echo "Finalizado em: $(date)"
echo ""
echo "Acesse a aplicacao em: https://portal.evo-eden.site"
echo ""
