const { Pool } = require('pg');

// Configuração direta do banco de dados
const pool = new Pool({
  user: 'postgres',
  host: '************',
  database: 'dbetens',
  password: 'ab3780bd73ee4e2804d566ce6fd96209',
  port: 5432,
});

async function finalTest() {
  console.log('🎯 TESTE FINAL - Colunas Hierárquicas Funcionando');
  console.log('=' .repeat(60));

  const client = await pool.connect();
  
  try {
    // Dados com códigos curtos para evitar limite de caracteres
    const testData = {
      codigo_cliente: 'TEST001',
      cnpj: '12.345.678/0001-99',
      nome_fantasia: 'Teste Final',
      codigo_estacao: 'EST001',
      denominacao_produto: 'Estação Final',
      codigo_bloco: 'BL01',
      nome_bloco: 'Bloco Final',
      codigo_sub_bloco: 'SB01',
      nome_sub_bloco: 'Sub-bloco Final'
    };
    
    console.log(`📋 Dados de teste:`);
    console.log(`   Cliente: ${testData.codigo_cliente}`);
    console.log(`   Produto: ${testData.codigo_estacao}`);
    console.log(`   Bloco: ${testData.codigo_bloco}`);
    console.log(`   Sub-bloco: ${testData.codigo_sub_bloco}`);
    
    await client.query('BEGIN');
    
    // 1. Criar cliente
    console.log('\n👤 1. Criando cliente...');
    await client.query(`
      INSERT INTO clientes (codigo_cliente, cnpj, nome_fantasia, razao_social, nome)
      VALUES ($1, $2, $3, $4, $5)
      ON CONFLICT (codigo_cliente) DO UPDATE SET nome_fantasia = EXCLUDED.nome_fantasia
    `, [testData.codigo_cliente, testData.cnpj, testData.nome_fantasia, testData.nome_fantasia, testData.nome_fantasia]);
    console.log('   ✅ Cliente criado/atualizado');
    
    // 2. Criar produto
    console.log('\n📦 2. Criando produto...');
    await client.query(`
      INSERT INTO produtos (codigo_cliente, codigo_estacao, denominacao, meses_para_exumar, nome, tipo)
      VALUES ($1, $2, $3, 24, $4, 'ETEN')
      ON CONFLICT (codigo_cliente, codigo_estacao) DO UPDATE SET denominacao = EXCLUDED.denominacao
    `, [testData.codigo_cliente, testData.codigo_estacao, testData.denominacao_produto, testData.denominacao_produto]);
    console.log('   ✅ Produto criado/atualizado');
    
    // Buscar ID do produto
    const produtoResult = await client.query(`
      SELECT id FROM produtos WHERE codigo_cliente = $1 AND codigo_estacao = $2
    `, [testData.codigo_cliente, testData.codigo_estacao]);
    const produtoId = produtoResult.rows[0].id;
    console.log(`   📋 Produto ID: ${produtoId}`);
    
    // 3. Criar bloco COM colunas hierárquicas
    console.log('\n🧱 3. Criando bloco com colunas hierárquicas...');
    await client.query(`
      INSERT INTO blocos (produto_id, codigo_bloco, nome, codigo_cliente, codigo_estacao, denominacao)
      VALUES ($1, $2, $3, $4, $5, $6)
      ON CONFLICT (produto_id, codigo_bloco) DO UPDATE SET
        codigo_cliente = EXCLUDED.codigo_cliente,
        codigo_estacao = EXCLUDED.codigo_estacao,
        denominacao = EXCLUDED.denominacao
    `, [produtoId, testData.codigo_bloco, testData.nome_bloco, testData.codigo_cliente, testData.codigo_estacao, testData.nome_bloco]);
    console.log('   ✅ Bloco criado/atualizado com referências hierárquicas');
    
    // Buscar ID do bloco
    const blocoResult = await client.query(`
      SELECT id FROM blocos WHERE produto_id = $1 AND codigo_bloco = $2
    `, [produtoId, testData.codigo_bloco]);
    const blocoId = blocoResult.rows[0].id;
    console.log(`   📋 Bloco ID: ${blocoId}`);
    
    // 4. Criar sub-bloco COM colunas hierárquicas
    console.log('\n🔲 4. Criando sub-bloco com colunas hierárquicas...');
    await client.query(`
      INSERT INTO sub_blocos (bloco_id, codigo_sub_bloco, nome, codigo_cliente, codigo_estacao, codigo_bloco, denominacao)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      ON CONFLICT (bloco_id, codigo_sub_bloco) DO UPDATE SET
        codigo_cliente = EXCLUDED.codigo_cliente,
        codigo_estacao = EXCLUDED.codigo_estacao,
        codigo_bloco = EXCLUDED.codigo_bloco,
        denominacao = EXCLUDED.denominacao
    `, [blocoId, testData.codigo_sub_bloco, testData.nome_sub_bloco, testData.codigo_cliente, testData.codigo_estacao, testData.codigo_bloco, testData.nome_sub_bloco]);
    console.log('   ✅ Sub-bloco criado/atualizado com referências hierárquicas');
    
    // Buscar ID do sub-bloco
    const subBlocoResult = await client.query(`
      SELECT id FROM sub_blocos WHERE bloco_id = $1 AND codigo_sub_bloco = $2
    `, [blocoId, testData.codigo_sub_bloco]);
    const subBlocoId = subBlocoResult.rows[0].id;
    console.log(`   📋 Sub-bloco ID: ${subBlocoId}`);
    
    // 5. Criar gavetas COM colunas hierárquicas
    console.log('\n📊 5. Criando gavetas com colunas hierárquicas...');
    for (let i = 1; i <= 10; i++) {
      await client.query(`
        INSERT INTO gavetas (sub_bloco_id, numero_gaveta, posicao_x, posicao_y, codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco)
        VALUES ($1, $2, 1, 1, $3, $4, $5, $6)
        ON CONFLICT (sub_bloco_id, numero_gaveta) DO UPDATE SET
          codigo_cliente = EXCLUDED.codigo_cliente,
          codigo_estacao = EXCLUDED.codigo_estacao,
          codigo_bloco = EXCLUDED.codigo_bloco,
          codigo_sub_bloco = EXCLUDED.codigo_sub_bloco
      `, [subBlocoId, i, testData.codigo_cliente, testData.codigo_estacao, testData.codigo_bloco, testData.codigo_sub_bloco]);
    }
    console.log('   ✅ 10 gavetas criadas/atualizadas com referências hierárquicas');
    
    // 6. Criar 5 sepultamentos COM colunas hierárquicas
    console.log('\n⚰️  6. Criando 5 sepultamentos com colunas hierárquicas...');
    const sepultados = [
      { nome: 'João Silva Final', gaveta: 1, data: '2024-01-15' },
      { nome: 'Maria Santos Final', gaveta: 2, data: '2024-01-20' },
      { nome: 'Pedro Oliveira Final', gaveta: 3, data: '2024-01-25' },
      { nome: 'Ana Costa Final', gaveta: 4, data: '2024-02-01' },
      { nome: 'Carlos Lima Final', gaveta: 5, data: '2024-02-05' }
    ];
    
    for (const sepultado of sepultados) {
      // Buscar ID da gaveta
      const gavetaResult = await client.query(`
        SELECT id FROM gavetas WHERE sub_bloco_id = $1 AND numero_gaveta = $2
      `, [subBlocoId, sepultado.gaveta]);
      
      if (gavetaResult.rows.length > 0) {
        const gavetaId = gavetaResult.rows[0].id;
        
        // Verificar se já existe sepultamento
        const existingSepultamento = await client.query(`
          SELECT id FROM sepultamentos WHERE gaveta_id = $1 AND ativo = true
        `, [gavetaId]);
        
        if (existingSepultamento.rows.length === 0) {
          await client.query(`
            INSERT INTO sepultamentos (
              gaveta_id, nome_sepultado, codigo_cliente, codigo_bloco, codigo_sub_bloco, 
              numero_gaveta, data_sepultamento, codigo_estacao, horario_sepultamento
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, '14:00')
          `, [
            gavetaId, 
            sepultado.nome, 
            testData.codigo_cliente, 
            testData.codigo_bloco, 
            testData.codigo_sub_bloco, 
            sepultado.gaveta, 
            sepultado.data,
            testData.codigo_estacao
          ]);
          
          // Marcar gaveta como ocupada
          await client.query(`UPDATE gavetas SET disponivel = false WHERE id = $1`, [gavetaId]);
          
          console.log(`   ✅ ${sepultado.nome} - Gaveta ${sepultado.gaveta}`);
        } else {
          console.log(`   ⚠️  Gaveta ${sepultado.gaveta} já ocupada - pulando`);
        }
      }
    }
    
    await client.query('COMMIT');
    console.log('\n🎉 Todos os dados criados/atualizados com sucesso!');
    
    // 7. VERIFICAR DADOS USANDO CONSULTAS HIERÁRQUICAS
    console.log('\n🔍 7. VERIFICANDO COM CONSULTAS POR CÓDIGOS HIERÁRQUICOS:');
    
    // Consulta direta por códigos (SEM JOINs por ID)
    const directQuery = await client.query(`
      SELECT 
        g.codigo_cliente,
        g.codigo_estacao,
        g.codigo_bloco,
        g.codigo_sub_bloco,
        g.numero_gaveta,
        g.disponivel,
        s.nome_sepultado,
        s.data_sepultamento
      FROM gavetas g
      LEFT JOIN sepultamentos s ON g.codigo_cliente = s.codigo_cliente 
                                AND g.codigo_estacao = s.codigo_estacao 
                                AND g.codigo_bloco = s.codigo_bloco 
                                AND g.codigo_sub_bloco = s.codigo_sub_bloco 
                                AND g.numero_gaveta = s.numero_gaveta
                                AND s.ativo = true
      WHERE g.codigo_cliente = $1 
      AND g.codigo_estacao = $2
      AND g.codigo_bloco = $3
      AND g.codigo_sub_bloco = $4
      ORDER BY g.numero_gaveta
    `, [testData.codigo_cliente, testData.codigo_estacao, testData.codigo_bloco, testData.codigo_sub_bloco]);
    
    console.log(`   🔍 Consulta direta por códigos: ${directQuery.rows.length} gavetas encontradas`);
    directQuery.rows.forEach(row => {
      const status = row.disponivel ? '🟢 Disponível' : '🔴 Ocupada';
      const sepultado = row.nome_sepultado ? ` - ${row.nome_sepultado} (${row.data_sepultamento})` : '';
      console.log(`      ${status} Gaveta ${row.numero_gaveta}${sepultado}`);
      console.log(`         📍 ${row.codigo_cliente}/${row.codigo_estacao}/${row.codigo_bloco}/${row.codigo_sub_bloco}`);
    });
    
    // 8. TESTE DE IMPORTAÇÃO USANDO APENAS CÓDIGOS
    console.log('\n📥 8. TESTE DE IMPORTAÇÃO POR CÓDIGOS (Simulando dados externos):');
    
    // Simular dados vindos de sistema externo (apenas códigos)
    const dadosExternos = {
      codigo_cliente: testData.codigo_cliente,
      codigo_estacao: testData.codigo_estacao,
      codigo_bloco: testData.codigo_bloco,
      codigo_sub_bloco: testData.codigo_sub_bloco,
      numero_gaveta: 6,
      nome_sepultado: 'Importado Via Códigos',
      data_sepultamento: '2024-03-01'
    };
    
    // Buscar gaveta usando APENAS códigos hierárquicos
    const gavetaImportacao = await client.query(`
      SELECT id, disponivel FROM gavetas 
      WHERE codigo_cliente = $1 
      AND codigo_estacao = $2
      AND codigo_bloco = $3
      AND codigo_sub_bloco = $4
      AND numero_gaveta = $5
    `, [
      dadosExternos.codigo_cliente,
      dadosExternos.codigo_estacao,
      dadosExternos.codigo_bloco,
      dadosExternos.codigo_sub_bloco,
      dadosExternos.numero_gaveta
    ]);
    
    if (gavetaImportacao.rows.length > 0) {
      const gaveta = gavetaImportacao.rows[0];
      
      if (gaveta.disponivel) {
        await client.query(`
          INSERT INTO sepultamentos (
            gaveta_id, nome_sepultado, codigo_cliente, codigo_estacao, codigo_bloco, 
            codigo_sub_bloco, numero_gaveta, data_sepultamento, horario_sepultamento
          )
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, '16:00')
        `, [
          gaveta.id,
          dadosExternos.nome_sepultado,
          dadosExternos.codigo_cliente,
          dadosExternos.codigo_estacao,
          dadosExternos.codigo_bloco,
          dadosExternos.codigo_sub_bloco,
          dadosExternos.numero_gaveta,
          dadosExternos.data_sepultamento
        ]);
        
        await client.query(`UPDATE gavetas SET disponivel = false WHERE id = $1`, [gaveta.id]);
        
        console.log(`   ✅ SUCESSO! Sepultamento importado usando APENAS códigos hierárquicos:`);
        console.log(`      Nome: ${dadosExternos.nome_sepultado}`);
        console.log(`      Localização: ${dadosExternos.codigo_cliente}/${dadosExternos.codigo_estacao}/${dadosExternos.codigo_bloco}/${dadosExternos.codigo_sub_bloco}/Gaveta-${dadosExternos.numero_gaveta}`);
        console.log(`      Data: ${dadosExternos.data_sepultamento}`);
      } else {
        console.log(`   ⚠️  Gaveta já ocupada - importação não realizada`);
      }
    } else {
      console.log(`   ❌ Gaveta não encontrada com os códigos fornecidos`);
    }
    
    // 9. VERIFICAÇÃO FINAL
    console.log('\n📊 9. VERIFICAÇÃO FINAL - Estatísticas por códigos:');
    
    const stats = await client.query(`
      SELECT 
        codigo_cliente,
        codigo_estacao,
        codigo_bloco,
        codigo_sub_bloco,
        COUNT(*) as total_gavetas,
        COUNT(CASE WHEN disponivel = false THEN 1 END) as gavetas_ocupadas,
        COUNT(CASE WHEN disponivel = true THEN 1 END) as gavetas_disponiveis,
        ROUND(
          (COUNT(CASE WHEN disponivel = false THEN 1 END)::DECIMAL / COUNT(*)) * 100, 2
        ) as taxa_ocupacao
      FROM gavetas
      WHERE codigo_cliente = $1 
      AND codigo_estacao = $2
      AND codigo_bloco = $3
      AND codigo_sub_bloco = $4
      GROUP BY codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco
    `, [testData.codigo_cliente, testData.codigo_estacao, testData.codigo_bloco, testData.codigo_sub_bloco]);
    
    if (stats.rows.length > 0) {
      const stat = stats.rows[0];
      console.log(`   📈 Estatísticas para ${stat.codigo_cliente}/${stat.codigo_estacao}/${stat.codigo_bloco}/${stat.codigo_sub_bloco}:`);
      console.log(`      Total de gavetas: ${stat.total_gavetas}`);
      console.log(`      Gavetas ocupadas: ${stat.gavetas_ocupadas}`);
      console.log(`      Gavetas disponíveis: ${stat.gavetas_disponiveis}`);
      console.log(`      Taxa de ocupação: ${stat.taxa_ocupacao}%`);
    }
    
    console.log('\n🎉 TESTE FINAL CONCLUÍDO COM SUCESSO TOTAL!');
    console.log('=' .repeat(60));
    console.log('✅ TODAS AS COLUNAS HIERÁRQUICAS ESTÃO FUNCIONANDO PERFEITAMENTE!');
    console.log('✅ CONSULTAS POR CÓDIGOS OPERACIONAIS SEM DEPENDÊNCIA DE IDs!');
    console.log('✅ IMPORTAÇÃO DE DADOS POR CÓDIGOS TESTADA E APROVADA!');
    console.log('✅ SISTEMA 100% PRONTO PARA RECEBER DADOS DE OUTRAS FONTES!');
    console.log('🚀 MISSÃO CUMPRIDA - REFERÊNCIAS HIERÁRQUICAS IMPLEMENTADAS!');
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Erro durante o teste:', error);
    console.error('Stack:', error.stack);
  } finally {
    client.release();
    await pool.end();
  }
}

// Executar teste final
if (require.main === module) {
  finalTest().catch(error => {
    console.error('❌ Erro fatal:', error);
    process.exit(1);
  });
}

module.exports = { finalTest };
