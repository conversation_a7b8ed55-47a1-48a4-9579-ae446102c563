#!/bin/bash

# Script de deploy para PRODUÇÃO - Portal Evolution
# Domínio: portal.evo-eden.site
# Ultrathink

echo "🚀 DEPLOY PARA PRODUÇÃO - PORTAL EVOLUTION"
echo "=========================================="
echo ""
echo "🌐 Domínio: portal.evo-eden.site"
echo "📦 Ambiente: PRODUÇÃO"
echo "🔄 Copiando correções do desenvolvimento"
echo ""

# Verificar se estamos no diretório correto
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ Erro: docker-compose.yml não encontrado!"
    echo "Execute este script no diretório raiz do projeto."
    exit 1
fi

echo "🛑 Parando serviços de produção..."
docker stack rm portal-evolution 2>/dev/null || echo "Stack portal-evolution não estava rodando"

echo "⏳ Aguardando remoção completa dos serviços..."
sleep 10

echo "🧹 Limpando imagens antigas de produção..."
docker image rm portal-evolution-backend:latest 2>/dev/null || echo "Imagem backend não encontrada"
docker image rm portal-evolution-frontend:latest 2>/dev/null || echo "Imagem frontend não encontrada"

echo "🔨 Construindo nova imagem do backend para produção..."
docker build -f portalcliente/docker/Dockerfile.backend -t portal-evolution-backend:latest ./portalcliente

if [ $? -ne 0 ]; then
    echo "❌ Erro ao construir imagem do backend!"
    exit 1
fi

echo "🔨 Construindo nova imagem do frontend para produção..."
docker build -f portalcliente/docker/Dockerfile.frontend -t portal-evolution-frontend:latest ./portalcliente

if [ $? -ne 0 ]; then
    echo "❌ Erro ao construir imagem do frontend!"
    exit 1
fi

echo "🚀 Fazendo deploy dos serviços de produção..."
docker stack deploy -c docker-compose.yml portal-evolution

if [ $? -ne 0 ]; then
    echo "❌ Erro ao fazer deploy!"
    exit 1
fi

echo "⏳ Aguardando serviços ficarem prontos..."
sleep 15

echo "📊 Status dos serviços de produção:"
docker service ls | grep portal-evolution

echo ""
echo "🎯 DEPLOY DE PRODUÇÃO CONCLUÍDO!"
echo "🌐 Ambiente de produção: https://portal.evo-eden.site"
echo ""

echo "🧪 TESTE AGORA - PRODUÇÃO ATUALIZADA:"
echo "1. Acesse: https://portal.evo-eden.site"
echo "2. Login: <EMAIL> / adminnbr5410!"
echo ""
echo "📋 TESTE 1 - VISIBILIDADE DE USUÁRIOS:"
echo "3. Vá para a aba 'Usuários'"
echo "4. ✅ Usuário '<EMAIL>' deve aparecer na lista"
echo "5. ✅ Usuários inativos devem ter status 'Inativo' (chip vermelho)"
echo "6. ✅ TODOS os usuários devem estar visíveis (ativos e inativos)"
echo "7. ✅ Usuários ativos devem aparecer primeiro na lista"
echo ""
echo "📋 TESTE 2 - CENTRALIZAÇÃO DOS BOTÕES:"
echo "8. Observe a coluna 'Ações' na lista de usuários"
echo "9. ✅ Botões 'Editar' e 'Deletar' devem estar centralizados"
echo "10. ✅ Botões NÃO devem estar alinhados à direita"
echo "11. ✅ Espaçamento entre botões deve estar correto"
echo ""
echo "📋 TESTE 3 - FUNCIONALIDADES ANTERIORES:"
echo "12. ✅ Emails de boas-vindas funcionando"
echo "13. ✅ Recuperação de senha funcionando"
echo "14. ✅ LOGs limitados aos últimos 30 dias"
echo "15. ✅ Modal de LOGs com informações detalhadas"
echo "16. ✅ Lista de exumações com localização real"
echo ""
echo "📊 CORREÇÕES MIGRADAS PARA PRODUÇÃO:"
echo "- ✅ Usuários inativos agora são visíveis"
echo "- ✅ Botões de ação centralizados"
echo "- ✅ Emails funcionando (boas-vindas e recuperação)"
echo "- ✅ LOGs otimizados (30 dias + detalhes)"
echo "- ✅ Lista de exumações com dados reais"
echo "- ✅ Todas as correções do desenvolvimento"
echo ""
echo "🎉 PRODUÇÃO ATUALIZADA COM SUCESSO!"
echo "Todas as melhorias do desenvolvimento foram aplicadas em produção."
