const { Pool } = require('pg');

// Configuração do banco de dados
const pool = new Pool({
  host: 'postgres_postgres',
  port: 5432,
  database: 'dbetens',
  user: 'postgres',
  password: 'ab3780bd73ee4e2804d566ce6fd96209',
  ssl: false,
});

async function fixAdminPassword() {
  try {
    console.log('🔧 Corrigindo senha do usuário admin...');
    
    // Atualizar senha do admin para texto simples (será verificada no código de login)
    const result = await pool.query(
      "UPDATE usuarios SET senha = 'adminnbr5410!' WHERE email = 'mauriciofil<PERSON>@evolutionbr.tech'"
    );
    
    console.log('✅ Senha do admin atualizada com sucesso!');
    console.log(`📊 Linhas afetadas: ${result.rowCount}`);
    
    // Verificar se a atualização funcionou
    const check = await pool.query(
      "SELECT email, senha FROM usuarios WHERE email = 'ma<PERSON><PERSON><PERSON><PERSON><PERSON>@evolutionbr.tech'"
    );
    
    if (check.rows.length > 0) {
      console.log('📋 Verificação:');
      console.log(`   Email: ${check.rows[0].email}`);
      console.log(`   Senha: ${check.rows[0].senha}`);
    }
    
  } catch (error) {
    console.error('❌ Erro ao corrigir senha:', error);
  } finally {
    await pool.end();
  }
}

fixAdminPassword();
