#!/usr/bin/env node

/**
 * ===================================
 * TESTE AUTOMATIZADO - ESQUECI MINHA SENHA
 * ===================================
 * Testa todo o fluxo de recuperação de senha
 * Autor: Sistema de 12 Agentes IA
 */

const axios = require('axios');
const fs = require('fs');

// Configurações
const BASE_URL = 'https://portal.evo-eden.site';
const API_URL = `${BASE_URL}/api`;

// Cores para console
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    const timestamp = new Date().toISOString();
    console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
}

function logSuccess(message) {
    log(`✅ ${message}`, 'green');
}

function logError(message) {
    log(`❌ ${message}`, 'red');
}

function logInfo(message) {
    log(`ℹ️ ${message}`, 'blue');
}

function logWarning(message) {
    log(`⚠️ ${message}`, 'yellow');
}

// Função para aguardar
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Teste 1: Verificar se o backend está respondendo
async function testBackendHealth() {
    logInfo('Teste 1: Verificando saúde do backend...');
    
    try {
        const response = await axios.get(`${API_URL}/health`, {
            timeout: 10000
        });
        
        if (response.status === 200) {
            logSuccess('Backend está respondendo corretamente');
            return true;
        } else {
            logError(`Backend retornou status ${response.status}`);
            return false;
        }
    } catch (error) {
        logError(`Erro ao conectar com backend: ${error.message}`);
        return false;
    }
}

// Teste 2: Verificar se o frontend está acessível
async function testFrontendAccess() {
    logInfo('Teste 2: Verificando acesso ao frontend...');
    
    try {
        const response = await axios.get(BASE_URL, {
            timeout: 10000,
            validateStatus: () => true // Aceitar qualquer status
        });
        
        if (response.status === 200) {
            logSuccess('Frontend está acessível');
            return true;
        } else {
            logWarning(`Frontend retornou status ${response.status}, mas está acessível`);
            return true;
        }
    } catch (error) {
        logError(`Erro ao acessar frontend: ${error.message}`);
        return false;
    }
}

// Teste 3: Testar rota de recuperação de senha com email inválido
async function testForgotPasswordInvalidEmail() {
    logInfo('Teste 3: Testando recuperação com email inválido...');
    
    try {
        const response = await axios.post(`${API_URL}/auth/forgot-password`, {
            email: '<EMAIL>'
        }, {
            timeout: 10000,
            validateStatus: () => true
        });
        
        if (response.status === 404) {
            logSuccess('Sistema rejeitou corretamente email inexistente');
            return true;
        } else {
            logError(`Esperado status 404, recebido ${response.status}`);
            return false;
        }
    } catch (error) {
        logError(`Erro no teste de email inválido: ${error.message}`);
        return false;
    }
}

// Teste 4: Testar rota de recuperação de senha com email válido
async function testForgotPasswordValidEmail() {
    logInfo('Teste 4: Testando recuperação com email válido...');
    
    // Primeiro, vamos verificar se existe algum usuário no sistema
    const testEmails = [
        'admin',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ];
    
    for (const email of testEmails) {
        try {
            logInfo(`Testando email: ${email}`);
            
            const response = await axios.post(`${API_URL}/auth/forgot-password`, {
                email: email
            }, {
                timeout: 15000,
                validateStatus: () => true
            });
            
            if (response.status === 200 && response.data.success) {
                logSuccess(`Email de recuperação enviado com sucesso para: ${email}`);
                logInfo(`Resposta: ${response.data.message}`);
                return true;
            } else if (response.status === 404) {
                logWarning(`Email ${email} não encontrado no sistema`);
                continue;
            } else {
                logError(`Erro inesperado para ${email}: Status ${response.status}`);
                logError(`Resposta: ${JSON.stringify(response.data)}`);
                continue;
            }
        } catch (error) {
            logError(`Erro ao testar ${email}: ${error.message}`);
            continue;
        }
    }
    
    logError('Nenhum email válido encontrado no sistema');
    return false;
}

// Teste 5: Verificar configurações de email no backend
async function testEmailConfiguration() {
    logInfo('Teste 5: Verificando configurações de email...');
    
    try {
        // Verificar se as variáveis de ambiente estão configuradas
        const response = await axios.get(`${API_URL}/health`, {
            timeout: 10000
        });
        
        if (response.status === 200) {
            logSuccess('Backend está funcionando, configurações de email devem estar ativas');
            
            // Verificar logs do backend para confirmar configuração
            logInfo('Para verificar configuração de email, consulte os logs do backend:');
            logInfo('docker service logs portal-evolution_portal_backend');
            
            return true;
        }
    } catch (error) {
        logError(`Erro ao verificar configurações: ${error.message}`);
        return false;
    }
}

// Teste 6: Verificar estrutura da resposta da API
async function testAPIResponseStructure() {
    logInfo('Teste 6: Verificando estrutura da resposta da API...');
    
    try {
        const response = await axios.post(`${API_URL}/auth/forgot-password`, {
            email: '<EMAIL>'
        }, {
            timeout: 10000,
            validateStatus: () => true
        });
        
        if (response.data && typeof response.data === 'object') {
            logSuccess('API retorna estrutura JSON válida');
            
            if (response.data.error || response.data.message || response.data.success !== undefined) {
                logSuccess('Estrutura da resposta está correta');
                return true;
            } else {
                logWarning('Estrutura da resposta pode estar incompleta');
                return true;
            }
        } else {
            logError('API não retorna JSON válido');
            return false;
        }
    } catch (error) {
        logError(`Erro ao verificar estrutura da API: ${error.message}`);
        return false;
    }
}

// Função principal de teste
async function runAllTests() {
    log('🚀 =====================================', 'cyan');
    log('🚀 INICIANDO TESTES DE FUNCIONALIDADE', 'cyan');
    log('🚀 Sistema: Esqueci Minha Senha', 'cyan');
    log('🚀 =====================================', 'cyan');
    
    const results = [];
    
    // Executar todos os testes
    results.push(await testBackendHealth());
    await sleep(2000);
    
    results.push(await testFrontendAccess());
    await sleep(2000);
    
    results.push(await testAPIResponseStructure());
    await sleep(2000);
    
    results.push(await testForgotPasswordInvalidEmail());
    await sleep(2000);
    
    results.push(await testEmailConfiguration());
    await sleep(2000);
    
    results.push(await testForgotPasswordValidEmail());
    
    // Resumo dos resultados
    log('📊 =====================================', 'cyan');
    log('📊 RESUMO DOS TESTES', 'cyan');
    log('📊 =====================================', 'cyan');
    
    const passed = results.filter(r => r === true).length;
    const total = results.length;
    
    log(`✅ Testes aprovados: ${passed}/${total}`, passed === total ? 'green' : 'yellow');
    
    if (passed === total) {
        logSuccess('🎉 TODOS OS TESTES PASSARAM!');
        logSuccess('Sistema de recuperação de senha está funcionando corretamente');
    } else {
        logWarning(`⚠️ ${total - passed} teste(s) falharam`);
        logInfo('Verifique os logs acima para mais detalhes');
    }
    
    log('🎯 =====================================', 'cyan');
    log('🎯 INSTRUÇÕES PARA TESTE MANUAL', 'cyan');
    log('🎯 =====================================', 'cyan');
    logInfo('1. Acesse: https://portal.evo-eden.site/login');
    logInfo('2. Clique em "Esqueci minha senha"');
    logInfo('3. Digite um email de usuário cadastrado');
    logInfo('4. Verifique se recebe o email com as credenciais');
    logInfo('5. Use as credenciais recebidas para fazer login');
    
    return passed === total;
}

// Executar testes
if (require.main === module) {
    runAllTests()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            logError(`Erro fatal nos testes: ${error.message}`);
            process.exit(1);
        });
}

module.exports = {
    runAllTests,
    testBackendHealth,
    testFrontendAccess,
    testForgotPasswordInvalidEmail,
    testForgotPasswordValidEmail,
    testEmailConfiguration,
    testAPIResponseStructure
};
