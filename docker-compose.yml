# PRODUÇÃO - portal.evo-eden.site
version: '3.8'

services:
  portalevo-backend:
    image: portal-evolution-backend:latest
    networks:
      - redeinterna
    environment:
      - NODE_ENV=production
      - DB_HOST=postgres_postgres
      - DB_PORT=5432
      - DB_NAME=dbetens
      - DB_USER=postgres
      - DB_PASSWORD=ab3780bd73ee4e2804d566ce6fd96209
      - JWT_SECRET=portal_jwt_secret_key_muito_segura_2025
      - PORT=3001
      # Configurações de Email
      - EMAIL_HOST=smtp.gmail.com
      - EMAIL_PORT=587
      - EMAIL_USER=<EMAIL>
      - EMAIL_PASS=jgvhevmyjpuucbhp
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 5
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.portal-api.rule=Host(`portal.evo-eden.site`) && PathPrefix(`/api`)"
        - "traefik.http.routers.portal-api.entrypoints=websecure"
        - "traefik.http.routers.portal-api.tls.certresolver=letsencrypt"
        - "traefik.http.services.portal-api.loadbalancer.server.port=3001"

  portalevo-frontend:
    image: portal-evolution-frontend:latest
    networks:
      - redeinterna
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.portal.rule=Host(`portal.evo-eden.site`)"
        - "traefik.http.routers.portal.entrypoints=websecure"
        - "traefik.http.routers.portal.tls.certresolver=letsencrypt"
        - "traefik.http.services.portal.loadbalancer.server.port=80"

networks:
  redeinterna:
    external: true
