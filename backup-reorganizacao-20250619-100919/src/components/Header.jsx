import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import { useAuth } from '../contexts/AuthContext';

const HeaderContainer = styled.header`
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

const PageTitle = styled.h1`
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
`;

const UserSection = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

const UserInfo = styled.div`
  text-align: right;
`;

const UserName = styled.div`
  font-weight: 500;
  color: #1f2937;
  font-size: 0.875rem;
`;

const UserRole = styled.div`
  font-size: 0.75rem;
  color: #6b7280;
`;

const UserMenu = styled.div`
  position: relative;
`;

const UserButton = styled.button`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1e3a8a 0%, #059669 100%);
  border: none;
  color: white;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
`;

const DropdownMenu = styled.div`
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  min-width: 200px;
  z-index: 1000;
  opacity: ${props => props.show ? 1 : 0};
  visibility: ${props => props.show ? 'visible' : 'hidden'};
  transform: ${props => props.show ? 'translateY(0)' : 'translateY(-10px)'};
  transition: all 0.2s ease;
`;

const DropdownItem = styled.button`
  width: 100%;
  padding: 12px 16px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  color: #374151;
  font-size: 0.875rem;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;

  &:hover {
    background-color: #f9fafb;
  }

  &:first-child {
    border-radius: 8px 8px 0 0;
  }

  &:last-child {
    border-radius: 0 0 8px 8px;
    border-top: 1px solid #e5e7eb;
    color: #dc2626;
  }
`;

const Breadcrumb = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 4px;
`;

const BreadcrumbItem = styled.span`
  &:not(:last-child)::after {
    content: '/';
    margin-left: 8px;
    color: #d1d5db;
  }
`;

const Header = ({ user }) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const { logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const getPageTitle = () => {
    const path = location.pathname;
    const titles = {
      '/dashboard': 'Início',
      '/dashboard/produtos': 'Produtos',
      '/dashboard/cadastros': 'Cadastros',
      '/dashboard/clientes': 'Clientes',
      '/dashboard/usuarios': 'Usuários',
      '/dashboard/logs': 'Logs de Auditoria',
    };
    return titles[path] || 'Dashboard';
  };

  const getBreadcrumb = () => {
    const path = location.pathname;
    const segments = path.split('/').filter(Boolean);
    
    if (segments.length <= 1) return null;
    
    return segments.map((segment, index) => {
      const isLast = index === segments.length - 1;
      const title = segment === 'dashboard' ? 'Dashboard' : 
                   segment.charAt(0).toUpperCase() + segment.slice(1);
      
      return (
        <BreadcrumbItem key={segment}>
          {title}
        </BreadcrumbItem>
      );
    });
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
    }
  };

  const getUserInitials = () => {
    if (!user?.nome) return 'U';
    return user.nome
      .split(' ')
      .map(name => name.charAt(0))
      .slice(0, 2)
      .join('')
      .toUpperCase();
  };

  return (
    <HeaderContainer>
      <div>
        {getBreadcrumb() && (
          <Breadcrumb>
            {getBreadcrumb()}
          </Breadcrumb>
        )}
        <PageTitle>{getPageTitle()}</PageTitle>
      </div>

      <UserSection>
        <UserInfo>
          <UserName>{user?.nome}</UserName>
          <UserRole>
            {user?.tipo_usuario === 'admin' ? 'Administrador' : 'Cliente'}
            {user?.codigo_cliente && ` • ${user.codigo_cliente}`}
          </UserRole>
        </UserInfo>

        <UserMenu>
          <UserButton 
            onClick={() => setShowDropdown(!showDropdown)}
            onBlur={() => setTimeout(() => setShowDropdown(false), 150)}
          >
            {getUserInitials()}
          </UserButton>

          <DropdownMenu show={showDropdown}>
            <DropdownItem onClick={() => {
              setShowDropdown(false);
              // Aqui você pode adicionar navegação para perfil
            }}>
              👤 Meu Perfil
            </DropdownItem>
            <DropdownItem onClick={() => {
              setShowDropdown(false);
              // Aqui você pode adicionar navegação para configurações
            }}>
              ⚙️ Configurações
            </DropdownItem>
            <DropdownItem onClick={handleLogout}>
              🚪 Sair
            </DropdownItem>
          </DropdownMenu>
        </UserMenu>
      </UserSection>
    </HeaderContainer>
  );
};

export default Header;
