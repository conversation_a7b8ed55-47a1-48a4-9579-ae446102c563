const axios = require('axios');

async function testDashboardComplete() {
  try {
    console.log('🔄 Testando todas as APIs do dashboard...');
    
    // Fazer login
    console.log('1. Fazendo login...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: 'admin',
      senha: 'adminnbr5410!'
    });
    
    const token = loginResponse.data.token;
    console.log('✅ Login realizado com sucesso');
    
    // Configurar headers
    const headers = { Authorization: `Bearer ${token}` };
    
    // Testar todas as APIs
    const apis = [
      { name: 'Stats', url: '/api/dashboard/stats' },
      { name: 'Próximas Exumações', url: '/api/dashboard/proximas-exumacoes' },
      { name: 'Atividades Recentes', url: '/api/dashboard/atividades-recentes' },
      { name: '<PERSON>al<PERSON> Sepultamentos', url: '/api/dashboard/sepultamentos-details' },
      { name: 'Detalhes Gavetas Ocupadas', url: '/api/dashboard/gavetas-ocupadas-details' },
      { name: 'Detalhes Gavetas Disponíveis', url: '/api/dashboard/gavetas-disponiveis-details' },
      { name: 'Detalhes Exumações', url: '/api/dashboard/exumacoes-details' }
    ];
    
    for (const api of apis) {
      try {
        console.log(`\n2. Testando ${api.name}...`);
        const response = await axios.get(`http://localhost:3001${api.url}`, { headers });
        console.log(`✅ ${api.name}: ${response.data.length || 'OK'} registros`);
        
        // Mostrar uma amostra dos dados se for array
        if (Array.isArray(response.data) && response.data.length > 0) {
          console.log(`   Exemplo:`, JSON.stringify(response.data[0], null, 2));
        } else if (typeof response.data === 'object') {
          console.log(`   Dados:`, JSON.stringify(response.data, null, 2));
        }
      } catch (error) {
        console.error(`❌ Erro em ${api.name}:`, error.response?.data || error.message);
      }
    }
    
    console.log('\n🎉 Teste completo finalizado!');
    
  } catch (error) {
    console.error('❌ Erro no teste:', error.response?.data || error.message);
  }
}

testDashboardComplete();
