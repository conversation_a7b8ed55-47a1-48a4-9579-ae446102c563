# Script para parar todos os serviços do Portal Evolution
Write-Host "=== PARANDO PORTAL EVOLUTION ===" -ForegroundColor Red
Write-Host ""

# Função para matar processos por porta
function Stop-ProcessByPort {
    param($Port, $ServiceName)
    
    Write-Host "Parando $ServiceName (porta $Port)..." -ForegroundColor Yellow
    
    try {
        # Encontrar processo usando a porta
        $netstat = netstat -ano | Select-String ":$Port "
        
        if ($netstat) {
            foreach ($line in $netstat) {
                $parts = $line.ToString().Split(' ', [StringSplitOptions]::RemoveEmptyEntries)
                $pid = $parts[-1]
                
                if ($pid -match '^\d+$') {
                    try {
                        $process = Get-Process -Id $pid -ErrorAction SilentlyContinue
                        if ($process) {
                            Write-Host "   Matando processo: $($process.ProcessName) (PID: $pid)" -ForegroundColor Gray
                            Stop-Process -Id $pid -Force
                            Write-Host "   ✅ $ServiceName parado" -ForegroundColor Green
                        }
                    } catch {
                        Write-Host "   ⚠️ Não foi possível parar o processo $pid" -ForegroundColor Yellow
                    }
                }
            }
        } else {
            Write-Host "   ℹ️ $ServiceName não está rodando na porta $Port" -ForegroundColor Gray
        }
    } catch {
        Write-Host "   ❌ Erro ao parar $ServiceName" -ForegroundColor Red
    }
}

# Parar Frontend (porta 5173 e 5174)
Stop-ProcessByPort 5173 "Frontend (5173)"
Stop-ProcessByPort 5174 "Frontend (5174)"

# Parar Backend (porta 3001)
Stop-ProcessByPort 3001 "Backend"

Write-Host ""

# Matar processos Node.js relacionados ao projeto
Write-Host "Verificando processos Node.js..." -ForegroundColor Yellow
try {
    $nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue | Where-Object {
        $_.Path -like "*portal-evolution*" -or 
        $_.CommandLine -like "*portal-evolution*" -or
        $_.CommandLine -like "*index.js*" -or
        $_.CommandLine -like "*vite*"
    }
    
    if ($nodeProcesses) {
        foreach ($process in $nodeProcesses) {
            Write-Host "   Matando processo Node.js: PID $($process.Id)" -ForegroundColor Gray
            Stop-Process -Id $process.Id -Force
        }
        Write-Host "   ✅ Processos Node.js relacionados foram parados" -ForegroundColor Green
    } else {
        Write-Host "   ℹ️ Nenhum processo Node.js relacionado encontrado" -ForegroundColor Gray
    }
} catch {
    Write-Host "   ⚠️ Erro ao verificar processos Node.js" -ForegroundColor Yellow
}

Write-Host ""

# Verificar se as portas estão livres
Write-Host "Verificando se as portas estão livres..." -ForegroundColor Yellow

function Test-Port {
    param($Port)
    try {
        $connection = Test-NetConnection -ComputerName localhost -Port $Port -WarningAction SilentlyContinue
        return $connection.TcpTestSucceeded
    } catch {
        return $false
    }
}

$ports = @(
    @{Port=3001; Name="Backend"},
    @{Port=5173; Name="Frontend"},
    @{Port=5174; Name="Frontend (alternativo)"}
)

foreach ($portInfo in $ports) {
    $inUse = Test-Port $portInfo.Port
    if ($inUse) {
        Write-Host "   ⚠️ Porta $($portInfo.Port) ($($portInfo.Name)) ainda está em uso" -ForegroundColor Yellow
    } else {
        Write-Host "   ✅ Porta $($portInfo.Port) ($($portInfo.Name)) está livre" -ForegroundColor Green
    }
}

Write-Host ""
Write-Host "🛑 PORTAL EVOLUTION PARADO!" -ForegroundColor Red
Write-Host ""
Write-Host "Para reiniciar o sistema, execute:" -ForegroundColor Gray
Write-Host "   .\start-portal.ps1" -ForegroundColor Cyan
Write-Host ""
