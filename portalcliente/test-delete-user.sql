-- ===================================
-- TESTE DE EXCLUSÃO DE USUÁRIO
-- ===================================

-- 1. <PERSON><PERSON>r usuário de teste
INSERT INTO usuarios (email, senha, tipo_usuario, nome, ativo) 
VALUES ('<EMAIL>', '$2a$10$dummy.hash.for.test', 'cliente', 'Usuário Teste Exclusão', true);

-- 2. Obter ID do usuário criado
SELECT id, email, nome FROM usuarios WHERE email = '<EMAIL>';

-- 3. <PERSON><PERSON><PERSON> logs para o usuário (substitua 999 pelo ID real)
INSERT INTO logs_auditoria (usuario_id, acao, tabela_afetada, descricao) 
VALUES 
(999, 'CREATE', 'usuarios', 'Log de teste 1'),
(999, 'UPDATE', 'usuarios', 'Log de teste 2'),
(999, 'LOGIN', 'auth', 'Log de teste 3');

-- 4. Verificar logs antes da exclusão
SELECT 'LOGS ANTES DA EXCLUSÃO:' as status;
SELECT usuario_id, acao, descricao FROM logs_auditoria WHERE usuario_id = 999;

-- 5. Deletar o usuário
DELETE FROM usuarios WHERE id = 999;

-- 6. Verificar logs após a exclusão
SELECT 'LOGS APÓS A EXCLUSÃO:' as status;
SELECT usuario_id, acao, descricao FROM logs_auditoria WHERE usuario_id IS NULL ORDER BY id DESC LIMIT 3;
