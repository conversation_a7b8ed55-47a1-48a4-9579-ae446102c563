#!/bin/bash

# 🔧 SCRIPT DE DEPLOY - CORREÇÃO CADASTRO DE PRODUTOS
# Corrige erro "Container is not defined" na tela de Cadastro de Produtos

set -e  # Parar em caso de erro

echo "🔧 INICIANDO CORREÇÃO DO CADASTRO DE PRODUTOS"
echo "============================================"

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log colorido
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# PASSO 1: Parar serviços atuais
log_info "Parando serviços atuais..."
docker stack rm portal-evolution || true
log_warning "Aguardando 15 segundos para limpeza..."
sleep 15
log_success "Serviços parados"

# PASSO 2: Limpar imagens antigas
log_info "Limpando imagens antigas..."
docker image rm portal-evolution-backend:latest || true
docker image rm portal-evolution-frontend:latest || true
docker image prune -f
docker system prune -f
log_success "Limpeza concluída"

# PASSO 3: Construir nova imagem do backend
log_info "Construindo nova imagem do backend..."
cd portalcliente
docker build -f docker/Dockerfile.backend -t portal-evolution-backend:latest . --no-cache
log_success "Backend reconstruído"

# PASSO 4: Construir nova imagem do frontend com correções
log_info "Construindo nova imagem do frontend com correções..."
docker build -f Dockerfile -t portal-evolution-frontend:latest . --no-cache
log_success "Frontend reconstruído com correções"

# PASSO 5: Deploy do stack
log_info "Fazendo deploy do stack corrigido..."
docker stack deploy -c docker-compose.prod.yml portal-evolution
log_success "Stack deployado"

# PASSO 6: Aguardar inicialização
log_warning "Aguardando 60 segundos para inicialização..."
sleep 60

# PASSO 7: Verificar status dos serviços
log_info "Verificando status dos serviços..."
docker stack services portal-evolution

# PASSO 8: Testes específicos do cadastro de produtos
log_info "Testando correção do cadastro de produtos..."

# Testar Health
log_info "1. Testando API Health..."
if curl -s -f https://portal.evo-eden.site/api/health > /dev/null; then
    log_success "API Health OK"
else
    log_warning "API Health não respondeu"
fi

# Testar Login Admin
log_info "2. Testando login admin..."
TOKEN=$(curl -s -X POST https://portal.evo-eden.site/api/auth/login \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"adminnbr5410!"}' | jq -r '.token' 2>/dev/null || echo "")

if [ ! -z "$TOKEN" ] && [ "$TOKEN" != "null" ]; then
    log_success "Login admin OK - Token obtido"
    
    # Testar API de produtos
    log_info "3. Testando API de produtos..."
    if curl -s -H "Authorization: Bearer $TOKEN" https://portal.evo-eden.site/api/produtos | grep -q "denominacao"; then
        log_success "API de produtos OK"
    else
        log_error "API de produtos falhou"
    fi
    
    # Testar API de clientes
    log_info "4. Testando API de clientes..."
    if curl -s -H "Authorization: Bearer $TOKEN" https://portal.evo-eden.site/api/clientes | grep -q "nome_fantasia"; then
        log_success "API de clientes OK"
    else
        log_error "API de clientes falhou"
    fi
    
else
    log_error "Falha no login admin - não foi possível testar APIs"
fi

# PASSO 9: Testar frontend
log_info "5. Testando frontend..."
if curl -s -o /dev/null -w '%{http_code}' https://portal.evo-eden.site | grep -q "200"; then
    log_success "Frontend carregando OK"
else
    log_error "Frontend não está carregando"
fi

# PASSO 10: Resumo final
echo ""
echo "🎉 CORREÇÃO DO CADASTRO DE PRODUTOS CONCLUÍDA!"
echo "=============================================="
log_success "Portal Principal: https://portal.evo-eden.site"
log_success "Cadastro de Produtos: https://portal.evo-eden.site/dashboard/cadastros-produtos"
echo ""
log_info "Correções implementadas:"
echo "  ✅ Importação do Container do Material-UI corrigida"
echo "  ✅ Importações de componentes de tabela adicionadas"
echo "  ✅ Frontend reconstruído com todas as correções"
echo ""

# Verificação final dos serviços
log_info "Status final dos serviços:"
docker stack services portal-evolution

echo ""
log_success "Deploy da correção concluído! 🔧"
echo "Acesse https://portal.evo-eden.site e teste o Cadastro de Produtos"
