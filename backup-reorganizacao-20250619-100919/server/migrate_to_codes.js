const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
require('dotenv').config();

// Configuração do banco de dados
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'portal_evolution',
  password: process.env.DB_PASSWORD || 'postgres',
  port: process.env.DB_PORT || 5432,
});

async function executeMigration() {
  const client = await pool.connect();
  
  try {
    console.log('🔄 Iniciando migração para estrutura por códigos...');
    
    // Ler o arquivo de migração
    const migrationPath = path.join(__dirname, 'database', 'migration_to_codes.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('📄 Arquivo de migração carregado');
    
    // Executar a migração
    console.log('⚠️  ATENÇÃO: Esta operação irá reestruturar completamente o banco de dados!');
    console.log('⚠️  Certifique-se de ter feito backup dos dados antes de continuar.');
    
    // Aguardar confirmação (em produção, remover esta parte)
    if (process.env.NODE_ENV !== 'production') {
      console.log('🔄 Executando migração em ambiente de desenvolvimento...');
    }
    
    // Executar a migração
    await client.query(migrationSQL);
    
    console.log('✅ Migração executada com sucesso!');
    
    // Verificar se a migração foi bem-sucedida
    const verificacao = await client.query(`
      SELECT 
        'produtos' as tabela, COUNT(*) as registros FROM produtos
      UNION ALL
      SELECT 
        'blocos' as tabela, COUNT(*) as registros FROM blocos
      UNION ALL
      SELECT 
        'sub_blocos' as tabela, COUNT(*) as registros FROM sub_blocos
      UNION ALL
      SELECT 
        'gavetas' as tabela, COUNT(*) as registros FROM gavetas
      UNION ALL
      SELECT 
        'sepultamentos' as tabela, COUNT(*) as registros FROM sepultamentos
    `);
    
    console.log('\n📊 Verificação pós-migração:');
    verificacao.rows.forEach(row => {
      console.log(`   ${row.tabela}: ${row.registros} registros`);
    });
    
    // Verificar estrutura das tabelas
    const estrutura = await client.query(`
      SELECT 
        table_name,
        column_name,
        data_type,
        is_nullable
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name IN ('produtos', 'blocos', 'sub_blocos', 'gavetas', 'sepultamentos')
      ORDER BY table_name, ordinal_position
    `);
    
    console.log('\n🏗️  Estrutura das tabelas após migração:');
    let currentTable = '';
    estrutura.rows.forEach(row => {
      if (row.table_name !== currentTable) {
        currentTable = row.table_name;
        console.log(`\n   📋 ${row.table_name.toUpperCase()}:`);
      }
      console.log(`      - ${row.column_name} (${row.data_type}) ${row.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'}`);
    });
    
    console.log('\n🎉 Migração concluída com sucesso!');
    console.log('🔧 O sistema agora usa referências por códigos ao invés de IDs.');
    console.log('📝 Estrutura hierárquica implementada:');
    console.log('   - Cliente: codigo_cliente');
    console.log('   - Produto: codigo_cliente + codigo_estacao');
    console.log('   - Bloco: codigo_cliente + codigo_estacao + codigo_bloco');
    console.log('   - Sub-bloco: codigo_cliente + codigo_estacao + codigo_bloco + codigo_sub_bloco');
    console.log('   - Gavetas: codigo_cliente + codigo_estacao + codigo_bloco + codigo_sub_bloco + numero_gaveta');
    
  } catch (error) {
    console.error('❌ Erro durante a migração:', error);
    console.error('💡 Dica: Verifique se o banco de dados está acessível e se você tem as permissões necessárias.');
    
    // Tentar rollback se possível
    try {
      await client.query('ROLLBACK');
      console.log('🔄 Rollback executado');
    } catch (rollbackError) {
      console.error('❌ Erro no rollback:', rollbackError);
    }
    
    process.exit(1);
  } finally {
    client.release();
    await pool.end();
  }
}

// Função para verificar se a migração já foi executada
async function checkMigrationStatus() {
  const client = await pool.connect();
  
  try {
    // Verificar se as tabelas já estão na nova estrutura
    const result = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'produtos' 
      AND column_name = 'codigo_estacao'
      AND table_schema = 'public'
    `);
    
    if (result.rows.length > 0) {
      // Verificar se é a nova estrutura (sem coluna id como PK)
      const pkCheck = await client.query(`
        SELECT column_name
        FROM information_schema.key_column_usage k
        JOIN information_schema.table_constraints t ON k.constraint_name = t.constraint_name
        WHERE t.table_name = 'produtos' 
        AND t.constraint_type = 'PRIMARY KEY'
        AND k.column_name = 'codigo_cliente'
      `);
      
      if (pkCheck.rows.length > 0) {
        console.log('✅ A migração já foi executada. O banco está na nova estrutura por códigos.');
        return true;
      }
    }
    
    return false;
  } catch (error) {
    console.error('❌ Erro ao verificar status da migração:', error);
    return false;
  } finally {
    client.release();
  }
}

// Executar migração
async function main() {
  console.log('🚀 Portal Evolution - Migração para Estrutura por Códigos');
  console.log('=' .repeat(60));
  
  const alreadyMigrated = await checkMigrationStatus();
  
  if (alreadyMigrated) {
    console.log('ℹ️  Nenhuma ação necessária.');
    process.exit(0);
  }
  
  console.log('🔍 Migração necessária detectada.');
  await executeMigration();
}

// Executar apenas se chamado diretamente
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Erro fatal:', error);
    process.exit(1);
  });
}

module.exports = { executeMigration, checkMigrationStatus };
