const { Pool } = require('pg');

// Configuração do banco de dados
const pool = new Pool({
  host: '************',
  port: 5432,
  database: 'dbetens',
  user: 'postgres',
  password: 'ab3780bd73ee4e2804d566ce6fd96209',
  ssl: false,
});

async function verificarEstruturaSubBlocos() {
  try {
    console.log('🔍 VERIFICANDO ESTRUTURA DA TABELA SUB_BLOCOS\n');
    
    // Verificar colunas existentes
    console.log('1. 📋 Colunas existentes na tabela sub_blocos:');
    const colunas = await pool.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'sub_blocos' 
      ORDER BY ordinal_position
    `);
    
    console.log('   Colunas encontradas:');
    colunas.rows.forEach((col, index) => {
      console.log(`   ${index + 1}. ${col.column_name} (${col.data_type}) - ${col.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'}`);
    });
    
    // Verificar alguns registros de exemplo
    console.log('\n2. 📊 Registros de exemplo (primeiros 3):');
    try {
      const registros = await pool.query(`
        SELECT id, codigo_sub_bloco, nome, denominacao, descricao, ativo
        FROM sub_blocos 
        WHERE ativo = true
        ORDER BY id 
        LIMIT 3
      `);
      
      if (registros.rows.length > 0) {
        console.log('   Registros encontrados:');
        registros.rows.forEach((reg, index) => {
          console.log(`   ${index + 1}. ID: ${reg.id} | Código: ${reg.codigo_sub_bloco}`);
          console.log(`      Nome: ${reg.nome || 'NULL'}`);
          console.log(`      Denominação: ${reg.denominacao || 'NULL'}`);
          console.log(`      Descrição: ${reg.descricao || 'NULL'}`);
          console.log(`      Ativo: ${reg.ativo}`);
          console.log('');
        });
      } else {
        console.log('   ⚠️  Nenhum registro encontrado');
      }
    } catch (error) {
      console.log('   ❌ Erro ao buscar registros:', error.message);
    }
    
    // Verificar se existe campo denominacao
    console.log('3. 🔍 Verificando campo denominacao:');
    const temDenominacao = colunas.rows.find(col => col.column_name === 'denominacao');
    if (temDenominacao) {
      console.log('   ✅ Campo denominacao existe');
    } else {
      console.log('   ❌ Campo denominacao NÃO existe');
      console.log('   💡 Campos disponíveis para exibição:');
      const camposTexto = colunas.rows.filter(col => 
        col.data_type.includes('character') || col.data_type.includes('text')
      );
      camposTexto.forEach(campo => {
        console.log(`      - ${campo.column_name}`);
      });
    }
    
    console.log('\n🎯 VERIFICAÇÃO CONCLUÍDA!');
    
  } catch (error) {
    console.error('❌ Erro ao verificar estrutura:', error);
    process.exit(1);
  } finally {
    await pool.end();
    console.log('🔌 Conexão encerrada');
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  verificarEstruturaSubBlocos();
}

module.exports = { verificarEstruturaSubBlocos };
