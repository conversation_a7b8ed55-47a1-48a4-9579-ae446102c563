import api from './api';

/**
 * Serviço para gerenciar ranges de gavetas (CONFORME INSTRUCAO.MD)
 */
class RangeService {
  /**
   * Lista ranges de um sub-bloco
   */
  async listRanges(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco) {
    try {
      const response = await api.get(`/ranges/sub-blocos/${codigo_cliente}/${codigo_estacao}/${codigo_bloco}/${codigo_sub_bloco}/ranges`);

      // Verificar se a resposta tem a estrutura esperada
      if (response.data && response.data.success && Array.isArray(response.data.ranges)) {
        return response.data.ranges;
      }

      // Se a resposta for um array direto
      if (Array.isArray(response.data)) {
        return response.data;
      }

      // Fallback para array vazio se a estrutura não for reconhecida
      console.warn('Estrutura de resposta inesperada:', response.data);
      return [];
    } catch (error) {
      console.error('Erro ao listar ranges:', error);
      // Retornar array vazio em caso de erro para evitar crash
      return [];
    }
  }

  /**
   * Cria um novo range (CONFORME INSTRUCAO.MD)
   */
  async createRange(rangeData) {
    try {
      const { codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_inicio, numero_fim } = rangeData;

      // Usar endpoint correto conforme instrucao.md
      const response = await api.post(
        `/ranges/sub-blocos/${codigo_cliente}/${codigo_estacao}/${codigo_bloco}/${codigo_sub_bloco}/ranges`,
        { numero_inicio, numero_fim }
      );

      return response.data;
    } catch (error) {
      console.error('Erro ao criar range:', error);
      // Retornar erro estruturado em vez de throw
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Erro ao criar range'
      };
    }
  }

  /**
   * Atualiza um range existente
   */
  async updateRange(id, rangeData) {
    try {
      const response = await api.put(`/ranges/ranges/${id}`, rangeData);
      return response.data;
    } catch (error) {
      console.error('Erro ao atualizar range:', error);
      throw error;
    }
  }

  /**
   * Deleta um range
   */
  async deleteRange(id, codes) {
    try {
      const response = await api.delete(`/ranges/ranges/${id}`, {
        data: codes
      });
      return response.data;
    } catch (error) {
      console.error('Erro ao deletar range:', error);
      // Retornar erro estruturado em vez de throw
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Erro ao deletar range'
      };
    }
  }

  /**
   * Valida um range
   */
  async validateRange(rangeData) {
    try {
      const response = await api.post('/ranges/validate-range', rangeData);
      return response.data;
    } catch (error) {
      console.error('Erro ao validar range:', error);
      // Retornar erro estruturado em vez de throw
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Erro ao validar range'
      };
    }
  }

  /**
   * Sincroniza gavetas de um sub-bloco
   */
  async syncGavetas(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco) {
    try {
      const response = await api.post(`/ranges/sub-blocos/${codigo_cliente}/${codigo_estacao}/${codigo_bloco}/${codigo_sub_bloco}/sync-gavetas`);
      return response.data;
    } catch (error) {
      console.error('Erro ao sincronizar gavetas:', error);
      throw error;
    }
  }

  /**
   * Lista gavetas de um range
   */
  async listGavetasRange(id) {
    try {
      const response = await api.get(`/ranges/ranges/${id}/gavetas`);
      return response.data;
    } catch (error) {
      console.error('Erro ao listar gavetas do range:', error);
      throw error;
    }
  }

  /**
   * Valida deleção hierárquica
   */
  async validateDeletion(tipo, params) {
    try {
      const response = await api.post('/ranges/validate-deletion', {
        tipo,
        ...params
      });
      return response.data;
    } catch (error) {
      console.error('Erro ao validar deleção:', error);
      throw error;
    }
  }

  /**
   * Calcula total de gavetas baseado nos ranges (CONFORME INSTRUCAO.MD)
   */
  async calcularTotalGavetas(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco) {
    try {
      const response = await api.get(`/ranges/sub-blocos/${codigo_cliente}/${codigo_estacao}/${codigo_bloco}/${codigo_sub_bloco}/total-gavetas`);
      return response.data;
    } catch (error) {
      console.error('Erro ao calcular total de gavetas:', error);
      throw error;
    }
  }
}

export default new RangeService();
