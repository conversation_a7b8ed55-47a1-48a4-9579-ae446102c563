const { Pool } = require('pg');

// Configuração direta do banco de dados
const pool = new Pool({
  user: 'postgres',
  host: '************',
  database: 'dbetens',
  password: 'ab3780bd73ee4e2804d566ce6fd96209',
  port: 5432,
});

async function realDatabaseCheck() {
  console.log('🔍 VERIFICAÇÃO REAL DO BANCO - Comparando com instrucao.md');
  console.log('=' .repeat(80));

  const client = await pool.connect();
  
  try {
    // 1. Verificar banco e schema atual
    console.log('📋 1. VERIFICANDO CONEXÃO:');
    
    const dbResult = await client.query('SELECT current_database()');
    const schemaResult = await client.query('SELECT current_schema()');
    
    console.log(`   Banco atual: ${dbResult.rows[0].current_database}`);
    console.log(`   Schema atual: ${schemaResult.rows[0].current_schema}`);
    
    // 2. Listar todas as tabelas (igual ao instrucao.md)
    console.log('\n📋 2. LISTANDO TODAS AS TABELAS:');
    
    const tablesResult = await client.query(`
      SELECT schemaname, tablename, tableowner 
      FROM pg_tables 
      WHERE schemaname = 'public' 
      ORDER BY tablename
    `);
    
    console.log('   Schema |        Name        | Type  |  Owner');
    console.log('   -------+--------------------+-------+----------');
    tablesResult.rows.forEach(row => {
      console.log(`   ${row.schemaname.padEnd(6)} | ${row.tablename.padEnd(18)} | table | ${row.tableowner}`);
    });
    
    // 3. Verificar estrutura da tabela blocos (igual ao instrucao.md)
    console.log('\n📋 3. ESTRUTURA DA TABELA BLOCOS (como no instrucao.md):');
    
    const blocosStructure = await client.query(`
      SELECT 
        column_name,
        data_type,
        character_maximum_length,
        is_nullable,
        column_default
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'blocos'
      ORDER BY ordinal_position
    `);
    
    console.log('   Column    |            Type             | Nullable |              Default');
    console.log('   ----------+-----------------------------+----------+------------------------------------');
    blocosStructure.rows.forEach(row => {
      const type = row.character_maximum_length ? 
        `${row.data_type}(${row.character_maximum_length})` : 
        row.data_type;
      const nullable = row.is_nullable === 'YES' ? '' : 'not null';
      const defaultVal = row.column_default || '';
      
      console.log(`   ${row.column_name.padEnd(12)} | ${type.padEnd(27)} | ${nullable.padEnd(8)} | ${defaultVal}`);
    });
    
    // 4. Verificar se as colunas hierárquicas existem REALMENTE
    console.log('\n📋 4. VERIFICANDO COLUNAS HIERÁRQUICAS ESPECÍFICAS:');
    
    const hierarchicalCheck = await client.query(`
      SELECT column_name
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'blocos'
      AND column_name IN ('codigo_cliente', 'codigo_estacao', 'denominacao')
    `);
    
    console.log(`   Colunas hierárquicas encontradas em blocos: ${hierarchicalCheck.rows.length}`);
    if (hierarchicalCheck.rows.length > 0) {
      hierarchicalCheck.rows.forEach(row => {
        console.log(`   ✅ ${row.column_name}`);
      });
    } else {
      console.log('   ❌ NENHUMA coluna hierárquica encontrada em blocos!');
    }
    
    // 5. Verificar outras tabelas importantes
    console.log('\n📋 5. VERIFICANDO OUTRAS TABELAS:');
    
    const importantTables = ['sub_blocos', 'gavetas', 'sepultamentos'];
    
    for (const tableName of importantTables) {
      const tableStructure = await client.query(`
        SELECT column_name
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = $1
        ORDER BY ordinal_position
      `, [tableName]);
      
      const hierarchicalCols = await client.query(`
        SELECT column_name
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = $1
        AND column_name IN ('codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco')
      `, [tableName]);
      
      console.log(`\n   📋 ${tableName.toUpperCase()}:`);
      console.log(`      Total de colunas: ${tableStructure.rows.length}`);
      console.log(`      Colunas hierárquicas: ${hierarchicalCols.rows.length}`);
      
      if (hierarchicalCols.rows.length > 0) {
        hierarchicalCols.rows.forEach(row => {
          console.log(`      ✅ ${row.column_name}`);
        });
      } else {
        console.log(`      ❌ Nenhuma coluna hierárquica encontrada`);
      }
      
      // Mostrar primeiras 5 colunas para comparação
      console.log(`      Primeiras colunas: ${tableStructure.rows.slice(0, 5).map(r => r.column_name).join(', ')}`);
    }
    
    // 6. TENTAR ADICIONAR AS COLUNAS AGORA
    console.log('\n📋 6. TENTANDO ADICIONAR COLUNAS HIERÁRQUICAS AGORA:');
    
    try {
      await client.query('BEGIN');
      
      console.log('   🔧 Adicionando colunas em blocos...');
      await client.query('ALTER TABLE blocos ADD COLUMN IF NOT EXISTS codigo_cliente VARCHAR(50)');
      await client.query('ALTER TABLE blocos ADD COLUMN IF NOT EXISTS codigo_estacao VARCHAR(50)');
      await client.query('ALTER TABLE blocos ADD COLUMN IF NOT EXISTS denominacao VARCHAR(255)');
      
      console.log('   🔧 Adicionando colunas em sub_blocos...');
      await client.query('ALTER TABLE sub_blocos ADD COLUMN IF NOT EXISTS codigo_cliente VARCHAR(50)');
      await client.query('ALTER TABLE sub_blocos ADD COLUMN IF NOT EXISTS codigo_estacao VARCHAR(50)');
      await client.query('ALTER TABLE sub_blocos ADD COLUMN IF NOT EXISTS codigo_bloco VARCHAR(50)');
      await client.query('ALTER TABLE sub_blocos ADD COLUMN IF NOT EXISTS denominacao VARCHAR(255)');
      
      console.log('   🔧 Adicionando colunas em gavetas...');
      await client.query('ALTER TABLE gavetas ADD COLUMN IF NOT EXISTS codigo_cliente VARCHAR(50)');
      await client.query('ALTER TABLE gavetas ADD COLUMN IF NOT EXISTS codigo_estacao VARCHAR(50)');
      await client.query('ALTER TABLE gavetas ADD COLUMN IF NOT EXISTS codigo_bloco VARCHAR(50)');
      await client.query('ALTER TABLE gavetas ADD COLUMN IF NOT EXISTS codigo_sub_bloco VARCHAR(50)');
      
      console.log('   🔧 Adicionando colunas em numeracoes_gavetas...');
      await client.query('ALTER TABLE numeracoes_gavetas ADD COLUMN IF NOT EXISTS codigo_cliente VARCHAR(50)');
      await client.query('ALTER TABLE numeracoes_gavetas ADD COLUMN IF NOT EXISTS codigo_estacao VARCHAR(50)');
      await client.query('ALTER TABLE numeracoes_gavetas ADD COLUMN IF NOT EXISTS codigo_bloco VARCHAR(50)');
      await client.query('ALTER TABLE numeracoes_gavetas ADD COLUMN IF NOT EXISTS codigo_sub_bloco VARCHAR(50)');
      
      console.log('   🔧 Adicionando colunas em logs_auditoria...');
      await client.query('ALTER TABLE logs_auditoria ADD COLUMN IF NOT EXISTS codigo_cliente VARCHAR(50)');
      await client.query('ALTER TABLE logs_auditoria ADD COLUMN IF NOT EXISTS codigo_estacao VARCHAR(50)');
      await client.query('ALTER TABLE logs_auditoria ADD COLUMN IF NOT EXISTS codigo_bloco VARCHAR(50)');
      await client.query('ALTER TABLE logs_auditoria ADD COLUMN IF NOT EXISTS codigo_sub_bloco VARCHAR(50)');
      await client.query('ALTER TABLE logs_auditoria ADD COLUMN IF NOT EXISTS descricao TEXT');
      
      await client.query('COMMIT');
      console.log('   ✅ TODAS as colunas adicionadas com sucesso!');
      
    } catch (error) {
      await client.query('ROLLBACK');
      console.log(`   ❌ Erro ao adicionar colunas: ${error.message}`);
    }
    
    // 7. VERIFICAR NOVAMENTE APÓS ADIÇÃO
    console.log('\n📋 7. VERIFICAÇÃO APÓS ADIÇÃO DAS COLUNAS:');
    
    const finalCheck = await client.query(`
      SELECT column_name
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'blocos'
      AND column_name IN ('codigo_cliente', 'codigo_estacao', 'denominacao')
    `);
    
    console.log(`   Colunas hierárquicas em blocos AGORA: ${finalCheck.rows.length}`);
    finalCheck.rows.forEach(row => {
      console.log(`   ✅ ${row.column_name}`);
    });
    
    // 8. MOSTRAR ESTRUTURA FINAL DA TABELA BLOCOS
    console.log('\n📋 8. ESTRUTURA FINAL DA TABELA BLOCOS:');
    
    const finalStructure = await client.query(`
      SELECT 
        column_name,
        data_type,
        character_maximum_length,
        is_nullable,
        column_default
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'blocos'
      ORDER BY ordinal_position
    `);
    
    console.log('   Column           |            Type             | Nullable |              Default');
    console.log('   -----------------+-----------------------------+----------+------------------------------------');
    finalStructure.rows.forEach(row => {
      const type = row.character_maximum_length ? 
        `${row.data_type}(${row.character_maximum_length})` : 
        row.data_type;
      const nullable = row.is_nullable === 'YES' ? '' : 'not null';
      const defaultVal = row.column_default || '';
      
      const isHierarchical = ['codigo_cliente', 'codigo_estacao', 'denominacao'].includes(row.column_name);
      const marker = isHierarchical ? '🔹' : '  ';
      
      console.log(`${marker} ${row.column_name.padEnd(15)} | ${type.padEnd(27)} | ${nullable.padEnd(8)} | ${defaultVal}`);
    });
    
    console.log('\n🎉 VERIFICAÇÃO CONCLUÍDA!');
    console.log('🔹 = Colunas hierárquicas adicionadas');
    
  } catch (error) {
    console.error('❌ Erro durante verificação:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Executar verificação
if (require.main === module) {
  realDatabaseCheck().catch(error => {
    console.error('❌ Erro fatal:', error);
    process.exit(1);
  });
}

module.exports = { realDatabaseCheck };
