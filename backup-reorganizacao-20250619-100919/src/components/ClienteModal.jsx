import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import Modal from './Modal';
import { clienteService } from '../services/api';

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 6px;
`;

const Label = styled.label`
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
`;

const Input = styled.input`
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: #059669;
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
  }

  &:disabled {
    background: #f9fafb;
    color: #6b7280;
  }
`;

const Select = styled.select`
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: #059669;
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
`;

const Button = styled.button`
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;

  &.primary {
    background: linear-gradient(135deg, #1e3a8a 0%, #059669 100%);
    color: white;

    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }
  }

  &.secondary {
    background: white;
    color: #374151;
    border-color: #d1d5db;

    &:hover {
      background: #f9fafb;
    }
  }
`;

const ErrorMessage = styled.div`
  color: #dc2626;
  font-size: 0.875rem;
  margin-top: 4px;
`;

const estados = [
  'AC', 'AL', 'AP', 'AM', 'BA', 'CE', 'DF', 'ES', 'GO', 'MA',
  'MT', 'MS', 'MG', 'PA', 'PB', 'PR', 'PE', 'PI', 'RJ', 'RN',
  'RS', 'RO', 'RR', 'SC', 'SP', 'SE', 'TO'
];

const ClienteModal = ({ isOpen, onClose, cliente = null, onSuccess }) => {
  const [formData, setFormData] = useState({
    codigo_cliente: '',
    cnpj: '',
    nome_fantasia: '',
    razao_social: '',
    cep: '',
    logradouro: '',
    numero: '',
    complemento: '',
    bairro: '',
    cidade: '',
    estado: 'SP',
    ativo: true
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (isOpen) {
      if (cliente) {
        setFormData({
          codigo_cliente: cliente.codigo_cliente || '',
          cnpj: cliente.cnpj || '',
          nome_fantasia: cliente.nome_fantasia || '',
          razao_social: cliente.razao_social || '',
          cep: cliente.cep || '',
          logradouro: cliente.logradouro || '',
          numero: cliente.numero || '',
          complemento: cliente.complemento || '',
          bairro: cliente.bairro || '',
          cidade: cliente.cidade || '',
          estado: cliente.estado || 'SP',
          ativo: cliente.ativo !== undefined ? cliente.ativo : true
        });
      } else {
        setFormData({
          codigo_cliente: '',
          cnpj: '',
          nome_fantasia: '',
          razao_social: '',
          cep: '',
          logradouro: '',
          numero: '',
          complemento: '',
          bairro: '',
          cidade: '',
          estado: 'SP',
          ativo: true
        });
      }
      setError('');
    }
  }, [isOpen, cliente]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    
    // Formatação automática para campos específicos
    let formattedValue = value;
    
    if (name === 'cnpj') {
      // Formatação CNPJ: 18.384.274/0001-78
      formattedValue = value
        .replace(/\D/g, '')
        .replace(/^(\d{2})(\d)/, '$1.$2')
        .replace(/^(\d{2})\.(\d{3})(\d)/, '$1.$2.$3')
        .replace(/\.(\d{3})(\d)/, '.$1/$2')
        .replace(/(\d{4})(\d)/, '$1-$2')
        .substring(0, 18);
    } else if (name === 'cep') {
      // Formatação CEP: 01310-100
      formattedValue = value
        .replace(/\D/g, '')
        .replace(/^(\d{5})(\d)/, '$1-$2')
        .substring(0, 9);
    } else if (name === 'codigo_cliente') {
      // Formatação código cliente: SAF_001
      formattedValue = value.toUpperCase();
    }
    
    setFormData(prev => ({
      ...prev,
      [name]: formattedValue
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      if (cliente) {
        await clienteService.atualizar(cliente.codigo_cliente, formData);
        alert('Cliente atualizado com sucesso!');
      } else {
        await clienteService.criar(formData);
        alert('Cliente criado com sucesso!');
      }

      onSuccess();
      onClose();
    } catch (error) {
      console.error('Erro ao salvar cliente:', error);
      setError(error.response?.data?.error || 'Erro ao salvar cliente');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={cliente ? 'Editar Cliente' : 'Novo Cliente'}
      maxWidth="800px"
    >
      <Form onSubmit={handleSubmit}>
        <FormRow>
          <FormGroup>
            <Label>Código do Cliente *</Label>
            <Input
              type="text"
              name="codigo_cliente"
              value={formData.codigo_cliente}
              onChange={handleChange}
              required
              placeholder="Ex: SAF_001"
              disabled={!!cliente}
            />
          </FormGroup>
          <FormGroup>
            <Label>CNPJ *</Label>
            <Input
              type="text"
              name="cnpj"
              value={formData.cnpj}
              onChange={handleChange}
              required
              placeholder="18.384.274/0001-78"
            />
          </FormGroup>
        </FormRow>

        <FormRow>
          <FormGroup>
            <Label>Nome Fantasia *</Label>
            <Input
              type="text"
              name="nome_fantasia"
              value={formData.nome_fantasia}
              onChange={handleChange}
              required
              placeholder="Ex: Safra Cemitérios"
            />
          </FormGroup>
          <FormGroup>
            <Label>Razão Social *</Label>
            <Input
              type="text"
              name="razao_social"
              value={formData.razao_social}
              onChange={handleChange}
              required
              placeholder="Ex: Safra Cemitérios Ltda"
            />
          </FormGroup>
        </FormRow>

        <FormRow>
          <FormGroup>
            <Label>CEP</Label>
            <Input
              type="text"
              name="cep"
              value={formData.cep}
              onChange={handleChange}
              placeholder="01310-100"
            />
          </FormGroup>
          <FormGroup>
            <Label>Logradouro</Label>
            <Input
              type="text"
              name="logradouro"
              value={formData.logradouro}
              onChange={handleChange}
              placeholder="Ex: Av. Paulista"
            />
          </FormGroup>
        </FormRow>

        <FormRow>
          <FormGroup>
            <Label>Número</Label>
            <Input
              type="text"
              name="numero"
              value={formData.numero}
              onChange={handleChange}
              placeholder="Ex: 1000"
            />
          </FormGroup>
          <FormGroup>
            <Label>Complemento</Label>
            <Input
              type="text"
              name="complemento"
              value={formData.complemento}
              onChange={handleChange}
              placeholder="Ex: Sala 101"
            />
          </FormGroup>
        </FormRow>

        <FormRow>
          <FormGroup>
            <Label>Bairro</Label>
            <Input
              type="text"
              name="bairro"
              value={formData.bairro}
              onChange={handleChange}
              placeholder="Ex: Bela Vista"
            />
          </FormGroup>
          <FormGroup>
            <Label>Cidade</Label>
            <Input
              type="text"
              name="cidade"
              value={formData.cidade}
              onChange={handleChange}
              placeholder="Ex: São Paulo"
            />
          </FormGroup>
        </FormRow>

        <FormGroup>
          <Label>Estado</Label>
          <Select
            name="estado"
            value={formData.estado}
            onChange={handleChange}
          >
            {estados.map(estado => (
              <option key={estado} value={estado}>{estado}</option>
            ))}
          </Select>
        </FormGroup>

        {error && <ErrorMessage>{error}</ErrorMessage>}

        <ButtonGroup>
          <Button type="button" className="secondary" onClick={onClose}>
            Cancelar
          </Button>
          <Button type="submit" className="primary" disabled={loading}>
            {loading ? 'Salvando...' : (cliente ? 'Atualizar' : 'Criar')}
          </Button>
        </ButtonGroup>
      </Form>
    </Modal>
  );
};

export default ClienteModal;
