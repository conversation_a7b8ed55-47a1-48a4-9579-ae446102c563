#!/bin/bash

# AGENTE 9: Teste de integração da API para gaveta 2838
# Portal Evolution - Ultrathink

echo "🤖 AGENTE 9 - TESTE DE INTEGRAÇÃO DA API"
echo "========================================"
echo ""

# Função para fazer login e obter token
get_auth_token() {
    echo "🔐 Fazendo login para obter token..."
    
    # Testar com usuário <EMAIL> (cliente ITV_001)
    TOKEN_RESPONSE=$(docker exec portal-evolution-dev_portalevo-backend-dev.1.pq2cq3o9o1kppqoyv4mpmztul wget -qO- --post-data='{"email":"<EMAIL>","senha":"54321"}' --header='Content-Type: application/json' "http://localhost:3001/api/auth/login" 2>/dev/null)
    
    if [[ "$TOKEN_RESPONSE" == *"token"* ]]; then
        TOKEN=$(echo "$TOKEN_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
        echo "✅ Login realizado com sucesso"
        echo "Token obtido: ${TOKEN:0:20}..."
        return 0
    else
        echo "❌ Erro no login:"
        echo "$TOKEN_RESPONSE"
        return 1
    fi
}

# Função para testar a API de gavetas
test_gavetas_api() {
    local token=$1
    
    echo ""
    echo "🔍 Testando API de gavetas com token de autenticação..."
    
    # URL exata que o frontend usa
    local url="http://localhost:3001/api/produtos/sub-blocos/ITV_001/ETEN_003/BL_003/SUB_003/gavetas?disponivel=true&limit=50"
    
    echo "URL testada: $url"
    echo "Cabeçalho: Authorization: Bearer $token"
    
    # Fazer requisição autenticada
    API_RESPONSE=$(docker exec portal-evolution-dev_portalevo-backend-dev.1.pq2cq3o9o1kppqoyv4mpmztul wget -qO- --header="Authorization: Bearer $token" "$url" 2>&1)
    
    echo ""
    echo "📋 Resposta da API:"
    echo "$API_RESPONSE"
    
    # Verificar se a gaveta 2838 está na resposta
    if [[ "$API_RESPONSE" == *"2838"* ]]; then
        echo ""
        echo "✅ SUCESSO: Gaveta 2838 encontrada na resposta da API!"
        
        # Extrair informações da gaveta 2838
        echo ""
        echo "🔍 Detalhes da gaveta 2838 na resposta:"
        echo "$API_RESPONSE" | grep -A5 -B5 "2838" || echo "Não foi possível extrair detalhes específicos"
        
        return 0
    else
        echo ""
        echo "❌ PROBLEMA: Gaveta 2838 NÃO encontrada na resposta da API"
        
        # Verificar se há outras gavetas na resposta
        if [[ "$API_RESPONSE" == *"numero_gaveta"* ]]; then
            echo ""
            echo "⚠️  A API retornou outras gavetas, mas não a 2838"
            echo "Primeiras gavetas na resposta:"
            echo "$API_RESPONSE" | head -20
        elif [[ "$API_RESPONSE" == *"error"* ]]; then
            echo ""
            echo "❌ Erro na API:"
            echo "$API_RESPONSE"
        else
            echo ""
            echo "❌ Resposta inesperada da API"
        fi
        
        return 1
    fi
}

# Função para verificar logs do backend
check_backend_logs() {
    echo ""
    echo "📋 Verificando logs do backend durante a requisição..."
    
    # Capturar logs recentes
    docker service logs portal-evolution-dev_portalevo-backend-dev --tail 20 | grep -E "(gavetas|2838|ITV_001|ETEN_003)" || echo "Nenhum log específico encontrado"
}

# Executar testes
echo "🚀 Iniciando teste de integração..."

# Obter token de autenticação
if get_auth_token; then
    # Testar API com token
    if test_gavetas_api "$TOKEN"; then
        echo ""
        echo "🎉 TESTE PASSOU: A gaveta 2838 está sendo retornada pela API!"
    else
        echo ""
        echo "❌ TESTE FALHOU: A gaveta 2838 não está sendo retornada pela API"
        
        # Verificar logs para debug
        check_backend_logs
    fi
else
    echo ""
    echo "❌ TESTE FALHOU: Não foi possível obter token de autenticação"
fi

echo ""
echo "📊 RESUMO DO TESTE:"
echo "- Cliente: ITV_001"
echo "- Estação: ETEN_003"
echo "- Bloco: BL_003"
echo "- Sub-bloco: SUB_003"
echo "- Gaveta procurada: 2838"
echo "- Usuário de teste: <EMAIL> (cliente ITV_001)"
echo ""
