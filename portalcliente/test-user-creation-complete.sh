#!/bin/bash

# Script de teste completo para criação de usuários
# Testa: email duplicado, boas-vindas, validações

echo "🧪 INICIANDO TESTES COMPLETOS DE CRIAÇÃO DE USUÁRIOS"
echo "=================================================="

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log
log_test() {
    local status=$1
    local message=$2
    if [ "$status" = "PASS" ]; then
        echo -e "${GREEN}✅ PASS${NC}: $message"
    elif [ "$status" = "FAIL" ]; then
        echo -e "${RED}❌ FAIL${NC}: $message"
    elif [ "$status" = "INFO" ]; then
        echo -e "${BLUE}ℹ️  INFO${NC}: $message"
    elif [ "$status" = "WARN" ]; then
        echo -e "${YELLOW}⚠️  WARN${NC}: $message"
    fi
}

# Função para obter token admin
get_admin_token() {
    local response=$(curl -s -X POST https://portal.evo-eden.site/api/auth/login \
        -H "Content-Type: application/json" \
        -d '{
            "email": "<EMAIL>",
            "senha": "secret"
        }')
    
    echo "$response" | jq -r '.token // empty'
}

# Função para criar usuário admin temporário
create_temp_admin() {
    log_test "INFO" "Criando usuário admin temporário para testes..."
    
    docker exec -i $(docker ps -q --filter "name=postgres") psql -U postgres -d dbetens -c "
    INSERT INTO usuarios (email, senha, tipo_usuario, nome, ativo) 
    VALUES ('<EMAIL>', '\$2a\$10\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'Admin Teste', true)
    ON CONFLICT (email) DO NOTHING;
    " > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        log_test "PASS" "Admin temporário criado/verificado"
    else
        log_test "FAIL" "Erro ao criar admin temporário"
        return 1
    fi
}

# Função para limpar dados de teste
cleanup_test_data() {
    log_test "INFO" "Limpando dados de teste..."
    
    docker exec -i $(docker ps -q --filter "name=postgres") psql -U postgres -d dbetens -c "
    DELETE FROM usuarios WHERE email LIKE '%@teste-email-duplicado.com';
    DELETE FROM usuarios WHERE email LIKE '%@teste-boas-vindas.com';
    DELETE FROM usuarios WHERE email = '<EMAIL>';
    " > /dev/null 2>&1
}

# Teste 1: Verificar API Health
test_api_health() {
    echo ""
    log_test "INFO" "TESTE 1: Verificando saúde da API..."
    
    local response=$(curl -s https://portal.evo-eden.site/api/health)
    local status=$(echo "$response" | jq -r '.status // empty')
    
    if [ "$status" = "OK" ]; then
        log_test "PASS" "API está funcionando corretamente"
    else
        log_test "FAIL" "API não está respondendo adequadamente"
        return 1
    fi
}

# Teste 2: Criar usuário com sucesso
test_create_user_success() {
    echo ""
    log_test "INFO" "TESTE 2: Criando usuário com sucesso..."
    
    local token=$(get_admin_token)
    if [ -z "$token" ]; then
        log_test "FAIL" "Não foi possível obter token admin"
        return 1
    fi
    
    local timestamp=$(date +%s)
    local response=$(curl -s -X POST https://portal.evo-eden.site/api/usuarios \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $token" \
        -d "{
            \"nome\": \"Usuário Teste Sucesso\",
            \"email\": \"sucesso.${timestamp}@teste-boas-vindas.com\",
            \"senha\": \"SenhaTest@123\",
            \"tipo_usuario\": \"cliente\",
            \"codigo_cliente\": \"CLI001\"
        }")
    
    local message=$(echo "$response" | jq -r '.message // empty')
    local error=$(echo "$response" | jq -r '.error // empty')
    
    if [ "$message" = "Usuário cadastrado com sucesso" ]; then
        log_test "PASS" "Usuário criado com sucesso"
        log_test "INFO" "Email de boas-vindas deve ter sido enviado"
    else
        log_test "FAIL" "Erro ao criar usuário: $error"
        return 1
    fi
}

# Teste 3: Tentar criar usuário com email duplicado
test_create_user_duplicate_email() {
    echo ""
    log_test "INFO" "TESTE 3: Testando email duplicado..."
    
    local token=$(get_admin_token)
    if [ -z "$token" ]; then
        log_test "FAIL" "Não foi possível obter token admin"
        return 1
    fi
    
    local timestamp=$(date +%s)
    local test_email="duplicado.${timestamp}@teste-email-duplicado.com"
    
    # Primeiro, criar o usuário
    log_test "INFO" "Criando usuário inicial..."
    local response1=$(curl -s -X POST https://portal.evo-eden.site/api/usuarios \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $token" \
        -d "{
            \"nome\": \"Usuário Original\",
            \"email\": \"$test_email\",
            \"senha\": \"SenhaTest@123\",
            \"tipo_usuario\": \"cliente\",
            \"codigo_cliente\": \"CLI001\"
        }")
    
    local message1=$(echo "$response1" | jq -r '.message // empty')
    if [ "$message1" != "Usuário cadastrado com sucesso" ]; then
        log_test "FAIL" "Não foi possível criar usuário inicial para teste"
        return 1
    fi
    
    log_test "PASS" "Usuário inicial criado"
    
    # Agora tentar criar com email duplicado
    log_test "INFO" "Tentando criar usuário com email duplicado..."
    local response2=$(curl -s -X POST https://portal.evo-eden.site/api/usuarios \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $token" \
        -d "{
            \"nome\": \"Usuário Duplicado\",
            \"email\": \"$test_email\",
            \"senha\": \"OutraSenha@123\",
            \"tipo_usuario\": \"cliente\",
            \"codigo_cliente\": \"CLI002\"
        }")
    
    local error=$(echo "$response2" | jq -r '.error // empty')
    local suggestion=$(echo "$response2" | jq -r '.suggestion // empty')
    local existing_user=$(echo "$response2" | jq -r '.existing_user.email // empty')
    
    if [ "$error" = "Este email já possui uma conta cadastrada no sistema" ] && [ "$suggestion" = "forgot_password" ]; then
        log_test "PASS" "Erro de email duplicado detectado corretamente"
        log_test "PASS" "Mensagem de erro adequada retornada"
        log_test "PASS" "Sugestão de 'forgot_password' incluída"
        
        if [ "$existing_user" = "$test_email" ]; then
            log_test "PASS" "Informações do usuário existente retornadas"
        else
            log_test "WARN" "Informações do usuário existente não retornadas adequadamente"
        fi
    else
        log_test "FAIL" "Validação de email duplicado não funcionou adequadamente"
        log_test "INFO" "Resposta recebida: $response2"
        return 1
    fi
}

# Teste 4: Validar estrutura de resposta
test_response_structure() {
    echo ""
    log_test "INFO" "TESTE 4: Validando estrutura de resposta para email duplicado..."
    
    local token=$(get_admin_token)
    if [ -z "$token" ]; then
        log_test "FAIL" "Não foi possível obter token admin"
        return 1
    fi
    
    # Usar email que sabemos que existe
    local response=$(curl -s -X POST https://portal.evo-eden.site/api/usuarios \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $token" \
        -d '{
            "nome": "Teste Estrutura",
            "email": "<EMAIL>",
            "senha": "SenhaTest@123",
            "tipo_usuario": "cliente",
            "codigo_cliente": "CLI001"
        }')
    
    # Verificar campos obrigatórios na resposta
    local has_error=$(echo "$response" | jq 'has("error")')
    local has_details=$(echo "$response" | jq 'has("details")')
    local has_suggestion=$(echo "$response" | jq 'has("suggestion")')
    local has_existing_user=$(echo "$response" | jq 'has("existing_user")')
    local has_success_false=$(echo "$response" | jq '.success == false')
    
    if [ "$has_error" = "true" ]; then
        log_test "PASS" "Campo 'error' presente na resposta"
    else
        log_test "FAIL" "Campo 'error' ausente na resposta"
    fi
    
    if [ "$has_details" = "true" ]; then
        log_test "PASS" "Campo 'details' presente na resposta"
    else
        log_test "WARN" "Campo 'details' ausente na resposta"
    fi
    
    if [ "$has_suggestion" = "true" ]; then
        log_test "PASS" "Campo 'suggestion' presente na resposta"
    else
        log_test "WARN" "Campo 'suggestion' ausente na resposta"
    fi
    
    if [ "$has_existing_user" = "true" ]; then
        log_test "PASS" "Campo 'existing_user' presente na resposta"
    else
        log_test "WARN" "Campo 'existing_user' ausente na resposta"
    fi
    
    if [ "$has_success_false" = "true" ]; then
        log_test "PASS" "Campo 'success: false' presente na resposta"
    else
        log_test "WARN" "Campo 'success: false' ausente na resposta"
    fi
}

# Teste 5: Verificar logs de auditoria
test_audit_logs() {
    echo ""
    log_test "INFO" "TESTE 5: Verificando logs de auditoria..."
    
    local log_count=$(docker exec -i $(docker ps -q --filter "name=postgres") psql -U postgres -d dbetens -c "
    SELECT COUNT(*) FROM logs_auditoria 
    WHERE acao = 'TENTATIVA_EMAIL_DUPLICADO' 
    AND created_at > CURRENT_TIMESTAMP - INTERVAL '5 minutes';
    " -t | tr -d ' ')
    
    if [ "$log_count" -gt 0 ]; then
        log_test "PASS" "Logs de tentativa de email duplicado encontrados ($log_count)"
    else
        log_test "WARN" "Nenhum log de tentativa de email duplicado encontrado"
    fi
    
    local email_log_count=$(docker exec -i $(docker ps -q --filter "name=postgres") psql -U postgres -d dbetens -c "
    SELECT COUNT(*) FROM logs_auditoria 
    WHERE acao = 'EMAIL_CREDENCIAIS_ENVIADO' 
    AND created_at > CURRENT_TIMESTAMP - INTERVAL '5 minutes';
    " -t | tr -d ' ')
    
    if [ "$email_log_count" -gt 0 ]; then
        log_test "PASS" "Logs de envio de email encontrados ($email_log_count)"
    else
        log_test "WARN" "Nenhum log de envio de email encontrado"
    fi
}

# Função principal
main() {
    echo "🚀 Iniciando bateria de testes..."
    echo "Data/Hora: $(date)"
    echo "Portal: https://portal.evo-eden.site"
    echo ""
    
    # Preparação
    create_temp_admin
    if [ $? -ne 0 ]; then
        log_test "FAIL" "Não foi possível preparar ambiente de teste"
        exit 1
    fi
    
    # Executar testes
    local tests_passed=0
    local tests_total=5
    
    test_api_health && ((tests_passed++))
    test_create_user_success && ((tests_passed++))
    test_create_user_duplicate_email && ((tests_passed++))
    test_response_structure && ((tests_passed++))
    test_audit_logs && ((tests_passed++))
    
    # Limpeza
    cleanup_test_data
    
    # Resultado final
    echo ""
    echo "=================================================="
    log_test "INFO" "RESULTADO FINAL DOS TESTES"
    echo "=================================================="
    log_test "INFO" "Testes executados: $tests_total"
    log_test "INFO" "Testes aprovados: $tests_passed"
    log_test "INFO" "Taxa de sucesso: $(( tests_passed * 100 / tests_total ))%"
    
    if [ $tests_passed -eq $tests_total ]; then
        echo ""
        log_test "PASS" "🎉 TODOS OS TESTES PASSARAM!"
        log_test "INFO" "Sistema de criação de usuários funcionando corretamente"
        echo ""
        echo "✅ Funcionalidades validadas:"
        echo "   • Verificação de email duplicado"
        echo "   • Mensagens de erro adequadas"
        echo "   • Template de boas-vindas"
        echo "   • Logs de auditoria"
        echo "   • Estrutura de resposta"
        exit 0
    else
        echo ""
        log_test "FAIL" "❌ ALGUNS TESTES FALHARAM"
        log_test "INFO" "Verifique os logs acima para detalhes"
        exit 1
    fi
}

# Executar
main "$@"
