#!/bin/bash

# ===================================
# SCRIPT DE VALIDAÇÃO - PORTAL EVOLUTION
# ===================================
# Execute: bash scripts/validar-estrutura.sh
# Valida se projeto está seguindo padrão da VPS

echo "🔍 Validando estrutura do Portal Evolution..."
echo "=============================================="

# Cores para output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

success=0
warnings=0
errors=0

check_ok() {
    echo -e "${GREEN}✅ $1${NC}"
    ((success++))
}

check_warn() {
    echo -e "${YELLOW}⚠️  $1${NC}"
    ((warnings++))
}

check_error() {
    echo -e "${RED}❌ $1${NC}"
    ((errors++))
}

check_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

echo ""
echo "📁 Verificando estrutura de pastas..."

# Verificar estrutura obrigatória
required_dirs=("app" "docker" "scripts" "dados")
for dir in "${required_dirs[@]}"; do
    if [[ -d "$dir" ]]; then
        check_ok "Diretório $dir/ existe"
    else
        check_error "Diretório $dir/ não encontrado"
    fi
done

echo ""
echo "📄 Verificando arquivos obrigatórios..."

# Verificar arquivos principais
required_files=(
    ".env:Arquivo de configuração principal"
    "portal-evolution.yaml:Docker Compose principal"
    "README-VPS.md:Documentação do projeto"
)

for file_desc in "${required_files[@]}"; do
    file=$(echo $file_desc | cut -d':' -f1)
    desc=$(echo $file_desc | cut -d':' -f2)
    
    if [[ -f "$file" ]]; then
        check_ok "$desc ($file)"
    else
        check_error "$desc não encontrado ($file)"
    fi
done

echo ""
echo "🐳 Verificando Dockerfiles..."

# Verificar Dockerfiles
docker_files=(
    "docker/Dockerfile.frontend:Dockerfile do frontend"
    "docker/Dockerfile.backend:Dockerfile do backend"
    "docker/nginx.conf:Configuração do Nginx"
)

for file_desc in "${docker_files[@]}"; do
    file=$(echo $file_desc | cut -d':' -f1)
    desc=$(echo $file_desc | cut -d':' -f2)
    
    if [[ -f "$file" ]]; then
        check_ok "$desc ($file)"
    else
        check_error "$desc não encontrado ($file)"
    fi
done

echo ""
echo "📜 Verificando scripts..."

# Verificar scripts
script_files=(
    "scripts/iniciar-servico.sh:Script de inicialização"
    "scripts/backup-dados.sh:Script de backup"
    "scripts/atualizar-containers.sh:Script de atualização"
    "scripts/monitorar-servico.sh:Script de monitoramento"
    "scripts/preparar-scripts.sh:Script de preparação"
)

for file_desc in "${script_files[@]}"; do
    file=$(echo $file_desc | cut -d':' -f1)
    desc=$(echo $file_desc | cut -d':' -f2)
    
    if [[ -f "$file" ]]; then
        if [[ -x "$file" ]]; then
            check_ok "$desc executável ($file)"
        else
            check_warn "$desc não é executável ($file)"
            check_info "Execute: chmod +x $file"
        fi
    else
        check_error "$desc não encontrado ($file)"
    fi
done

echo ""
echo "⚙️ Verificando configuração..."

if [[ -f ".env" ]]; then
    # Verificar configurações obrigatórias
    required_vars=(
        "VPS_HOST:IP da VPS"
        "DB_PASSWORD:Senha do banco"
        "JWT_SECRET:Chave JWT"
        "TRAEFIK_NETWORK:Rede Traefik"
    )
    
    for var_desc in "${required_vars[@]}"; do
        var=$(echo $var_desc | cut -d':' -f1)
        desc=$(echo $var_desc | cut -d':' -f2)
        
        if grep -q "^$var=" .env; then
            value=$(grep "^$var=" .env | cut -d'=' -f2)
            if [[ -n "$value" && "$value" != "seu_valor_aqui" && "$value" != "SUA_SENHA_SEGURA_AQUI" && "$value" != "SUA_CHAVE_JWT_MUITO_SEGURA_AQUI" ]]; then
                check_ok "$desc configurado"
            else
                check_warn "$desc precisa ser configurado"
                check_info "Edite .env e configure $var"
            fi
        else
            check_error "$desc não encontrado no .env"
        fi
    done
    
    # Verificar se senhas padrão foram alteradas
    if grep -q "portal_senha_muito_segura_2025" .env; then
        check_warn "DB_PASSWORD ainda está com valor padrão"
        check_info "Para gerar nova senha: openssl rand -base64 32"
    fi
    
    if grep -q "jwt_chave_muito_segura_portal_evolution_2025" .env; then
        check_warn "JWT_SECRET ainda está com valor padrão"
        check_info "Para gerar nova chave: openssl rand -hex 64"
    fi
else
    check_error "Arquivo .env não encontrado"
fi

echo ""
echo "🐳 Verificando Docker Compose..."

if [[ -f "portal-evolution.yaml" ]]; then
    # Verificar se usa rede traefik
    if grep -q "traefik:" portal-evolution.yaml && grep -q "external: true" portal-evolution.yaml; then
        check_ok "Configurado para usar rede traefik externa"
    else
        check_error "Não está configurado para rede traefik externa"
    fi
    
    # Verificar serviços obrigatórios
    required_services=("portal-database" "portal-backend" "portal-frontend")
    for service in "${required_services[@]}"; do
        if grep -q "$service:" portal-evolution.yaml; then
            check_ok "Serviço $service definido"
        else
            check_error "Serviço $service não encontrado"
        fi
    done
    
    # Verificar labels do Traefik
    if grep -q "traefik.enable=true" portal-evolution.yaml; then
        check_ok "Labels do Traefik configurados"
    else
        check_warn "Labels do Traefik podem estar faltando"
    fi
else
    check_error "Arquivo portal-evolution.yaml não encontrado"
fi

echo ""
echo "📱 Verificando código da aplicação..."

# Verificar estrutura do app
if [[ -d "app" ]]; then
    if [[ -d "app/src" ]]; then
        check_ok "Código frontend encontrado (app/src/)"
    else
        check_warn "Código frontend não encontrado (app/src/)"
    fi
    
    if [[ -d "app/server" ]]; then
        check_ok "Código backend encontrado (app/server/)"
    else
        check_warn "Código backend não encontrado (app/server/)"
    fi
    
    if [[ -f "app/package.json" ]]; then
        check_ok "package.json do frontend encontrado"
    else
        check_warn "package.json do frontend não encontrado"
    fi
    
    if [[ -f "app/server/package.json" ]]; then
        check_ok "package.json do backend encontrado"
    else
        check_warn "package.json do backend não encontrado"
    fi
fi

echo ""
echo "🔧 Verificando ambiente Docker..."

# Verificar se Docker está disponível
if command -v docker &> /dev/null; then
    check_ok "Docker instalado"
    
    if docker info &> /dev/null; then
        check_ok "Docker rodando"
    else
        check_error "Docker não está rodando"
        check_info "Execute: sudo systemctl start docker"
    fi
else
    check_error "Docker não instalado"
    check_info "Instale Docker primeiro"
fi

# Verificar Docker Compose
if command -v docker-compose &> /dev/null; then
    check_ok "Docker Compose instalado"
else
    check_error "Docker Compose não instalado"
    check_info "Instale Docker Compose primeiro"
fi

# Verificar rede traefik (se Docker estiver disponível)
if command -v docker &> /dev/null && docker info &> /dev/null; then
    if docker network ls | grep -q traefik; then
        check_ok "Rede traefik existe"
    else
        check_warn "Rede traefik não encontrada"
        check_info "Execute: docker network create traefik"
    fi
fi

echo ""
echo "📊 RESUMO DA VALIDAÇÃO:"
echo "======================="
echo -e "${GREEN}✅ Sucessos: $success${NC}"
echo -e "${YELLOW}⚠️  Avisos: $warnings${NC}"
echo -e "${RED}❌ Erros: $errors${NC}"

echo ""
if [[ $errors -eq 0 ]]; then
    echo -e "${GREEN}🎉 VALIDAÇÃO PASSOU! Projeto está seguindo padrão da VPS.${NC}"
    echo ""
    echo "📋 Próximos passos:"
    echo "1. Configure o arquivo .env com seus dados"
    echo "2. Execute: bash scripts/preparar-scripts.sh"
    echo "3. Execute: bash scripts/iniciar-servico.sh"
    echo ""
    if [[ $warnings -gt 0 ]]; then
        echo -e "${YELLOW}⚠️  Há alguns avisos acima. Revise se necessário.${NC}"
    fi
else
    echo -e "${RED}❌ VALIDAÇÃO FALHOU! Corrija os erros antes de continuar.${NC}"
    echo ""
    echo "🔧 Ações recomendadas:"
    if [[ $errors -gt 0 ]]; then
        echo "- Corrija os arquivos/diretórios faltando"
        echo "- Verifique a estrutura do projeto"
        echo "- Configure o arquivo .env"
    fi
fi

echo ""
echo "📖 Para mais informações, consulte README-VPS.md"
