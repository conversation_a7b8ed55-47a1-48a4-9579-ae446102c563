-- MIGRAÇÃO PARA NOVA ESTRUTURA DE REFERÊNCIAS POR CÓDIGOS
-- <PERSON>ste script migra os dados existentes da estrutura baseada em IDs para códigos

-- ATENÇÃO: Execute este script apenas uma vez e faça backup antes!

BEGIN;

-- 1. <PERSON><PERSON>r tabelas temporárias para backup dos dados existentes
CREATE TABLE IF NOT EXISTS backup_produtos AS SELECT * FROM produtos;
CREATE TABLE IF NOT EXISTS backup_blocos AS SELECT * FROM blocos;
CREATE TABLE IF NOT EXISTS backup_sub_blocos AS SELECT * FROM sub_blocos;
CREATE TABLE IF NOT EXISTS backup_gavetas AS SELECT * FROM gavetas;
CREATE TABLE IF NOT EXISTS backup_numeracoes_gavetas AS SELECT * FROM numeracoes_gavetas;
CREATE TABLE IF NOT EXISTS backup_sepultamentos AS SELECT * FROM sepultamentos;
CREATE TABLE IF NOT EXISTS backup_logs_auditoria AS SELECT * FROM logs_auditoria;

-- 2. Remover constraints e índices antigos
DROP INDEX IF EXISTS idx_sepultamentos_gaveta;
DROP INDEX IF EXISTS idx_sepultamentos_cliente;

-- 3. Remover tabelas existentes (dados já estão no backup)
DROP TABLE IF EXISTS sepultamentos CASCADE;
DROP TABLE IF EXISTS gavetas CASCADE;
DROP TABLE IF EXISTS numeracoes_gavetas CASCADE;
DROP TABLE IF EXISTS sub_blocos CASCADE;
DROP TABLE IF EXISTS blocos CASCADE;
DROP TABLE IF EXISTS produtos CASCADE;

-- 3.1. Atualizar tabela de logs para incluir referências hierárquicas
-- Adicionar colunas de referência hierárquica se não existirem
ALTER TABLE logs_auditoria
ADD COLUMN IF NOT EXISTS codigo_cliente VARCHAR(50),
ADD COLUMN IF NOT EXISTS codigo_estacao VARCHAR(50),
ADD COLUMN IF NOT EXISTS codigo_bloco VARCHAR(50),
ADD COLUMN IF NOT EXISTS codigo_sub_bloco VARCHAR(50),
ADD COLUMN IF NOT EXISTS descricao TEXT;

-- Adicionar foreign key para codigo_cliente se não existir
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'logs_auditoria_codigo_cliente_fkey'
    ) THEN
        ALTER TABLE logs_auditoria
        ADD CONSTRAINT logs_auditoria_codigo_cliente_fkey
        FOREIGN KEY (codigo_cliente) REFERENCES clientes(codigo_cliente);
    END IF;
END $$;

-- 4. Recriar tabelas com nova estrutura
-- Tabela de produtos (ETENs e Ossuários)
CREATE TABLE produtos (
    codigo_cliente VARCHAR(50) NOT NULL,
    codigo_estacao VARCHAR(50) NOT NULL,
    meses_para_exumar INTEGER NOT NULL DEFAULT 24,
    denominacao VARCHAR(255) NOT NULL,
    observacao TEXT,
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (codigo_cliente, codigo_estacao),
    FOREIGN KEY (codigo_cliente) REFERENCES clientes(codigo_cliente)
);

-- Tabela de blocos
CREATE TABLE blocos (
    codigo_cliente VARCHAR(50) NOT NULL,
    codigo_estacao VARCHAR(50) NOT NULL,
    codigo_bloco VARCHAR(50) NOT NULL,
    denominacao VARCHAR(255) NOT NULL,
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (codigo_cliente, codigo_estacao, codigo_bloco),
    FOREIGN KEY (codigo_cliente, codigo_estacao) REFERENCES produtos(codigo_cliente, codigo_estacao)
);

-- Tabela de sub-blocos
CREATE TABLE sub_blocos (
    codigo_cliente VARCHAR(50) NOT NULL,
    codigo_estacao VARCHAR(50) NOT NULL,
    codigo_bloco VARCHAR(50) NOT NULL,
    codigo_sub_bloco VARCHAR(50) NOT NULL,
    denominacao VARCHAR(255) NOT NULL,
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco),
    FOREIGN KEY (codigo_cliente, codigo_estacao, codigo_bloco) REFERENCES blocos(codigo_cliente, codigo_estacao, codigo_bloco)
);

-- Tabela de numerações de gavetas
CREATE TABLE numeracoes_gavetas (
    codigo_cliente VARCHAR(50) NOT NULL,
    codigo_estacao VARCHAR(50) NOT NULL,
    codigo_bloco VARCHAR(50) NOT NULL,
    codigo_sub_bloco VARCHAR(50) NOT NULL,
    numero_inicio INTEGER NOT NULL,
    numero_fim INTEGER NOT NULL,
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_inicio),
    FOREIGN KEY (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco) 
        REFERENCES sub_blocos(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco)
);

-- Tabela de gavetas
CREATE TABLE gavetas (
    codigo_cliente VARCHAR(50) NOT NULL,
    codigo_estacao VARCHAR(50) NOT NULL,
    codigo_bloco VARCHAR(50) NOT NULL,
    codigo_sub_bloco VARCHAR(50) NOT NULL,
    numero_gaveta INTEGER NOT NULL,
    posicao_x INTEGER DEFAULT 1,
    posicao_y INTEGER DEFAULT 1,
    altura_especial DECIMAL(3,1) DEFAULT 1.0,
    disponivel BOOLEAN DEFAULT true,
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta),
    FOREIGN KEY (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco) 
        REFERENCES sub_blocos(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco)
);

-- Tabela de sepultamentos
CREATE TABLE sepultamentos (
    id SERIAL PRIMARY KEY,
    codigo_cliente VARCHAR(50) NOT NULL,
    codigo_estacao VARCHAR(50) NOT NULL,
    codigo_bloco VARCHAR(50) NOT NULL,
    codigo_sub_bloco VARCHAR(50) NOT NULL,
    numero_gaveta INTEGER NOT NULL,
    nome_sepultado VARCHAR(255) NOT NULL,
    data_sepultamento DATE NOT NULL,
    hora_sepultamento TIME NOT NULL,
    data_exumacao TIMESTAMP NULL,
    observacoes TEXT,
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta) 
        REFERENCES gavetas(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta)
);

-- 5. Migrar dados dos backups para as novas tabelas
-- Produtos
INSERT INTO produtos (codigo_cliente, codigo_estacao, meses_para_exumar, denominacao, observacao, ativo, created_at, updated_at)
SELECT codigo_cliente, codigo_estacao, meses_para_exumar, denominacao, observacao, ativo, created_at, updated_at
FROM backup_produtos;

-- Blocos
INSERT INTO blocos (codigo_cliente, codigo_estacao, codigo_bloco, denominacao, ativo, created_at, updated_at)
SELECT bp.codigo_cliente, bp.codigo_estacao, bb.codigo_bloco, bb.denominacao, bb.ativo, bb.created_at, bb.updated_at
FROM backup_blocos bb
JOIN backup_produtos bp ON bb.produto_id = bp.id;

-- Sub-blocos
INSERT INTO sub_blocos (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, denominacao, ativo, created_at, updated_at)
SELECT bp.codigo_cliente, bp.codigo_estacao, bb.codigo_bloco, bsb.codigo_sub_bloco, bsb.denominacao, bsb.ativo, bsb.created_at, bsb.updated_at
FROM backup_sub_blocos bsb
JOIN backup_blocos bb ON bsb.bloco_id = bb.id
JOIN backup_produtos bp ON bb.produto_id = bp.id;

-- Numerações de gavetas
INSERT INTO numeracoes_gavetas (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_inicio, numero_fim, ativo, created_at, updated_at)
SELECT bp.codigo_cliente, bp.codigo_estacao, bb.codigo_bloco, bsb.codigo_sub_bloco, bng.numero_inicio, bng.numero_fim, bng.ativo, bng.created_at, bng.updated_at
FROM backup_numeracoes_gavetas bng
JOIN backup_sub_blocos bsb ON bng.sub_bloco_id = bsb.id
JOIN backup_blocos bb ON bsb.bloco_id = bb.id
JOIN backup_produtos bp ON bb.produto_id = bp.id;

-- Gavetas
INSERT INTO gavetas (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta, disponivel, ativo, created_at, updated_at)
SELECT bp.codigo_cliente, bp.codigo_estacao, bb.codigo_bloco, bsb.codigo_sub_bloco, bg.numero_gaveta, bg.disponivel, bg.ativo, bg.created_at, bg.updated_at
FROM backup_gavetas bg
JOIN backup_sub_blocos bsb ON bg.sub_bloco_id = bsb.id
JOIN backup_blocos bb ON bsb.bloco_id = bb.id
JOIN backup_produtos bp ON bb.produto_id = bp.id;

-- Sepultamentos
INSERT INTO sepultamentos (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta, nome_sepultado, data_sepultamento, hora_sepultamento, data_exumacao, observacoes, ativo, created_at, updated_at)
SELECT bp.codigo_cliente, bp.codigo_estacao, bb.codigo_bloco, bsb.codigo_sub_bloco, bg.numero_gaveta, bs.nome_sepultado, bs.data_sepultamento, bs.hora_sepultamento, bs.data_exumacao, bs.observacoes, bs.ativo, bs.created_at, bs.updated_at
FROM backup_sepultamentos bs
JOIN backup_gavetas bg ON bs.gaveta_id = bg.id
JOIN backup_sub_blocos bsb ON bg.sub_bloco_id = bsb.id
JOIN backup_blocos bb ON bsb.bloco_id = bb.id
JOIN backup_produtos bp ON bb.produto_id = bp.id;

-- 6. Recriar índices
CREATE INDEX idx_usuarios_email ON usuarios(email);
CREATE INDEX idx_usuarios_codigo_cliente ON usuarios(codigo_cliente);
CREATE INDEX idx_clientes_codigo ON clientes(codigo_cliente);
CREATE INDEX idx_produtos_codigo_cliente ON produtos(codigo_cliente);
CREATE INDEX idx_produtos_codigo_estacao ON produtos(codigo_cliente, codigo_estacao);
CREATE INDEX idx_blocos_codigo_cliente ON blocos(codigo_cliente);
CREATE INDEX idx_blocos_codigo_estacao ON blocos(codigo_cliente, codigo_estacao);
CREATE INDEX idx_blocos_codigo_bloco ON blocos(codigo_cliente, codigo_estacao, codigo_bloco);
CREATE INDEX idx_sub_blocos_codigo_cliente ON sub_blocos(codigo_cliente);
CREATE INDEX idx_sub_blocos_codigo_estacao ON sub_blocos(codigo_cliente, codigo_estacao);
CREATE INDEX idx_sub_blocos_codigo_bloco ON sub_blocos(codigo_cliente, codigo_estacao, codigo_bloco);
CREATE INDEX idx_gavetas_codigo_cliente ON gavetas(codigo_cliente);
CREATE INDEX idx_gavetas_codigo_estacao ON gavetas(codigo_cliente, codigo_estacao);
CREATE INDEX idx_gavetas_codigo_bloco ON gavetas(codigo_cliente, codigo_estacao, codigo_bloco);
CREATE INDEX idx_gavetas_disponivel ON gavetas(disponivel);
CREATE INDEX idx_sepultamentos_codigo_cliente ON sepultamentos(codigo_cliente);
CREATE INDEX idx_sepultamentos_codigo_estacao ON sepultamentos(codigo_cliente, codigo_estacao);
CREATE INDEX idx_sepultamentos_codigo_bloco ON sepultamentos(codigo_cliente, codigo_estacao, codigo_bloco);
CREATE INDEX idx_sepultamentos_gaveta ON sepultamentos(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta);
CREATE INDEX idx_sepultamentos_data ON sepultamentos(data_sepultamento);
CREATE INDEX idx_logs_usuario ON logs_auditoria(usuario_id);
CREATE INDEX idx_logs_data ON logs_auditoria(created_at);
CREATE INDEX idx_logs_codigo_cliente ON logs_auditoria(codigo_cliente);
CREATE INDEX idx_logs_codigo_estacao ON logs_auditoria(codigo_cliente, codigo_estacao);
CREATE INDEX idx_logs_codigo_bloco ON logs_auditoria(codigo_cliente, codigo_estacao, codigo_bloco);
CREATE INDEX idx_logs_codigo_sub_bloco ON logs_auditoria(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco);
CREATE INDEX idx_logs_acao ON logs_auditoria(acao);
CREATE INDEX idx_logs_tabela ON logs_auditoria(tabela_afetada);

COMMIT;

-- Verificar se a migração foi bem-sucedida
SELECT 'Migração concluída com sucesso!' as status;
SELECT 'Produtos migrados: ' || COUNT(*) FROM produtos;
SELECT 'Blocos migrados: ' || COUNT(*) FROM blocos;
SELECT 'Sub-blocos migrados: ' || COUNT(*) FROM sub_blocos;
SELECT 'Gavetas migradas: ' || COUNT(*) FROM gavetas;
SELECT 'Sepultamentos migrados: ' || COUNT(*) FROM sepultamentos;
