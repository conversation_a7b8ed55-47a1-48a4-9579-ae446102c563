# Dockerfile para Portal Cliente - Backend Node.js
FROM node:18-alpine

# Definir diretório de trabalho
WORKDIR /app

# Copiar arquivos de dependências do servidor
COPY server/package*.json ./

# Instalar dependências
RUN npm ci --only=production

# Copiar código fonte do servidor
COPY server/ ./

# Criar usuário não-root para segurança
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# Mudar propriedade dos arquivos
RUN chown -R nodejs:nodejs /app
USER nodejs

# Expor porta 3001
EXPOSE 3001

# Comando para iniciar o servidor
CMD ["node", "index.js"]
