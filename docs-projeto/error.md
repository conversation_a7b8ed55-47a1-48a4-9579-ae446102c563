🏁 LoginPage renderizado - timestamp: 15:40:55
LoginPage.jsx:45 🔍 Estados atuais: {email: '', senha: '', loading: false, error: '', errorPersistente: '', …}
LoginPage.jsx:30 🏁 LoginPage renderizado - timestamp: 15:40:55
LoginPage.jsx:45 🔍 Estados atuais: {email: '', senha: '', loading: false, error: '', errorPersistente: '', …}
LoginPage.jsx:48 👤 useEffect user executado, user: null
LoginPage.jsx:54 👤 Nenhum usuário logado
LoginPage.jsx:64 🟢 ERRO LIMPO
LoginPage.jsx:65 🟢 Timestamp: 15:40:56
LoginPage.jsx:75 🧹 ERRO PERSISTENTE LIMPO
LoginPage.jsx:76 🧹 Timestamp: 15:40:56
LoginPage.jsx:48 👤 useEffect user executado, user: null
LoginPage.jsx:54 👤 Nenhum usuário logado
LoginPage.jsx:64 🟢 ERRO LIMPO
LoginPage.jsx:65 🟢 Timestamp: 15:40:56
LoginPage.jsx:75 🧹 ERRO PERSISTENTE LIMPO
LoginPage.jsx:76 🧹 Timestamp: 15:40:56
LoginPage.jsx:30 🏁 LoginPage renderizado - timestamp: 15:40:56
LoginPage.jsx:45 🔍 Estados atuais: {email: '', senha: '', loading: false, error: '', errorPersistente: '', …}
LoginPage.jsx:30 🏁 LoginPage renderizado - timestamp: 15:40:56
LoginPage.jsx:45 🔍 Estados atuais: {email: '', senha: '', loading: false, error: '', errorPersistente: '', …}