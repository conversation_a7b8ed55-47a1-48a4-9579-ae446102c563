#!/bin/bash

# Script de teste rápido para VPS
# Execute na VPS: bash teste-rapido-vps.sh

echo "🧪 Teste Rápido - Portal Evolution"
echo "=================================="

# Verificar se é root
if [[ $EUID -ne 0 ]]; then
   echo "⚠️  Execute como root para melhor resultado"
fi

echo ""
echo "📋 Verificando sistema..."

# Verificar sistema operacional
echo "🖥️  Sistema: $(lsb_release -d 2>/dev/null | cut -f2 || echo "$(uname -s) $(uname -r)")"

# Verificar recursos
echo "💾 Memória: $(free -h | grep Mem | awk '{print $2}')"
echo "💿 Disco: $(df -h / | tail -1 | awk '{print $4}') disponível"

# Verificar Docker
echo ""
echo "🐳 Verificando Docker..."
if command -v docker &> /dev/null; then
    echo "✅ Docker instalado: $(docker --version)"
    if systemctl is-active --quiet docker; then
        echo "✅ Docker rodando"
    else
        echo "❌ Docker não está rodando"
    fi
else
    echo "❌ Docker não instalado"
fi

# Verificar Docker Compose
if command -v docker-compose &> /dev/null; then
    echo "✅ Docker Compose instalado: $(docker-compose --version)"
else
    echo "❌ Docker Compose não instalado"
fi

# Verificar firewall
echo ""
echo "🔥 Verificando firewall..."
if command -v ufw &> /dev/null; then
    if ufw status | grep -q "Status: active"; then
        echo "✅ UFW ativo"
        echo "📋 Portas abertas:"
        ufw status | grep ALLOW | head -5
    else
        echo "⚠️  UFW não está ativo"
    fi
else
    echo "❌ UFW não instalado"
fi

# Verificar diretório do projeto
echo ""
echo "📁 Verificando projeto..."
if [[ -d "/opt/portal-evolution" ]]; then
    echo "✅ Diretório /opt/portal-evolution existe"
    
    if [[ -d "/opt/portal-evolution/portalcliente" ]]; then
        echo "✅ Código do portal encontrado"
        
        if [[ -f "/opt/portal-evolution/portalcliente/configuracao.env" ]]; then
            echo "✅ Arquivo de configuração existe"
            
            # Verificar se senhas foram alteradas
            if grep -q "SUA_SENHA_SEGURA_AQUI" /opt/portal-evolution/portalcliente/configuracao.env; then
                echo "⚠️  DB_PASSWORD ainda está com valor padrão"
            else
                echo "✅ DB_PASSWORD foi alterado"
            fi
            
            if grep -q "SUA_CHAVE_JWT_MUITO_SEGURA_AQUI" /opt/portal-evolution/portalcliente/configuracao.env; then
                echo "⚠️  JWT_SECRET ainda está com valor padrão"
            else
                echo "✅ JWT_SECRET foi alterado"
            fi
        else
            echo "❌ Arquivo configuracao.env não encontrado"
        fi
        
        # Verificar scripts
        scripts=("deploy.sh" "atualizar.sh" "setup-manual-vps.sh")
        for script in "${scripts[@]}"; do
            if [[ -f "/opt/portal-evolution/portalcliente/$script" ]]; then
                if [[ -x "/opt/portal-evolution/portalcliente/$script" ]]; then
                    echo "✅ $script executável"
                else
                    echo "⚠️  $script não é executável"
                fi
            else
                echo "❌ $script não encontrado"
            fi
        done
    else
        echo "❌ Código do portal não encontrado"
    fi
else
    echo "❌ Diretório do projeto não existe"
fi

# Verificar serviço systemd
echo ""
echo "🔧 Verificando auto-start..."
if systemctl list-unit-files | grep -q portal-evolution; then
    echo "✅ Serviço portal-evolution configurado"
    if systemctl is-enabled --quiet portal-evolution; then
        echo "✅ Auto-start habilitado"
    else
        echo "⚠️  Auto-start não habilitado"
    fi
    
    if systemctl is-active --quiet portal-evolution; then
        echo "✅ Serviço rodando"
    else
        echo "⚠️  Serviço não está rodando"
    fi
else
    echo "❌ Serviço não configurado"
fi

# Verificar containers
echo ""
echo "📦 Verificando containers..."
if command -v docker &> /dev/null && systemctl is-active --quiet docker; then
    containers=$(docker ps --format "table {{.Names}}\t{{.Status}}" | grep portal)
    if [[ -n "$containers" ]]; then
        echo "✅ Containers do portal rodando:"
        echo "$containers"
    else
        echo "⚠️  Nenhum container do portal rodando"
    fi
else
    echo "❌ Docker não disponível"
fi

# Verificar conectividade
echo ""
echo "🌐 Verificando conectividade..."
if curl -s --connect-timeout 5 http://localhost/health > /dev/null; then
    echo "✅ Frontend respondendo"
else
    echo "❌ Frontend não responde"
fi

if curl -s --connect-timeout 5 http://localhost/api/health > /dev/null; then
    echo "✅ Backend respondendo"
else
    echo "❌ Backend não responde"
fi

# Verificar portas
echo ""
echo "🔌 Verificando portas..."
if netstat -tlnp 2>/dev/null | grep -q ":80 "; then
    echo "✅ Porta 80 em uso"
else
    echo "❌ Porta 80 não está em uso"
fi

if netstat -tlnp 2>/dev/null | grep -q ":3001 "; then
    echo "✅ Porta 3001 em uso"
else
    echo "❌ Porta 3001 não está em uso"
fi

# Resumo e recomendações
echo ""
echo "📊 RESUMO E RECOMENDAÇÕES:"
echo "=========================="

# Verificar se sistema está pronto
ready=true

if ! command -v docker &> /dev/null; then
    echo "❌ Instale Docker primeiro"
    ready=false
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Instale Docker Compose primeiro"
    ready=false
fi

if [[ ! -d "/opt/portal-evolution/portalcliente" ]]; then
    echo "❌ Baixe o código do projeto primeiro"
    ready=false
fi

if [[ -f "/opt/portal-evolution/portalcliente/configuracao.env" ]]; then
    if grep -q "SUA_SENHA_SEGURA_AQUI\|SUA_CHAVE_JWT_MUITO_SEGURA_AQUI" /opt/portal-evolution/portalcliente/configuracao.env; then
        echo "⚠️  Configure senhas em configuracao.env"
        ready=false
    fi
fi

if $ready; then
    echo ""
    echo "🎉 SISTEMA PRONTO PARA DEPLOY!"
    echo ""
    echo "📋 Próximos passos:"
    echo "1. cd /opt/portal-evolution/portalcliente"
    echo "2. bash deploy.sh"
    echo "3. Acessar: http://$(curl -s ifconfig.me 2>/dev/null || echo "SEU_IP")"
else
    echo ""
    echo "⚠️  SISTEMA NÃO ESTÁ PRONTO"
    echo ""
    echo "📋 Execute primeiro:"
    echo "bash setup-manual-vps.sh"
    echo "nano /opt/portal-evolution/portalcliente/configuracao.env"
fi

echo ""
echo "🔧 Comandos úteis:"
echo "Monitor: bash /opt/portal-evolution/monitor.sh"
echo "Backup:  bash /opt/portal-evolution/backup.sh"
echo "Logs:    journalctl -u portal-evolution -f"
