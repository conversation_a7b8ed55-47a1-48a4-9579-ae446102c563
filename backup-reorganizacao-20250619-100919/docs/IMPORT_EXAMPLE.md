# Exemplo de Importação de Dados - Referências Hierárquicas

## 🎯 Objetivo

Este documento mostra como importar dados de outras fontes usando as **referências hierárquicas por códigos** implementadas no sistema.

## 📋 Estrutura de Importação

### 1. **Ordem de Importação (Hierárquica)**

```
1. clientes
2. usuarios (opcional)
3. produtos
4. blocos
5. sub_blocos
6. numeracoes_gavetas
7. gavetas (geradas automaticamente)
8. sepultamentos
9. logs_auditoria (opcional)
```

## 📊 Exemplos de Dados para Importação

### 1. **Clientes**
```sql
INSERT INTO clientes (codigo_cliente, cnpj, nome_fantasia, razao_social, cep, logradouro, numero, bairro, cidade, estado) VALUES
('SAF_001', '18.384.274/0001-78', '<PERSON><PERSON> Cemitérios', 'Safra Cemitérios Ltda', '01310-100', 'Av. <PERSON>', '1000', 'Bela Vista', 'São Paulo', 'SP'),
('CEM_002', '12.345.678/0001-90', 'Cemitério Central', 'Cemitério Central S.A.', '04567-890', 'Rua das Flores', '500', 'Centro', 'Rio de Janeiro', 'RJ');
```

### 2. **Produtos/Estações**
```sql
INSERT INTO produtos (codigo_cliente, codigo_estacao, meses_para_exumar, denominacao, observacao) VALUES
('SAF_001', 'ETEN_001', 24, 'Estação de Tratamento 001', 'Estação principal'),
('SAF_001', 'ETEN_002', 36, 'Estação de Tratamento 002', 'Estação secundária'),
('CEM_002', 'OSS_001', 24, 'Ossuário Central', 'Ossuário principal');
```

### 3. **Blocos**
```sql
INSERT INTO blocos (codigo_cliente, codigo_estacao, codigo_bloco, denominacao) VALUES
('SAF_001', 'ETEN_001', 'A', 'Bloco A - Setor Norte'),
('SAF_001', 'ETEN_001', 'B', 'Bloco B - Setor Sul'),
('SAF_001', 'ETEN_002', 'A', 'Bloco A - Área Central'),
('CEM_002', 'OSS_001', '1', 'Bloco 1 - Térreo');
```

### 4. **Sub-blocos**
```sql
INSERT INTO sub_blocos (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, denominacao) VALUES
('SAF_001', 'ETEN_001', 'A', 'A1', 'Sub-bloco A1 - Lado Esquerdo'),
('SAF_001', 'ETEN_001', 'A', 'A2', 'Sub-bloco A2 - Lado Direito'),
('SAF_001', 'ETEN_001', 'B', 'B1', 'Sub-bloco B1 - Entrada'),
('SAF_001', 'ETEN_002', 'A', 'A1', 'Sub-bloco A1 - Principal'),
('CEM_002', 'OSS_001', '1', '1A', 'Sub-bloco 1A - Seção 1');
```

### 5. **Numerações de Gavetas**
```sql
INSERT INTO numeracoes_gavetas (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_inicio, numero_fim) VALUES
('SAF_001', 'ETEN_001', 'A', 'A1', 1, 50),
('SAF_001', 'ETEN_001', 'A', 'A2', 51, 100),
('SAF_001', 'ETEN_001', 'B', 'B1', 1, 30),
('SAF_001', 'ETEN_002', 'A', 'A1', 1, 75),
('CEM_002', 'OSS_001', '1', '1A', 1, 40);
```

### 6. **Gavetas (Geradas Automaticamente)**
```sql
-- Exemplo: Para SAF_001, ETEN_001, A, A1 (gavetas 1-50)
INSERT INTO gavetas (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta, disponivel) VALUES
('SAF_001', 'ETEN_001', 'A', 'A1', 1, true),
('SAF_001', 'ETEN_001', 'A', 'A1', 2, true),
-- ... (continua até 50)
('SAF_001', 'ETEN_001', 'A', 'A1', 50, true);
```

### 7. **Sepultamentos**
```sql
INSERT INTO sepultamentos (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta, nome_sepultado, data_sepultamento, hora_sepultamento, observacoes) VALUES
('SAF_001', 'ETEN_001', 'A', 'A1', 1, 'João Silva Santos', '2024-01-15', '14:30:00', 'Sepultamento realizado conforme protocolo'),
('SAF_001', 'ETEN_001', 'A', 'A1', 2, 'Maria Oliveira Costa', '2024-01-20', '10:15:00', 'Família presente na cerimônia'),
('CEM_002', 'OSS_001', '1', '1A', 1, 'Pedro Almeida', '2024-02-01', '16:00:00', 'Transferência de outro cemitério');
```

## 🔧 Scripts de Importação

### Script SQL Completo
```sql
-- 1. Importar Clientes
INSERT INTO clientes (codigo_cliente, cnpj, nome_fantasia, razao_social, cidade, estado) VALUES
('IMPORT_001', '11.111.111/0001-11', 'Cemitério Importado', 'Cemitério Importado Ltda', 'São Paulo', 'SP');

-- 2. Importar Produtos
INSERT INTO produtos (codigo_cliente, codigo_estacao, meses_para_exumar, denominacao) VALUES
('IMPORT_001', 'EST_001', 24, 'Estação Importada 001');

-- 3. Importar Blocos
INSERT INTO blocos (codigo_cliente, codigo_estacao, codigo_bloco, denominacao) VALUES
('IMPORT_001', 'EST_001', 'X', 'Bloco X - Importado');

-- 4. Importar Sub-blocos
INSERT INTO sub_blocos (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, denominacao) VALUES
('IMPORT_001', 'EST_001', 'X', 'X1', 'Sub-bloco X1 - Importado');

-- 5. Importar Numerações
INSERT INTO numeracoes_gavetas (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_inicio, numero_fim) VALUES
('IMPORT_001', 'EST_001', 'X', 'X1', 1, 20);

-- 6. Gerar Gavetas (pode ser feito via script ou API)
INSERT INTO gavetas (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta, disponivel)
SELECT 'IMPORT_001', 'EST_001', 'X', 'X1', generate_series(1, 20), true;

-- 7. Importar Sepultamentos
INSERT INTO sepultamentos (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta, nome_sepultado, data_sepultamento, hora_sepultamento) VALUES
('IMPORT_001', 'EST_001', 'X', 'X1', 1, 'Pessoa Importada', '2024-01-01', '12:00:00');
```

## 📝 Script Node.js para Importação

```javascript
const { Pool } = require('pg');

const pool = new Pool({
  // configuração do banco
});

async function importarDados(dadosImportacao) {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    // 1. Importar cliente
    await client.query(`
      INSERT INTO clientes (codigo_cliente, cnpj, nome_fantasia, razao_social)
      VALUES ($1, $2, $3, $4)
      ON CONFLICT (codigo_cliente) DO NOTHING
    `, [dadosImportacao.cliente.codigo, dadosImportacao.cliente.cnpj, 
        dadosImportacao.cliente.nome, dadosImportacao.cliente.razao]);
    
    // 2. Importar produtos
    for (const produto of dadosImportacao.produtos) {
      await client.query(`
        INSERT INTO produtos (codigo_cliente, codigo_estacao, denominacao, meses_para_exumar)
        VALUES ($1, $2, $3, $4)
        ON CONFLICT (codigo_cliente, codigo_estacao) DO NOTHING
      `, [dadosImportacao.cliente.codigo, produto.codigo_estacao, 
          produto.denominacao, produto.meses_para_exumar]);
    }
    
    // 3. Importar blocos, sub-blocos, etc...
    // (continuar com a hierarquia)
    
    await client.query('COMMIT');
    console.log('✅ Importação concluída com sucesso');
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Erro na importação:', error);
    throw error;
  } finally {
    client.release();
  }
}
```

## 🔍 Validação de Dados Importados

### Consultas de Verificação
```sql
-- Verificar hierarquia completa
SELECT 
  c.codigo_cliente,
  c.nome_fantasia,
  p.codigo_estacao,
  p.denominacao as produto,
  b.codigo_bloco,
  b.denominacao as bloco,
  sb.codigo_sub_bloco,
  sb.denominacao as sub_bloco,
  COUNT(g.numero_gaveta) as total_gavetas,
  COUNT(s.id) as total_sepultamentos
FROM clientes c
LEFT JOIN produtos p ON c.codigo_cliente = p.codigo_cliente
LEFT JOIN blocos b ON p.codigo_cliente = b.codigo_cliente AND p.codigo_estacao = b.codigo_estacao
LEFT JOIN sub_blocos sb ON b.codigo_cliente = sb.codigo_cliente AND b.codigo_estacao = sb.codigo_estacao AND b.codigo_bloco = sb.codigo_bloco
LEFT JOIN gavetas g ON sb.codigo_cliente = g.codigo_cliente AND sb.codigo_estacao = g.codigo_estacao AND sb.codigo_bloco = g.codigo_bloco AND sb.codigo_sub_bloco = g.codigo_sub_bloco
LEFT JOIN sepultamentos s ON g.codigo_cliente = s.codigo_cliente AND g.codigo_estacao = s.codigo_estacao AND g.codigo_bloco = s.codigo_bloco AND g.codigo_sub_bloco = s.codigo_sub_bloco AND g.numero_gaveta = s.numero_gaveta
GROUP BY c.codigo_cliente, c.nome_fantasia, p.codigo_estacao, p.denominacao, b.codigo_bloco, b.denominacao, sb.codigo_sub_bloco, sb.denominacao
ORDER BY c.codigo_cliente, p.codigo_estacao, b.codigo_bloco, sb.codigo_sub_bloco;
```

## ✅ Vantagens da Nova Estrutura

1. **🔗 Relacionamento Direto**: Não precisa de JOINs complexos por IDs
2. **📥 Importação Simples**: Dados podem ser importados diretamente com códigos
3. **🔄 Sincronização Fácil**: Sistemas externos podem referenciar por códigos
4. **📊 Consultas Intuitivas**: Filtros por códigos são mais legíveis
5. **🛡️ Integridade**: Foreign keys garantem consistência hierárquica
6. **📝 Logs Contextuais**: Logs incluem contexto hierárquico completo

---

**🎉 Pronto!** Sua estrutura está otimizada para importação de dados de qualquer fonte externa usando referências hierárquicas por códigos.
