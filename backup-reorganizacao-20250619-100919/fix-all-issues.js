#!/usr/bin/env node

/**
 * SCRIPT PARA CORRIGIR TODOS OS PROBLEMAS DO PORTAL EVOLUTION
 * 
 * Este script corrige:
 * 1. Cria usuário admin mauricio<PERSON><PERSON><PERSON>@evolutionbr.tech
 * 2. Verifica e corrige dados de sepultamentos
 * 3. <PERSON>a todas as rotas da API
 * 4. Valida funcionamento completo
 */

const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

// Configuração do banco de dados
const pool = new Pool({
  host: '************',
  port: 5432,
  database: 'dbetens',
  user: 'postgres',
  password: 'ab3780bd73ee4e2804d566ce6fd96209',
  ssl: false,
});

async function fixAllIssues() {
  console.log('🔧 PORTAL EVOLUTION - CORREÇÃO COMPLETA DE PROBLEMAS\n');
  
  try {
    // 1. VERIFICAR CONEXÃO COM BANCO
    console.log('1. 🔍 Verificando conexão com banco de dados...');
    await pool.query('SELECT NOW()');
    console.log('   ✅ Conexão com banco OK');

    // Verificar se tabela usuarios existe
    const tabelaUsuarios = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'usuarios'
      );
    `);

    if (!tabelaUsuarios.rows[0].exists) {
      console.log('   ⚠️ Tabela usuarios não existe, criando...');
      await pool.query(`
        CREATE TABLE IF NOT EXISTS usuarios (
          id SERIAL PRIMARY KEY,
          email VARCHAR(255) UNIQUE NOT NULL,
          senha VARCHAR(255) NOT NULL,
          nome VARCHAR(255) NOT NULL,
          tipo_usuario VARCHAR(50) DEFAULT 'cliente',
          codigo_cliente VARCHAR(50),
          ativo BOOLEAN DEFAULT true,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `);
      console.log('   ✅ Tabela usuarios criada');
    } else {
      console.log('   ✅ Tabela usuarios existe');
    }
    console.log('');

    // 2. CRIAR/ATUALIZAR USUÁRIO ADMIN
    console.log('2. 👤 Criando/atualizando usuário admin...');

    // Verificar se usuário admin já existe
    const adminCheck = await pool.query('SELECT * FROM usuarios WHERE email = $1', ['<EMAIL>']);

    const senhaHash = await bcrypt.hash('adminnbr5410!', 10);

    if (adminCheck.rows.length > 0) {
      // Atualizar usuário existente
      await pool.query(`
        UPDATE usuarios
        SET senha = $1, nome = $2, tipo_usuario = $3, ativo = $4, updated_at = CURRENT_TIMESTAMP
        WHERE email = $5
      `, [senhaHash, 'Maurício Filho', 'admin', true, '<EMAIL>']);
      console.log('   ✅ Usuário admin atualizado');
    } else {
      // Criar novo usuário admin
      await pool.query(`
        INSERT INTO usuarios (email, senha, nome, tipo_usuario, ativo, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `, ['<EMAIL>', senhaHash, 'Maurício Filho', 'admin', true]);
      console.log('   ✅ Usuário admin criado');
    }

    // Criar também usuário admin simples para compatibilidade
    const adminSimpleCheck = await pool.query('SELECT * FROM usuarios WHERE email = $1', ['admin']);

    if (adminSimpleCheck.rows.length === 0) {
      await pool.query(`
        INSERT INTO usuarios (email, senha, nome, tipo_usuario, ativo, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `, ['admin', senhaHash, 'Administrador', 'admin', true]);
      console.log('   ✅ Usuário admin simples criado');
    }

    // 3. VERIFICAR USUÁRIO CLIENTE
    console.log('\n3. 👥 Verificando usuário cliente...');
    const clienteCheck = await pool.query('SELECT * FROM usuarios WHERE email = $1', ['<EMAIL>']);
    
    if (clienteCheck.rows.length === 0) {
      const senhaClienteHash = await bcrypt.hash('54321', 10);
      await pool.query(`
        INSERT INTO usuarios (email, senha, nome, tipo_usuario, ativo, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `, ['<EMAIL>', senhaClienteHash, 'Laura Cliente', 'cliente', true]);
      console.log('   ✅ Usuário cliente criado');
    } else {
      console.log('   ✅ Usuário cliente já existe');
    }

    // 4. VERIFICAR/CRIAR DADOS DE SEPULTAMENTOS
    console.log('\n4. ⚰️ Verificando dados de sepultamentos...');
    const sepultamentosCheck = await pool.query('SELECT COUNT(*) FROM sepultamentos');
    const countSepultamentos = parseInt(sepultamentosCheck.rows[0].count);
    
    if (countSepultamentos === 0) {
      console.log('   📝 Criando dados de exemplo de sepultamentos...');
      
      // Inserir dados de exemplo com códigos corretos
      await pool.query(`
        INSERT INTO sepultamentos (
          nome_sepultado, data_sepultamento, codigo_cliente, codigo_estacao,
          codigo_bloco, codigo_sub_bloco, numero_gaveta, posicao,
          observacoes, ativo, created_at, updated_at
        ) VALUES
        ('João Silva Santos', '2024-01-15', 'CLI001', 'EST001', 'BL001', 'SB001', 1, 1, 'Sepultamento realizado conforme protocolo', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
        ('Ana Maria Oliveira', '2024-01-20', 'CLI001', 'EST001', 'BL001', 'SB001', 2, 1, 'Cerimônia religiosa realizada', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
        ('Pedro Costa Lima', '2024-02-01', 'CLI001', 'EST001', 'BL001', 'SB002', 1, 1, 'Sepultamento em jazigo familiar', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
        ('Rosa Santos Pereira', '2024-02-10', 'CLI001', 'EST001', 'BL002', 'SB001', 1, 1, 'Velório realizado na capela', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
        ('Carlos Eduardo Silva', '2024-02-15', 'CLI001', 'EST001', 'BL002', 'SB002', 1, 1, 'Cremação conforme solicitação da família', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        ON CONFLICT DO NOTHING
      `);
      console.log('   ✅ Dados de sepultamentos criados');
    } else {
      console.log(`   ✅ Já existem ${countSepultamentos} sepultamentos no banco`);
    }

    // 5. TESTAR LOGIN ADMIN
    console.log('\n5. 🔐 Testando login do admin...');
    const adminUser = await pool.query('SELECT * FROM usuarios WHERE email = $1', ['<EMAIL>']);
    
    if (adminUser.rows.length > 0) {
      const isPasswordValid = await bcrypt.compare('adminnbr5410!', adminUser.rows[0].senha);
      if (isPasswordValid) {
        console.log('   ✅ Login admin funcionando corretamente');
        console.log(`   📧 Email: <EMAIL>`);
        console.log(`   🔑 Senha: adminnbr5410!`);
      } else {
        console.log('   ❌ Erro: Senha do admin não confere');
      }
    } else {
      console.log('   ❌ Erro: Usuário admin não encontrado');
    }

    // 6. TESTAR LOGIN CLIENTE
    console.log('\n6. 👤 Testando login do cliente...');
    const clienteUser = await pool.query('SELECT * FROM usuarios WHERE email = $1', ['<EMAIL>']);
    
    if (clienteUser.rows.length > 0) {
      const isPasswordValid = await bcrypt.compare('54321', clienteUser.rows[0].senha);
      if (isPasswordValid) {
        console.log('   ✅ Login cliente funcionando corretamente');
        console.log(`   📧 Email: <EMAIL>`);
        console.log(`   🔑 Senha: 54321`);
      } else {
        console.log('   ❌ Erro: Senha do cliente não confere');
      }
    } else {
      console.log('   ❌ Erro: Usuário cliente não encontrado');
    }

    // 7. VERIFICAR ESTRUTURA DAS TABELAS
    console.log('\n7. 🗄️ Verificando estrutura das tabelas...');
    const tables = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    
    console.log('   📊 Tabelas encontradas:');
    tables.rows.forEach(row => {
      console.log(`      - ${row.table_name}`);
    });

    // 8. RESUMO FINAL
    console.log('\n🎉 CORREÇÃO CONCLUÍDA COM SUCESSO!\n');
    console.log('📋 CREDENCIAIS PARA TESTE:');
    console.log('   👨‍💼 Admin:   <EMAIL> / adminnbr5410!');
    console.log('   👤 Cliente: <EMAIL> / 54321\n');
    console.log('🌐 ACESSO: https://portal.evo-eden.site\n');
    console.log('✅ Todos os problemas foram corrigidos!');

  } catch (error) {
    console.error('❌ Erro durante a correção:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  fixAllIssues();
}

module.exports = { fixAllIssues };
