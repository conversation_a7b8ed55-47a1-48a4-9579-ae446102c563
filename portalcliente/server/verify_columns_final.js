const { Pool } = require('pg');

// Configuração direta do banco de dados
const pool = new Pool({
  user: 'postgres',
  host: '************',
  database: 'dbetens',
  password: 'ab3780bd73ee4e2804d566ce6fd96209',
  port: 5432,
});

async function verifyColumnsFinal() {
  console.log('🔍 VERIFICAÇÃO FINAL DAS COLUNAS HIERÁRQUICAS');
  console.log('=' .repeat(80));
  console.log('Banco: dbetens @ ************:5432');
  console.log('=' .repeat(80));

  const client = await pool.connect();
  
  try {
    const tables = [
      { name: 'blocos', expected: ['codigo_cliente', 'codigo_estacao', 'codigo_bloco'] },
      { name: 'sub_blocos', expected: ['codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco'] },
      { name: 'gavetas', expected: ['codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco'] },
      { name: 'numeracoes_gavetas', expected: ['codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco'] },
      { name: 'sepultamentos', expected: ['codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco'] },
      { name: 'logs_auditoria', expected: ['codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco'] }
    ];
    
    let allTablesComplete = true;
    let totalHierarchicalColumns = 0;
    
    for (const table of tables) {
      console.log(`\n📋 VERIFICANDO TABELA: ${table.name.toUpperCase()}`);
      console.log('-'.repeat(60));
      
      // Verificar se a tabela existe
      const tableExists = await client.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = $1
        )
      `, [table.name]);
      
      if (!tableExists.rows[0].exists) {
        console.log(`   ❌ TABELA NÃO EXISTE!`);
        allTablesComplete = false;
        continue;
      }
      
      console.log(`   ✅ Tabela existe`);
      
      // Verificar cada coluna hierárquica esperada
      const foundColumns = [];
      const missingColumns = [];
      
      for (const expectedCol of table.expected) {
        const columnExists = await client.query(`
          SELECT 
            column_name,
            data_type,
            is_nullable,
            character_maximum_length
          FROM information_schema.columns 
          WHERE table_schema = 'public' 
          AND table_name = $1
          AND column_name = $2
        `, [table.name, expectedCol]);
        
        if (columnExists.rows.length > 0) {
          foundColumns.push(columnExists.rows[0]);
          console.log(`   ✅ ${expectedCol} (${columnExists.rows[0].data_type})`);
        } else {
          missingColumns.push(expectedCol);
          console.log(`   ❌ ${expectedCol} - FALTANDO!`);
        }
      }
      
      totalHierarchicalColumns += foundColumns.length;
      
      if (missingColumns.length > 0) {
        allTablesComplete = false;
        console.log(`   ⚠️  TABELA INCOMPLETA: ${missingColumns.length} colunas faltando`);
      } else {
        console.log(`   🎉 TABELA COMPLETA: Todas as ${foundColumns.length} colunas hierárquicas presentes!`);
      }
      
      // Verificar se há dados nas colunas hierárquicas
      if (foundColumns.length > 0) {
        try {
          const dataCheck = await client.query(`
            SELECT COUNT(*) as total,
                   COUNT(${foundColumns[0].column_name}) as with_data
            FROM ${table.name}
          `);
          
          const total = parseInt(dataCheck.rows[0].total);
          const withData = parseInt(dataCheck.rows[0].with_data);
          
          console.log(`   📊 Registros: ${total} total, ${withData} com dados hierárquicos`);
          
          if (withData > 0) {
            // Mostrar exemplo de dados
            const sampleQuery = `
              SELECT ${foundColumns.map(col => col.column_name).join(', ')}
              FROM ${table.name} 
              WHERE ${foundColumns[0].column_name} IS NOT NULL
              LIMIT 3
            `;
            
            const sampleData = await client.query(sampleQuery);
            
            if (sampleData.rows.length > 0) {
              console.log(`   📝 Exemplos de dados hierárquicos:`);
              sampleData.rows.forEach((row, index) => {
                const values = foundColumns.map(col => row[col.column_name] || 'NULL').join('/');
                console.log(`      ${index + 1}. ${values}`);
              });
            }
          }
        } catch (error) {
          console.log(`   ⚠️  Erro ao verificar dados: ${error.message}`);
        }
      }
    }
    
    // Resumo final
    console.log('\n' + '=' .repeat(80));
    console.log('📊 RESUMO FINAL DA VERIFICAÇÃO');
    console.log('=' .repeat(80));
    console.log(`📋 Total de tabelas verificadas: ${tables.length}`);
    console.log(`🔹 Total de colunas hierárquicas encontradas: ${totalHierarchicalColumns}`);
    console.log(`🔹 Total de colunas hierárquicas esperadas: ${tables.reduce((sum, table) => sum + table.expected.length, 0)}`);
    
    if (allTablesComplete) {
      console.log('\n🎉 SUCESSO TOTAL!');
      console.log('✅ TODAS as tabelas têm TODAS as colunas hierárquicas necessárias!');
      console.log('✅ Sistema 100% preparado para importação de dados por códigos!');
      console.log('✅ Referências hierárquicas implementadas conforme instrucao.md!');
    } else {
      console.log('\n⚠️  ATENÇÃO!');
      console.log('❌ Algumas tabelas ainda não têm todas as colunas hierárquicas.');
      console.log('💡 Será necessário executar comandos ALTER TABLE para adicionar as colunas faltantes.');
    }
    
    // Teste final de consulta hierárquica
    console.log('\n🧪 TESTE FINAL: Consulta hierárquica usando códigos');
    console.log('-'.repeat(60));
    
    try {
      const hierarchicalQuery = await client.query(`
        SELECT 
          g.codigo_cliente,
          g.codigo_estacao,
          g.codigo_bloco,
          g.codigo_sub_bloco,
          COUNT(*) as total_gavetas
        FROM gavetas g
        WHERE g.codigo_cliente IS NOT NULL
        AND g.codigo_estacao IS NOT NULL
        AND g.codigo_bloco IS NOT NULL
        AND g.codigo_sub_bloco IS NOT NULL
        GROUP BY g.codigo_cliente, g.codigo_estacao, g.codigo_bloco, g.codigo_sub_bloco
        ORDER BY g.codigo_cliente, g.codigo_estacao, g.codigo_bloco, g.codigo_sub_bloco
        LIMIT 5
      `);
      
      if (hierarchicalQuery.rows.length > 0) {
        console.log(`✅ Consulta hierárquica FUNCIONANDO! ${hierarchicalQuery.rows.length} grupos encontrados:`);
        hierarchicalQuery.rows.forEach((row, index) => {
          console.log(`   ${index + 1}. ${row.codigo_cliente}/${row.codigo_estacao}/${row.codigo_bloco}/${row.codigo_sub_bloco} (${row.total_gavetas} gavetas)`);
        });
        console.log('\n🎉 CONFIRMADO: Sistema funcionando com referências hierárquicas!');
      } else {
        console.log(`⚠️  Consulta hierárquica retornou 0 resultados.`);
        console.log(`💡 As colunas podem existir mas ainda não foram populadas com dados.`);
      }
    } catch (error) {
      console.log(`❌ Erro na consulta hierárquica: ${error.message}`);
      console.log(`💡 Isso indica que as colunas hierárquicas podem não existir ainda.`);
    }
    
    console.log('\n' + '=' .repeat(80));
    console.log('🏁 VERIFICAÇÃO CONCLUÍDA');
    console.log('=' .repeat(80));
    
  } catch (error) {
    console.error('❌ Erro durante verificação:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Executar verificação
if (require.main === module) {
  verifyColumnsFinal().catch(error => {
    console.error('❌ Erro fatal:', error);
    process.exit(1);
  });
}

module.exports = { verifyColumnsFinal };
