import React from 'react';
import { Container, Box, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledContainer = styled(Container)(({ theme }) => ({
  paddingTop: theme.spacing(3),
  paddingBottom: theme.spacing(3),
  paddingLeft: '32px',
  paddingRight: '32px',
  
  [theme.breakpoints.down('sm')]: {
    paddingLeft: '16px',
    paddingRight: '16px',
  },
}));

const PageHeader = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(4),
  paddingBottom: theme.spacing(2),
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const PageTitle = styled(Typography)(({ theme }) => ({
  fontSize: '32px',
  fontWeight: 700,
  lineHeight: '40px',
  color: theme.palette.text.primary,
  marginBottom: theme.spacing(1),
  
  [theme.breakpoints.down('sm')]: {
    fontSize: '24px',
    lineHeight: '32px',
  },
}));

const PageSubtitle = styled(Typography)(({ theme }) => ({
  fontSize: '16px',
  fontWeight: 400,
  lineHeight: '24px',
  color: theme.palette.text.secondary,
}));

const StyledContentSection = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(3),

  '&:last-child': {
    marginBottom: 0,
  },
}));

const StandardContainer = ({
  children,
  title,
  subtitle,
  maxWidth = 'xl',
  disableGutters = false,
  headerAction,
  sx = {},
  ...props
}) => {
  return (
    <StyledContainer
      maxWidth={maxWidth}
      disableGutters={disableGutters}
      sx={{
        backgroundColor: 'background.default',
        minHeight: '100vh',
        ...sx,
      }}
      {...props}
    >
      {(title || subtitle || headerAction) && (
        <PageHeader>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
            <Box>
              {title && (
                <PageTitle variant="h1">
                  {title}
                </PageTitle>
              )}
              {subtitle && (
                <PageSubtitle>
                  {subtitle}
                </PageSubtitle>
              )}
            </Box>
            {headerAction && (
              <Box sx={{ ml: 2 }}>
                {headerAction}
              </Box>
            )}
          </Box>
        </PageHeader>
      )}
      
      <Box>
        {children}
      </Box>
    </StyledContainer>
  );
};

// Componente para seções de conteúdo
export const ContentSection = ({ children, title, sx = {}, ...props }) => {
  return (
    <StyledContentSection sx={sx} {...props}>
      {title && (
        <Typography
          variant="h3"
          sx={{
            fontSize: '20px',
            fontWeight: 500,
            lineHeight: '28px',
            color: 'text.primary',
            mb: 2,
          }}
        >
          {title}
        </Typography>
      )}
      {children}
    </StyledContentSection>
  );
};

export default StandardContainer;
