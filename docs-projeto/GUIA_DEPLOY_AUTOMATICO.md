# 🚀 Guia de Deploy Automático - Portal Evolution

## 📋 **Visão Geral**

Este guia explica como usar o novo sistema de deploy automático do Portal Evolution, que permite desenvolvimento local e deploy na VPS com um único comando.

## 🎯 **Fluxo Automático**

```
1. Configuração inicial (uma vez) → quick-setup.ps1
2. Desenvolvimento local → start-app.ps1 -Local
3. Deploy automático → start-app.ps1 -Deploy
4. Ambos simultaneamente → start-app.ps1
```

---

## 📝 **PASSO 1: Configuração Inicial (Uma vez)**

### **1.1 Executar Configuração Rápida**
```powershell
cd portalcliente
.\quick-setup.ps1
```

**O script irá:**
- ✅ Verificar dependências (Git, SSH, Node.js)
- ✅ Coletar informações da VPS
- ✅ Configurar domínio e SSL
- ✅ Configurar banco de dados
- ✅ Gerar JWT secret
- ✅ Testar conectividade SSH
- ✅ Criar arquivo `configuracao.env`

### **1.2 Configuração Manual (Opcional)**
Se preferir configurar manualmente:
```powershell
notepad configuracao.env
```

---

## 📝 **PASSO 2: Uso Diário**

### **2.1 Desenvolvimento Local Apenas**
```powershell
.\start-app.ps1 -Local
```

**Isso irá:**
- ✅ Iniciar backend (porta 3001)
- ✅ Iniciar frontend (porta 5173)
- ✅ Abrir navegador automaticamente
- ✅ Monitorar saúde dos serviços

### **2.2 Deploy na VPS Apenas**
```powershell
.\start-app.ps1 -Deploy
```

**Isso irá:**
- ✅ Verificar alterações no código
- ✅ Fazer commit e push (se necessário)
- ✅ Conectar na VPS via SSH
- ✅ Atualizar código na VPS
- ✅ Executar deploy Docker Swarm
- ✅ Testar aplicação online
- ✅ Abrir navegador com a aplicação

### **2.3 Ambos (Local + Deploy)**
```powershell
.\start-app.ps1
```

**Isso irá:**
- ✅ Perguntar o que você quer fazer
- ✅ Executar local em background
- ✅ Fazer deploy na VPS
- ✅ Monitorar ambos

### **2.4 Deploy Forçado (Sem Confirmações)**
```powershell
.\start-app.ps1 -Deploy -Force
```

---

## 📝 **PASSO 3: Scripts Auxiliares**

### **3.1 Sincronização Manual**
```powershell
# Sincronizar arquivos com VPS
.\sync-to-vps.ps1

# Teste (mostrar o que seria sincronizado)
.\sync-to-vps.ps1 -DryRun

# Forçar sincronização
.\sync-to-vps.ps1 -Force
```

### **3.2 Reconfiguração**
```powershell
# Reconfigurar tudo
.\quick-setup.ps1 -Force

# Pular teste SSH
.\quick-setup.ps1 -SkipSSH

# Pular verificação Git
.\quick-setup.ps1 -SkipGit
```

---

## 🔧 **Parâmetros Avançados**

### **start-app.ps1**
```powershell
# Parâmetros disponíveis
.\start-app.ps1 [opções]

-Local          # Executar apenas localmente
-Deploy         # Fazer deploy na VPS
-Force          # Forçar sem confirmações
-Message "msg"  # Mensagem personalizada do commit
```

### **sync-to-vps.ps1**
```powershell
# Parâmetros disponíveis
.\sync-to-vps.ps1 [opções]

-Force          # Forçar sincronização
-DryRun         # Apenas mostrar o que seria feito
-VpsPath "path" # Caminho customizado na VPS
```

### **quick-setup.ps1**
```powershell
# Parâmetros disponíveis
.\quick-setup.ps1 [opções]

-Force          # Sobrescrever configuração existente
-SkipGit        # Pular verificação Git
-SkipSSH        # Pular teste SSH
```

---

## 🔍 **Exemplos de Uso**

### **Cenário 1: Primeiro Uso**
```powershell
# 1. Configurar pela primeira vez
.\quick-setup.ps1

# 2. Testar localmente
.\start-app.ps1 -Local

# 3. Fazer primeiro deploy
.\start-app.ps1 -Deploy
```

### **Cenário 2: Desenvolvimento Diário**
```powershell
# Trabalhar localmente
.\start-app.ps1 -Local

# Quando terminar, fazer deploy
.\start-app.ps1 -Deploy
```

### **Cenário 3: Deploy Rápido**
```powershell
# Deploy direto (com commit automático)
.\start-app.ps1 -Deploy -Force -Message "Correção de bug crítico"
```

### **Cenário 4: Sincronização Manual**
```powershell
# Ver o que seria sincronizado
.\sync-to-vps.ps1 -DryRun

# Sincronizar apenas arquivos específicos
.\sync-to-vps.ps1
```

---

## 🚨 **Troubleshooting**

### **Problema: SSH não conecta**
```powershell
# Testar SSH manualmente
ssh root@************

# Reconfigurar com skip SSH
.\quick-setup.ps1 -SkipSSH -Force
```

### **Problema: Git não funciona**
```powershell
# Verificar status do Git
git status

# Reconfigurar sem Git
.\quick-setup.ps1 -SkipGit -Force
```

### **Problema: Deploy falha**
```powershell
# Ver logs detalhados
.\start-app.ps1 -Deploy -Force

# Sincronizar manualmente primeiro
.\sync-to-vps.ps1
.\start-app.ps1 -Deploy
```

### **Problema: Aplicação local não inicia**
```powershell
# Verificar Node.js
node --version

# Instalar dependências
npm install

# Tentar novamente
.\start-app.ps1 -Local
```

---

## 📊 **Estrutura dos Scripts**

```
portalcliente/
├── start-app.ps1           # 🚀 Script principal de deploy
├── quick-setup.ps1         # ⚡ Configuração rápida
├── sync-to-vps.ps1         # 🔄 Sincronização manual
├── deploy-auto.sh          # 🐧 Deploy automático (Linux)
├── configuracao.env        # ⚙️ Configurações
└── scripts/
    ├── start-portal.ps1    # 🖥️ Desenvolvimento local
    └── ...
```

---

## ✅ **Checklist de Uso**

### **Configuração Inicial**
- [ ] Executar `.\quick-setup.ps1`
- [ ] Testar SSH para VPS
- [ ] Verificar arquivo `configuracao.env`
- [ ] Testar localmente com `.\start-app.ps1 -Local`
- [ ] Fazer primeiro deploy com `.\start-app.ps1 -Deploy`

### **Uso Diário**
- [ ] Fazer alterações no código
- [ ] Testar localmente (opcional)
- [ ] Executar `.\start-app.ps1 -Deploy`
- [ ] Verificar aplicação online

### **Manutenção**
- [ ] Verificar logs periodicamente
- [ ] Atualizar configurações se necessário
- [ ] Fazer backup das configurações

---

## 🎉 **Vantagens do Sistema Automático**

### **✅ Facilidade de Uso**
- Um comando para tudo
- Configuração automática
- Detecção de problemas

### **✅ Segurança**
- Backup automático de configurações
- Verificação de conectividade
- Rollback em caso de erro

### **✅ Produtividade**
- Deploy em segundos
- Desenvolvimento local integrado
- Monitoramento automático

### **✅ Flexibilidade**
- Modo local apenas
- Modo deploy apenas
- Modo combinado
- Parâmetros customizáveis

---

## 📞 **Suporte**

- **Documentação**: `README.md` e `docs-projeto/`
- **Logs**: Verificar saída dos scripts
- **SSH**: Conectar manualmente na VPS para diagnóstico
- **Email**: <EMAIL>

---

**🚀 Com este sistema, você pode focar no desenvolvimento enquanto o deploy é automatizado!**
