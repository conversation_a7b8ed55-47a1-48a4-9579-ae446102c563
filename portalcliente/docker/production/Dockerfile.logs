# Dockerfile para Sistema de Logs Web
FROM node:18-alpine

# Instalar dependências do sistema
RUN apk add --no-cache curl docker-cli

# Criar diretório de trabalho
WORKDIR /app

# Criar package.json específico para logs
RUN echo '{ \
  "name": "portal-evolution-logs", \
  "version": "1.0.0", \
  "description": "Sistema de logs web para Portal Evolution", \
  "main": "logs-server.js", \
  "dependencies": { \
    "express": "^4.18.2", \
    "socket.io": "^4.7.2", \
    "cors": "^2.8.5", \
    "helmet": "^7.0.0" \
  } \
}' > package.json

# Instalar dependências
RUN npm install

# Copiar código do sistema de logs
COPY logs-system/ ./

# Expor porta
EXPOSE 3003

# Comando para iniciar
CMD ["node", "logs-server.js"]
