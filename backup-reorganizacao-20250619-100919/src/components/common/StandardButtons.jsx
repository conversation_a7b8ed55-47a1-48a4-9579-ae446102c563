import React from 'react';
import { Button, IconButton } from '@mui/material';
import { styled } from '@mui/material/styles';

// Botão padrão para ações principais
export const StandardButton = styled(Button)(({ theme, variant = 'contained', color = 'primary' }) => ({
  textTransform: 'none',
  fontWeight: 600,
  borderRadius: 8,
  padding: '8px 16px',
  fontSize: '0.875rem',
  boxShadow: variant === 'contained' ? '0 2px 4px rgba(0,0,0,0.1)' : 'none',
  transition: 'all 0.2s ease-in-out',
  
  '&:hover': {
    transform: 'translateY(-1px)',
    boxShadow: variant === 'contained' ? '0 4px 8px rgba(0,0,0,0.15)' : 'none',
  },
  
  // Cores específicas para cada tipo de ação
  ...(color === 'primary' && variant === 'contained' && {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.contrastText,
    '&:hover': {
      backgroundColor: theme.palette.primary.dark,
    }
  }),
  
  ...(color === 'secondary' && variant === 'contained' && {
    backgroundColor: theme.palette.secondary.main,
    color: theme.palette.secondary.contrastText,
    '&:hover': {
      backgroundColor: theme.palette.secondary.dark,
    }
  }),
  
  ...(color === 'success' && variant === 'contained' && {
    backgroundColor: theme.palette.success.main,
    color: theme.palette.success.contrastText,
    '&:hover': {
      backgroundColor: theme.palette.success.dark,
    }
  }),
  
  ...(color === 'warning' && variant === 'contained' && {
    backgroundColor: theme.palette.warning.main,
    color: theme.palette.warning.contrastText,
    '&:hover': {
      backgroundColor: theme.palette.warning.dark,
    }
  }),
  
  ...(color === 'error' && variant === 'contained' && {
    backgroundColor: theme.palette.error.main,
    color: theme.palette.error.contrastText,
    '&:hover': {
      backgroundColor: theme.palette.error.dark,
    }
  }),
}));

// Botão específico para ação "Editar"
export const EditButton = (props) => (
  <StandardButton
    variant="outlined"
    color="primary"
    size="small"
    {...props}
  >
    {props.children || 'Editar'}
  </StandardButton>
);

// Botão específico para ação "Deletar"
export const DeleteButton = (props) => (
  <StandardButton
    variant="outlined"
    color="error"
    size="small"
    {...props}
  >
    {props.children || 'Deletar'}
  </StandardButton>
);

// Botão específico para ação "Exumar"
export const ExumarButton = (props) => (
  <StandardButton
    variant="outlined"
    color="warning"
    size="small"
    {...props}
  >
    {props.children || 'Exumar'}
  </StandardButton>
);

// Botão específico para ação "Adicionar"
export const AddButton = (props) => (
  <StandardButton
    variant="contained"
    color="primary"
    {...props}
  >
    {props.children || 'Adicionar'}
  </StandardButton>
);

// Botão específico para ação "Salvar"
export const SaveButton = (props) => (
  <StandardButton
    variant="contained"
    color="success"
    {...props}
  >
    {props.children || 'Salvar'}
  </StandardButton>
);

// Botão específico para ação "Cancelar"
export const CancelButton = (props) => (
  <StandardButton
    variant="outlined"
    color="inherit"
    {...props}
  >
    {props.children || 'Cancelar'}
  </StandardButton>
);

// Botão específico para ação "Ver/Visualizar"
export const ViewButton = (props) => (
  <StandardButton
    variant="contained"
    color="primary"
    {...props}
  >
    {props.children || 'Ver'}
  </StandardButton>
);

// Botão de ícone padronizado
export const StandardIconButton = styled(IconButton)(({ theme }) => ({
  borderRadius: 8,
  padding: 8,
  transition: 'all 0.2s ease-in-out',
  
  '&:hover': {
    transform: 'translateY(-1px)',
    backgroundColor: theme.palette.action.hover,
  }
}));

export default {
  StandardButton,
  EditButton,
  DeleteButton,
  ExumarButton,
  AddButton,
  SaveButton,
  CancelButton,
  ViewButton,
  StandardIconButton
};
