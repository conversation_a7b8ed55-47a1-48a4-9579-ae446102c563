# ===================================
# CONFIGURAÇÃO DO PORTAL EVOLUTION
# ===================================
# Copie este arquivo para configuracao.env e configure os valores

# ===================================
# DADOS DA VPS
# ===================================
VPS_HOST=************
VPS_USER=root
VPS_PORT=22

# ===================================
# DOMÍNIO E SSL
# ===================================
DOMAIN=portal.evo-eden.site
SSL_EMAIL=<EMAIL>
ENABLE_SSL=true

# ===================================
# BANCO DE DADOS
# ===================================
DB_HOST=portal_database
DB_PORT=5432
DB_NAME=portal_evolution
DB_USER=portal_user
DB_PASSWORD=ALTERE_ESTA_SENHA_PARA_UMA_SEGURA

# ===================================
# APLICAÇÃO
# ===================================
NODE_ENV=production
PORT=3001
JWT_SECRET=ALTERE_ESTA_CHAVE_JWT_PARA_UMA_SEGURA

# ===================================
# EMAIL (OPCIONAL)
# ===================================
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=sua_senha_de_app

# ===================================
# CONFIGURAÇÕES AVANÇADAS
# ===================================
# Número de réplicas dos serviços
FRONTEND_REPLICAS=1
BACKEND_REPLICAS=1

# Recursos dos containers
FRONTEND_MEMORY=256m
BACKEND_MEMORY=512m
DATABASE_MEMORY=1g

# ===================================
# BACKUP
# ===================================
BACKUP_ENABLED=true
BACKUP_RETENTION_DAYS=7

# ===================================
# CONFIGURAÇÕES DE DEPLOY AUTOMÁTICO
# ===================================
# Configurações para os scripts automáticos

# Timeout para conexões SSH (segundos)
SSH_TIMEOUT=30

# Timeout para testes de aplicação (segundos)
APP_TEST_TIMEOUT=60

# Caminho na VPS onde o projeto está localizado
VPS_PROJECT_PATH=/root/portalevo

# Branch do Git para deploy
GIT_BRANCH=master

# ===================================
# BACKUP
# ===================================
BACKUP_ENABLED=true
BACKUP_RETENTION_DAYS=7
