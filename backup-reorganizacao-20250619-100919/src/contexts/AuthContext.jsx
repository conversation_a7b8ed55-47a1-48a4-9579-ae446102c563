import React, { createContext, useContext, useState, useEffect } from 'react';
import api, { authService } from '../services/api';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth deve ser usado dentro de um AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Verificar se há token salvo no localStorage
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const token = localStorage.getItem('token');
        if (token) {
          api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
          const response = await authService.verify();
          setUser(response.data.usuario);
        }
      } catch (error) {
        // Erro ao verificar autenticação - removendo token inválido
        console.log('🔄 Token inválido ou expirado, removendo...');
        localStorage.removeItem('token');
        delete api.defaults.headers.common['Authorization'];
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  const login = async (email, senha) => {
    try {
      setError(null);
      setLoading(true);

      console.log('🔐 Tentativa de login:', { email, senha: '***' });

      // Usar authService em vez de api diretamente
      const response = await authService.login(email, senha);
      const { token, usuario } = response.data;

      console.log('✅ Login bem-sucedido:', { usuario: usuario.nome, email: usuario.email });

      // Salvar token no localStorage
      localStorage.setItem('token', token);

      // Configurar header de autorização para futuras requisições
      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;

      setUser(usuario);
      return { success: true, usuario };
    } catch (error) {
      // Erro no login
      const errorMessage = error.response?.data?.error || 'Erro ao fazer login';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      // Usar authService em vez de api diretamente
      await authService.logout();
    } catch (error) {
      // Erro ao fazer logout - continuando com limpeza local
      console.log('Erro no logout:', error);
    } finally {
      // Limpar dados locais
      localStorage.removeItem('token');
      localStorage.clear(); // Limpar todo o localStorage
      delete api.defaults.headers.common['Authorization'];
      setUser(null);
      setError(null);

      // Limpar cache do navegador e forçar redirecionamento
      window.history.replaceState(null, '', '/login');
      window.location.href = '/login';
    }
  };

  const isAdmin = () => {
    return user?.tipo_usuario === 'admin';
  };

  const isClient = () => {
    return user?.tipo_usuario === 'cliente';
  };

  const canAccessClientes = () => {
    return isAdmin();
  };

  const canAccessUsuarios = () => {
    return isAdmin();
  };

  const canAccessLogs = () => {
    return isAdmin();
  };

  const canEditProdutos = () => {
    return isAdmin();
  };

  const canDeleteSepultamentos = () => {
    // Tanto admin quanto cliente podem deletar sepultamentos
    return isAdmin() || isClient();
  };

  const canAddEditExhumeSepultamentos = () => {
    // Tanto admin quanto cliente podem adicionar/editar/exumar
    return isAdmin() || isClient();
  };

  const isCliente = () => {
    return user?.tipo_usuario === 'cliente';
  };

  const getCodigoCliente = () => {
    return user?.codigo_cliente;
  };

  const value = {
    user,
    loading,
    error,
    login,
    logout,
    isAdmin,
    isClient,
    isCliente,
    getCodigoCliente,
    setError,
    canAccessClientes,
    canAccessUsuarios,
    canAccessLogs,
    canEditProdutos,
    canDeleteSepultamentos,
    canAddEditExhumeSepultamentos
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
