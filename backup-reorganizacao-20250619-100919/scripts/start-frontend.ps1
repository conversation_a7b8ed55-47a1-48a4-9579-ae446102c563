# Script para iniciar o Frontend do Portal Evolution
Write-Host "=== INICIANDO FRONTEND PORTAL EVOLUTION ===" -ForegroundColor Green
Write-Host ""

# Navegar para o diretório do projeto
$projectPath = "C:\Users\<USER>\Documents\portalevo\portal-evolution"
Write-Host "Navegando para: $projectPath" -ForegroundColor Yellow

if (Test-Path $projectPath) {
    Set-Location $projectPath
    Write-Host "✅ Diretório encontrado" -ForegroundColor Green
} else {
    Write-Host "❌ Diretório do projeto não encontrado!" -ForegroundColor Red
    Write-Host "Verifique se o caminho está correto: $projectPath" -ForegroundColor Red
    exit 1
}

# Verificar se o package.json existe
if (Test-Path "package.json") {
    Write-Host "✅ Arquivo package.json encontrado" -ForegroundColor Green
} else {
    Write-Host "❌ Arquivo package.json não encontrado!" -ForegroundColor Red
    exit 1
}

# Verificar se o backend está rodando
Write-Host ""
Write-Host "Verificando se o backend está rodando..." -ForegroundColor Yellow
try {
    $health = Invoke-RestMethod -Uri "http://localhost:3001/api/health" -TimeoutSec 5
    Write-Host "✅ Backend está rodando: $($health.message)" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Backend não está rodando!" -ForegroundColor Yellow
    Write-Host "   Certifique-se de iniciar o backend primeiro com:" -ForegroundColor Yellow
    Write-Host "   .\start-backend.ps1" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "   Continuando mesmo assim..." -ForegroundColor Yellow
}

# Verificar se as dependências estão instaladas
Write-Host ""
Write-Host "Verificando dependências..." -ForegroundColor Yellow
if (Test-Path "node_modules") {
    Write-Host "✅ Dependências instaladas" -ForegroundColor Green
} else {
    Write-Host "⚠️ Dependências não encontradas. Instalando..." -ForegroundColor Yellow
    npm install
}

# Iniciar o frontend
Write-Host ""
Write-Host "Iniciando frontend..." -ForegroundColor Yellow
Write-Host "Frontend será executado em: http://localhost:5173" -ForegroundColor Cyan
Write-Host "Se a porta 5173 estiver ocupada, será usada outra porta automaticamente" -ForegroundColor Cyan
Write-Host ""
Write-Host "Para parar o servidor, pressione Ctrl+C" -ForegroundColor Yellow
Write-Host ""

# Executar o frontend
npm run dev
