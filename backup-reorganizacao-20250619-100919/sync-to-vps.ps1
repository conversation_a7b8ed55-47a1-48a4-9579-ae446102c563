# ===================================
# PORTAL EVOLUTION - SINCRONIZAÇÃO VPS
# ===================================
# Script para sincronizar arquivos com a VPS
# Execute: .\sync-to-vps.ps1

param(
    [switch]$Force,      # Forçar sincronização sem confirmação
    [switch]$DryRun,     # Apenas mostrar o que seria sincronizado
    [string]$VpsPath = "/root/portalevo"  # Caminho na VPS
)

# Função para log
function Write-Log {
    param([string]$Message, [string]$Color = "Green")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] $Message" -ForegroundColor $Color
}

function Write-Info {
    param([string]$Message)
    Write-Log $Message "Cyan"
}

function Write-Success {
    param([string]$Message)
    Write-Log $Message "Green"
}

function Write-Warning {
    param([string]$Message)
    Write-Log $Message "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-Log $Message "Red"
}

# Definir diretório do script
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $scriptPath

Write-Host ""
Write-Host "🔄 PORTAL EVOLUTION - SINCRONIZAÇÃO VPS" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan
Write-Host ""

# Verificar se está no diretório correto
if (-not (Test-Path "configuracao.env")) {
    Write-Error "Arquivo configuracao.env não encontrado!"
    Write-Host "Execute este script no diretório portalcliente" -ForegroundColor Yellow
    exit 1
}

# Carregar configurações
Write-Info "Carregando configurações..."
$config = @{}
Get-Content "configuracao.env" | ForEach-Object {
    if ($_ -match "^([^#][^=]+)=(.*)$") {
        $config[$matches[1].Trim()] = $matches[2].Trim()
    }
}

$VPS_HOST = $config["VPS_HOST"]
$VPS_USER = $config["VPS_USER"]

Write-Success "Configurações carregadas:"
Write-Host "   VPS: $VPS_USER@$VPS_HOST" -ForegroundColor White
Write-Host "   Caminho VPS: $VpsPath" -ForegroundColor White
Write-Host ""

# Verificar SSH
Write-Info "Testando conectividade SSH..."
$sshTest = ssh -o ConnectTimeout=10 -o BatchMode=yes $VPS_USER@$VPS_HOST "echo 'SSH OK'" 2>$null

if ($LASTEXITCODE -ne 0) {
    Write-Error "❌ Não foi possível conectar na VPS!"
    Write-Host "Verifique:" -ForegroundColor Yellow
    Write-Host "   - Conectividade de rede" -ForegroundColor Yellow
    Write-Host "   - Credenciais SSH" -ForegroundColor Yellow
    Write-Host "   - Firewall da VPS" -ForegroundColor Yellow
    exit 1
}

Write-Success "✅ SSH conectado com sucesso!"

# Verificar rsync
Write-Info "Verificando rsync..."
try {
    $rsyncVersion = rsync --version 2>$null | Select-Object -First 1
    if ($LASTEXITCODE -eq 0) {
        Write-Success "rsync encontrado: $rsyncVersion"
    } else {
        Write-Warning "rsync não encontrado - usando método alternativo"
        $useAlternative = $true
    }
} catch {
    Write-Warning "rsync não encontrado - usando método alternativo"
    $useAlternative = $true
}

# Definir arquivos/pastas a sincronizar
$itemsToSync = @(
    "src/",
    "server/",
    "public/",
    "package.json",
    "package-lock.json",
    "vite.config.js",
    "index.html",
    "configuracao.env",
    "docker-compose.prod.yml",
    "Dockerfile.backend",
    "Dockerfile.frontend",
    "nginx-traefik.conf",
    "deploy-traefik.sh",
    "atualizar.sh",
    "monitor.sh",
    "validate-setup.sh",
    "remove-stack.sh",
    "scripts/",
    "docs/"
)

# Arquivos/pastas a excluir
$excludeItems = @(
    "node_modules/",
    "dist/",
    ".git/",
    ".vscode/",
    "*.log",
    "*.tmp",
    ".env.local",
    "backup/",
    "dados/"
)

Write-Host ""
Write-Info "Itens a sincronizar:"
$itemsToSync | ForEach-Object { Write-Host "   ✓ $_" -ForegroundColor Green }

Write-Host ""
Write-Warning "Itens excluídos:"
$excludeItems | ForEach-Object { Write-Host "   ✗ $_" -ForegroundColor Red }

if (-not $Force -and -not $DryRun) {
    Write-Host ""
    Write-Host "Deseja continuar com a sincronização? (s/N): " -NoNewline -ForegroundColor Yellow
    $confirm = Read-Host
    
    if ($confirm -ne "s" -and $confirm -ne "S") {
        Write-Info "Sincronização cancelada."
        exit 0
    }
}

Write-Host ""

if ($DryRun) {
    Write-Info "🔍 MODO DRY-RUN - Apenas mostrando o que seria sincronizado"
    Write-Host ""
}

# Criar diretório na VPS se não existir
Write-Info "Verificando/criando diretório na VPS..."
ssh $VPS_USER@$VPS_HOST "mkdir -p $VpsPath/portalcliente"

if ($useAlternative) {
    # Método alternativo usando SCP
    Write-Info "Sincronizando arquivos via SCP..."
    
    foreach ($item in $itemsToSync) {
        if (Test-Path $item) {
            Write-Host "   Sincronizando: $item" -ForegroundColor Gray
            
            if (-not $DryRun) {
                if ($item.EndsWith("/")) {
                    # É um diretório
                    scp -r $item $VPS_USER@${VPS_HOST}:$VpsPath/portalcliente/
                } else {
                    # É um arquivo
                    scp $item $VPS_USER@${VPS_HOST}:$VpsPath/portalcliente/
                }
                
                if ($LASTEXITCODE -ne 0) {
                    Write-Warning "⚠️ Erro ao sincronizar: $item"
                }
            }
        } else {
            Write-Warning "⚠️ Item não encontrado: $item"
        }
    }
    
} else {
    # Método preferido usando rsync
    Write-Info "Sincronizando arquivos via rsync..."
    
    # Construir comando rsync
    $rsyncCmd = "rsync -avz --progress"
    
    # Adicionar exclusões
    foreach ($exclude in $excludeItems) {
        $rsyncCmd += " --exclude='$exclude'"
    }
    
    if ($DryRun) {
        $rsyncCmd += " --dry-run"
    }
    
    # Adicionar origem e destino
    $rsyncCmd += " ./ $VPS_USER@${VPS_HOST}:$VpsPath/portalcliente/"
    
    Write-Host "Comando: $rsyncCmd" -ForegroundColor Gray
    Write-Host ""
    
    # Executar rsync
    Invoke-Expression $rsyncCmd
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "✅ Sincronização concluída!"
    } else {
        Write-Error "❌ Erro durante a sincronização!"
        exit 1
    }
}

if (-not $DryRun) {
    # Ajustar permissões na VPS
    Write-Info "Ajustando permissões na VPS..."
    ssh $VPS_USER@$VPS_HOST "chmod +x $VpsPath/portalcliente/*.sh"
    
    Write-Host ""
    Write-Success "🎉 Sincronização concluída com sucesso!"
    Write-Host ""
    Write-Info "Para fazer deploy na VPS:"
    Write-Host "   ssh $VPS_USER@$VPS_HOST" -ForegroundColor White
    Write-Host "   cd $VpsPath/portalcliente" -ForegroundColor White
    Write-Host "   bash deploy-traefik.sh" -ForegroundColor White
    Write-Host ""
    Write-Info "Ou use o script start-app.ps1 com parâmetro -Deploy"
} else {
    Write-Info "Dry-run concluído. Use sem -DryRun para sincronizar de fato."
}
