# ===================================
# DOCKERFILE FRONTEND - PORTAL EVOLUTION
# ===================================
# Build React + Nginx seguindo padrão da VPS
# Para verificar build: docker build -f docker/Dockerfile.frontend -t portal-frontend .

FROM node:18-alpine AS builder

# Definir diretório de trabalho
WORKDIR /app

# Copiar arquivos de dependências
# Para verificar dependências: cat package.json | grep dependencies
COPY package*.json ./

# Instalar dependências
# Para verificar instalação: npm list --depth=0
RUN npm ci --only=production

# Copiar código fonte
COPY . .

# Build da aplicação
# Para verificar build: ls -la dist/
RUN npm run build

# ===================================
# ESTÁGIO DE PRODUÇÃO COM NGINX
# ===================================
FROM nginx:alpine

# Copiar configuração customizada do Nginx
# Para verificar config: docker exec portal-frontend cat /etc/nginx/nginx.conf
COPY ../docker/nginx.conf /etc/nginx/nginx.conf

# Copiar arquivos buildados
# Para verificar arquivos: docker exec portal-frontend ls -la /usr/share/nginx/html/
COPY --from=builder /app/dist /usr/share/nginx/html

# Expor porta
EXPOSE 80

# Health check
# Para testar: docker exec portal-frontend curl -f http://localhost/
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/ || exit 1

# Comando para iniciar
CMD ["nginx", "-g", "daemon off;"]

# ===================================
# INSTRUÇÕES DE BUILD E DEBUG
# ===================================

# Para fazer build:
# docker build -f docker/Dockerfile.frontend -t portal-frontend .

# Para executar localmente:
# docker run -p 80:80 portal-frontend

# Para debug:
# docker run -it portal-frontend sh

# Para verificar logs:
# docker logs portal-frontend
