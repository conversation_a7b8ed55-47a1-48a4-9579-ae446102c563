# 🚀 G<PERSON><PERSON> de Deploy CORRIGIDO - Portal Evolution

## ❌ **PROBLEMA IDENTIFICADO**

O erro que você encontrou:
```
remote: Support for password authentication was removed on August 13, 2021.
fatal: Authentication failed for 'https://github.com/Ma<PERSON>cioFilh/portalevo.git/'
```

**Causa**: GitHub não aceita mais senha para autenticação HTTPS.

## ✅ **SOLUÇÃO IMPLEMENTADA**

Criamos scripts que **não dependem de autenticação do GitHub**, baixando o código como ZIP.

---

## 📝 **MÉTODO CORRIGIDO - PASSO A PASSO**

### **PASSO 1: Conectar na VPS**
```bash
ssh root@************
```

### **PASSO 2: Configurar VPS (MÉTODO CORRIGIDO)**

#### **Opção A: Script Automático (RECOMENDADO)**
```bash
# Baixar script corrigido
curl -fsSL https://raw.githubusercontent.com/MauricioFilh/portalevo/master/portalcliente/setup-manual-vps.sh -o setup-manual-vps.sh

# Executar
chmod +x setup-manual-vps.sh
bash setup-manual-vps.sh
```

#### **Opção B: Comandos Manuais (se script não funcionar)**
```bash
# Atualizar sistema
apt update && apt upgrade -y

# Instalar dependências
apt install -y curl wget git unzip nano htop ufw docker.io

# Instalar Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Configurar Docker
systemctl enable docker
systemctl start docker

# Configurar firewall
ufw --force reset
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp
ufw --force enable

# Criar diretório e baixar código
mkdir -p /opt/portal-evolution
cd /opt/portal-evolution

# Baixar código como ZIP (SEM AUTENTICAÇÃO)
wget -O repo.zip https://github.com/MauricioFilh/portalevo/archive/refs/heads/master.zip
unzip repo.zip
mv portalevo-master/* .
mv portalevo-master/.* . 2>/dev/null || true
rm -rf portalevo-master repo.zip

# Tornar scripts executáveis
chmod +x portalcliente/*.sh
```

### **PASSO 3: Configurar Dados**
```bash
# Editar configurações
nano /opt/portal-evolution/portalcliente/configuracao.env
```

**ALTERE OBRIGATORIAMENTE:**
```env
# Linha ~18 - Senha do banco (ALTERE!)
DB_PASSWORD=SUA_SENHA_MUITO_FORTE_AQUI

# Linha ~24 - Chave JWT (ALTERE!)
JWT_SECRET=SUA_CHAVE_JWT_MUITO_SEGURA_AQUI

# Linha ~8 - Seu domínio (opcional)
DOMAIN=seu-dominio.com
```

### **PASSO 4: Deploy**
```bash
# Navegar para diretório
cd /opt/portal-evolution/portalcliente

# Executar deploy
bash deploy.sh
```

### **PASSO 5: Verificar**
```bash
# Verificar containers
docker-compose -f docker-compose.prod.yml ps

# Verificar logs
docker-compose -f docker-compose.prod.yml logs -f

# Testar aplicação
curl http://localhost/health
curl http://localhost/api/health
```

### **PASSO 6: Acessar Sistema**
- **URL**: http://************
- **Admin**: <EMAIL> / adminnbr5410!
- **Cliente**: <EMAIL> / 54321

---

## 🔄 **ATUALIZAR SISTEMA (MÉTODO CORRIGIDO)**

### **Sempre que quiser atualizar:**

#### **No seu computador:**
```bash
git add .
git commit -m "Suas alterações"
git push origin master
```

#### **Na VPS:**
```bash
# Conectar na VPS
ssh root@************

# Atualizar (MÉTODO CORRIGIDO)
bash /opt/portal-evolution/atualizar-manual.sh
```

**O script de atualização irá:**
- ✅ Baixar nova versão como ZIP (sem autenticação)
- ✅ Preservar suas configurações
- ✅ Reconstruir containers
- ✅ Verificar funcionamento

---

## 🔧 **COMANDOS ÚTEIS**

### **Gerenciar Sistema**
```bash
# Iniciar sistema
systemctl start portal-evolution

# Parar sistema
systemctl stop portal-evolution

# Reiniciar sistema
systemctl restart portal-evolution

# Ver status
systemctl status portal-evolution

# Ver logs do sistema
journalctl -u portal-evolution -f
```

### **Gerenciar Containers**
```bash
# Ver status
docker-compose -f docker-compose.prod.yml ps

# Ver logs
docker-compose -f docker-compose.prod.yml logs -f

# Reiniciar containers
docker-compose -f docker-compose.prod.yml restart

# Parar containers
docker-compose -f docker-compose.prod.yml down

# Iniciar containers
docker-compose -f docker-compose.prod.yml up -d
```

### **Monitoramento**
```bash
# Status completo
bash /opt/portal-evolution/monitor.sh

# Verificar saúde
curl http://localhost/health
curl http://localhost/api/health

# Ver recursos
docker stats

# Backup manual
bash /opt/portal-evolution/backup.sh
```

---

## 🚨 **TROUBLESHOOTING**

### **Problema: Script não baixa**
```bash
# Método alternativo
wget https://github.com/MauricioFilh/portalevo/archive/refs/heads/master.zip
unzip master.zip
cd portalevo-master/portalcliente
bash setup-manual-vps.sh
```

### **Problema: Docker não instala**
```bash
# Método alternativo
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
```

### **Problema: Containers não iniciam**
```bash
# Ver logs detalhados
docker-compose -f docker-compose.prod.yml logs

# Verificar configuração
cat configuracao.env

# Reiniciar Docker
systemctl restart docker
```

### **Problema: Aplicação não responde**
```bash
# Verificar portas
netstat -tlnp | grep :80
netstat -tlnp | grep :3001

# Verificar firewall
ufw status

# Verificar containers
docker ps -a
```

---

## 📋 **DIFERENÇAS DO MÉTODO CORRIGIDO**

### **❌ Método Antigo (com problema)**
- Dependia de `git clone` com autenticação
- Falhava com erro de senha
- Precisava configurar token GitHub

### **✅ Método Novo (corrigido)**
- Baixa código como ZIP público
- Não precisa autenticação
- Funciona sempre
- Preserva configurações na atualização

### **🔄 Atualização Corrigida**
- Script `atualizar-manual.sh` baixa ZIP
- Preserva arquivo `configuracao.env`
- Reconstrói containers automaticamente
- Verifica funcionamento

---

## ✅ **CHECKLIST DE DEPLOY CORRIGIDO**

### **Configuração Inicial**
- [ ] VPS configurada com script corrigido
- [ ] Arquivo `configuracao.env` editado
- [ ] DB_PASSWORD alterado
- [ ] JWT_SECRET alterado
- [ ] Deploy executado com sucesso
- [ ] Aplicação acessível via browser

### **Teste de Funcionamento**
- [ ] Frontend carrega: http://************
- [ ] Backend responde: http://************/api/health
- [ ] Login funciona
- [ ] Dashboard carrega dados

### **Auto-Start**
- [ ] Sistema inicia automaticamente: `systemctl status portal-evolution`
- [ ] Containers rodando: `docker ps`

---

## 🎉 **PROBLEMA RESOLVIDO!**

**Agora você tem:**
- ✅ **Deploy sem autenticação** GitHub
- ✅ **Atualização simples** via ZIP
- ✅ **Auto-start** configurado
- ✅ **Backup automático** funcionando
- ✅ **Monitoramento** implementado

**Para usar:**
1. Execute o script corrigido na VPS
2. Configure `configuracao.env`
3. Execute `bash deploy.sh`
4. Sistema funcionando!

**Para atualizar:**
1. `git push` no seu PC
2. `bash /opt/portal-evolution/atualizar-manual.sh` na VPS
3. Pronto!

**Sistema funcionando sem problemas de autenticação!** 🚀
