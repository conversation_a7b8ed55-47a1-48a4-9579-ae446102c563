# ===================================
# CONFIGURAÇÃO NGINX - PORTAL EVOLUTION
# ===================================
# Proxy reverso seguindo padrão da VPS
# Para verificar config: docker exec portal-frontend nginx -t

events {
    # Número de conexões simultâneas
    # Para verificar: docker exec portal-frontend cat /proc/sys/net/core/somaxconn
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # ===================================
    # CONFIGURAÇÕES DE SEGURANÇA
    # ===================================
    # Para verificar headers: curl -I http://seu-dominio.com
    server_tokens off;
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";

    # ===================================
    # CONFIGURAÇÕES DE PERFORMANCE
    # ===================================
    sendfile        on;
    tcp_nopush      on;
    tcp_nodelay     on;
    keepalive_timeout  65;

    # Compressão GZIP
    # Para verificar: curl -H "Accept-Encoding: gzip" -I http://seu-dominio.com
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml;

    # ===================================
    # CONFIGURAÇÃO DO SERVIDOR
    # ===================================
    server {
        listen 80;
        server_name _;
        root /usr/share/nginx/html;
        index index.html;

        # Logs de acesso e erro
        # Para verificar logs: docker logs portal-frontend
        access_log /var/log/nginx/access.log;
        error_log /var/log/nginx/error.log;

        # ===================================
        # SERVIR ARQUIVOS ESTÁTICOS
        # ===================================
        location / {
            try_files $uri $uri/ /index.html;
            
            # Cache para arquivos estáticos (DESABILITADO PARA DESENVOLVIMENTO)
            # Para verificar cache: curl -I http://seu-dominio.com/static/js/main.js
            location ~* \.(js|css)$ {
                expires -1;
                add_header Cache-Control "no-cache, no-store, must-revalidate";
                add_header Pragma "no-cache";
                add_header Expires "0";
            }

            # Cache apenas para imagens e fontes
            location ~* \.(png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
                expires 1d;
                add_header Cache-Control "public";
            }
        }

        # ===================================
        # PROXY PARA API DO BACKEND
        # ===================================
        location /api/ {
            # Proxy para container do backend
            # Para verificar conectividade: docker exec portal-frontend ping portal_backend
            proxy_pass http://portal_backend:3001;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }

        # ===================================
        # HEALTH CHECK
        # ===================================
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # ===================================
        # BLOQUEAR ACESSO A ARQUIVOS SENSÍVEIS
        # ===================================
        location ~ /\. {
            deny all;
        }

        location ~ \.(env|log|conf)$ {
            deny all;
        }
    }
}

# ===================================
# INSTRUÇÕES DE VERIFICAÇÃO
# ===================================

# Para testar configuração:
# docker exec portal-frontend nginx -t

# Para recarregar configuração:
# docker exec portal-frontend nginx -s reload

# Para verificar status:
# docker exec portal-frontend nginx -s status

# Para ver logs:
# docker exec portal-frontend tail -f /var/log/nginx/access.log
# docker exec portal-frontend tail -f /var/log/nginx/error.log
