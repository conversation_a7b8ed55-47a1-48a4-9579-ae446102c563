# Script para preparar deploy no Windows
Write-Host "Preparando deploy do Portal Evolution..." -ForegroundColor Green

# Verificar arquivos obrigatórios
$arquivos = @(
    "Dockerfile.frontend",
    "Dockerfile.backend", 
    "docker-compose.prod.yml",
    "nginx.conf",
    "configuracao.env"
)

$scripts = @(
    "deploy.sh",
    "atualizar.sh",
    "configurar-vps.sh",
    "validar-deploy.sh"
)

Write-Host ""
Write-Host "Verificando arquivos obrigatorios..." -ForegroundColor Yellow

$todosPresentes = $true
foreach ($arquivo in $arquivos) {
    if (Test-Path $arquivo) {
        Write-Host "OK: $arquivo" -ForegroundColor Green
    } else {
        Write-Host "FALTANDO: $arquivo" -ForegroundColor Red
        $todosPresentes = $false
    }
}

Write-Host ""
Write-Host "Verificando scripts..." -ForegroundColor Yellow

foreach ($script in $scripts) {
    if (Test-Path $script) {
        Write-Host "OK: $script" -ForegroundColor Green
    } else {
        Write-Host "FALTANDO: $script" -ForegroundColor Red
        $todosPresentes = $false
    }
}

Write-Host ""
Write-Host "Verificando configuracao..." -ForegroundColor Yellow

if (Test-Path "configuracao.env") {
    $config = Get-Content "configuracao.env" -Raw
    
    if ($config -match "SUA_SENHA_SEGURA_AQUI") {
        Write-Host "AVISO: DB_PASSWORD ainda esta com valor padrao" -ForegroundColor Yellow
    } else {
        Write-Host "OK: DB_PASSWORD foi alterado" -ForegroundColor Green
    }
    
    if ($config -match "SUA_CHAVE_JWT_MUITO_SEGURA_AQUI") {
        Write-Host "AVISO: JWT_SECRET ainda esta com valor padrao" -ForegroundColor Yellow
    } else {
        Write-Host "OK: JWT_SECRET foi alterado" -ForegroundColor Green
    }
    
    if ($config -match "VPS_HOST=************") {
        Write-Host "OK: VPS_HOST configurado" -ForegroundColor Green
    } else {
        Write-Host "AVISO: Verifique VPS_HOST" -ForegroundColor Yellow
    }
}

Write-Host ""
if ($todosPresentes) {
    Write-Host "VALIDACAO PASSOU! Sistema pronto para deploy." -ForegroundColor Green
    Write-Host ""
    Write-Host "Proximos passos:" -ForegroundColor Cyan
    Write-Host "1. Conecte na VPS: ssh root@************" -ForegroundColor White
    Write-Host "2. Execute: bash configurar-vps.sh" -ForegroundColor White
    Write-Host "3. Edite: nano configuracao.env" -ForegroundColor White
    Write-Host "4. Execute: bash deploy.sh" -ForegroundColor White
    Write-Host ""
    Write-Host "Para atualizar depois:" -ForegroundColor Cyan
    Write-Host "1. git push origin master (no seu PC)" -ForegroundColor White
    Write-Host "2. bash atualizar.sh (na VPS)" -ForegroundColor White
} else {
    Write-Host "VALIDACAO FALHOU! Corrija os arquivos faltando." -ForegroundColor Red
}

Write-Host ""
Write-Host "Arquivos criados para deploy:" -ForegroundColor Cyan
Write-Host "- configuracao.env (configure seus dados)" -ForegroundColor White
Write-Host "- docker-compose.prod.yml (configuracao Docker)" -ForegroundColor White
Write-Host "- deploy.sh (script de deploy)" -ForegroundColor White
Write-Host "- atualizar.sh (script de atualizacao)" -ForegroundColor White
Write-Host "- configurar-vps.sh (configuracao da VPS)" -ForegroundColor White
