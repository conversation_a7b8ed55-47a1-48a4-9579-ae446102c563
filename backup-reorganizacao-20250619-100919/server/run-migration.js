const { query } = require('./database/connection');
const fs = require('fs');
const path = require('path');

async function runMigration() {
  try {
    console.log('🔄 Executando migração de sepultamentos...');
    
    const sqlPath = path.join(__dirname, 'database', 'migrate-sepultamentos.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    await query(sql);
    
    console.log('✅ Migração executada com sucesso!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Erro ao executar migração:', error);
    process.exit(1);
  }
}

runMigration();
