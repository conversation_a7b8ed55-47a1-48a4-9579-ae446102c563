import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { sepultamentoService, produtoService } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import Modal from '../components/Modal';
import SepultamentoModal from '../components/SepultamentoModal';

const PageContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
`;

const PageTitle = styled.h1`
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
`;

const AddButton = styled.button`
  background: linear-gradient(135deg, #1e3a8a 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
`;

const TableContainer = styled.div`
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  overflow: hidden;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
`;

const TableHeader = styled.thead`
  background: #f9fafb;
`;

const TableRow = styled.tr`
  border-bottom: 1px solid #e5e7eb;

  &:hover {
    background: #f9fafb;
  }
`;

const TableHeaderCell = styled.th`
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
`;

const TableCell = styled.td`
  padding: 12px 16px;
  color: #6b7280;
  font-size: 0.875rem;
`;

const StatusBadge = styled.span`
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  background: ${props => props.exumado ? '#fef2f2' : '#f0fdf4'};
  color: ${props => props.exumado ? '#dc2626' : '#059669'};
`;

const LoadingSpinner = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  
  &::after {
    content: '';
    width: 40px;
    height: 40px;
    border: 4px solid #e5e7eb;
    border-top: 4px solid #059669;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 48px 24px;
  color: #6b7280;
`;

const EmptyIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 16px;
`;

const EmptyTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 500;
  color: #374151;
  margin: 0 0 8px 0;
`;

const EmptyDescription = styled.p`
  margin: 0;
`;

const ActionButton = styled.button`
  background: none;
  border: none;
  color: #059669;
  cursor: pointer;
  font-size: 0.875rem;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;

  &:hover {
    background: #f0fdf4;
  }
`;

const DetailModal = styled.div`
  max-height: 70vh;
  overflow-y: auto;
`;

const DetailSection = styled.div`
  margin-bottom: 24px;
`;

const DetailTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 12px 0;
`;

const DetailGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
`;

const DetailItem = styled.div`
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
`;

const DetailLabel = styled.div`
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 4px;
  text-transform: uppercase;
  font-weight: 500;
`;

const DetailValue = styled.div`
  font-size: 1rem;
  color: #1f2937;
  font-weight: 500;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 12px;
  margin-top: 24px;
  flex-wrap: wrap;
`;

const ActionButtonModal = styled.button`
  padding: 10px 20px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  flex: 1;
  min-width: 120px;

  &.primary {
    background: #059669;
    color: white;

    &:hover {
      background: #047857;
    }
  }

  &.secondary {
    background: white;
    color: #374151;
    border-color: #d1d5db;

    &:hover {
      background: #f9fafb;
    }
  }

  &.danger {
    background: #dc2626;
    color: white;

    &:hover {
      background: #b91c1c;
    }
  }
`;

const FilterContainer = styled.div`
  display: flex;
  gap: 16px;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
`;

const FilterSelect = styled.select`
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  min-width: 200px;
  font-size: 0.875rem;
`;

const FilterLabel = styled.label`
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
`;

const SepultamentosPage = () => {
  const [sepultamentos, setSepultamentos] = useState([]);
  const [produtos, setProdutos] = useState([]);
  const [filtroCodigoEstacao, setFiltroCodigoEstacao] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedSepultamento, setSelectedSepultamento] = useState(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showSepultamentoModal, setShowSepultamentoModal] = useState(false);
  const { user, canAddEditExhumeSepultamentos, canDeleteSepultamentos } = useAuth();

  useEffect(() => {
    loadSepultamentos();
    loadProdutos();
  }, []);

  useEffect(() => {
    loadSepultamentos();
  }, [filtroCodigoEstacao]);

  const loadSepultamentos = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = {};
      if (filtroCodigoEstacao) {
        params.codigo_estacao = filtroCodigoEstacao;
      }

      const response = await sepultamentoService.listar(params);
      setSepultamentos(response.data);
    } catch (error) {
      console.error('Erro ao carregar sepultamentos:', error);
      setError('Erro ao carregar sepultamentos');
    } finally {
      setLoading(false);
    }
  };

  const loadProdutos = async () => {
    try {
      const response = await produtoService.listar();
      setProdutos(response.data);
    } catch (error) {
      console.error('Erro ao carregar produtos:', error);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const handleViewDetails = (sepultamento) => {
    setSelectedSepultamento(sepultamento);
    setShowDetailModal(true);
  };

  const handleAddSepultamento = () => {
    setSelectedSepultamento(null);
    setShowSepultamentoModal(true);
  };

  const handleEditSepultamento = (sepultamento) => {
    // Verificar se é cliente tentando editar sepultamento exumado
    if (user.tipo_usuario !== 'admin' && sepultamento.data_exumacao) {
      alert('Sepultamentos exumados só podem ser editados por administradores');
      return;
    }

    setSelectedSepultamento(sepultamento);
    setShowSepultamentoModal(true);
  };

  const canEditSepultamento = (sepultamento) => {
    if (!canAddEditExhumeSepultamentos()) return false;
    if (user.tipo_usuario === 'admin') return true;
    return !sepultamento.data_exumacao; // Cliente só pode editar se não estiver exumado
  };

  const canDeleteSepultamentoItem = (sepultamento) => {
    if (!canDeleteSepultamentos()) return false;
    // Tanto admin quanto cliente podem deletar qualquer sepultamento (sem exigir exumação)
    return true;
  };

  const handleExumar = async (sepultamento) => {
    if (!window.confirm(`Tem certeza que deseja exumar o sepultamento de ${sepultamento.nome_sepultado}?`)) {
      return;
    }

    try {
      const dataExumacao = new Date().toISOString();
      await sepultamentoService.exumar(sepultamento.id, {
        data_exumacao: dataExumacao,
        observacoes_exumacao: 'Exumação realizada via sistema'
      });

      // Recarregar lista
      loadSepultamentos();
      setShowDetailModal(false);
    } catch (error) {
      console.error('Erro ao exumar sepultamento:', error);
      alert('Erro ao exumar sepultamento');
    }
  };

  const handleDeleteSepultamento = async (sepultamento) => {
    if (!canDeleteSepultamentos()) {
      alert('Você não tem permissão para deletar sepultamentos');
      return;
    }

    // Mensagem de confirmação mais clara sobre a permanência da ação
    const confirmMessage = `⚠️ ATENÇÃO: DELEÇÃO PERMANENTE ⚠️

Tem certeza que deseja DELETAR PERMANENTEMENTE o sepultamento de "${sepultamento.nome_sepultado}"?

🚨 IMPORTANTE: Esta ação irá:
• Remover o registro DEFINITIVAMENTE do banco de dados
• NÃO PODE ser desfeita
• O registro será perdido para sempre

Digite "CONFIRMAR" para prosseguir:`;

    const userConfirmation = prompt(confirmMessage);

    if (userConfirmation !== 'CONFIRMAR') {
      alert('Deleção cancelada. O registro foi preservado.');
      return;
    }

    try {
      await sepultamentoService.deletar(sepultamento.id);
      loadSepultamentos();
      setShowDetailModal(false);
      alert('✅ Sepultamento deletado permanentemente com sucesso!');
    } catch (error) {
      console.error('Erro ao deletar sepultamento:', error);
      const errorMessage = error.response?.data?.error || 'Erro interno do servidor';
      alert(`❌ Erro ao deletar sepultamento: ${errorMessage}`);
    }
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <PageContainer>
        <div style={{ color: '#dc2626', textAlign: 'center', padding: '48px' }}>
          {error}
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <PageHeader>
        <PageTitle>Book de Sepultamentos</PageTitle>
        {canAddEditExhumeSepultamentos() && (
          <AddButton onClick={handleAddSepultamento}>
            + Novo Sepultamento
          </AddButton>
        )}
      </PageHeader>

      <FilterContainer>
        <FilterLabel htmlFor="filtro-estacao">Filtrar por Produto/Estação:</FilterLabel>
        <FilterSelect
          id="filtro-estacao"
          value={filtroCodigoEstacao}
          onChange={(e) => setFiltroCodigoEstacao(e.target.value)}
        >
          <option value="">Todos os produtos</option>
          {produtos.map(produto => (
            <option key={produto.codigo_estacao} value={produto.codigo_estacao}>
              {produto.denominacao} ({produto.codigo_estacao})
            </option>
          ))}
        </FilterSelect>
      </FilterContainer>

      <TableContainer>
        {sepultamentos.length === 0 ? (
          <EmptyState>
            <EmptyIcon>⚰️</EmptyIcon>
            <EmptyTitle>Nenhum sepultamento encontrado</EmptyTitle>
            <EmptyDescription>
              Comece adicionando o primeiro sepultamento ao sistema.
            </EmptyDescription>
          </EmptyState>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHeaderCell>Nome do Sepultado</TableHeaderCell>
                <TableHeaderCell>Código Estação</TableHeaderCell>
                <TableHeaderCell>Denominação do Bloco</TableHeaderCell>
                <TableHeaderCell>Gaveta</TableHeaderCell>
                <TableHeaderCell>Data Sepultamento</TableHeaderCell>
                <TableHeaderCell>Horário</TableHeaderCell>
                <TableHeaderCell>Status</TableHeaderCell>
                <TableHeaderCell>Ações</TableHeaderCell>
              </TableRow>
            </TableHeader>
            <tbody>
              {sepultamentos.map((sepultamento) => (
                <TableRow key={sepultamento.id} onClick={() => handleViewDetails(sepultamento)} style={{ cursor: 'pointer' }}>
                  <TableCell>{sepultamento.nome_sepultado}</TableCell>
                  <TableCell>{sepultamento.codigo_estacao}</TableCell>
                  <TableCell>{sepultamento.denominacao_bloco}</TableCell>
                  <TableCell>{sepultamento.numero_gaveta}</TableCell>
                  <TableCell>{formatDate(sepultamento.data_sepultamento)}</TableCell>
                  <TableCell>{sepultamento.horario_sepultamento || '-'}</TableCell>
                  <TableCell>
                    <StatusBadge exumado={sepultamento.status_exumacao}>
                      {sepultamento.status_exumacao ? 'Exumado' : 'Sepultado'}
                    </StatusBadge>
                  </TableCell>
                  <TableCell>
                    <ActionButton onClick={(e) => {
                      e.stopPropagation();
                      handleViewDetails(sepultamento);
                    }}>
                      Ver Detalhes
                    </ActionButton>
                  </TableCell>
                </TableRow>
              ))}
            </tbody>
          </Table>
        )}
      </TableContainer>

      {/* Modal de Detalhes */}
      <Modal
        isOpen={showDetailModal}
        onClose={() => setShowDetailModal(false)}
        title={`Detalhes: ${selectedSepultamento?.nome_sepultado}`}
        maxWidth="800px"
      >
        {selectedSepultamento && (
          <DetailModal>
            <DetailSection>
              <DetailTitle>Informações do Sepultado</DetailTitle>
              <DetailGrid>
                <DetailItem>
                  <DetailLabel>Nome</DetailLabel>
                  <DetailValue>{selectedSepultamento.nome_sepultado}</DetailValue>
                </DetailItem>
                <DetailItem>
                  <DetailLabel>Data de Sepultamento</DetailLabel>
                  <DetailValue>{formatDate(selectedSepultamento.data_sepultamento)}</DetailValue>
                </DetailItem>
                <DetailItem>
                  <DetailLabel>Horário de Sepultamento</DetailLabel>
                  <DetailValue>{selectedSepultamento.horario_sepultamento || '-'}</DetailValue>
                </DetailItem>
                {selectedSepultamento.observacoes && (
                  <DetailItem>
                    <DetailLabel>Observações</DetailLabel>
                    <DetailValue>{selectedSepultamento.observacoes}</DetailValue>
                  </DetailItem>
                )}
              </DetailGrid>
            </DetailSection>

            <DetailSection>
              <DetailTitle>Localização</DetailTitle>
              <DetailGrid>
                <DetailItem>
                  <DetailLabel>Código Estação</DetailLabel>
                  <DetailValue>{selectedSepultamento.codigo_estacao}</DetailValue>
                </DetailItem>
                <DetailItem>
                  <DetailLabel>Denominação do Bloco</DetailLabel>
                  <DetailValue>{selectedSepultamento.denominacao_bloco}</DetailValue>
                </DetailItem>
                <DetailItem>
                  <DetailLabel>Gaveta</DetailLabel>
                  <DetailValue>{selectedSepultamento.numero_gaveta}</DetailValue>
                </DetailItem>
                <DetailItem>
                  <DetailLabel>Localização Completa</DetailLabel>
                  <DetailValue>{selectedSepultamento.localizacao}</DetailValue>
                </DetailItem>
              </DetailGrid>
            </DetailSection>



            {selectedSepultamento.data_exumacao && (
              <DetailSection>
                <DetailTitle>Informações de Exumação</DetailTitle>
                <DetailGrid>
                  <DetailItem>
                    <DetailLabel>Data de Exumação</DetailLabel>
                    <DetailValue>{formatDate(selectedSepultamento.data_exumacao)}</DetailValue>
                  </DetailItem>
                  <DetailItem>
                    <DetailLabel>Observações</DetailLabel>
                    <DetailValue>{selectedSepultamento.observacoes_exumacao || '-'}</DetailValue>
                  </DetailItem>
                </DetailGrid>
              </DetailSection>
            )}

            <DetailSection>
              <DetailTitle>Status</DetailTitle>
              <DetailGrid>
                <DetailItem>
                  <DetailLabel>Status Atual</DetailLabel>
                  <DetailValue>
                    <StatusBadge exumado={selectedSepultamento.status_exumacao}>
                      {selectedSepultamento.status_exumacao ? 'Exumado' : 'Sepultado'}
                    </StatusBadge>
                  </DetailValue>
                </DetailItem>
              </DetailGrid>
            </DetailSection>

            <ActionButtons>
              {canEditSepultamento(selectedSepultamento) && (
                <ActionButtonModal
                  className="primary"
                  onClick={() => {
                    setShowDetailModal(false);
                    handleEditSepultamento(selectedSepultamento);
                  }}
                >
                  Editar
                </ActionButtonModal>
              )}

              {canAddEditExhumeSepultamentos() && !selectedSepultamento.status_exumacao && (
                <ActionButtonModal
                  className="danger"
                  onClick={() => handleExumar(selectedSepultamento)}
                >
                  Exumar
                </ActionButtonModal>
              )}

              {canDeleteSepultamentoItem(selectedSepultamento) && (
                <ActionButtonModal
                  className="danger"
                  onClick={() => handleDeleteSepultamento(selectedSepultamento)}
                >
                  Deletar
                </ActionButtonModal>
              )}

              <ActionButtonModal
                className="secondary"
                onClick={() => setShowDetailModal(false)}
              >
                Fechar
              </ActionButtonModal>
            </ActionButtons>
          </DetailModal>
        )}
      </Modal>

      {/* Modal de Sepultamento */}
      <SepultamentoModal
        isOpen={showSepultamentoModal}
        onClose={() => setShowSepultamentoModal(false)}
        sepultamento={selectedSepultamento}
        onSuccess={loadSepultamentos}
      />
    </PageContainer>
  );
};

export default SepultamentosPage;
