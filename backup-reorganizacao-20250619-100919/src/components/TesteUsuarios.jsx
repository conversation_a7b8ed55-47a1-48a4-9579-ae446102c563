import React, { useState } from 'react';
import { usuarioService, authService } from '../services/api';

const TesteUsuarios = () => {
  const [resultados, setResultados] = useState([]);
  const [testando, setTestando] = useState(false);

  const adicionarResultado = (teste, sucesso, detalhes) => {
    const resultado = {
      teste,
      sucesso,
      detalhes,
      timestamp: new Date().toLocaleTimeString()
    };
    setResultados(prev => [...prev, resultado]);
    console.log(`${sucesso ? '✅' : '❌'} ${teste}:`, detalhes);
  };

  const executarTestes = async () => {
    setTestando(true);
    setResultados([]);

    try {
      // Teste 1: Criar usuário admin
      adicionarResultado('Iniciando testes', true, 'Começando bateria de testes');

      // Teste 2: Criar usuário cliente
      const dadosUsuarioTeste = {
        nome: 'Usuário Teste',
        email: '<EMAIL>',
        senha: 'senha123',
        tipo_usuario: 'admin',
        ativo: true
      };

      try {
        const responseCreate = await usuarioService.criar(dadosUsuarioTeste);
        adicionarResultado('Criar usuário', true, `Usuário criado com ID: ${responseCreate.data.id}`);
        
        const usuarioId = responseCreate.data.id;

        // Teste 3: Listar usuários
        try {
          const responseList = await usuarioService.listar();
          const usuarioEncontrado = responseList.data.find(u => u.id === usuarioId);
          adicionarResultado('Listar usuários', !!usuarioEncontrado, 
            usuarioEncontrado ? 'Usuário encontrado na lista' : 'Usuário não encontrado na lista');
        } catch (error) {
          adicionarResultado('Listar usuários', false, error.message);
        }

        // Teste 4: Buscar usuário específico
        try {
          const responseBuscar = await usuarioService.buscar(usuarioId);
          adicionarResultado('Buscar usuário', true, `Usuário encontrado: ${responseBuscar.data.nome}`);
        } catch (error) {
          adicionarResultado('Buscar usuário', false, error.message);
        }

        // Teste 5: Atualizar usuário
        try {
          const dadosAtualizacao = {
            nome: 'Usuário Teste Atualizado',
            email: '<EMAIL>',
            tipo_usuario: 'admin',
            ativo: true
          };
          await usuarioService.atualizar(usuarioId, dadosAtualizacao);
          adicionarResultado('Atualizar usuário', true, 'Usuário atualizado com sucesso');
        } catch (error) {
          adicionarResultado('Atualizar usuário', false, error.message);
        }

        // Teste 6: Testar login com usuário criado
        try {
          const responseLogin = await authService.login('<EMAIL>', 'senha123');
          adicionarResultado('Login usuário', true, 'Login realizado com sucesso');
          
          // Fazer logout para não interferir
          await authService.logout();
        } catch (error) {
          adicionarResultado('Login usuário', false, error.message);
        }

        // Teste 7: Deletar usuário
        try {
          await usuarioService.deletar(usuarioId);
          adicionarResultado('Deletar usuário', true, 'Usuário deletado com sucesso');
        } catch (error) {
          adicionarResultado('Deletar usuário', false, error.message);
        }

        // Teste 8: Verificar se usuário foi deletado
        try {
          await usuarioService.buscar(usuarioId);
          adicionarResultado('Verificar deleção', false, 'Usuário ainda existe após deleção');
        } catch (error) {
          adicionarResultado('Verificar deleção', true, 'Usuário não encontrado (deletado corretamente)');
        }

      } catch (error) {
        adicionarResultado('Criar usuário', false, error.response?.data?.error || error.message);
      }

      // Teste específico para o usuário <EMAIL>
      try {
        const responseLogin = await authService.login('<EMAIL>', '321');
        adicionarResultado('Login Laura', true, 'Login da Laura realizado com sucesso');
        await authService.logout();
      } catch (error) {
        adicionarResultado('Login Laura', false, error.response?.data?.error || error.message);
      }

    } catch (error) {
      adicionarResultado('Erro geral', false, error.message);
    } finally {
      setTestando(false);
    }
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h2>🧪 Teste de Funcionalidades de Usuários</h2>
      
      <button 
        onClick={executarTestes} 
        disabled={testando}
        style={{
          padding: '10px 20px',
          backgroundColor: testando ? '#ccc' : '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '5px',
          cursor: testando ? 'not-allowed' : 'pointer',
          marginBottom: '20px'
        }}
      >
        {testando ? 'Executando Testes...' : 'Executar Testes'}
      </button>

      <div style={{ marginTop: '20px' }}>
        <h3>📋 Resultados dos Testes:</h3>
        {resultados.length === 0 && !testando && (
          <p>Clique no botão acima para executar os testes</p>
        )}
        
        {resultados.map((resultado, index) => (
          <div 
            key={index}
            style={{
              padding: '10px',
              margin: '5px 0',
              backgroundColor: resultado.sucesso ? '#d4edda' : '#f8d7da',
              border: `1px solid ${resultado.sucesso ? '#c3e6cb' : '#f5c6cb'}`,
              borderRadius: '5px',
              color: resultado.sucesso ? '#155724' : '#721c24'
            }}
          >
            <strong>{resultado.sucesso ? '✅' : '❌'} {resultado.teste}</strong>
            <br />
            <small>{resultado.timestamp}</small>
            <br />
            {resultado.detalhes}
          </div>
        ))}
      </div>

      <div style={{ marginTop: '30px', padding: '15px', backgroundColor: '#f8f9fa', borderRadius: '5px' }}>
        <h4>📝 Testes Executados:</h4>
        <ul>
          <li>✅ Criar usuário</li>
          <li>✅ Listar usuários</li>
          <li>✅ Buscar usuário específico</li>
          <li>✅ Atualizar usuário</li>
          <li>✅ Testar login</li>
          <li>✅ Deletar usuário</li>
          <li>✅ Verificar deleção</li>
          <li>✅ Teste específico login Laura</li>
        </ul>
      </div>
    </div>
  );
};

export default TesteUsuarios;
