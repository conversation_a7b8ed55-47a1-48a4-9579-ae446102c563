const express = require('express');
const { query } = require('../database/connection');
const { logAction, logProduto, logBloco, logSubBloco } = require('../utils/logger');

const router = express.Router();

// Listar produtos - TODOS os produtos (ativos e inativos)
router.get('/', async (req, res) => {
  try {
    const { codigo_cliente, tipo } = req.query;

    console.log('📋 Listando TODOS os produtos (ativos e inativos)...');

    let whereClause = 'WHERE 1=1';
    let params = [];
    let paramCount = 0;

    // Se não for admin, filtrar por código do cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      paramCount++;
      whereClause += ` AND p.codigo_cliente = $${paramCount}`;
      params.push(req.user.codigo_cliente);
    } else if (codigo_cliente) {
      paramCount++;
      whereClause += ` AND p.codigo_cliente = $${paramCount}`;
      params.push(codigo_cliente);
    }

    if (tipo) {
      paramCount++;
      whereClause += ` AND p.tipo = $${paramCount}`;
      params.push(tipo);
    }

    const result = await query(`
      SELECT
        p.id,
        p.codigo_cliente,
        p.codigo_estacao,
        p.meses_para_exumar,
        p.denominacao,
        p.observacao,
        p.ativo,
        p.created_at,
        p.updated_at,
        c.nome_fantasia as nome_cliente
      FROM produtos p
      LEFT JOIN clientes c ON p.codigo_cliente = c.codigo_cliente
      ${whereClause}
      ORDER BY p.ativo DESC, p.denominacao ASC
    `, params);

    console.log(`📊 Total de produtos encontrados: ${result.rows.length}`);
    console.log(`✅ Ativos: ${result.rows.filter(p => p.ativo).length}`);
    console.log(`❌ Inativos: ${result.rows.filter(p => !p.ativo).length}`);

    res.json(result.rows);
  } catch (error) {
    console.error('Erro ao listar produtos:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Buscar estatísticas de produtos
router.get('/estatisticas', async (req, res) => {
  try {
    let whereClause = 'WHERE p.ativo = true';
    let params = [];

    // Se não for admin, filtrar por código do cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      whereClause += ' AND p.codigo_cliente = $1';
      params.push(req.user.codigo_cliente);
    }

    const result = await query(`
      SELECT
        p.id,
        p.codigo_estacao,
        p.denominacao,
        p.meses_para_exumar,
        p.observacao,
        p.ativo,
        p.codigo_cliente,
        c.nome_fantasia as nome_cliente,
        COUNT(DISTINCT b.id) as total_blocos,
        COUNT(DISTINCT sb.id) as total_sub_blocos,
        COUNT(DISTINCT g.id) as total_gavetas,
        COUNT(DISTINCT CASE WHEN g.disponivel = false THEN g.id END) as gavetas_ocupadas,
        COUNT(DISTINCT CASE WHEN g.disponivel = true THEN g.id END) as gavetas_disponiveis,
        ROUND(
          (COUNT(DISTINCT CASE WHEN g.disponivel = false THEN g.id END)::decimal /
           NULLIF(COUNT(DISTINCT g.id), 0)) * 100, 2
        ) as percentual_ocupacao
      FROM produtos p
      LEFT JOIN clientes c ON p.codigo_cliente = c.codigo_cliente
      LEFT JOIN blocos b ON p.id = b.produto_id AND b.ativo = true
      LEFT JOIN sub_blocos sb ON b.id = sb.bloco_id AND sb.ativo = true
      LEFT JOIN gavetas g ON sb.id = g.sub_bloco_id AND g.ativo = true
      ${whereClause}
      GROUP BY p.id, p.codigo_estacao, p.denominacao, p.meses_para_exumar, p.observacao, p.ativo, p.codigo_cliente, c.nome_fantasia
      ORDER BY p.denominacao
    `, params);

    res.json(result.rows);
  } catch (error) {
    console.error('Erro ao buscar estatísticas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Buscar produto por ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    let whereClause = 'WHERE p.id = $1';
    let params = [id];

    // Se não for admin, filtrar por código do cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      whereClause += ' AND p.codigo_cliente = $2';
      params.push(req.user.codigo_cliente);
    }

    const result = await query(`
      SELECT 
        p.*,
        c.nome as nome_cliente
      FROM produtos p
      LEFT JOIN clientes c ON p.codigo_cliente = c.codigo_cliente
      ${whereClause}
    `, params);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Produto não encontrado' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Erro ao buscar produto:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Listar blocos de um produto
router.get('/:id/blocos', async (req, res) => {
  try {
    const { id } = req.params;
    
    let whereClause = 'WHERE b.produto_id = $1 AND b.ativo = true';
    let params = [id];

    // Se não for admin, verificar se o produto pertence ao cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      whereClause += ' AND p.codigo_cliente = $2';
      params.push(req.user.codigo_cliente);
    }

    const result = await query(`
      SELECT b.*
      FROM blocos b
      LEFT JOIN produtos p ON b.produto_id = p.id
      ${whereClause}
      ORDER BY b.codigo_bloco
    `, params);

    res.json(result.rows);
  } catch (error) {
    console.error('Erro ao listar blocos:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Listar sub-blocos de um bloco
router.get('/blocos/:bloco_id/sub-blocos', async (req, res) => {
  try {
    const { bloco_id } = req.params;

    let whereClause = 'WHERE sb.bloco_id = $1 AND sb.ativo = true';
    let params = [bloco_id];

    // Se não for admin, verificar se o bloco pertence ao cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      whereClause += ' AND p.codigo_cliente = $2';
      params.push(req.user.codigo_cliente);
    }

    const result = await query(`
      SELECT
        sb.*,
        COUNT(g.id) as total_gavetas,
        COALESCE(
          STRING_AGG(
            CASE
              WHEN g.numero_gaveta IS NOT NULL
              THEN g.numero_gaveta::text
              ELSE NULL
            END,
            ','
            ORDER BY g.numero_gaveta
          ),
          ''
        ) as gavetas_numeros
      FROM sub_blocos sb
      LEFT JOIN blocos b ON sb.bloco_id = b.id
      LEFT JOIN produtos p ON b.produto_id = p.id
      LEFT JOIN gavetas g ON sb.id = g.sub_bloco_id AND g.ativo = true
      ${whereClause}
      GROUP BY sb.id
      ORDER BY sb.codigo_sub_bloco
    `, params);

    // Processar ranges de gavetas
    const subBlocosComRanges = result.rows.map(subBloco => {
      console.log('🔍 Processando sub-bloco na listagem:', subBloco.nome, 'gavetas_numeros:', subBloco.gavetas_numeros);

      if (subBloco.gavetas_numeros && subBloco.gavetas_numeros.trim() !== '') {
        const numeros = subBloco.gavetas_numeros.split(',').map(n => parseInt(n)).sort((a, b) => a - b);
        console.log('📊 Números encontrados na listagem:', numeros);

        const ranges = [];
        let inicio = numeros[0];
        let fim = numeros[0];

        for (let i = 1; i < numeros.length; i++) {
          if (numeros[i] === fim + 1) {
            fim = numeros[i];
          } else {
            ranges.push(inicio === fim ? `${inicio}` : `${inicio}-${fim}`);
            inicio = numeros[i];
            fim = numeros[i];
          }
        }

        if (numeros.length > 0) {
          ranges.push(inicio === fim ? `${inicio}` : `${inicio}-${fim}`);
        }

        subBloco.ranges_gavetas = ranges.join(', ');
        console.log('✅ Ranges calculados na listagem:', subBloco.ranges_gavetas);
      } else {
        subBloco.ranges_gavetas = '';
        console.log('⚠️ Nenhuma gaveta na listagem para:', subBloco.nome);
      }

      delete subBloco.gavetas_numeros; // Remove campo temporário
      return subBloco;
    });

    res.json(subBlocosComRanges);
  } catch (error) {
    console.error('Erro ao listar sub-blocos:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar novo produto (apenas admin)
router.post('/', async (req, res) => {
  if (req.user.tipo_usuario !== 'admin') {
    return res.status(403).json({ error: 'Acesso negado. Apenas administradores.' });
  }

  try {
    const { codigo_cliente, codigo_estacao, meses_para_exumar, denominacao, observacao } = req.body;

    console.log('🔍 Dados recebidos para criação de produto:', {
      codigo_cliente,
      codigo_estacao,
      meses_para_exumar,
      denominacao,
      observacao
    });

    if (!codigo_cliente || !codigo_estacao || !denominacao) {
      console.log('❌ Campos obrigatórios faltando');
      return res.status(400).json({ error: 'Código do cliente, código da estação e denominação são obrigatórios' });
    }

    // Verificar se o código da estação já existe para este cliente
    const existingEstacao = await query(
      'SELECT id FROM produtos WHERE codigo_cliente = $1 AND codigo_estacao = $2',
      [codigo_cliente, codigo_estacao]
    );

    if (existingEstacao.rows.length > 0) {
      return res.status(400).json({ error: 'Código da estação já existe para este cliente' });
    }

    console.log('✅ Validações passaram, inserindo produto...');

    const result = await query(`
      INSERT INTO produtos (codigo_cliente, codigo_estacao, meses_para_exumar, denominacao, observacao, nome, tipo, descricao, localizacao, ativo)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING *
    `, [
      codigo_cliente,
      codigo_estacao,
      meses_para_exumar || 24,
      denominacao,
      observacao || '',
      denominacao, // nome
      'ETEN', // tipo
      observacao || '', // descricao
      codigo_estacao, // localizacao
      true // ativo
    ]);

    console.log('✅ Produto inserido no banco de dados');

    const novoProduto = result.rows[0];

    // Registrar log de criação
    await logProduto(
      req.user.id,
      'CREATE',
      novoProduto,
      null,
      req.ip,
      req.get('User-Agent')
    );

    res.status(201).json({
      message: 'Produto cadastrado com sucesso',
      produto: novoProduto
    });

  } catch (error) {
    console.error('Erro ao criar produto:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar produto (apenas admin)
router.put('/:id', async (req, res) => {
  if (req.user.tipo_usuario !== 'admin') {
    return res.status(403).json({ error: 'Acesso negado. Apenas administradores.' });
  }

  try {
    const { id } = req.params;
    const { codigo_cliente, codigo_estacao, meses_para_exumar, denominacao, observacao, ativo } = req.body;

    // Buscar dados anteriores
    const dadosAnteriores = await query('SELECT * FROM produtos WHERE id = $1', [id]);

    if (dadosAnteriores.rows.length === 0) {
      return res.status(404).json({ error: 'Produto não encontrado' });
    }

    // Verificar se o código da estação já existe para outro produto do mesmo cliente
    if (codigo_estacao && codigo_cliente) {
      const existingEstacao = await query(
        'SELECT id FROM produtos WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND id != $3',
        [codigo_cliente, codigo_estacao, id]
      );

      if (existingEstacao.rows.length > 0) {
        return res.status(400).json({ error: 'Código da estação já existe para este cliente' });
      }
    }

    const result = await query(`
      UPDATE produtos
      SET codigo_cliente = $1, codigo_estacao = $2, meses_para_exumar = $3, denominacao = $4, observacao = $5, ativo = $6,
          nome = $7, tipo = $8, descricao = $9, localizacao = $10, updated_at = CURRENT_TIMESTAMP
      WHERE id = $11
      RETURNING *
    `, [
      codigo_cliente || dadosAnteriores.rows[0].codigo_cliente,
      codigo_estacao || dadosAnteriores.rows[0].codigo_estacao,
      meses_para_exumar || dadosAnteriores.rows[0].meses_para_exumar,
      denominacao || dadosAnteriores.rows[0].denominacao,
      observacao || dadosAnteriores.rows[0].observacao,
      ativo !== undefined ? ativo : dadosAnteriores.rows[0].ativo,
      denominacao || dadosAnteriores.rows[0].denominacao, // nome
      'ETEN', // tipo
      observacao || dadosAnteriores.rows[0].observacao || '', // descricao
      codigo_estacao || dadosAnteriores.rows[0].codigo_estacao || '', // localizacao
      id
    ]);

    const produtoAtualizado = result.rows[0];

    // Registrar log de edição
    await logProduto(
      req.user.id,
      'EDIT',
      produtoAtualizado,
      dadosAnteriores.rows[0],
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      message: 'Produto atualizado com sucesso',
      produto: produtoAtualizado
    });

  } catch (error) {
    console.error('Erro ao atualizar produto:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar novo bloco
router.post('/:id/blocos', async (req, res) => {
  if (req.user.tipo_usuario !== 'admin') {
    return res.status(403).json({ error: 'Acesso negado. Apenas administradores.' });
  }

  try {
    const { id } = req.params;
    const { codigo_bloco, nome, descricao } = req.body;

    if (!codigo_bloco || !nome) {
      return res.status(400).json({ error: 'Código do bloco e nome são obrigatórios' });
    }

    const result = await query(`
      INSERT INTO blocos (produto_id, codigo_bloco, nome, descricao, ativo)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `, [id, codigo_bloco, nome, descricao, true]);

    const novoBloco = result.rows[0];

    // Registrar log da criação
    await logBloco(
      req.user.id,
      'CREATE',
      novoBloco,
      null,
      req.ip,
      req.get('User-Agent')
    );

    console.log('✅ Bloco criado e log registrado:', novoBloco.id);

    res.status(201).json({
      message: 'Bloco cadastrado com sucesso',
      bloco: novoBloco
    });

  } catch (error) {
    console.error('Erro ao criar bloco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar bloco
router.put('/blocos/:bloco_id', async (req, res) => {
  if (req.user.tipo_usuario !== 'admin') {
    return res.status(403).json({ error: 'Acesso negado. Apenas administradores.' });
  }

  try {
    const { bloco_id } = req.params;
    const { codigo_bloco, nome, descricao, ativo } = req.body;

    const dadosAnteriores = await query('SELECT * FROM blocos WHERE id = $1', [bloco_id]);

    if (dadosAnteriores.rows.length === 0) {
      return res.status(404).json({ error: 'Bloco não encontrado' });
    }

    const result = await query(`
      UPDATE blocos
      SET codigo_bloco = $1, nome = $2, descricao = $3, ativo = $4, updated_at = CURRENT_TIMESTAMP
      WHERE id = $5
      RETURNING *
    `, [
      codigo_bloco || dadosAnteriores.rows[0].codigo_bloco,
      nome || dadosAnteriores.rows[0].nome,
      descricao !== undefined ? descricao : dadosAnteriores.rows[0].descricao,
      ativo !== undefined ? ativo : dadosAnteriores.rows[0].ativo,
      bloco_id
    ]);

    const blocoAtualizado = result.rows[0];

    // Registrar log da edição
    await logBloco(
      req.user.id,
      'EDIT',
      blocoAtualizado,
      dadosAnteriores.rows[0],
      req.ip,
      req.get('User-Agent')
    );

    console.log('✅ Bloco atualizado e log registrado:', blocoAtualizado.id);

    res.json({
      message: 'Bloco atualizado com sucesso',
      bloco: blocoAtualizado
    });

  } catch (error) {
    console.error('Erro ao atualizar bloco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar novo sub-bloco
router.post('/blocos/:bloco_id/sub-blocos', async (req, res) => {
  if (req.user.tipo_usuario !== 'admin') {
    return res.status(403).json({ error: 'Acesso negado. Apenas administradores.' });
  }

  try {
    const { bloco_id } = req.params;
    const { codigo_sub_bloco, nome, descricao, numeracao_gavetas } = req.body;

    if (!codigo_sub_bloco || !nome) {
      return res.status(400).json({ error: 'Código do sub-bloco e nome são obrigatórios' });
    }

    // Verificar se já existe um sub-bloco com o mesmo código no bloco
    const subBlocoExistente = await query(
      'SELECT id, nome FROM sub_blocos WHERE bloco_id = $1 AND codigo_sub_bloco = $2 AND ativo = true',
      [bloco_id, codigo_sub_bloco]
    );

    if (subBlocoExistente.rows.length > 0) {
      return res.status(400).json({
        error: `Já existe um sub-bloco com o código "${codigo_sub_bloco}" neste bloco: ${subBlocoExistente.rows[0].nome}`
      });
    }

    const result = await query(`
      INSERT INTO sub_blocos (bloco_id, codigo_sub_bloco, nome, descricao, ativo)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `, [bloco_id, codigo_sub_bloco, nome, descricao, true]);

    const novoSubBloco = result.rows[0];

    // Criar gavetas se numeração foi fornecida
    if (numeracao_gavetas && numeracao_gavetas.length > 0) {
      // Verificar se há conflito com gavetas de outros sub-blocos do mesmo bloco
      const gavetasDesejadas = [];
      for (const range of numeracao_gavetas) {
        const { inicio, fim } = range;
        for (let num = inicio; num <= fim; num++) {
          gavetasDesejadas.push(num);
        }
      }

      // Verificar conflitos dentro do mesmo produto/estação (não apenas bloco)
      const gavetasConflitantes = await query(`
        SELECT
          g.numero_gaveta,
          sb.nome as sub_bloco_nome,
          sb.codigo_sub_bloco,
          b.nome as bloco_nome,
          b.codigo_bloco
        FROM gavetas g
        JOIN sub_blocos sb ON g.sub_bloco_id = sb.id
        JOIN blocos b ON sb.bloco_id = b.id
        JOIN produtos p ON b.produto_id = p.id
        WHERE p.id = (
          SELECT p2.id
          FROM blocos b2
          JOIN produtos p2 ON b2.produto_id = p2.id
          WHERE b2.id = $1
        )
          AND sb.ativo = true
          AND g.ativo = true
          AND g.numero_gaveta = ANY($2)
        ORDER BY g.numero_gaveta
      `, [bloco_id, gavetasDesejadas]);

      if (gavetasConflitantes.rows.length > 0) {
        const conflitos = gavetasConflitantes.rows.map(row =>
          `Gaveta ${row.numero_gaveta} (${row.sub_bloco_nome} - ${row.bloco_nome})`
        ).join(', ');

        return res.status(400).json({
          error: `Range não é válido para esta estação. As seguintes gavetas já estão cadastradas em outros sub-blocos: ${conflitos}`
        });
      }

      for (const range of numeracao_gavetas) {
        const { inicio, fim, posicao_x_inicial = 1, posicao_y_inicial = 1 } = range;

        let posX = posicao_x_inicial;
        let posY = posicao_y_inicial;
        const maxX = 5; // Grid padrão 5x4

        for (let num = inicio; num <= fim; num++) {
          await query(`
            INSERT INTO gavetas (sub_bloco_id, numero_gaveta, posicao_x, posicao_y, altura_especial, disponivel, ativo)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
          `, [novoSubBloco.id, num, posX, posY, 1.0, true, true]);

          console.log(`✅ Gaveta ${num} criada para sub-bloco ${novoSubBloco.id}`);

          posX++;
          if (posX > maxX) {
            posX = 1;
            posY++;
          }
        }
      }
    }

    // Registrar log da criação
    await logSubBloco(
      req.user.id,
      'CREATE',
      novoSubBloco,
      null,
      req.ip,
      req.get('User-Agent')
    );

    console.log('✅ Sub-bloco criado e log registrado:', novoSubBloco.id);

    res.status(201).json({
      message: 'Sub-bloco cadastrado com sucesso',
      subBloco: novoSubBloco
    });

  } catch (error) {
    console.error('Erro ao criar sub-bloco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar sub-bloco
router.put('/sub-blocos/:sub_bloco_id', async (req, res) => {
  if (req.user.tipo_usuario !== 'admin') {
    return res.status(403).json({ error: 'Acesso negado. Apenas administradores.' });
  }

  try {
    const { sub_bloco_id } = req.params;
    const { codigo_sub_bloco, nome, descricao, ativo, numeracao_gavetas } = req.body;

    console.log('🔄 Atualizando sub-bloco:', { sub_bloco_id, codigo_sub_bloco, nome, numeracao_gavetas });

    const dadosAnteriores = await query('SELECT * FROM sub_blocos WHERE id = $1', [sub_bloco_id]);

    if (dadosAnteriores.rows.length === 0) {
      return res.status(404).json({ error: 'Sub-bloco não encontrado' });
    }

    // Verificar se o código do sub-bloco não conflita com outro no mesmo bloco
    if (codigo_sub_bloco && codigo_sub_bloco !== dadosAnteriores.rows[0].codigo_sub_bloco) {
      const subBlocoConflitante = await query(
        'SELECT id, nome FROM sub_blocos WHERE bloco_id = $1 AND codigo_sub_bloco = $2 AND id != $3 AND ativo = true',
        [dadosAnteriores.rows[0].bloco_id, codigo_sub_bloco, sub_bloco_id]
      );

      if (subBlocoConflitante.rows.length > 0) {
        return res.status(400).json({
          error: `Já existe um sub-bloco com o código "${codigo_sub_bloco}" neste bloco: ${subBlocoConflitante.rows[0].nome}`
        });
      }
    }

    const result = await query(`
      UPDATE sub_blocos
      SET codigo_sub_bloco = $1, nome = $2, descricao = $3, ativo = $4, updated_at = CURRENT_TIMESTAMP
      WHERE id = $5
      RETURNING *
    `, [
      codigo_sub_bloco || dadosAnteriores.rows[0].codigo_sub_bloco,
      nome || dadosAnteriores.rows[0].nome,
      descricao !== undefined ? descricao : dadosAnteriores.rows[0].descricao,
      ativo !== undefined ? ativo : dadosAnteriores.rows[0].ativo,
      sub_bloco_id
    ]);

    const subBlocoAtualizado = result.rows[0];

    // Atualizar gavetas se numeração foi fornecida
    if (numeracao_gavetas && numeracao_gavetas.length > 0) {
      console.log('🔄 Atualizando gavetas para ranges:', numeracao_gavetas);

      // Verificar se há sepultamentos ativos nas gavetas antes de deletar
      const sepultamentosAtivos = await query(`
        SELECT COUNT(*) as total
        FROM sepultamentos s
        JOIN gavetas g ON s.gaveta_id = g.id
        WHERE g.sub_bloco_id = $1 AND s.ativo = true AND s.data_exumacao IS NULL
      `, [sub_bloco_id]);

      if (sepultamentosAtivos.rows[0].total > 0) {
        return res.status(400).json({
          error: `Não é possível alterar os ranges pois há ${sepultamentosAtivos.rows[0].total} sepultamento(s) ativo(s) neste sub-bloco. Exume os sepultamentos primeiro.`
        });
      }

      // Calcular quais gavetas devem existir baseado nos ranges
      const gavetasDesejadas = [];
      for (const range of numeracao_gavetas) {
        const { inicio, fim } = range;
        for (let num = inicio; num <= fim; num++) {
          gavetasDesejadas.push(num);
        }
      }

      console.log('📊 Gavetas desejadas:', gavetasDesejadas);

      // Verificar se há conflito com gavetas de outros sub-blocos da mesma estação
      const subBlocoAtual = await query('SELECT bloco_id FROM sub_blocos WHERE id = $1', [sub_bloco_id]);
      const blocoId = subBlocoAtual.rows[0].bloco_id;

      // Verificar conflitos dentro do mesmo produto/estação (não apenas bloco)
      const gavetasConflitantes = await query(`
        SELECT
          g.numero_gaveta,
          sb.nome as sub_bloco_nome,
          sb.codigo_sub_bloco,
          b.nome as bloco_nome,
          b.codigo_bloco
        FROM gavetas g
        JOIN sub_blocos sb ON g.sub_bloco_id = sb.id
        JOIN blocos b ON sb.bloco_id = b.id
        JOIN produtos p ON b.produto_id = p.id
        WHERE p.id = (
          SELECT p2.id
          FROM blocos b2
          JOIN produtos p2 ON b2.produto_id = p2.id
          WHERE b2.id = $1
        )
          AND sb.id != $2
          AND sb.ativo = true
          AND g.ativo = true
          AND g.numero_gaveta = ANY($3)
        ORDER BY g.numero_gaveta
      `, [blocoId, sub_bloco_id, gavetasDesejadas]);

      if (gavetasConflitantes.rows.length > 0) {
        const conflitos = gavetasConflitantes.rows.map(row =>
          `Gaveta ${row.numero_gaveta} (${row.sub_bloco_nome} - ${row.bloco_nome})`
        ).join(', ');

        return res.status(400).json({
          error: `Range não é válido para esta estação. As seguintes gavetas já estão cadastradas em outros sub-blocos: ${conflitos}`
        });
      }

      // Buscar gavetas existentes
      const gavetasExistentes = await query(
        'SELECT numero_gaveta FROM gavetas WHERE sub_bloco_id = $1 AND ativo = true',
        [sub_bloco_id]
      );

      const numerosExistentes = gavetasExistentes.rows.map(g => g.numero_gaveta);
      console.log('📊 Gavetas existentes:', numerosExistentes);

      // Gavetas para remover (existem mas não estão nos ranges desejados)
      const gavetasParaRemover = numerosExistentes.filter(num => !gavetasDesejadas.includes(num));

      // Gavetas para adicionar (estão nos ranges desejados mas não existem)
      const gavetasParaAdicionar = gavetasDesejadas.filter(num => !numerosExistentes.includes(num));

      console.log('🗑️ Gavetas para remover:', gavetasParaRemover);
      console.log('➕ Gavetas para adicionar:', gavetasParaAdicionar);

      // Remover gavetas que não devem mais existir
      if (gavetasParaRemover.length > 0) {
        await query(
          'DELETE FROM gavetas WHERE sub_bloco_id = $1 AND numero_gaveta = ANY($2)',
          [sub_bloco_id, gavetasParaRemover]
        );
        console.log(`🗑️ ${gavetasParaRemover.length} gavetas removidas`);
      }

      // Adicionar novas gavetas
      if (gavetasParaAdicionar.length > 0) {
        let posX = 1;
        let posY = 1;
        const maxX = 5; // Grid padrão 5x4

        for (const num of gavetasParaAdicionar.sort((a, b) => a - b)) {
          await query(`
            INSERT INTO gavetas (sub_bloco_id, numero_gaveta, posicao_x, posicao_y, altura_especial, disponivel, ativo)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
          `, [sub_bloco_id, num, posX, posY, 1.0, true, true]);

          console.log(`✅ Gaveta ${num} criada para sub-bloco ${sub_bloco_id}`);

          posX++;
          if (posX > maxX) {
            posX = 1;
            posY++;
          }
        }
        console.log(`➕ ${gavetasParaAdicionar.length} gavetas adicionadas`);
      }

      console.log('✅ Gavetas atualizadas com sucesso');
    }

    // Registrar log da edição
    await logSubBloco(
      req.user.id,
      'EDIT',
      subBlocoAtualizado,
      dadosAnteriores.rows[0],
      req.ip,
      req.get('User-Agent')
    );

    console.log('✅ Sub-bloco atualizado e log registrado:', subBlocoAtualizado.id);

    res.json({
      message: 'Sub-bloco atualizado com sucesso',
      subBloco: subBlocoAtualizado
    });

  } catch (error) {
    console.error('Erro ao atualizar sub-bloco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar bloco
router.delete('/blocos/:bloco_id', async (req, res) => {
  console.log('🗑️ Rota DELETE /blocos/:bloco_id chamada');
  console.log('🗑️ Parâmetros:', req.params);
  console.log('🗑️ Usuário:', req.user?.email, 'Tipo:', req.user?.tipo_usuario);

  if (!req.user || req.user.tipo_usuario !== 'admin') {
    console.log('❌ Acesso negado - usuário não é admin');
    return res.status(403).json({ error: 'Acesso negado. Apenas administradores.' });
  }

  try {
    const { bloco_id } = req.params;

    console.log('🗑️ Tentando deletar bloco:', bloco_id);

    // Verificar se o bloco existe
    const blocoExistente = await query('SELECT * FROM blocos WHERE id = $1', [bloco_id]);

    if (blocoExistente.rows.length === 0) {
      console.log('❌ Bloco não encontrado:', bloco_id);
      return res.status(404).json({ error: 'Bloco não encontrado' });
    }

    console.log('✅ Bloco encontrado:', blocoExistente.rows[0].nome);

    // CASO 1: Verificar se há sub-blocos associados ao bloco
    const subBlocosAssociados = await query(`
      SELECT COUNT(*) as total, STRING_AGG(nome, ', ') as nomes
      FROM sub_blocos
      WHERE bloco_id = $1 AND ativo = true
    `, [bloco_id]);

    if (subBlocosAssociados.rows[0].total > 0) {
      console.log('❌ Não é possível deletar - há sub-blocos associados');
      return res.status(400).json({
        error: `Não é possível deletar o bloco pois há ${subBlocosAssociados.rows[0].total} sub-bloco(s) associado(s): ${subBlocosAssociados.rows[0].nomes}. Delete os sub-blocos primeiro.`
      });
    }

    // Verificar se há sepultamentos ativos (mesmo sem sub-blocos ativos, pode haver gavetas)
    const sepultamentosAtivos = await query(`
      SELECT COUNT(*) as total
      FROM sepultamentos s
      JOIN gavetas g ON s.gaveta_id = g.id
      JOIN sub_blocos sb ON g.sub_bloco_id = sb.id
      WHERE sb.bloco_id = $1 AND s.ativo = true AND s.data_exumacao IS NULL
    `, [bloco_id]);

    if (sepultamentosAtivos.rows[0].total > 0) {
      console.log('❌ Não é possível deletar - há sepultamentos ativos');
      return res.status(400).json({
        error: `Não é possível deletar o bloco pois há ${sepultamentosAtivos.rows[0].total} sepultamento(s) ativo(s). Exume os sepultamentos primeiro.`
      });
    }

    // Se chegou até aqui, pode deletar o bloco
    // Deletar gavetas de sub-blocos inativos primeiro (se houver)
    await query(`
      DELETE FROM gavetas
      WHERE sub_bloco_id IN (
        SELECT id FROM sub_blocos WHERE bloco_id = $1
      )
    `, [bloco_id]);
    console.log('🗑️ Gavetas de sub-blocos removidas');

    // Deletar sub-blocos inativos (se houver)
    await query('DELETE FROM sub_blocos WHERE bloco_id = $1', [bloco_id]);
    console.log('🗑️ Sub-blocos removidos');

    // Deletar o bloco
    const blocoResult = await query('DELETE FROM blocos WHERE id = $1', [bloco_id]);
    console.log('✅ Bloco deletado:', blocoResult.rowCount, 'linhas afetadas');

    res.json({
      message: 'Bloco deletado com sucesso'
    });

  } catch (error) {
    console.error('❌ Erro ao deletar bloco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar sub-bloco
router.delete('/sub-blocos/:sub_bloco_id', async (req, res) => {
  console.log('🗑️ Rota DELETE /sub-blocos/:sub_bloco_id chamada');
  console.log('🗑️ Parâmetros:', req.params);
  console.log('🗑️ Usuário:', req.user?.email, 'Tipo:', req.user?.tipo_usuario);

  if (!req.user || req.user.tipo_usuario !== 'admin') {
    console.log('❌ Acesso negado - usuário não é admin');
    return res.status(403).json({ error: 'Acesso negado. Apenas administradores.' });
  }

  try {
    const { sub_bloco_id } = req.params;

    console.log('🗑️ Deletando sub-bloco:', sub_bloco_id);

    // Verificar se o sub-bloco existe
    const subBlocoExistente = await query('SELECT * FROM sub_blocos WHERE id = $1', [sub_bloco_id]);

    if (subBlocoExistente.rows.length === 0) {
      console.log('❌ Sub-bloco não encontrado:', sub_bloco_id);
      return res.status(404).json({ error: 'Sub-bloco não encontrado' });
    }

    console.log('✅ Sub-bloco encontrado:', subBlocoExistente.rows[0].nome);

    // Verificar se há sepultamentos ativos nas gavetas deste sub-bloco
    const sepultamentosAtivos = await query(`
      SELECT COUNT(*) as total
      FROM sepultamentos s
      JOIN gavetas g ON s.gaveta_id = g.id
      WHERE g.sub_bloco_id = $1 AND s.ativo = true AND s.data_exumacao IS NULL
    `, [sub_bloco_id]);

    console.log('📊 Sepultamentos ativos encontrados:', sepultamentosAtivos.rows[0].total);

    if (sepultamentosAtivos.rows[0].total > 0) {
      console.log('❌ Não é possível deletar - há sepultamentos ativos');
      return res.status(400).json({
        error: `Não é possível deletar o sub-bloco pois há ${sepultamentosAtivos.rows[0].total} sepultamento(s) ativo(s). Exume os sepultamentos primeiro.`
      });
    }

    // Deletar gavetas do sub-bloco primeiro (devido a foreign key)
    const gavetasResult = await query('DELETE FROM gavetas WHERE sub_bloco_id = $1', [sub_bloco_id]);
    console.log('🗑️ Gavetas removidas:', gavetasResult.rowCount);

    // Deletar o sub-bloco
    const subBlocoResult = await query('DELETE FROM sub_blocos WHERE id = $1', [sub_bloco_id]);
    console.log('✅ Sub-bloco deletado:', subBlocoResult.rowCount, 'linhas afetadas');

    res.json({
      message: 'Sub-bloco deletado com sucesso'
    });

  } catch (error) {
    console.error('❌ Erro ao deletar sub-bloco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar produto - VERSÃO CORRIGIDA
router.delete('/:id', async (req, res) => {
  console.log('🗑️ Rota DELETE /produtos/:id chamada');
  console.log('🗑️ Parâmetros:', req.params);
  console.log('🗑️ Usuário:', req.user?.email, 'Tipo:', req.user?.tipo_usuario);
  console.log('🗑️ Headers:', req.headers.authorization ? 'Token presente' : 'Token ausente');

  // VALIDAÇÃO DE AUTENTICAÇÃO MELHORADA
  if (!req.user) {
    console.log('❌ Usuário não autenticado');
    return res.status(401).json({ error: 'Usuário não autenticado' });
  }

  if (req.user.tipo_usuario !== 'admin') {
    console.log('❌ Acesso negado - usuário não é admin:', req.user.tipo_usuario);
    return res.status(403).json({ error: 'Acesso negado. Apenas administradores podem deletar produtos.' });
  }

  console.log('✅ Usuário autenticado e autorizado:', req.user.email);

  try {
    const { id } = req.params;

    console.log('🗑️ Tentando deletar produto:', id);

    // Verificar se o produto existe
    const produtoExistente = await query('SELECT * FROM produtos WHERE id = $1', [id]);

    if (produtoExistente.rows.length === 0) {
      console.log('❌ Produto não encontrado:', id);
      return res.status(404).json({ error: 'Produto não encontrado' });
    }

    console.log('✅ Produto encontrado:', produtoExistente.rows[0].denominacao);

    // VALIDAÇÃO HIERÁRQUICA MELHORADA
    console.log('🔍 Iniciando validação hierárquica para produto:', id);

    // CASO 1: Verificar se há blocos associados ao produto
    const blocosAssociados = await query(`
      SELECT COUNT(*) as total, STRING_AGG(nome, ', ') as nomes
      FROM blocos
      WHERE produto_id = $1 AND ativo = true
    `, [id]);

    console.log('🔍 Blocos associados encontrados:', blocosAssociados.rows[0]);

    if (blocosAssociados.rows[0].total > 0) {
      console.log('❌ Não é possível deletar - há blocos associados');
      return res.status(400).json({
        error: `Não é possível deletar o produto pois há ${blocosAssociados.rows[0].total} bloco(s) associado(s): ${blocosAssociados.rows[0].nomes}. Delete os blocos primeiro.`
      });
    }

    // CASO 2: Verificar se há sub-blocos associados (mesmo com blocos inativos)
    const subBlocosAssociados = await query(`
      SELECT COUNT(*) as total, STRING_AGG(sb.nome, ', ') as nomes
      FROM sub_blocos sb
      JOIN blocos b ON sb.bloco_id = b.id
      WHERE b.produto_id = $1 AND sb.ativo = true
    `, [id]);

    console.log('🔍 Sub-blocos associados encontrados:', subBlocosAssociados.rows[0]);

    if (subBlocosAssociados.rows[0].total > 0) {
      console.log('❌ Não é possível deletar - há sub-blocos associados');
      return res.status(400).json({
        error: `Não é possível deletar o produto pois há ${subBlocosAssociados.rows[0].total} sub-bloco(s) associado(s): ${subBlocosAssociados.rows[0].nomes}. Delete os sub-blocos primeiro.`
      });
    }

    // CASO 3: Verificar se há gavetas associadas
    const gavetasAssociadas = await query(`
      SELECT COUNT(*) as total
      FROM gavetas g
      JOIN sub_blocos sb ON g.sub_bloco_id = sb.id
      JOIN blocos b ON sb.bloco_id = b.id
      WHERE b.produto_id = $1 AND g.ativo = true
    `, [id]);

    console.log('🔍 Gavetas associadas encontradas:', gavetasAssociadas.rows[0]);

    if (gavetasAssociadas.rows[0].total > 0) {
      console.log('❌ Não é possível deletar - há gavetas associadas');
      return res.status(400).json({
        error: `Não é possível deletar o produto pois há ${gavetasAssociadas.rows[0].total} gaveta(s) associada(s). Delete os ranges de gavetas primeiro.`
      });
    }

    // Verificar se há sepultamentos ativos (mesmo sem blocos ativos)
    const sepultamentosAtivos = await query(`
      SELECT COUNT(*) as total
      FROM sepultamentos s
      JOIN gavetas g ON s.gaveta_id = g.id
      JOIN sub_blocos sb ON g.sub_bloco_id = sb.id
      JOIN blocos b ON sb.bloco_id = b.id
      WHERE b.produto_id = $1 AND s.ativo = true AND s.data_exumacao IS NULL
    `, [id]);

    if (sepultamentosAtivos.rows[0].total > 0) {
      console.log('❌ Não é possível deletar - há sepultamentos ativos');
      return res.status(400).json({
        error: `Não é possível deletar a estação pois há ${sepultamentosAtivos.rows[0].total} sepultamento(s) ativo(s). Exume os sepultamentos primeiro.`
      });
    }

    console.log('✅ Validação hierárquica aprovada - produto pode ser deletado');
    console.log('🔍 Resumo das validações:');
    console.log('  - Blocos ativos:', blocosAssociados.rows[0].total);
    console.log('  - Sub-blocos ativos:', subBlocosAssociados.rows[0].total);
    console.log('  - Gavetas ativas:', gavetasAssociadas.rows[0].total);
    console.log('  - Sepultamentos ativos:', sepultamentosAtivos.rows[0].total);

    // Se chegou até aqui, pode deletar o produto
    // Deletar em cascata: gavetas → sub-blocos → blocos → produto
    await query(`
      DELETE FROM gavetas
      WHERE sub_bloco_id IN (
        SELECT sb.id FROM sub_blocos sb
        JOIN blocos b ON sb.bloco_id = b.id
        WHERE b.produto_id = $1
      )
    `, [id]);
    console.log('🗑️ Gavetas removidas');

    await query(`
      DELETE FROM sub_blocos
      WHERE bloco_id IN (
        SELECT id FROM blocos WHERE produto_id = $1
      )
    `, [id]);
    console.log('🗑️ Sub-blocos removidos');

    await query('DELETE FROM blocos WHERE produto_id = $1', [id]);
    console.log('🗑️ Blocos removidos');

    // Registrar log antes de deletar (com tratamento de erro)
    try {
      await logProduto(
        req.user.id,
        'DELETE',
        produtoExistente.rows[0],
        null,
        req.ip,
        req.get('User-Agent')
      );
      console.log('✅ Log de deleção registrado com sucesso');
    } catch (logError) {
      console.error('⚠️ Erro ao registrar log (continuando com deleção):', logError);
      // Não interromper a deleção por erro de log
    }

    // Deletar o produto
    const produtoResult = await query('DELETE FROM produtos WHERE id = $1', [id]);
    console.log('✅ Produto deletado:', produtoResult.rowCount, 'linhas afetadas');

    res.json({
      message: 'Produto deletado com sucesso'
    });

  } catch (error) {
    console.error('❌ Erro ao deletar produto:', error);
    console.error('📋 Stack trace:', error.stack);
    console.error('📋 Detalhes do erro:', {
      message: error.message,
      code: error.code,
      detail: error.detail,
      hint: error.hint
    });

    // TRATAMENTO ESPECÍFICO DE ERROS
    if (error.code === '23503') {
      // Violação de chave estrangeira
      return res.status(400).json({
        error: 'Não é possível deletar o produto pois há registros dependentes associados.'
      });
    } else if (error.code === '23505') {
      // Violação de constraint única
      return res.status(400).json({
        error: 'Conflito de dados ao deletar o produto.'
      });
    } else if (error.message && error.message.includes('permission')) {
      // Erro de permissão
      return res.status(403).json({
        error: 'Permissão insuficiente para deletar o produto.'
      });
    } else {
      // Erro genérico
      return res.status(500).json({
        error: 'Erro interno do servidor ao deletar produto.',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }
});

// Buscar informações completas de um produto
router.get('/:id/completo', async (req, res) => {
  // Verificar se usuário está autenticado
  if (!req.user) {
    return res.status(401).json({ error: 'Usuário não autenticado' });
  }

  try {
    const { id } = req.params;

    console.log('🔍 Buscando produto completo, ID:', id, 'Usuário:', req.user.email);

    // Buscar produto
    const produto = await query('SELECT * FROM produtos WHERE id = $1', [id]);
    if (produto.rows.length === 0) {
      console.log('❌ Produto não encontrado:', id);
      return res.status(404).json({ error: 'Produto não encontrado' });
    }

    console.log('✅ Produto encontrado:', produto.rows[0].denominacao);

    // Buscar blocos com sub-blocos e gavetas
    const blocos = await query(`
      SELECT
        b.*,
        COUNT(DISTINCT sb.id) as total_sub_blocos,
        COUNT(DISTINCT g.id) as total_gavetas
      FROM blocos b
      LEFT JOIN sub_blocos sb ON b.id = sb.bloco_id AND sb.ativo = true
      LEFT JOIN gavetas g ON sb.id = g.sub_bloco_id AND g.ativo = true
      WHERE b.produto_id = $1 AND b.ativo = true
      GROUP BY b.id
      ORDER BY b.codigo_bloco
    `, [id]);

    // Para cada bloco, buscar sub-blocos com ranges de gavetas
    const blocosCompletos = await Promise.all(
      blocos.rows.map(async (bloco) => {
        const subBlocos = await query(`
          SELECT
            sb.*,
            COUNT(g.id) as total_gavetas,
            COALESCE(
              STRING_AGG(
                CASE
                  WHEN g.numero_gaveta IS NOT NULL
                  THEN g.numero_gaveta::text
                  ELSE NULL
                END,
                ','
                ORDER BY g.numero_gaveta
              ),
              ''
            ) as gavetas_numeros
          FROM sub_blocos sb
          LEFT JOIN gavetas g ON sb.id = g.sub_bloco_id AND g.ativo = true
          WHERE sb.bloco_id = $1 AND sb.ativo = true
          GROUP BY sb.id
          ORDER BY sb.codigo_sub_bloco
        `, [bloco.id]);

        // Processar ranges para cada sub-bloco
        const subBlocosComRanges = subBlocos.rows.map(subBloco => {
          console.log('🔍 Processando sub-bloco:', subBloco.nome, 'gavetas_numeros:', subBloco.gavetas_numeros);

          if (subBloco.gavetas_numeros && subBloco.gavetas_numeros.trim() !== '') {
            const numeros = subBloco.gavetas_numeros.split(',').map(n => parseInt(n)).sort((a, b) => a - b);
            console.log('📊 Números de gavetas encontrados:', numeros);

            const ranges = [];
            let inicio = numeros[0];
            let fim = numeros[0];

            for (let i = 1; i < numeros.length; i++) {
              if (numeros[i] === fim + 1) {
                fim = numeros[i];
              } else {
                ranges.push(inicio === fim ? `${inicio}` : `${inicio}-${fim}`);
                inicio = numeros[i];
                fim = numeros[i];
              }
            }

            if (numeros.length > 0) {
              ranges.push(inicio === fim ? `${inicio}` : `${inicio}-${fim}`);
            }

            subBloco.ranges_gavetas = ranges.join(', ');
            console.log('✅ Ranges calculados:', subBloco.ranges_gavetas);
          } else {
            subBloco.ranges_gavetas = '';
            console.log('⚠️ Nenhuma gaveta encontrada para sub-bloco:', subBloco.nome);
          }

          delete subBloco.gavetas_numeros;
          return subBloco;
        });

        return {
          ...bloco,
          sub_blocos: subBlocosComRanges
        };
      })
    );

    console.log('✅ Retornando dados completos do produto');

    res.json({
      produto: produto.rows[0],
      blocos: blocosCompletos
    });

  } catch (error) {
    console.error('❌ Erro ao buscar produto completo:', error);
    console.error('📋 Stack trace:', error.stack);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Rota de teste para verificar se o servidor está funcionando
router.get('/test-server', (req, res) => {
  console.log('🧪 Rota de teste chamada');
  res.json({
    message: 'Servidor funcionando',
    timestamp: new Date().toISOString(),
    user: req.user?.email || 'não autenticado'
  });
});

// Rota de teste DELETE
router.delete('/test-delete/:id', (req, res) => {
  console.log('🧪 Rota DELETE de teste chamada, ID:', req.params.id);
  res.json({
    message: 'DELETE funcionando',
    id: req.params.id,
    timestamp: new Date().toISOString()
  });
});

// ========================================
// ROTAS SIMPLIFICADAS PARA CRUD DE CADASTROS
// ========================================

// Criar bloco (rota simplificada)
router.post('/blocos', async (req, res) => {
  try {
    const { nome, codigo_bloco, descricao, produto_id, ativo = true } = req.body;

    console.log('🔨 Criando novo bloco:', { nome, codigo_bloco, produto_id });

    const result = await query(`
      INSERT INTO blocos (nome, codigo_bloco, descricao, produto_id, ativo)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `, [nome, codigo_bloco, descricao, produto_id, ativo]);

    console.log('✅ Bloco criado com sucesso:', result.rows[0]);
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('❌ Erro ao criar bloco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar bloco (rota simplificada)
router.put('/blocos/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { nome, codigo_bloco, descricao, ativo } = req.body;

    console.log('🔄 Atualizando bloco:', { id, nome, codigo_bloco });

    const result = await query(`
      UPDATE blocos
      SET nome = $1, codigo_bloco = $2, descricao = $3, ativo = $4
      WHERE id = $5
      RETURNING *
    `, [nome, codigo_bloco, descricao, ativo, id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Bloco não encontrado' });
    }

    console.log('✅ Bloco atualizado com sucesso:', result.rows[0]);
    res.json(result.rows[0]);
  } catch (error) {
    console.error('❌ Erro ao atualizar bloco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar bloco (rota simplificada)
router.delete('/blocos/:id', async (req, res) => {
  try {
    const { id } = req.params;

    console.log('🗑️ Tentando deletar bloco:', id);

    // Verificar se há sub-blocos associados
    const subBlocosCheck = await query('SELECT COUNT(*) as count FROM sub_blocos WHERE bloco_id = $1 AND ativo = true', [id]);
    if (parseInt(subBlocosCheck.rows[0].count) > 0) {
      return res.status(400).json({ error: 'Não é possível deletar bloco com sub-blocos associados' });
    }

    // Verificar se há sepultamentos associados
    const sepultamentosCheck = await query(`
      SELECT COUNT(*) as count
      FROM sepultamentos s
      JOIN gavetas g ON s.gaveta_id = g.id
      JOIN sub_blocos sb ON g.sub_bloco_id = sb.id
      WHERE sb.bloco_id = $1 AND s.ativo = true
    `, [id]);

    if (parseInt(sepultamentosCheck.rows[0].count) > 0) {
      return res.status(400).json({ error: 'Não é possível deletar bloco com sepultamentos associados' });
    }

    const result = await query('DELETE FROM blocos WHERE id = $1 RETURNING *', [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Bloco não encontrado' });
    }

    console.log('✅ Bloco deletado com sucesso');
    res.json({ message: 'Bloco deletado com sucesso' });
  } catch (error) {
    console.error('❌ Erro ao deletar bloco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Listar sub-blocos por bloco (rota simplificada) - ATUALIZADA PARA RASTREAR RANGES
router.get('/blocos/:bloco_id/sub-blocos', async (req, res) => {
  try {
    const { bloco_id } = req.params;

    console.log('📋 Listando sub-blocos do bloco com rastreamento de ranges:', bloco_id);

    // Primeiro buscar o bloco para obter os códigos
    const blocoResult = await query(`
      SELECT codigo_cliente, codigo_estacao, codigo_bloco FROM blocos WHERE id = $1 AND ativo = true
    `, [bloco_id]);

    if (blocoResult.rows.length === 0) {
      return res.status(404).json({ error: 'Bloco não encontrado' });
    }

    const { codigo_cliente, codigo_estacao, codigo_bloco } = blocoResult.rows[0];

    const result = await query(`
      SELECT
        sb.*,
        -- Rastreamento de ranges da tabela numeracoes_gavetas
        COALESCE(
          (SELECT COUNT(*) FROM numeracoes_gavetas ng
           WHERE ng.codigo_cliente = sb.codigo_cliente
           AND ng.codigo_estacao = sb.codigo_estacao
           AND ng.codigo_bloco = sb.codigo_bloco
           AND ng.codigo_sub_bloco = sb.codigo_sub_bloco
           AND ng.ativo = true), 0
        ) as total_numeracoes,
        COALESCE(
          (SELECT STRING_AGG(ng.numero_inicio::text || '-' || ng.numero_fim::text, ', ' ORDER BY ng.numero_inicio)
           FROM numeracoes_gavetas ng
           WHERE ng.codigo_cliente = sb.codigo_cliente
           AND ng.codigo_estacao = sb.codigo_estacao
           AND ng.codigo_bloco = sb.codigo_bloco
           AND ng.codigo_sub_bloco = sb.codigo_sub_bloco
           AND ng.ativo = true), ''
        ) as ranges_gavetas,
        COALESCE(
          (SELECT SUM(ng.numero_fim - ng.numero_inicio + 1)
           FROM numeracoes_gavetas ng
           WHERE ng.codigo_cliente = sb.codigo_cliente
           AND ng.codigo_estacao = sb.codigo_estacao
           AND ng.codigo_bloco = sb.codigo_bloco
           AND ng.codigo_sub_bloco = sb.codigo_sub_bloco
           AND ng.ativo = true), 0
        ) as total_gavetas_ranges,
        -- Buscar primeiro range para compatibilidade com frontend
        COALESCE(
          (SELECT ng.numero_inicio
           FROM numeracoes_gavetas ng
           WHERE ng.codigo_cliente = sb.codigo_cliente
           AND ng.codigo_estacao = sb.codigo_estacao
           AND ng.codigo_bloco = sb.codigo_bloco
           AND ng.codigo_sub_bloco = sb.codigo_sub_bloco
           AND ng.ativo = true
           ORDER BY ng.numero_inicio LIMIT 1), sb.numero_inicio
        ) as numero_inicio,
        COALESCE(
          (SELECT ng.numero_fim
           FROM numeracoes_gavetas ng
           WHERE ng.codigo_cliente = sb.codigo_cliente
           AND ng.codigo_estacao = sb.codigo_estacao
           AND ng.codigo_bloco = sb.codigo_bloco
           AND ng.codigo_sub_bloco = sb.codigo_sub_bloco
           AND ng.ativo = true
           ORDER BY ng.numero_fim DESC LIMIT 1), sb.numero_fim
        ) as numero_fim,
        -- Contagem de gavetas reais
        COUNT(g.id) as total_gavetas_criadas
      FROM sub_blocos sb
      LEFT JOIN gavetas g ON sb.codigo_cliente = g.codigo_cliente
        AND sb.codigo_estacao = g.codigo_estacao
        AND sb.codigo_bloco = g.codigo_bloco
        AND sb.codigo_sub_bloco = g.codigo_sub_bloco
        AND g.ativo = true
      WHERE sb.codigo_cliente = $1 AND sb.codigo_estacao = $2 AND sb.codigo_bloco = $3 AND sb.ativo = true
      GROUP BY sb.id, sb.codigo_cliente, sb.codigo_estacao, sb.codigo_bloco, sb.codigo_sub_bloco,
               sb.nome, sb.descricao, sb.ativo, sb.created_at, sb.updated_at, sb.numero_inicio, sb.numero_fim
      ORDER BY sb.codigo_sub_bloco
    `, [codigo_cliente, codigo_estacao, codigo_bloco]);

    console.log('✅ Sub-blocos encontrados com rastreamento de ranges:', result.rows.length);

    // Log detalhado dos ranges encontrados
    result.rows.forEach(sb => {
      console.log(`📋 Sub-bloco: ${sb.nome} | Ranges: ${sb.ranges_gavetas} | Total gavetas: ${sb.total_gavetas_ranges}`);
    });

    res.json(result.rows);
  } catch (error) {
    console.error('❌ Erro ao listar sub-blocos:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar sub-bloco (rota simplificada)
router.post('/sub-blocos', async (req, res) => {
  try {
    const { nome, codigo_sub_bloco, descricao, bloco_id, numero_inicio, numero_fim, ativo = true } = req.body;

    console.log('🔨 Criando novo sub-bloco:', { nome, codigo_sub_bloco, bloco_id, numero_inicio, numero_fim });

    // Verificar sobreposição de ranges
    const overlapCheck = await query(`
      SELECT * FROM sub_blocos
      WHERE bloco_id = $1 AND ativo = true
      AND (
        ($2 >= numero_inicio AND $2 <= numero_fim) OR
        ($3 >= numero_inicio AND $3 <= numero_fim) OR
        ($2 <= numero_inicio AND $3 >= numero_fim)
      )
    `, [bloco_id, numero_inicio, numero_fim]);

    if (overlapCheck.rows.length > 0) {
      return res.status(400).json({
        error: `Range de gavetas conflita com o sub-bloco "${overlapCheck.rows[0].nome}" (gavetas ${overlapCheck.rows[0].numero_inicio}-${overlapCheck.rows[0].numero_fim})`
      });
    }

    const result = await query(`
      INSERT INTO sub_blocos (nome, codigo_sub_bloco, descricao, bloco_id, numero_inicio, numero_fim, ativo)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `, [nome, codigo_sub_bloco, descricao, bloco_id, numero_inicio, numero_fim, ativo]);

    // Criar gavetas automaticamente
    for (let i = numero_inicio; i <= numero_fim; i++) {
      await query(`
        INSERT INTO gavetas (numero_gaveta, sub_bloco_id, posicao_x, posicao_y, ativo)
        VALUES ($1, $2, 1, 1, true)
      `, [i, result.rows[0].id]);
    }

    console.log('✅ Sub-bloco criado com sucesso:', result.rows[0]);
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('❌ Erro ao criar sub-bloco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar sub-bloco (rota simplificada)
router.put('/sub-blocos/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { nome, codigo_sub_bloco, descricao, numero_inicio, numero_fim, ativo } = req.body;

    console.log('🔄 Atualizando sub-bloco:', { id, nome, codigo_sub_bloco, numero_inicio, numero_fim });

    // Buscar sub-bloco atual
    const currentSubBloco = await query('SELECT * FROM sub_blocos WHERE id = $1', [id]);
    if (currentSubBloco.rows.length === 0) {
      return res.status(404).json({ error: 'Sub-bloco não encontrado' });
    }

    const current = currentSubBloco.rows[0];

    // Verificar sobreposição de ranges (excluindo o próprio sub-bloco)
    const overlapCheck = await query(`
      SELECT * FROM sub_blocos
      WHERE bloco_id = $1 AND ativo = true AND id != $2
      AND (
        ($3 >= numero_inicio AND $3 <= numero_fim) OR
        ($4 >= numero_inicio AND $4 <= numero_fim) OR
        ($3 <= numero_inicio AND $4 >= numero_fim)
      )
    `, [current.bloco_id, id, numero_inicio, numero_fim]);

    if (overlapCheck.rows.length > 0) {
      return res.status(400).json({
        error: `Range de gavetas conflita com o sub-bloco "${overlapCheck.rows[0].nome}" (gavetas ${overlapCheck.rows[0].numero_inicio}-${overlapCheck.rows[0].numero_fim})`
      });
    }

    const result = await query(`
      UPDATE sub_blocos
      SET nome = $1, codigo_sub_bloco = $2, descricao = $3, numero_inicio = $4, numero_fim = $5, ativo = $6
      WHERE id = $7
      RETURNING *
    `, [nome, codigo_sub_bloco, descricao, numero_inicio, numero_fim, ativo, id]);

    console.log('✅ Sub-bloco atualizado com sucesso:', result.rows[0]);
    res.json(result.rows[0]);
  } catch (error) {
    console.error('❌ Erro ao atualizar sub-bloco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar sub-bloco (rota simplificada)
router.delete('/sub-blocos/:id', async (req, res) => {
  try {
    const { id } = req.params;

    console.log('🗑️ Tentando deletar sub-bloco:', id);

    // Verificar se há sepultamentos associados
    const sepultamentosCheck = await query(`
      SELECT COUNT(*) as count
      FROM sepultamentos s
      JOIN gavetas g ON s.gaveta_id = g.id
      WHERE g.sub_bloco_id = $1 AND s.ativo = true
    `, [id]);

    if (parseInt(sepultamentosCheck.rows[0].count) > 0) {
      return res.status(400).json({ error: 'Não é possível deletar sub-bloco com sepultamentos associados' });
    }

    // Deletar gavetas associadas primeiro
    await query('DELETE FROM gavetas WHERE sub_bloco_id = $1', [id]);

    const result = await query('DELETE FROM sub_blocos WHERE id = $1 RETURNING *', [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Sub-bloco não encontrado' });
    }

    console.log('✅ Sub-bloco deletado com sucesso');
    res.json({ message: 'Sub-bloco deletado com sucesso' });
  } catch (error) {
    console.error('❌ Erro ao deletar sub-bloco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Ativar/Inativar produto (apenas admin)
router.patch('/:id/toggle-status', async (req, res) => {
  try {
    const { id } = req.params;

    // Verificar se é admin
    if (req.user.tipo_usuario !== 'admin') {
      return res.status(403).json({ error: 'Acesso negado. Apenas administradores.' });
    }

    console.log(`🔄 Alterando status do produto ID: ${id}`);

    // Buscar dados atuais do produto
    const produtoAtual = await query(
      'SELECT * FROM produtos WHERE id = $1',
      [id]
    );

    if (produtoAtual.rows.length === 0) {
      return res.status(404).json({ error: 'Produto não encontrado' });
    }

    const produto = produtoAtual.rows[0];
    const novoStatus = !produto.ativo;

    console.log(`📊 Produto ${produto.denominacao}: ${produto.ativo ? 'ATIVO' : 'INATIVO'} → ${novoStatus ? 'ATIVO' : 'INATIVO'}`);

    // Verificar dados subsequentes para relatório de integridade
    const dadosSubsequentes = await verificarDadosSubsequentes(produto);

    console.log('📋 Verificação de integridade dos dados subsequentes:');
    console.log(`   - Blocos: ${dadosSubsequentes.blocos}`);
    console.log(`   - Sub-blocos: ${dadosSubsequentes.subBlocos}`);
    console.log(`   - Gavetas: ${dadosSubsequentes.gavetas}`);
    console.log(`   - Sepultamentos: ${dadosSubsequentes.sepultamentos}`);
    console.log(`   - Sepultamentos ativos: ${dadosSubsequentes.sepultamentosAtivos}`);

    // IMPORTANTE: Não alteramos nenhum dado subsequente, apenas o status do produto
    console.log('⚠️ GARANTIA DE INTEGRIDADE: Todos os dados subsequentes serão mantidos intactos');

    // Atualizar status do produto
    const result = await query(`
      UPDATE produtos
      SET ativo = $1, updated_at = CURRENT_TIMESTAMP
      WHERE id = $2
      RETURNING *
    `, [novoStatus, id]);

    const produtoAtualizado = result.rows[0];

    // Log da ação com detalhes de integridade
    await logAction(
      req.user.id,
      'UPDATE',
      'produtos',
      produtoAtualizado.id,
      {
        ativo: produto.ativo,
        dados_subsequentes: dadosSubsequentes
      },
      {
        ativo: novoStatus,
        dados_subsequentes_mantidos: dadosSubsequentes
      },
      req.ip,
      req.get('User-Agent'),
      {
        codigo_cliente: produto.codigo_cliente,
        codigo_estacao: produto.codigo_estacao
      },
      `Produto ${produto.denominacao} ${novoStatus ? 'ativado' : 'inativado'}. INTEGRIDADE GARANTIDA: ${dadosSubsequentes.blocos} blocos, ${dadosSubsequentes.subBlocos} sub-blocos, ${dadosSubsequentes.gavetas} gavetas, ${dadosSubsequentes.sepultamentos} sepultamentos (${dadosSubsequentes.sepultamentosAtivos} ativos) mantidos intactos.`
    );

    console.log(`✅ Status do produto ${produto.denominacao} alterado com sucesso para: ${novoStatus ? 'ATIVO' : 'INATIVO'}`);
    console.log(`📋 IMPORTANTE: Todos os dados subsequentes (blocos, sub-blocos, gavetas, sepultamentos) foram mantidos intactos`);

    res.json({
      message: `Produto ${novoStatus ? 'ativado' : 'inativado'} com sucesso. Dados subsequentes mantidos intactos.`,
      produto: produtoAtualizado
    });

  } catch (error) {
    console.error('❌ Erro ao alterar status do produto:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Função auxiliar para verificar dados subsequentes
async function verificarDadosSubsequentes(produto) {
  try {
    // Contar blocos
    const blocosResult = await query(`
      SELECT COUNT(*) as total
      FROM blocos
      WHERE codigo_cliente = $1 AND codigo_estacao = $2
    `, [produto.codigo_cliente, produto.codigo_estacao]);

    // Contar sub-blocos
    const subBlocosResult = await query(`
      SELECT COUNT(*) as total
      FROM sub_blocos sb
      JOIN blocos b ON sb.codigo_cliente = b.codigo_cliente
        AND sb.codigo_estacao = b.codigo_estacao
        AND sb.codigo_bloco = b.codigo_bloco
      WHERE b.codigo_cliente = $1 AND b.codigo_estacao = $2
    `, [produto.codigo_cliente, produto.codigo_estacao]);

    // Contar gavetas
    const gavetasResult = await query(`
      SELECT COUNT(*) as total
      FROM gavetas
      WHERE codigo_cliente = $1 AND codigo_estacao = $2
    `, [produto.codigo_cliente, produto.codigo_estacao]);

    // Contar sepultamentos (total)
    const sepultamentosResult = await query(`
      SELECT COUNT(*) as total
      FROM sepultamentos
      WHERE codigo_cliente = $1 AND codigo_estacao = $2
    `, [produto.codigo_cliente, produto.codigo_estacao]);

    // Contar sepultamentos ativos (não exumados)
    const sepultamentosAtivosResult = await query(`
      SELECT COUNT(*) as total
      FROM sepultamentos
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND exumado = false
    `, [produto.codigo_cliente, produto.codigo_estacao]);

    return {
      blocos: parseInt(blocosResult.rows[0].total) || 0,
      subBlocos: parseInt(subBlocosResult.rows[0].total) || 0,
      gavetas: parseInt(gavetasResult.rows[0].total) || 0,
      sepultamentos: parseInt(sepultamentosResult.rows[0].total) || 0,
      sepultamentosAtivos: parseInt(sepultamentosAtivosResult.rows[0].total) || 0
    };
  } catch (error) {
    console.error('❌ Erro ao verificar dados subsequentes:', error);
    return {
      blocos: 0,
      subBlocos: 0,
      gavetas: 0,
      sepultamentos: 0,
      sepultamentosAtivos: 0
    };
  }
}

module.exports = router;
