import axios from 'axios';

// Configuração base da API
const getBaseURL = () => {
  // Em desenvolvimento, usar localhost
  if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    return 'http://localhost:3001/api';
  }

  // Em produção, usar o domínio atual com HTTPS
  const protocol = window.location.protocol;
  const hostname = window.location.hostname;
  return `${protocol}//${hostname}/api`;
};

const api = axios.create({
  baseURL: getBaseURL(),
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Interceptor para requisições
api.interceptors.request.use(
  (config) => {
    // Adicionar token se existir
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    console.log(`🔄 ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('❌ Erro na requisição:', error);
    return Promise.reject(error);
  }
);

// Interceptor para respostas
api.interceptors.response.use(
  (response) => {
    console.log(`✅ ${response.config.method?.toUpperCase()} ${response.config.url} - ${response.status}`);
    return response;
  },
  (error) => {
    console.error(`❌ ${error.config?.method?.toUpperCase()} ${error.config?.url} - ${error.response?.status}`);

    // Se token expirou ou é inválido, redirecionar para login
    // MAS NÃO redirecionar se for tentativa de login (para permitir mensagem de erro)
    if ((error.response?.status === 401 || error.response?.status === 403) &&
        !error.config?.url?.includes('/auth/login')) {
      console.log('🔄 Token inválido, redirecionando para login');
      localStorage.removeItem('token');
      delete api.defaults.headers.common['Authorization'];
      window.location.href = '/login';
    } else if (error.config?.url?.includes('/auth/login')) {
      console.log('🔐 Erro de login - não redirecionando para permitir mensagem de erro');
    }

    return Promise.reject(error);
  }
);

// Serviços de autenticação
export const authService = {
  login: (email, senha) => api.post('/auth/login', { email, senha }),
  logout: () => api.post('/auth/logout'),
  verify: () => api.get('/auth/verify'),
  forgotPassword: (email) => api.post('/auth/forgot-password', { email }),
  validateResetToken: (token) => api.get(`/auth/validate-reset-token/${token}`),
  resetPassword: (data) => api.post('/auth/reset-password', data),
  getPasswordCriteria: () => api.get('/auth/password-criteria'),
  validatePassword: (senha) => api.post('/auth/validate-password', { senha }),
};

// Serviços de clientes
export const clienteService = {
  listar: () => api.get('/clientes'),
  listarComGavetas: () => api.get('/clientes/com-gavetas'),
  buscar: (codigo) => api.get(`/clientes/${codigo}`),
  criar: (dados) => api.post('/clientes', dados),
  atualizar: (codigo, dados) => api.put(`/clientes/${codigo}`, dados),
  toggleStatus: (codigo) => api.patch(`/clientes/${codigo}/toggle-status`),
};

// Serviços de produtos
export const produtoService = {
  listar: (params = {}) => api.get('/produtos', { params }),
  buscar: (codigoCliente, codigoEstacao) => api.get(`/produtos/${codigoCliente}/${codigoEstacao}`),
  buscarCompleto: (id) => api.get(`/produtos/${id}/completo`), // Manter compatibilidade para rotas antigas
  criar: (dados) => api.post('/produtos', dados),
  atualizar: (codigoCliente, codigoEstacao, dados) => api.put(`/produtos/${codigoCliente}/${codigoEstacao}`, dados),
  deletar: (id) => api.delete(`/produtos/${id}`), // Manter compatibilidade para rotas antigas
  toggleStatus: (id) => api.patch(`/produtos/${id}/toggle-status`),
  estatisticas: () => api.get('/produtos/estatisticas'),
  listarSepultamentos: (id, params = {}) => api.get(`/produtos/${id}/sepultamentos`, { params }),

  // Blocos - Nova estrutura baseada em códigos
  listarBlocos: (codigoCliente, codigoEstacao) => api.get(`/produtos/${codigoCliente}/${codigoEstacao}/blocos`),
  criarBloco: (codigoCliente, codigoEstacao, dados) => api.post(`/produtos/${codigoCliente}/${codigoEstacao}/blocos`, dados),
  atualizarBloco: (codigoCliente, codigoEstacao, codigoBloco, dados) => api.put(`/produtos/${codigoCliente}/${codigoEstacao}/${codigoBloco}`, dados),
  deletarBloco: (blocoId) => api.delete(`/produtos/blocos/${blocoId}`), // Manter compatibilidade

  // Sub-blocos - Nova estrutura baseada em códigos
  listarSubBlocos: (codigoCliente, codigoEstacao, codigoBloco) => api.get(`/produtos/${codigoCliente}/${codigoEstacao}/${codigoBloco}/sub-blocos`),
  criarSubBloco: (codigoCliente, codigoEstacao, codigoBloco, dados) => api.post(`/sub-blocos/${codigoCliente}/${codigoEstacao}/${codigoBloco}/sub-blocos`, dados),
  atualizarSubBloco: (codigoCliente, codigoEstacao, codigoBloco, codigoSubBloco, dados) => api.put(`/sub-blocos/${codigoCliente}/${codigoEstacao}/${codigoBloco}/${codigoSubBloco}`, dados),
  deletarSubBloco: (subBlocoId) => api.delete(`/produtos/sub-blocos/${subBlocoId}`), // Manter compatibilidade

  // CRUD simplificado baseado em IDs para a nova página de cadastros
  listarBlocosPorProduto: (produtoId) => api.get(`/produtos/${produtoId}/blocos`),
  criarBlocoSimples: (dados) => api.post('/produtos/blocos', dados),
  atualizarBlocoSimples: (blocoId, dados) => api.put(`/produtos/blocos/${blocoId}`, dados),
  deletarBlocoSimples: (blocoId) => api.delete(`/produtos/blocos/${blocoId}`),

  listarSubBlocosPorBloco: (blocoId) => api.get(`/produtos/blocos/${blocoId}/sub-blocos`),
  criarSubBlocoSimples: (dados) => api.post('/produtos/sub-blocos', dados),
  atualizarSubBlocoSimples: (subBlocoId, dados) => api.put(`/produtos/sub-blocos/${subBlocoId}`, dados),
  deletarSubBlocoSimples: (subBlocoId) => api.delete(`/produtos/sub-blocos/${subBlocoId}`),

  // Gavetas por sub-bloco
  listarGavetas: (codigoCliente, codigoEstacao, codigoBloco, codigoSubBloco, params = {}) => api.get(`/produtos/sub-blocos/${codigoCliente}/${codigoEstacao}/${codigoBloco}/${codigoSubBloco}/gavetas`, { params }),
  listarNumeracoes: (codigoCliente, codigoEstacao, codigoBloco, codigoSubBloco) => api.get(`/produtos/sub-blocos/${codigoCliente}/${codigoEstacao}/${codigoBloco}/${codigoSubBloco}/numeracoes`),
};

// Serviços de gavetas
export const gavetaService = {
  listar: (params = {}) => api.get('/gavetas', { params }),
  listarPorSubBloco: (subBlocoId, params = {}) => api.get('/produtos/sub-blocos/' + subBlocoId + '/gavetas', { params }),
  listarPorBloco: (blocoId, params = {}) => api.get('/gavetas/bloco', { params: { bloco_id: blocoId, ...params } }),
  listarPorProduto: (params = {}) => api.get('/gavetas', { params }), // Para relatórios
  buscar: (id) => api.get(`/gavetas/${id}`),
  buscarHistorico: (id) => api.get(`/gavetas/${id}/historico`),
};

// Serviços de usuários
export const usuarioService = {
  listar: () => api.get('/usuarios'),
  buscar: (id) => api.get(`/usuarios/${id}`),
  criar: (dados) => api.post('/usuarios', dados),
  atualizar: (id, dados) => api.put(`/usuarios/${id}`, dados),
  deletar: (id) => api.delete(`/usuarios/${id}`),
};

// Serviços de sepultamentos
export const sepultamentoService = {
  listar: (params = {}) => api.get('/sepultamentos', { params }),
  listarPorProduto: (produtoId) => api.get(`/sepultamentos/produto/${produtoId}`), // Manter compatibilidade
  listarPorEstacao: (codigoEstacao) => {
    console.log('🔍 CHAMANDO listarPorEstacao para:', codigoEstacao);
    // Forçar cache bust com timestamp
    const timestamp = Date.now();
    return api.get(`/sepultamentos/estacao/${codigoEstacao}?_t=${timestamp}`);
  },
  buscar: (id) => api.get(`/sepultamentos/${id}`),
  criar: (dados) => api.post('/sepultamentos', dados),
  atualizar: (id, dados) => api.put(`/sepultamentos/${id}`, dados),
  exumar: (id, dados) => api.post(`/sepultamentos/${id}/exumar`, dados),
  deletar: (id) => api.delete(`/sepultamentos/${id}`),
};

// Serviços de logs
export const logService = {
  listar: (params = {}) => api.get('/logs', { params }),
  buscar: (id) => api.get(`/logs/${id}`),
  estatisticas: (params = {}) => api.get('/logs/stats/summary', { params }),
};

// Serviços de ranges de gavetas
export const rangeService = {
  listRanges: (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco) =>
    api.get(`/ranges/sub-blocos/${codigo_cliente}/${codigo_estacao}/${codigo_bloco}/${codigo_sub_bloco}/ranges`),

  createRange: (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, dados) =>
    api.post(`/ranges/sub-blocos/${codigo_cliente}/${codigo_estacao}/${codigo_bloco}/${codigo_sub_bloco}/ranges`, dados),

  updateRange: (id, dados) =>
    api.put(`/ranges/ranges/${id}`, dados),

  deleteRange: (id, dados) =>
    api.delete(`/ranges/ranges/${id}`, { data: dados }),

  validateRange: (dados) =>
    api.post('/ranges/validate-range', dados),

  syncGavetas: (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco) =>
    api.post(`/ranges/sub-blocos/${codigo_cliente}/${codigo_estacao}/${codigo_bloco}/${codigo_sub_bloco}/sync-gavetas`),

  validateDeletion: (dados) =>
    api.post('/ranges/validate-deletion', dados),

  listGavetasRange: (id) =>
    api.get(`/ranges/ranges/${id}/gavetas`)
};

// Serviços de dashboard
export const dashboardService = {
  getStats: (params = {}) => api.get('/dashboard/stats', { params }),
  getSepultamentosDetails: (params = {}) => api.get('/dashboard/sepultamentos-details', { params }),
  getGavetasOcupadasDetails: (params = {}) => api.get('/dashboard/gavetas-ocupadas-details', { params }),
  getGavetasDisponiveisDetails: (params = {}) => api.get('/dashboard/gavetas-disponiveis-details', { params }),
  getGavetasPorProdutoDetails: (params = {}) => api.get('/dashboard/gavetas-por-produto-details', { params }),
  getExumacoesDetails: (params = {}) => api.get('/dashboard/exumacoes-details', { params }),
  getProximasExumacoes: (params = {}) => api.get('/dashboard/proximas-exumacoes', { params }),
  getAtividadesRecentes: (params = {}) => api.get('/dashboard/atividades-recentes', { params }),
  getExumacoesPrevistas30Dias: (params = {}) => api.get('/dashboard/exumacoes-previstas-30-dias', { params }),
  getTodasExumacoes30Dias: (params = {}) => api.get('/dashboard/todas-exumacoes-30-dias', { params }),
  getExumacoesPrevistasDetalhes: (params = {}) => api.get('/dashboard/exumacoes-previstas-detalhes', { params }),

  // Novas funções para modais de detalhes
  getTaxaSepultamentoDetalhes: (params = {}) => api.get('/dashboard/taxa-sepultamento-detalhes', { params }),
  getTaxaOcupacaoDetalhes: (params = {}) => api.get('/dashboard/taxa-ocupacao-detalhes', { params }),
  getExumacoes30Dias: (params = {}) => api.get('/dashboard/exumacoes-30-dias', { params }),
  getTodasExumacoes: (params = {}) => api.get('/dashboard/todas-exumacoes', { params }),
};

// Serviço de health check
export const healthService = {
  check: () => api.get('/health'),
};

export default api;
