#!/bin/bash

# Deploy final do ambiente de desenvolvimento
# Portal Evolution - Ultrathink

echo "🚀 DEPLOY FINAL - CORREÇÃO VISIBILIDADE USUÁRIOS"
echo "================================================"
echo ""

# Parar serviços de desenvolvimento
echo "🛑 Parando serviços de desenvolvimento..."
docker service rm portal-evolution-dev_portalevo-frontend-dev portal-evolution-dev_portalevo-backend-dev 2>/dev/null || true

# Aguardar remoção completa
echo "⏳ Aguardando remoção completa dos serviços..."
sleep 10

# Limpar imagens antigas
echo "🧹 Limpando imagens antigas..."
docker image rm portal-evolution-frontend-dev:latest portal-evolution-backend-dev:latest 2>/dev/null || true

# Construir nova imagem do backend
echo "🔨 Construindo nova imagem do backend..."
docker build -t portal-evolution-backend-dev:latest -f portalcliente/docker/Dockerfile.backend portalcliente/

# Construir nova imagem do frontend
echo "🔨 Construindo nova imagem do frontend..."
docker build -t portal-evolution-frontend-dev:latest -f portalcliente/docker/Dockerfile.frontend portalcliente/

# Deploy usando docker-compose
echo "🚀 Fazendo deploy dos serviços..."
docker stack deploy -c docker-compose.dev.yml portal-evolution-dev

# Aguardar serviços ficarem prontos
echo "⏳ Aguardando serviços ficarem prontos..."
sleep 30

# Verificar status dos serviços
echo "📊 Status dos serviços:"
docker service ls | grep portal-evolution-dev

echo ""
echo "🎯 DEPLOY CONCLUÍDO!"
echo "🌐 Ambiente de desenvolvimento: https://portaldev.evo-eden.site"
echo ""
echo "🧪 TESTE AGORA - CORREÇÕES VISIBILIDADE USUÁRIOS:"
echo "1. Acesse: https://portaldev.evo-eden.site"
echo "2. Login: <EMAIL> / adminnbr5410!"
echo ""
echo "📋 TESTE 1 - VISIBILIDADE DE USUÁRIOS:"
echo "3. Vá para a aba 'Usuários'"
echo "4. ✅ Usuário '<EMAIL>' deve aparecer na lista"
echo "5. ✅ Usuários inativos devem ter status 'Inativo' (chip vermelho)"
echo "6. ✅ TODOS os usuários devem estar visíveis (ativos e inativos)"
echo "7. ✅ Usuários ativos devem aparecer primeiro na lista"
echo ""
echo "📋 TESTE 2 - CENTRALIZAÇÃO DOS BOTÕES:"
echo "8. Observe a coluna 'Ações' na lista de usuários"
echo "9. ✅ Botões 'Editar' e 'Deletar' devem estar centralizados"
echo "10. ✅ Botões NÃO devem estar alinhados à direita"
echo "11. ✅ Espaçamento entre botões deve estar correto"
echo ""
echo "📋 TESTE 3 - CRIAÇÃO DE USUÁRIO:"
echo "12. Tente criar usuário com email '<EMAIL>'"
echo "13. ✅ Deve mostrar erro 'Email já está em uso'"
echo "14. ✅ Agora você pode ver que o usuário existe na lista"
echo ""
echo "📊 CORREÇÕES VISIBILIDADE USUÁRIOS IMPLEMENTADAS:"
echo "- ✅ Usuários inativos agora são visíveis"
echo "- ✅ Botões de ação centralizados"
echo "- ✅ Ordenação otimizada (ativos primeiro)"
echo "- ✅ Identificação clara de usuários existentes"
echo ""
