/**
 * =====================================================
 * COMPONENTE PARA GERENCIAMENTO DE RANGES DE GAVETAS
 * =====================================================
 * Interface para criar, editar e deletar ranges de gavetas
 * conforme especificado em definindo_range.md
 */

import React, { useState, useEffect } from 'react';
import {
    Box,
    Card,
    CardContent,
    Typography,
    Button,
    TextField,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    IconButton,
    Chip,
    Alert,
    Grid,
    Divider
} from '@mui/material';
import {
    Add as AddIcon,
    Edit as EditIcon,
    Delete as DeleteIcon,
    Sync as SyncIcon,
    Warning as WarningIcon
} from '@mui/icons-material';
import { rangeService } from '../services/api';

const RangeManager = ({ subBloco, onUpdate }) => {
    const [ranges, setRanges] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    
    // Estados do modal
    const [modalOpen, setModalOpen] = useState(false);
    const [editingRange, setEditingRange] = useState(null);
    const [formData, setFormData] = useState({
        numero_inicio: '',
        numero_fim: ''
    });

    // Estados de validação
    const [validationErrors, setValidationErrors] = useState({});
    const [conflicts, setConflicts] = useState([]);

    useEffect(() => {
        if (subBloco) {
            loadRanges();
        }
    }, [subBloco]);

    const loadRanges = async () => {
        try {
            setLoading(true);
            setError('');
            
            const response = await rangeService.listRanges(
                subBloco.codigo_cliente,
                subBloco.codigo_estacao,
                subBloco.codigo_bloco,
                subBloco.codigo_sub_bloco
            );
            
            setRanges(response.data.ranges || []);
        } catch (error) {
            console.error('Erro ao carregar ranges:', error);
            setError('Erro ao carregar ranges de gavetas');
        } finally {
            setLoading(false);
        }
    };

    const handleOpenModal = (range = null) => {
        setEditingRange(range);
        setFormData({
            numero_inicio: range ? range.numero_inicio.toString() : '',
            numero_fim: range ? range.numero_fim.toString() : ''
        });
        setValidationErrors({});
        setConflicts([]);
        setModalOpen(true);
    };

    const handleCloseModal = () => {
        setModalOpen(false);
        setEditingRange(null);
        setFormData({ numero_inicio: '', numero_fim: '' });
        setValidationErrors({});
        setConflicts([]);
    };

    const validateForm = () => {
        const errors = {};
        
        if (!formData.numero_inicio) {
            errors.numero_inicio = 'Número de início é obrigatório';
        } else if (parseInt(formData.numero_inicio) < 1) {
            errors.numero_inicio = 'Número de início deve ser maior que 0';
        }
        
        if (!formData.numero_fim) {
            errors.numero_fim = 'Número de fim é obrigatório';
        } else if (parseInt(formData.numero_fim) < 1) {
            errors.numero_fim = 'Número de fim deve ser maior que 0';
        }
        
        if (formData.numero_inicio && formData.numero_fim) {
            if (parseInt(formData.numero_inicio) > parseInt(formData.numero_fim)) {
                errors.numero_inicio = 'Número de início não pode ser maior que o fim';
            }
        }
        
        setValidationErrors(errors);
        return Object.keys(errors).length === 0;
    };

    const handleSave = async () => {
        if (!validateForm()) {
            return;
        }

        try {
            setLoading(true);
            setError('');
            setSuccess('');

            const rangeData = {
                codigo_cliente: subBloco.codigo_cliente,
                codigo_estacao: subBloco.codigo_estacao,
                codigo_bloco: subBloco.codigo_bloco,
                codigo_sub_bloco: subBloco.codigo_sub_bloco,
                numero_inicio: parseInt(formData.numero_inicio),
                numero_fim: parseInt(formData.numero_fim)
            };

            let response;
            if (editingRange) {
                response = await rangeService.updateRange(editingRange.id, rangeData);
            } else {
                response = await rangeService.createRange(
                    subBloco.codigo_cliente,
                    subBloco.codigo_estacao,
                    subBloco.codigo_bloco,
                    subBloco.codigo_sub_bloco,
                    rangeData
                );
            }

            if (response.data.success) {
                setSuccess(editingRange ? 'Range atualizado com sucesso!' : 'Range criado com sucesso!');
                handleCloseModal();
                loadRanges();
                if (onUpdate) onUpdate();
            } else {
                setError(response.data.error || 'Erro ao salvar range');
                if (response.data.conflicts) {
                    setConflicts(response.data.conflicts);
                }
            }
        } catch (error) {
            console.error('Erro ao salvar range:', error);
            setError(error.response?.data?.error || 'Erro ao salvar range');
            if (error.response?.data?.conflicts) {
                setConflicts(error.response.data.conflicts);
            }
        } finally {
            setLoading(false);
        }
    };

    const handleDelete = async (range) => {
        if (!window.confirm(`Tem certeza que deseja deletar o range ${range.numero_inicio} a ${range.numero_fim}?`)) {
            return;
        }

        try {
            setLoading(true);
            setError('');
            setSuccess('');

            const response = await rangeService.deleteRange(range.id, {
                codigo_cliente: subBloco.codigo_cliente,
                codigo_estacao: subBloco.codigo_estacao,
                codigo_bloco: subBloco.codigo_bloco,
                codigo_sub_bloco: subBloco.codigo_sub_bloco
            });

            if (response.data.success) {
                setSuccess('Range deletado com sucesso!');
                loadRanges();
                if (onUpdate) onUpdate();
            } else {
                setError(response.data.error || 'Erro ao deletar range');
            }
        } catch (error) {
            console.error('Erro ao deletar range:', error);
            setError(error.response?.data?.error || 'Erro ao deletar range');
        } finally {
            setLoading(false);
        }
    };

    const handleSyncGavetas = async () => {
        try {
            setLoading(true);
            setError('');
            setSuccess('');

            const response = await rangeService.syncGavetas(
                subBloco.codigo_cliente,
                subBloco.codigo_estacao,
                subBloco.codigo_bloco,
                subBloco.codigo_sub_bloco
            );

            if (response.data.success) {
                setSuccess('Gavetas sincronizadas com sucesso!');
                loadRanges();
                if (onUpdate) onUpdate();
            } else {
                setError(response.data.error || 'Erro ao sincronizar gavetas');
            }
        } catch (error) {
            console.error('Erro ao sincronizar gavetas:', error);
            setError(error.response?.data?.error || 'Erro ao sincronizar gavetas');
        } finally {
            setLoading(false);
        }
    };

    const getTotalGavetas = () => {
        return ranges.reduce((total, range) => {
            return total + (range.numero_fim - range.numero_inicio + 1);
        }, 0);
    };

    const getGavetasDisponiveis = () => {
        return ranges.reduce((total, range) => {
            return total + parseInt(range.gavetas_disponiveis || 0);
        }, 0);
    };

    const getGavetasOcupadas = () => {
        return ranges.reduce((total, range) => {
            return total + parseInt(range.gavetas_ocupadas || 0);
        }, 0);
    };

    if (!subBloco) {
        return (
            <Card>
                <CardContent>
                    <Typography variant="h6" gutterBottom>
                        Gerenciamento de Ranges
                    </Typography>
                    <Typography color="textSecondary">
                        Selecione um sub-bloco para gerenciar os ranges de gavetas
                    </Typography>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card>
            <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                    <Typography variant="h6">
                        Ranges de Gavetas - {subBloco.denominacao}
                    </Typography>
                    <Box>
                        <Button
                            variant="outlined"
                            startIcon={<SyncIcon />}
                            onClick={handleSyncGavetas}
                            disabled={loading}
                            sx={{ mr: 1 }}
                        >
                            Sincronizar
                        </Button>
                        <Button
                            variant="contained"
                            startIcon={<AddIcon />}
                            onClick={() => handleOpenModal()}
                            disabled={loading}
                        >
                            Novo Range
                        </Button>
                    </Box>
                </Box>

                {/* Estatísticas */}
                <Grid container spacing={2} mb={2}>
                    <Grid item xs={4}>
                        <Paper sx={{ p: 2, textAlign: 'center' }}>
                            <Typography variant="h4" color="primary">
                                {getTotalGavetas()}
                            </Typography>
                            <Typography variant="body2" color="textSecondary">
                                Total de Gavetas
                            </Typography>
                        </Paper>
                    </Grid>
                    <Grid item xs={4}>
                        <Paper sx={{ p: 2, textAlign: 'center' }}>
                            <Typography variant="h4" color="success.main">
                                {getGavetasDisponiveis()}
                            </Typography>
                            <Typography variant="body2" color="textSecondary">
                                Disponíveis
                            </Typography>
                        </Paper>
                    </Grid>
                    <Grid item xs={4}>
                        <Paper sx={{ p: 2, textAlign: 'center' }}>
                            <Typography variant="h4" color="error.main">
                                {getGavetasOcupadas()}
                            </Typography>
                            <Typography variant="body2" color="textSecondary">
                                Ocupadas
                            </Typography>
                        </Paper>
                    </Grid>
                </Grid>

                <Divider sx={{ my: 2 }} />

                {/* Mensagens */}
                {error && (
                    <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
                        {error}
                    </Alert>
                )}
                {success && (
                    <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess('')}>
                        {success}
                    </Alert>
                )}

                {/* Conflitos */}
                {conflicts.length > 0 && (
                    <Alert severity="warning" sx={{ mb: 2 }}>
                        <Typography variant="subtitle2" gutterBottom>
                            Conflitos encontrados:
                        </Typography>
                        {conflicts.map((conflict, index) => (
                            <Typography key={index} variant="body2">
                                • Sub-bloco {conflict.codigo_sub_bloco} ({conflict.sub_bloco_nome}): 
                                Range {conflict.numero_inicio} a {conflict.numero_fim}
                            </Typography>
                        ))}
                    </Alert>
                )}

                {/* Tabela de Ranges */}
                <TableContainer component={Paper}>
                    <Table>
                        <TableHead>
                            <TableRow>
                                <TableCell>Range</TableCell>
                                <TableCell align="center">Total Gavetas</TableCell>
                                <TableCell align="center">Disponíveis</TableCell>
                                <TableCell align="center">Ocupadas</TableCell>
                                <TableCell align="center">Status</TableCell>
                                <TableCell align="center">Ações</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {ranges.length === 0 ? (
                                <TableRow>
                                    <TableCell colSpan={6} align="center">
                                        <Typography color="textSecondary">
                                            Nenhum range cadastrado
                                        </Typography>
                                    </TableCell>
                                </TableRow>
                            ) : (
                                ranges.map((range) => (
                                    <TableRow key={range.id}>
                                        <TableCell>
                                            <Typography variant="body2" fontWeight="bold">
                                                {range.numero_inicio} a {range.numero_fim}
                                            </Typography>
                                            <Typography variant="caption" color="textSecondary">
                                                {range.numero_fim - range.numero_inicio + 1} gavetas
                                            </Typography>
                                        </TableCell>
                                        <TableCell align="center">
                                            {range.total_gavetas || 0}
                                        </TableCell>
                                        <TableCell align="center">
                                            <Chip 
                                                label={range.gavetas_disponiveis || 0}
                                                color="success"
                                                size="small"
                                            />
                                        </TableCell>
                                        <TableCell align="center">
                                            <Chip 
                                                label={range.gavetas_ocupadas || 0}
                                                color="error"
                                                size="small"
                                            />
                                        </TableCell>
                                        <TableCell align="center">
                                            <Chip 
                                                label={range.ativo ? 'Ativo' : 'Inativo'}
                                                color={range.ativo ? 'success' : 'default'}
                                                size="small"
                                            />
                                        </TableCell>
                                        <TableCell align="center">
                                            <IconButton
                                                size="small"
                                                onClick={() => handleOpenModal(range)}
                                                disabled={loading}
                                            >
                                                <EditIcon />
                                            </IconButton>
                                            <IconButton
                                                size="small"
                                                onClick={() => handleDelete(range)}
                                                disabled={loading || parseInt(range.gavetas_ocupadas || 0) > 0}
                                                color="error"
                                            >
                                                <DeleteIcon />
                                            </IconButton>
                                            {parseInt(range.gavetas_ocupadas || 0) > 0 && (
                                                <WarningIcon 
                                                    color="warning" 
                                                    fontSize="small"
                                                    title="Range possui gavetas ocupadas"
                                                />
                                            )}
                                        </TableCell>
                                    </TableRow>
                                ))
                            )}
                        </TableBody>
                    </Table>
                </TableContainer>

                {/* Modal de Criação/Edição */}
                <Dialog open={modalOpen} onClose={handleCloseModal} maxWidth="sm" fullWidth>
                    <DialogTitle>
                        {editingRange ? 'Editar Range' : 'Novo Range'}
                    </DialogTitle>
                    <DialogContent>
                        <Grid container spacing={2} sx={{ mt: 1 }}>
                            <Grid item xs={6}>
                                <TextField
                                    fullWidth
                                    label="Número de Início"
                                    type="number"
                                    value={formData.numero_inicio}
                                    onChange={(e) => setFormData({ ...formData, numero_inicio: e.target.value })}
                                    error={!!validationErrors.numero_inicio}
                                    helperText={validationErrors.numero_inicio}
                                    inputProps={{ min: 1 }}
                                />
                            </Grid>
                            <Grid item xs={6}>
                                <TextField
                                    fullWidth
                                    label="Número de Fim"
                                    type="number"
                                    value={formData.numero_fim}
                                    onChange={(e) => setFormData({ ...formData, numero_fim: e.target.value })}
                                    error={!!validationErrors.numero_fim}
                                    helperText={validationErrors.numero_fim}
                                    inputProps={{ min: 1 }}
                                />
                            </Grid>
                        </Grid>

                        {formData.numero_inicio && formData.numero_fim && (
                            <Alert severity="info" sx={{ mt: 2 }}>
                                Este range criará {parseInt(formData.numero_fim) - parseInt(formData.numero_inicio) + 1} gavetas
                            </Alert>
                        )}
                    </DialogContent>
                    <DialogActions>
                        <Button onClick={handleCloseModal}>
                            Cancelar
                        </Button>
                        <Button 
                            onClick={handleSave} 
                            variant="contained"
                            disabled={loading}
                        >
                            {editingRange ? 'Atualizar' : 'Criar'}
                        </Button>
                    </DialogActions>
                </Dialog>
            </CardContent>
        </Card>
    );
};

export default RangeManager;
