import React from 'react';
import { Card, CardContent, CardHeader, CardA<PERSON>, Typography, Box } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledCard = styled(Card, {
  shouldForwardProp: (prop) => prop !== 'clickable',
})(({ theme, clickable }) => ({
  borderRadius: '16px',
  boxShadow: theme.shadows[2],
  backgroundColor: '#ffffff',
  transition: 'all 0.3s ease',
  border: '1px solid transparent',

  ...(clickable && {
    cursor: 'pointer',
    '&:hover': {
      transform: 'translateY(-4px)',
      boxShadow: theme.shadows[8],
      borderColor: theme.palette.primary.light,
    },
    '&:active': {
      transform: 'translateY(-2px)',
    },
  }),
  
  '& .MuiCardContent-root': {
    padding: '24px',
    '&:last-child': {
      paddingBottom: '24px',
    },
  },
  
  '& .MuiCardHeader-root': {
    padding: '24px 24px 16px 24px',
  },
  
  '& .MuiCardActions-root': {
    padding: '16px 24px 24px 24px',
    justifyContent: 'flex-end',
  },
}));

const StyledCardHeader = styled(CardHeader)(({ theme }) => ({
  '& .MuiCardHeader-title': {
    fontSize: '20px',
    fontWeight: 500,
    lineHeight: '28px',
    color: theme.palette.text.primary,
  },
  '& .MuiCardHeader-subheader': {
    fontSize: '14px',
    fontWeight: 400,
    lineHeight: '20px',
    color: theme.palette.text.secondary,
    marginTop: '4px',
  },
}));

const StandardCard = ({
  children,
  title,
  subtitle,
  actions,
  clickable = false,
  onClick,
  elevation = 2,
  sx = {},
  headerAction,
  ...props
}) => {
  const handleClick = clickable && onClick ? onClick : undefined;
  
  return (
    <StyledCard
      elevation={elevation}
      clickable={clickable}
      onClick={handleClick}
      sx={sx}
      {...props}
    >
      {(title || subtitle || headerAction) && (
        <StyledCardHeader
          title={title}
          subheader={subtitle}
          action={headerAction}
        />
      )}
      
      <CardContent>
        {children}
      </CardContent>
      
      {actions && (
        <CardActions>
          {actions}
        </CardActions>
      )}
    </StyledCard>
  );
};

// Componente para cards de estatísticas
export const StatsCard = ({
  title,
  value,
  subtitle,
  icon,
  color = 'primary',
  progress,
  onClick,
  ...props
}) => {
  return (
    <StandardCard
      clickable={!!onClick}
      onClick={onClick}
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
      }}
      {...props}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        {icon && (
          <Box
            sx={{
              width: 56,
              height: 56,
              borderRadius: '50%',
              backgroundColor: `${color}.main`,
              color: 'white',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mr: 2,
            }}
          >
            {icon}
          </Box>
        )}
        <Box sx={{ flex: 1 }}>
          <Typography variant="h6" color="text.primary" gutterBottom>
            {title}
          </Typography>
          <Typography variant="h3" color="text.primary" sx={{ fontWeight: 'bold', mb: 1 }}>
            {value}
          </Typography>
          {subtitle && (
            <Typography variant="body2" color="text.secondary">
              {subtitle}
            </Typography>
          )}
        </Box>
      </Box>
      
      {progress && (
        <Box sx={{ mt: 'auto' }}>
          {progress}
        </Box>
      )}
    </StandardCard>
  );
};

export default StandardCard;
