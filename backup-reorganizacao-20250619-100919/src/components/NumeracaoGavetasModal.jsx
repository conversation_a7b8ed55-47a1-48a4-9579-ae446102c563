import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import Modal from './Modal';

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const Label = styled.label`
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
`;

const Input = styled.input`
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: #059669;
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
  }
`;

const RangeContainer = styled.div`
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  background: #f9fafb;
`;

const RangeHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const RangeTitle = styled.h4`
  margin: 0;
  color: #374151;
  font-size: 0.875rem;
`;

const RemoveButton = styled.button`
  background: #dc2626;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 0.75rem;
  cursor: pointer;

  &:hover {
    background: #b91c1c;
  }
`;

const RangeInputs = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
`;

const AddRangeButton = styled.button`
  background: #059669;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: #047857;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
`;

const Button = styled.button`
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;

  &.primary {
    background: linear-gradient(135deg, #1e3a8a 0%, #059669 100%);
    color: white;

    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  &.secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;

    &:hover {
      background: #e5e7eb;
    }
  }
`;

const ErrorMessage = styled.div`
  color: #dc2626;
  font-size: 0.875rem;
  margin-top: 4px;
`;

const InfoText = styled.p`
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0 0 16px 0;
`;

const NumeracaoGavetasModal = ({ isOpen, onClose, subBloco, onSuccess }) => {
  const [ranges, setRanges] = useState([{ inicio: '', fim: '' }]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (isOpen && !subBloco) {
      setRanges([{ inicio: '', fim: '' }]);
      setError('');
    }
  }, [isOpen, subBloco]);

  const handleRangeChange = (index, field, value) => {
    const newRanges = [...ranges];
    newRanges[index][field] = value;
    setRanges(newRanges);
  };

  const addRange = () => {
    setRanges([...ranges, { inicio: '', fim: '' }]);
  };

  const removeRange = (index) => {
    if (ranges.length > 1) {
      const newRanges = ranges.filter((_, i) => i !== index);
      setRanges(newRanges);
    }
  };

  const validateRanges = () => {
    for (let i = 0; i < ranges.length; i++) {
      const range = ranges[i];
      const inicio = parseInt(range.inicio);
      const fim = parseInt(range.fim);

      if (!range.inicio || !range.fim) {
        setError(`Range ${i + 1}: Início e fim são obrigatórios`);
        return false;
      }

      if (inicio >= fim) {
        setError(`Range ${i + 1}: Início deve ser menor que o fim`);
        return false;
      }

      if (inicio < 1) {
        setError(`Range ${i + 1}: Início deve ser maior que 0`);
        return false;
      }

      // Verificar sobreposição com outros ranges
      for (let j = i + 1; j < ranges.length; j++) {
        const otherRange = ranges[j];
        const outroInicio = parseInt(otherRange.inicio);
        const outroFim = parseInt(otherRange.fim);

        if (
          (inicio >= outroInicio && inicio <= outroFim) ||
          (fim >= outroInicio && fim <= outroFim) ||
          (inicio <= outroInicio && fim >= outroFim)
        ) {
          setError(`Ranges ${i + 1} e ${j + 1} se sobrepõem`);
          return false;
        }
      }
    }

    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    if (!validateRanges()) {
      setLoading(false);
      return;
    }

    try {
      // Aqui você implementaria a chamada para a API
      // para salvar as numerações e gerar as gavetas
      console.log('Salvando numerações:', ranges);
      
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Erro ao salvar numerações:', error);
      setError('Erro ao salvar numerações');
    } finally {
      setLoading(false);
    }
  };

  const getTotalGavetas = () => {
    return ranges.reduce((total, range) => {
      const inicio = parseInt(range.inicio) || 0;
      const fim = parseInt(range.fim) || 0;
      return total + (fim >= inicio ? fim - inicio + 1 : 0);
    }, 0);
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Configurar Numeração de Gavetas"
      maxWidth="600px"
    >
      <Form onSubmit={handleSubmit}>
        <InfoText>
          Configure os ranges de numeração para as gavetas deste sub-bloco. 
          Você pode criar múltiplos ranges não consecutivos.
        </InfoText>

        {ranges.map((range, index) => (
          <RangeContainer key={index}>
            <RangeHeader>
              <RangeTitle>Range {index + 1}</RangeTitle>
              {ranges.length > 1 && (
                <RemoveButton 
                  type="button" 
                  onClick={() => removeRange(index)}
                >
                  Remover
                </RemoveButton>
              )}
            </RangeHeader>
            <RangeInputs>
              <FormGroup>
                <Label>Número Inicial</Label>
                <Input
                  type="number"
                  min="1"
                  value={range.inicio}
                  onChange={(e) => handleRangeChange(index, 'inicio', e.target.value)}
                  placeholder="Ex: 1"
                  required
                />
              </FormGroup>
              <FormGroup>
                <Label>Número Final</Label>
                <Input
                  type="number"
                  min="1"
                  value={range.fim}
                  onChange={(e) => handleRangeChange(index, 'fim', e.target.value)}
                  placeholder="Ex: 20"
                  required
                />
              </FormGroup>
            </RangeInputs>
          </RangeContainer>
        ))}

        <AddRangeButton type="button" onClick={addRange}>
          + Adicionar Range
        </AddRangeButton>

        {getTotalGavetas() > 0 && (
          <InfoText>
            <strong>Total de gavetas que serão criadas: {getTotalGavetas()}</strong>
          </InfoText>
        )}

        {error && <ErrorMessage>{error}</ErrorMessage>}

        <ButtonGroup>
          <Button type="button" className="secondary" onClick={onClose}>
            Cancelar
          </Button>
          <Button type="submit" className="primary" disabled={loading}>
            {loading ? 'Salvando...' : 'Criar Gavetas'}
          </Button>
        </ButtonGroup>
      </Form>
    </Modal>
  );
};

export default NumeracaoGavetasModal;
