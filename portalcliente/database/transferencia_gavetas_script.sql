-- ===================================================================
-- SCRIPT DE TRANSFERÊNCIA DE GAVETAS - PORTAL EVOLUTION
-- ===================================================================
-- Cliente: TV_001 | Estação: ETEN_002 | Bloco: BL_001
-- Transferir gavetas específicas do SUB_002 para SUB_003
-- IMPORTANTE: Preservar TODOS os dados de sepultamentos
-- ===================================================================

-- Definir variáveis para facilitar manutenção
\set cliente 'TV_001'
\set estacao 'ETEN_002'
\set bloco 'BL_001'
\set sub_origem 'SUB_002'
\set sub_destino 'SUB_003'

-- ===================================================================
-- ETAPA 1: VERIFICAÇÕES DE SEGURANÇA
-- ===================================================================

-- Verificar se os sub-blocos existem
DO $$
BEGIN
    -- Verificar sub-bloco origem
    IF NOT EXISTS (
        SELECT 1 FROM sub_blocos 
        WHERE codigo_cliente = 'TV_001' 
        AND codigo_estacao = 'ETEN_002' 
        AND codigo_bloco = 'BL_001' 
        AND codigo_sub_bloco = 'SUB_002'
    ) THEN
        RAISE EXCEPTION 'Sub-bloco origem SUB_002 não encontrado!';
    END IF;
    
    -- Verificar sub-bloco destino
    IF NOT EXISTS (
        SELECT 1 FROM sub_blocos 
        WHERE codigo_cliente = 'TV_001' 
        AND codigo_estacao = 'ETEN_002' 
        AND codigo_bloco = 'BL_001' 
        AND codigo_sub_bloco = 'SUB_003'
    ) THEN
        RAISE EXCEPTION 'Sub-bloco destino SUB_003 não encontrado!';
    END IF;
    
    RAISE NOTICE 'Verificações de segurança: OK';
END $$;

-- ===================================================================
-- ETAPA 2: BACKUP DE SEGURANÇA
-- ===================================================================

-- Criar tabela de backup para gavetas
CREATE TEMP TABLE backup_gavetas_transferencia AS
SELECT * FROM gavetas 
WHERE codigo_cliente = 'TV_001' 
AND codigo_estacao = 'ETEN_002' 
AND codigo_bloco = 'BL_001' 
AND codigo_sub_bloco IN ('SUB_002', 'SUB_003');

-- Criar tabela de backup para sepultamentos
CREATE TEMP TABLE backup_sepultamentos_transferencia AS
SELECT * FROM sepultamentos 
WHERE codigo_cliente = 'TV_001' 
AND codigo_estacao = 'ETEN_002' 
AND codigo_bloco = 'BL_001' 
AND codigo_sub_bloco IN ('SUB_002', 'SUB_003');

-- Criar tabela de backup para numerações
CREATE TEMP TABLE backup_numeracoes_transferencia AS
SELECT * FROM numeracoes_gavetas 
WHERE codigo_cliente = 'TV_001' 
AND codigo_estacao = 'ETEN_002' 
AND codigo_bloco = 'BL_001' 
AND codigo_sub_bloco IN ('SUB_002', 'SUB_003');

RAISE NOTICE 'Backup de segurança criado';

-- ===================================================================
-- ETAPA 3: LISTAR GAVETAS A SEREM TRANSFERIDAS
-- ===================================================================

-- Gavetas específicas que devem ser transferidas do SUB_002 para SUB_003
CREATE TEMP TABLE gavetas_para_transferir (numero_gaveta INTEGER);

INSERT INTO gavetas_para_transferir VALUES 
(1279), (1280), (1281), (1282), (1283), (1284), (1285),
(1303), (1304), (1305), (1306), (1307), (1308), (1309),
(1327), (1328), (1329), (1330), (1331), (1332),
(1350), (1351), (1352), (1353), (1354), (1355), (1356),
(1374), (1375), (1376), (1377), (1378), (1379), (1380),
(1398), (1399), (1400), (1401), (1402), (1403),
(1421), (1422), (1423), (1424), (1425), (1426), (1427);

-- Verificar quantas gavetas serão transferidas
SELECT COUNT(*) as total_gavetas_transferir FROM gavetas_para_transferir;

-- ===================================================================
-- ETAPA 4: VERIFICAR SEPULTAMENTOS EXISTENTES
-- ===================================================================

-- Listar sepultamentos nas gavetas que serão transferidas
SELECT 
    s.numero_gaveta,
    s.nome_sepultado,
    s.data_sepultamento,
    s.data_exumacao,
    CASE WHEN s.data_exumacao IS NULL THEN 'ATIVO' ELSE 'EXUMADO' END as status
FROM sepultamentos s
INNER JOIN gavetas_para_transferir gpt ON s.numero_gaveta = gpt.numero_gaveta
WHERE s.codigo_cliente = 'TV_001' 
AND s.codigo_estacao = 'ETEN_002' 
AND s.codigo_bloco = 'BL_001' 
AND s.codigo_sub_bloco = 'SUB_002'
AND s.ativo = true
ORDER BY s.numero_gaveta;

RAISE NOTICE 'Verificação de sepultamentos concluída';

-- ===================================================================
-- ETAPA 5: TRANSFERÊNCIA DOS SEPULTAMENTOS
-- ===================================================================

-- Atualizar sepultamentos para o novo sub-bloco
UPDATE sepultamentos
SET
    codigo_sub_bloco = 'SUB_003',
    updated_at = CURRENT_TIMESTAMP
WHERE codigo_cliente = 'TV_001'
AND codigo_estacao = 'ETEN_002'
AND codigo_bloco = 'BL_001'
AND codigo_sub_bloco = 'SUB_002'
AND numero_gaveta IN (SELECT numero_gaveta FROM gavetas_para_transferir);

-- Verificar quantos sepultamentos foram atualizados
GET DIAGNOSTICS updated_sepultamentos = ROW_COUNT;
RAISE NOTICE 'Sepultamentos transferidos: %', updated_sepultamentos;

-- ===================================================================
-- ETAPA 6: TRANSFERÊNCIA DAS GAVETAS
-- ===================================================================

-- Atualizar gavetas para o novo sub-bloco
UPDATE gavetas
SET
    codigo_sub_bloco = 'SUB_003',
    updated_at = CURRENT_TIMESTAMP
WHERE codigo_cliente = 'TV_001'
AND codigo_estacao = 'ETEN_002'
AND codigo_bloco = 'BL_001'
AND codigo_sub_bloco = 'SUB_002'
AND numero_gaveta IN (SELECT numero_gaveta FROM gavetas_para_transferir);

-- Verificar quantas gavetas foram atualizadas
GET DIAGNOSTICS updated_gavetas = ROW_COUNT;
RAISE NOTICE 'Gavetas transferidas: %', updated_gavetas;

-- ===================================================================
-- ETAPA 7: REMOÇÃO DO RANGE 1279-1444 DO SUB_002
-- ===================================================================

-- Remover o range de numeração 1279-1444 do SUB_002
DELETE FROM numeracoes_gavetas
WHERE codigo_cliente = 'TV_001'
AND codigo_estacao = 'ETEN_002'
AND codigo_bloco = 'BL_001'
AND codigo_sub_bloco = 'SUB_002'
AND numero_inicio = 1279
AND numero_fim = 1444;

-- Verificar se o range foi removido
GET DIAGNOSTICS deleted_ranges = ROW_COUNT;
RAISE NOTICE 'Ranges removidos: %', deleted_ranges;

-- ===================================================================
-- ETAPA 8: CRIAR NOVO RANGE NO SUB_003 (SE NECESSÁRIO)
-- ===================================================================

-- Verificar se já existe um range que cubra as gavetas transferidas no SUB_003
DO $$
DECLARE
    min_gaveta INTEGER := 1279;
    max_gaveta INTEGER := 1427;
    range_exists BOOLEAN := FALSE;
BEGIN
    -- Verificar se existe range que cubra todas as gavetas
    SELECT EXISTS(
        SELECT 1 FROM numeracoes_gavetas
        WHERE codigo_cliente = 'TV_001'
        AND codigo_estacao = 'ETEN_002'
        AND codigo_bloco = 'BL_001'
        AND codigo_sub_bloco = 'SUB_003'
        AND numero_inicio <= min_gaveta
        AND numero_fim >= max_gaveta
    ) INTO range_exists;

    IF NOT range_exists THEN
        -- Criar novo range para as gavetas transferidas
        INSERT INTO numeracoes_gavetas (
            codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco,
            numero_inicio, numero_fim, ativo, created_at, updated_at
        ) VALUES (
            'TV_001', 'ETEN_002', 'BL_001', 'SUB_003',
            min_gaveta, max_gaveta, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
        );

        RAISE NOTICE 'Novo range criado no SUB_003: % - %', min_gaveta, max_gaveta;
    ELSE
        RAISE NOTICE 'Range já existe no SUB_003 que cobre as gavetas transferidas';
    END IF;
END $$;

-- ===================================================================
-- ETAPA 9: VERIFICAÇÕES FINAIS
-- ===================================================================

-- Verificar integridade dos dados após transferência
SELECT 'VERIFICAÇÃO FINAL' as etapa;

-- 1. Verificar se todas as gavetas foram transferidas
SELECT
    'Gavetas no SUB_003' as tipo,
    COUNT(*) as quantidade
FROM gavetas
WHERE codigo_cliente = 'TV_001'
AND codigo_estacao = 'ETEN_002'
AND codigo_bloco = 'BL_001'
AND codigo_sub_bloco = 'SUB_003'
AND numero_gaveta IN (SELECT numero_gaveta FROM gavetas_para_transferir)

UNION ALL

-- 2. Verificar se não restaram gavetas no SUB_002
SELECT
    'Gavetas restantes no SUB_002' as tipo,
    COUNT(*) as quantidade
FROM gavetas
WHERE codigo_cliente = 'TV_001'
AND codigo_estacao = 'ETEN_002'
AND codigo_bloco = 'BL_001'
AND codigo_sub_bloco = 'SUB_002'
AND numero_gaveta IN (SELECT numero_gaveta FROM gavetas_para_transferir)

UNION ALL

-- 3. Verificar sepultamentos transferidos
SELECT
    'Sepultamentos no SUB_003' as tipo,
    COUNT(*) as quantidade
FROM sepultamentos
WHERE codigo_cliente = 'TV_001'
AND codigo_estacao = 'ETEN_002'
AND codigo_bloco = 'BL_001'
AND codigo_sub_bloco = 'SUB_003'
AND numero_gaveta IN (SELECT numero_gaveta FROM gavetas_para_transferir)
AND ativo = true

UNION ALL

-- 4. Verificar se não restaram sepultamentos no SUB_002
SELECT
    'Sepultamentos restantes no SUB_002' as tipo,
    COUNT(*) as quantidade
FROM sepultamentos
WHERE codigo_cliente = 'TV_001'
AND codigo_estacao = 'ETEN_002'
AND codigo_bloco = 'BL_001'
AND codigo_sub_bloco = 'SUB_002'
AND numero_gaveta IN (SELECT numero_gaveta FROM gavetas_para_transferir)
AND ativo = true;

-- ===================================================================
-- ETAPA 10: RELATÓRIO FINAL
-- ===================================================================

-- Relatório detalhado das gavetas transferidas
SELECT
    g.numero_gaveta,
    g.codigo_sub_bloco,
    g.disponivel,
    CASE
        WHEN s.id IS NOT NULL AND s.data_exumacao IS NULL THEN 'OCUPADA'
        WHEN s.id IS NOT NULL AND s.data_exumacao IS NOT NULL THEN 'EXUMADA'
        ELSE 'DISPONÍVEL'
    END as status_gaveta,
    s.nome_sepultado,
    s.data_sepultamento
FROM gavetas g
LEFT JOIN sepultamentos s ON (
    g.codigo_cliente = s.codigo_cliente AND
    g.codigo_estacao = s.codigo_estacao AND
    g.codigo_bloco = s.codigo_bloco AND
    g.codigo_sub_bloco = s.codigo_sub_bloco AND
    g.numero_gaveta = s.numero_gaveta AND
    s.ativo = true
)
WHERE g.codigo_cliente = 'TV_001'
AND g.codigo_estacao = 'ETEN_002'
AND g.codigo_bloco = 'BL_001'
AND g.codigo_sub_bloco = 'SUB_003'
AND g.numero_gaveta IN (SELECT numero_gaveta FROM gavetas_para_transferir)
ORDER BY g.numero_gaveta;

-- ===================================================================
-- FINALIZAÇÃO
-- ===================================================================

RAISE NOTICE '=== TRANSFERÊNCIA CONCLUÍDA COM SUCESSO ===';
RAISE NOTICE 'Todas as gavetas especificadas foram transferidas do SUB_002 para SUB_003';
RAISE NOTICE 'Todos os sepultamentos foram preservados e transferidos junto';
RAISE NOTICE 'Range 1279-1444 foi removido do SUB_002';
RAISE NOTICE 'Sistema mantém integridade total dos dados';

-- Limpar tabelas temporárias
DROP TABLE gavetas_para_transferir;
DROP TABLE backup_gavetas_transferencia;
DROP TABLE backup_sepultamentos_transferencia;
DROP TABLE backup_numeracoes_transferencia;
