const { Pool } = require('pg');
require('dotenv').config();

// Configuração do banco de dados
const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
});

async function testHierarchicalQueries() {
  console.log('🔍 Testando consultas hierárquicas...');
  console.log('=' .repeat(60));

  const client = await pool.connect();
  
  try {
    // 1. Teste de consulta hierárquica completa
    console.log('📊 1. Consulta hierárquica completa:');
    const hierarchyResult = await client.query(`
      SELECT 
        c.codigo_cliente,
        c.nome_fantasia,
        p.codigo_estacao,
        p.denominacao as produto_nome,
        b.codigo_bloco,
        b.denominacao as bloco_nome,
        sb.codigo_sub_bloco,
        sb.denominacao as sub_bloco_nome,
        COUNT(g.numero_gaveta) as total_gavetas,
        COUNT(s.id) as total_sepultamentos
      FROM clientes c
      LEFT JOIN produtos p ON c.codigo_cliente = p.codigo_cliente
      LEFT JOIN blocos b ON p.codigo_cliente = b.codigo_cliente AND p.codigo_estacao = b.codigo_estacao
      LEFT JOIN sub_blocos sb ON b.codigo_cliente = sb.codigo_cliente 
                              AND b.codigo_estacao = sb.codigo_estacao 
                              AND b.codigo_bloco = sb.codigo_bloco
      LEFT JOIN gavetas g ON sb.codigo_cliente = g.codigo_cliente 
                          AND sb.codigo_estacao = g.codigo_estacao 
                          AND sb.codigo_bloco = g.codigo_bloco 
                          AND sb.codigo_sub_bloco = g.codigo_sub_bloco
      LEFT JOIN sepultamentos s ON g.codigo_cliente = s.codigo_cliente 
                                AND g.codigo_estacao = s.codigo_estacao 
                                AND g.codigo_bloco = s.codigo_bloco 
                                AND g.codigo_sub_bloco = s.codigo_sub_bloco 
                                AND g.numero_gaveta = s.numero_gaveta
                                AND s.ativo = true
      GROUP BY c.codigo_cliente, c.nome_fantasia, p.codigo_estacao, p.denominacao, 
               b.codigo_bloco, b.denominacao, sb.codigo_sub_bloco, sb.denominacao
      HAVING COUNT(g.numero_gaveta) > 0
      ORDER BY c.codigo_cliente, p.codigo_estacao, b.codigo_bloco, sb.codigo_sub_bloco
      LIMIT 10
    `);
    
    console.log(`   Resultados encontrados: ${hierarchyResult.rows.length}`);
    hierarchyResult.rows.forEach(row => {
      console.log(`   📋 ${row.codigo_cliente}/${row.codigo_estacao}/${row.codigo_bloco}/${row.codigo_sub_bloco}`);
      console.log(`      Cliente: ${row.nome_fantasia}`);
      console.log(`      Produto: ${row.produto_nome}`);
      console.log(`      Bloco: ${row.bloco_nome}`);
      console.log(`      Sub-bloco: ${row.sub_bloco_nome}`);
      console.log(`      Gavetas: ${row.total_gavetas}, Sepultamentos: ${row.total_sepultamentos}`);
      console.log('');
    });
    
    // 2. Teste de busca por códigos específicos
    console.log('🔍 2. Busca por códigos específicos:');
    const specificResult = await client.query(`
      SELECT 
        g.codigo_cliente,
        g.codigo_estacao,
        g.codigo_bloco,
        g.codigo_sub_bloco,
        g.numero_gaveta,
        g.disponivel,
        s.nome_sepultado,
        s.data_sepultamento
      FROM gavetas g
      LEFT JOIN sepultamentos s ON g.codigo_cliente = s.codigo_cliente 
                                AND g.codigo_estacao = s.codigo_estacao 
                                AND g.codigo_bloco = s.codigo_bloco 
                                AND g.codigo_sub_bloco = s.codigo_sub_bloco 
                                AND g.numero_gaveta = s.numero_gaveta
                                AND s.ativo = true
      WHERE g.codigo_cliente = 'CLI001'
      ORDER BY g.codigo_estacao, g.codigo_bloco, g.codigo_sub_bloco, g.numero_gaveta
      LIMIT 10
    `);
    
    console.log(`   Gavetas encontradas para CLI001: ${specificResult.rows.length}`);
    specificResult.rows.forEach(row => {
      const status = row.nome_sepultado ? '🔴 Ocupada' : '🟢 Disponível';
      console.log(`   ${status} ${row.codigo_estacao}/${row.codigo_bloco}/${row.codigo_sub_bloco}/Gaveta-${row.numero_gaveta}`);
      if (row.nome_sepultado) {
        console.log(`      Sepultado: ${row.nome_sepultado} (${row.data_sepultamento})`);
      }
    });
    
    // 3. Teste de estatísticas por cliente
    console.log('\n📊 3. Estatísticas por cliente:');
    const statsResult = await client.query(`
      SELECT 
        c.codigo_cliente,
        c.nome_fantasia,
        COUNT(DISTINCT p.codigo_estacao) as total_produtos,
        COUNT(DISTINCT CONCAT(b.codigo_cliente, b.codigo_estacao, b.codigo_bloco)) as total_blocos,
        COUNT(DISTINCT CONCAT(sb.codigo_cliente, sb.codigo_estacao, sb.codigo_bloco, sb.codigo_sub_bloco)) as total_sub_blocos,
        COUNT(DISTINCT CONCAT(g.codigo_cliente, g.codigo_estacao, g.codigo_bloco, g.codigo_sub_bloco, g.numero_gaveta)) as total_gavetas,
        COUNT(s.id) as total_sepultamentos
      FROM clientes c
      LEFT JOIN produtos p ON c.codigo_cliente = p.codigo_cliente
      LEFT JOIN blocos b ON p.codigo_cliente = b.codigo_cliente AND p.codigo_estacao = b.codigo_estacao
      LEFT JOIN sub_blocos sb ON b.codigo_cliente = sb.codigo_cliente 
                              AND b.codigo_estacao = sb.codigo_estacao 
                              AND b.codigo_bloco = sb.codigo_bloco
      LEFT JOIN gavetas g ON sb.codigo_cliente = g.codigo_cliente 
                          AND sb.codigo_estacao = g.codigo_estacao 
                          AND sb.codigo_bloco = g.codigo_bloco 
                          AND sb.codigo_sub_bloco = g.codigo_sub_bloco
      LEFT JOIN sepultamentos s ON g.codigo_cliente = s.codigo_cliente 
                                AND g.codigo_estacao = s.codigo_estacao 
                                AND g.codigo_bloco = s.codigo_bloco 
                                AND g.codigo_sub_bloco = s.codigo_sub_bloco 
                                AND g.numero_gaveta = s.numero_gaveta
                                AND s.ativo = true
      GROUP BY c.codigo_cliente, c.nome_fantasia
      ORDER BY c.codigo_cliente
    `);
    
    statsResult.rows.forEach(row => {
      console.log(`   📋 ${row.codigo_cliente} - ${row.nome_fantasia}`);
      console.log(`      Produtos: ${row.total_produtos}`);
      console.log(`      Blocos: ${row.total_blocos}`);
      console.log(`      Sub-blocos: ${row.total_sub_blocos}`);
      console.log(`      Gavetas: ${row.total_gavetas}`);
      console.log(`      Sepultamentos: ${row.total_sepultamentos}`);
      console.log('');
    });
    
    // 4. Teste de importação simulada
    console.log('📥 4. Teste de importação simulada:');
    
    // Simular inserção de dados usando códigos hierárquicos
    try {
      await client.query('BEGIN');
      
      // Inserir cliente de teste
      await client.query(`
        INSERT INTO clientes (codigo_cliente, cnpj, nome_fantasia, razao_social)
        VALUES ('TEST_001', '99.999.999/0001-99', 'Cliente Teste Hierárquico', 'Cliente Teste Ltda')
        ON CONFLICT (codigo_cliente) DO NOTHING
      `);
      
      // Inserir produto de teste
      await client.query(`
        INSERT INTO produtos (codigo_cliente, codigo_estacao, denominacao, meses_para_exumar)
        VALUES ('TEST_001', 'EST_TEST', 'Estação de Teste', 24)
        ON CONFLICT (codigo_cliente, codigo_estacao) DO NOTHING
      `);
      
      // Inserir bloco usando referências hierárquicas
      await client.query(`
        INSERT INTO blocos (codigo_cliente, codigo_estacao, codigo_bloco, denominacao, produto_id)
        SELECT 'TEST_001', 'EST_TEST', 'BL_TEST', 'Bloco de Teste', p.id
        FROM produtos p 
        WHERE p.codigo_cliente = 'TEST_001' AND p.codigo_estacao = 'EST_TEST'
        ON CONFLICT (produto_id, codigo_bloco) DO NOTHING
      `);
      
      // Inserir sub-bloco usando referências hierárquicas
      await client.query(`
        INSERT INTO sub_blocos (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, denominacao, bloco_id)
        SELECT 'TEST_001', 'EST_TEST', 'BL_TEST', 'SB_TEST', 'Sub-bloco de Teste', b.id
        FROM blocos b 
        WHERE b.codigo_cliente = 'TEST_001' AND b.codigo_estacao = 'EST_TEST' AND b.codigo_bloco = 'BL_TEST'
        ON CONFLICT (bloco_id, codigo_sub_bloco) DO NOTHING
      `);
      
      // Inserir gaveta usando referências hierárquicas
      await client.query(`
        INSERT INTO gavetas (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta, posicao_x, posicao_y, sub_bloco_id)
        SELECT 'TEST_001', 'EST_TEST', 'BL_TEST', 'SB_TEST', 999, 1, 1, sb.id
        FROM sub_blocos sb 
        WHERE sb.codigo_cliente = 'TEST_001' AND sb.codigo_estacao = 'EST_TEST' AND sb.codigo_bloco = 'BL_TEST' AND sb.codigo_sub_bloco = 'SB_TEST'
        ON CONFLICT (sub_bloco_id, numero_gaveta) DO NOTHING
      `);
      
      await client.query('COMMIT');
      console.log('   ✅ Dados de teste inseridos com sucesso usando referências hierárquicas!');
      
      // Verificar dados inseridos
      const testResult = await client.query(`
        SELECT 
          g.codigo_cliente,
          g.codigo_estacao,
          g.codigo_bloco,
          g.codigo_sub_bloco,
          g.numero_gaveta
        FROM gavetas g
        WHERE g.codigo_cliente = 'TEST_001'
      `);
      
      console.log(`   📊 Gavetas de teste criadas: ${testResult.rows.length}`);
      testResult.rows.forEach(row => {
        console.log(`      ${row.codigo_cliente}/${row.codigo_estacao}/${row.codigo_bloco}/${row.codigo_sub_bloco}/Gaveta-${row.numero_gaveta}`);
      });
      
    } catch (error) {
      await client.query('ROLLBACK');
      console.log(`   ❌ Erro na importação simulada: ${error.message}`);
    }
    
    console.log('\n🎉 TESTES DE CONSULTAS HIERÁRQUICAS CONCLUÍDOS!');
    console.log('✅ Sistema funcionando corretamente com referências por códigos.');
    
  } catch (error) {
    console.error('❌ Erro nos testes:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Executar testes
if (require.main === module) {
  testHierarchicalQueries().catch(error => {
    console.error('❌ Erro fatal:', error);
    process.exit(1);
  });
}

module.exports = { testHierarchicalQueries };
