#!/bin/bash

# Script para corrigir problemas de duplo "Styled" e tags não correspondentes

echo "🔧 Corrigindo problemas de duplo Styled..."

# Lista de arquivos que podem ter problemas
files_to_check=(
    "portalcliente/src/components/UsuarioModal.jsx"
    "portalcliente/src/components/ClienteModal.jsx"
    "portalcliente/src/components/BlocoModal.jsx"
    "portalcliente/src/components/SubBlocoModal.jsx"
)

for file in "${files_to_check[@]}"; do
    if [ -f "$file" ]; then
        echo "Verificando $file..."
        
        # Remove duplo "Styled"
        sed -i 's/StyledStyled/Styled/g' "$file"
        
        # Corrige tags não correspondentes específicas
        sed -i 's/<StyledButtonGroup>/<StyledButtonGroup>/g' "$file"
        sed -i 's/<\/ButtonGroup>/<\/StyledButtonGroup>/g' "$file"
        sed -i 's/<StyledFormGroup>/<StyledFormGroup>/g' "$file"
        sed -i 's/<\/FormGroup>/<\/StyledFormGroup>/g' "$file"
        
        echo "  ✅ Problemas corrigidos em $file"
    else
        echo "  ⚠️  Arquivo $file não encontrado"
    fi
done

echo ""
echo "🎯 Verificação final..."

# Verifica se ainda há problemas
echo "Verificando duplo Styled..."
double_styled=$(find portalcliente/src -name "*.jsx" -o -name "*.js" | xargs grep -l "StyledStyled" 2>/dev/null || true)

if [ -z "$double_styled" ]; then
    echo "✅ Nenhum duplo Styled encontrado!"
else
    echo "❌ Ainda há duplo Styled em:"
    echo "$double_styled"
fi

echo ""
echo "🚀 Correção de duplo Styled concluída!"
