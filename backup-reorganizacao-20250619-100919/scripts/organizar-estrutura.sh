#!/bin/bash

# ===================================
# SCRIPT PARA ORGANIZAR ESTRUTURA
# ===================================
# Execute: bash scripts/organizar-estrutura.sh
# Organiza projeto seguindo padrão da VPS

echo "📁 Organizando estrutura do Portal Evolution..."

# Cores para output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
    exit 1
}

# Criar estrutura de pastas se não existir
log "Criando estrutura de pastas..."

mkdir -p app
mkdir -p dados/{postgres,backups,uploads,logs}

# Mover código para pasta app (se ainda não estiver)
if [[ ! -d "app/src" && -d "src" ]]; then
    log "Movendo código frontend para app/..."
    mv src app/
    mv public app/ 2>/dev/null || true
    mv package.json app/ 2>/dev/null || true
    mv package-lock.json app/ 2>/dev/null || true
    mv index.html app/ 2>/dev/null || true
    mv vite.config.js app/ 2>/dev/null || true
fi

if [[ ! -d "app/server" && -d "server" ]]; then
    log "Movendo código backend para app/..."
    mv server app/
fi

# Criar links simbólicos para manter compatibilidade
if [[ -d "app/src" && ! -d "src" ]]; then
    log "Criando link simbólico para src..."
    ln -sf app/src src
fi

if [[ -d "app/server" && ! -d "server" ]]; then
    log "Criando link simbólico para server..."
    ln -sf app/server server
fi

if [[ -f "app/package.json" && ! -f "package.json" ]]; then
    log "Criando link simbólico para package.json..."
    ln -sf app/package.json package.json
fi

# Tornar scripts executáveis
log "Tornando scripts executáveis..."
chmod +x scripts/*.sh

# Criar .gitignore se não existir
if [[ ! -f ".gitignore" ]]; then
    log "Criando .gitignore..."
    cat > .gitignore << 'EOF'
# Dependências
node_modules/
app/node_modules/
app/server/node_modules/

# Builds
dist/
app/dist/
build/

# Logs
logs/
*.log
dados/logs/

# Dados sensíveis
.env.local
.env.production

# Dados persistentes
dados/postgres/
dados/backups/
dados/uploads/

# Arquivos temporários
.tmp/
.temp/

# IDE
.vscode/
.idea/

# Sistema
.DS_Store
Thumbs.db

# Docker
.dockerignore
EOF
fi

log "✅ Estrutura organizada com sucesso!"
log ""
log "📋 Estrutura atual:"
log "   app/          - Código da aplicação"
log "   docker/       - Dockerfiles"
log "   scripts/      - Scripts de automação"
log "   dados/        - Dados persistentes"
log "   .env          - Configurações"
log ""
log "🔗 Links simbólicos criados para compatibilidade:"
if [[ -L "src" ]]; then
    log "   src -> app/src"
fi
if [[ -L "server" ]]; then
    log "   server -> app/server"
fi
if [[ -L "package.json" ]]; then
    log "   package.json -> app/package.json"
fi

log ""
log "🎯 Próximos passos:"
log "1. bash scripts/validar-estrutura.sh"
log "2. nano .env (configure suas variáveis)"
log "3. bash scripts/iniciar-servico.sh"
