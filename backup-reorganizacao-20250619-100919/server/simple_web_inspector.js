const express = require('express');
const { Pool } = require('pg');

const app = express();
const port = 3002;

// Configuração do banco de dados
const pool = new Pool({
  user: 'postgres',
  host: '************',
  database: 'dbetens',
  password: 'ab3780bd73ee4e2804d566ce6fd96209',
  port: 5432,
});

// Rota principal
app.get('/', async (req, res) => {
  try {
    const client = await pool.connect();
    
    // Buscar todas as tabelas
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    
    let html = `
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inspetor de Banco - dbetens</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 10px;
        }
        .table-section {
            margin: 30px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }
        .table-header {
            background: #007bff;
            color: white;
            padding: 15px;
            font-weight: bold;
            font-size: 18px;
        }
        .table-content {
            padding: 15px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .hierarchical {
            background-color: #d4edda !important;
            font-weight: bold;
            color: #155724;
        }
        .summary {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .info-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Inspetor de Banco de Dados</h1>
        <div class="info-box">
            <strong>Banco:</strong> dbetens @ ************:5432<br>
            <strong>Total de tabelas:</strong> ${tablesResult.rows.length}<br>
            <strong>Data/Hora:</strong> ${new Date().toLocaleString('pt-BR')}
        </div>
        
        <div class="summary">
            <h3>📋 Colunas Hierárquicas Esperadas:</h3>
            <ul>
                <li><strong>blocos:</strong> codigo_cliente, codigo_estacao, codigo_bloco</li>
                <li><strong>sub_blocos:</strong> codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco</li>
                <li><strong>gavetas:</strong> codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco</li>
                <li><strong>numeracoes_gavetas:</strong> codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco</li>
                <li><strong>sepultamentos:</strong> codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco</li>
                <li><strong>logs_auditoria:</strong> codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco</li>
            </ul>
            <p><strong>Legenda:</strong> Colunas hierárquicas aparecem destacadas em <span style="background-color: #d4edda; padding: 2px 4px; border-radius: 3px;">verde</span></p>
        </div>
    `;
    
    const hierarchicalColumns = {
      'blocos': ['codigo_cliente', 'codigo_estacao', 'codigo_bloco'],
      'sub_blocos': ['codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco'],
      'gavetas': ['codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco'],
      'numeracoes_gavetas': ['codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco'],
      'sepultamentos': ['codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco'],
      'logs_auditoria': ['codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco']
    };
    
    // Para cada tabela, buscar suas colunas
    for (const tableRow of tablesResult.rows) {
      const tableName = tableRow.table_name;
      
      const columnsResult = await client.query(`
        SELECT 
          column_name,
          data_type,
          is_nullable,
          column_default,
          character_maximum_length
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = $1
        ORDER BY ordinal_position
      `, [tableName]);
      
      const expectedHierarchical = hierarchicalColumns[tableName] || [];
      const foundHierarchical = columnsResult.rows.filter(col => 
        expectedHierarchical.includes(col.column_name)
      );
      const missingHierarchical = expectedHierarchical.filter(exp => 
        !foundHierarchical.some(found => found.column_name === exp)
      );
      
      const isImportantTable = expectedHierarchical.length > 0;
      const isComplete = missingHierarchical.length === 0 && expectedHierarchical.length > 0;
      
      html += `
        <div class="table-section">
          <div class="table-header">
            📋 ${tableName.toUpperCase()} 
            (${columnsResult.rows.length} colunas)
            ${isImportantTable ? (isComplete ? ' ✅ COMPLETA' : ' ❌ INCOMPLETA') : ''}
          </div>
          <div class="table-content">
      `;
      
      if (isImportantTable) {
        html += `
          <p><strong>Colunas hierárquicas:</strong> 
             ${foundHierarchical.length}/${expectedHierarchical.length} encontradas</p>
        `;
        
        if (missingHierarchical.length > 0) {
          html += `
            <p style="color: #dc3545;"><strong>Faltando:</strong> ${missingHierarchical.join(', ')}</p>
          `;
        }
      }
      
      html += `
            <table>
              <thead>
                <tr>
                  <th>Nome da Coluna</th>
                  <th>Tipo de Dados</th>
                  <th>Permite NULL</th>
                  <th>Valor Padrão</th>
                  <th>Tamanho Máx.</th>
                </tr>
              </thead>
              <tbody>
      `;
      
      columnsResult.rows.forEach(col => {
        const isHierarchical = expectedHierarchical.includes(col.column_name);
        const rowClass = isHierarchical ? 'hierarchical' : '';
        
        html += `
          <tr class="${rowClass}">
            <td><strong>${col.column_name}</strong>${isHierarchical ? ' 🔹' : ''}</td>
            <td>${col.data_type}</td>
            <td>${col.is_nullable === 'YES' ? 'Sim' : 'Não'}</td>
            <td>${col.column_default || '-'}</td>
            <td>${col.character_maximum_length || '-'}</td>
          </tr>
        `;
      });
      
      html += `
              </tbody>
            </table>
          </div>
        </div>
      `;
    }
    
    html += `
        <div class="summary">
          <h3>📊 Resumo Final</h3>
          <p>Verificação concluída em ${new Date().toLocaleString('pt-BR')}</p>
          <p>Todas as tabelas e suas colunas foram listadas acima.</p>
          <p>Colunas hierárquicas estão destacadas em verde com o símbolo 🔹</p>
        </div>
      </div>
    </body>
    </html>
    `;
    
    client.release();
    res.send(html);
    
  } catch (error) {
    console.error('Erro:', error);
    res.status(500).send(`
      <h1>Erro ao conectar com o banco</h1>
      <p>Erro: ${error.message}</p>
      <p>Verifique se o banco PostgreSQL está acessível.</p>
    `);
  }
});

// Iniciar servidor
app.listen(port, () => {
  console.log(`🌐 Servidor web rodando em http://localhost:${port}`);
  console.log(`📋 Mostrando todas as colunas do banco dbetens`);
  console.log(`🔗 Acesse: http://localhost:${port}`);
});

module.exports = app;
