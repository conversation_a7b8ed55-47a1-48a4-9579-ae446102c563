# ===================================
# CONFIGURAÇÃO DO PORTAL EVOLUTION
# ===================================
# Configuração para portal.evo-eden.site

# ===================================
# DADOS DA VPS
# ===================================
VPS_HOST=************
VPS_USER=vscode
VPS_PASSWORD=nbr5410!
VPS_PORT=22
VPS_PROJECT_PATH=/home/<USER>/portalevo

# ===================================
# DOMÍNIO E SSL
# ===================================
DOMAIN=portal.evo-eden.site
SSL_EMAIL=<EMAIL>
ENABLE_SSL=true

# ===================================
# BANCO DE DADOS (PostgreSQL Existente)
# ===================================
DB_HOST=postgres_postgres
DB_PORT=5432
DB_NAME=dbetens
DB_USER=postgres
DB_PASSWORD=ab3780bd73ee4e2804d566ce6fd96209

# ===================================
# APLICAÇÃO
# ===================================
NODE_ENV=production
PORT=3001
JWT_SECRET=portal_jwt_secret_key_muito_segura_2025

# ===================================
# EMAIL (OPCIONAL)
# ===================================
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=jgvhevmyjpuucbhp

# ===================================
# CONFIGURAÇÕES AVANÇADAS
# ===================================
# Número de réplicas dos serviços
FRONTEND_REPLICAS=1
BACKEND_REPLICAS=1

# Recursos dos containers
FRONTEND_MEMORY=256m
BACKEND_MEMORY=512m
DATABASE_MEMORY=1g

# ===================================
# BACKUP
# ===================================
BACKUP_ENABLED=true
BACKUP_RETENTION_DAYS=7
