# Portal Evolution - Inicializador
Write-Host "Iniciando Portal Evolution..." -ForegroundColor Green

# Definir diretório do script
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $scriptPath
Write-Host "Diretorio: $scriptPath" -ForegroundColor Gray

# Verificar Node.js
try {
    $nodeVersion = node --version
    Write-Host "Node.js: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "Node.js nao encontrado! Instale primeiro." -ForegroundColor Red
    exit 1
}

# Verificar diretorios
if (-not (Test-Path ".\server\index.js")) {
    Write-Host "Arquivo backend nao encontrado!" -ForegroundColor Red
    Write-Host "Caminho procurado: $(Join-Path (Get-Location) 'server\index.js')" -ForegroundColor Yellow
    exit 1
}

if (-not (Test-Path "package.json")) {
    Write-Host "package.json nao encontrado!" -ForegroundColor Red
    exit 1
}

Write-Host "Preparando ambiente..." -ForegroundColor Yellow

# Finalizar processos existentes
try {
    Get-Process | Where-Object {$_.ProcessName -eq "node"} | Stop-Process -Force -ErrorAction SilentlyContinue
    Start-Sleep -Seconds 2
} catch {}

Write-Host "Iniciando Backend..." -ForegroundColor Yellow
$backendPath = Join-Path $scriptPath "server"
Write-Host "Caminho do backend: $backendPath" -ForegroundColor Gray

# Iniciar backend em nova janela do PowerShell
$backendScript = @"
Set-Location '$backendPath'
Write-Host 'Iniciando servidor backend...' -ForegroundColor Green
node index.js
Write-Host 'Backend finalizado.' -ForegroundColor Red
pause
"@

$backend = Start-Process powershell -ArgumentList "-NoExit", "-Command", $backendScript -PassThru

Write-Host "Backend iniciado (PID: $($backend.Id))" -ForegroundColor Green
Start-Sleep -Seconds 8

Write-Host "Iniciando Frontend..." -ForegroundColor Yellow

# Iniciar frontend em nova janela do PowerShell
$frontendScript = @"
Set-Location '$scriptPath'
Write-Host 'Iniciando servidor frontend...' -ForegroundColor Green
npx vite --host 0.0.0.0 --port 5173
Write-Host 'Frontend finalizado.' -ForegroundColor Red
pause
"@

$frontend = Start-Process powershell -ArgumentList "-NoExit", "-Command", $frontendScript -PassThru

Write-Host "Frontend iniciado (PID: $($frontend.Id))" -ForegroundColor Green
Start-Sleep -Seconds 10

Write-Host ""
Write-Host "Aguardando servicos iniciarem..." -ForegroundColor Yellow

# Função para testar conectividade
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    } catch {
        return $false
    }
}

# Aguardar backend
Write-Host "Testando backend (porta 3001)..." -ForegroundColor Yellow
$backendReady = $false
for ($i = 1; $i -le 30; $i++) {
    if (Test-Port 3001) {
        Write-Host "✅ Backend respondendo!" -ForegroundColor Green
        $backendReady = $true
        break
    }
    Write-Host "   Tentativa $i/30..." -ForegroundColor Gray
    Start-Sleep -Seconds 2
}

if (-not $backendReady) {
    Write-Host "⚠️ Backend nao respondeu em 60 segundos" -ForegroundColor Yellow
}

# Aguardar frontend
Write-Host "Testando frontend (porta 5173)..." -ForegroundColor Yellow
$frontendReady = $false
for ($i = 1; $i -le 30; $i++) {
    if (Test-Port 5173) {
        Write-Host "✅ Frontend respondendo!" -ForegroundColor Green
        $frontendReady = $true
        break
    }
    Write-Host "   Tentativa $i/30..." -ForegroundColor Gray
    Start-Sleep -Seconds 2
}

if (-not $frontendReady) {
    Write-Host "⚠️ Frontend nao respondeu em 60 segundos" -ForegroundColor Yellow
}

Write-Host ""
if ($backendReady -and $frontendReady) {
    Write-Host "🎉 PORTAL EVOLUTION INICIADO COM SUCESSO!" -ForegroundColor Green
} else {
    Write-Host "⚠️ PORTAL EVOLUTION INICIADO COM PROBLEMAS" -ForegroundColor Yellow
    Write-Host "Verifique as janelas do backend e frontend para erros" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "📋 INFORMACOES DE ACESSO:" -ForegroundColor Cyan
Write-Host "   Aplicacao: http://localhost:5173" -ForegroundColor White
Write-Host "   Backend: http://localhost:3001" -ForegroundColor White
Write-Host ""
Write-Host "🔑 CREDENCIAIS:" -ForegroundColor Cyan
Write-Host "   Admin: admin / adminnbr5410!" -ForegroundColor White
Write-Host "   Cliente: <EMAIL> / cliente123" -ForegroundColor White
Write-Host ""

# Abrir navegador apenas se frontend estiver funcionando
if ($frontendReady) {
    Write-Host "🌐 Abrindo navegador..." -ForegroundColor Yellow
    try {
        Start-Process "http://localhost:5173"
        Write-Host "✅ Navegador aberto!" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ Erro ao abrir navegador" -ForegroundColor Yellow
        Write-Host "   Acesse manualmente: http://localhost:5173" -ForegroundColor Cyan
    }
} else {
    Write-Host "❌ Navegador nao aberto - frontend nao esta respondendo" -ForegroundColor Red
    Write-Host "   Verifique a janela do frontend para erros" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Pressione qualquer tecla para finalizar..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

Write-Host "Finalizando servicos..." -ForegroundColor Yellow

try {
    if ($backend -and -not $backend.HasExited) {
        $backend.Kill()
    }
} catch {}

try {
    if ($frontend -and -not $frontend.HasExited) {
        $frontend.Kill()
    }
} catch {}

try {
    Get-Process | Where-Object {$_.ProcessName -eq "node"} | Stop-Process -Force -ErrorAction SilentlyContinue
} catch {}

Write-Host "Portal Evolution finalizado!" -ForegroundColor Green
