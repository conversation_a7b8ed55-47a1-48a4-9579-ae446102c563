import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import Modal from './Modal';
import { usuarioService, clienteService } from '../services/api';

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 6px;
`;

const Label = styled.label`
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
`;

const Input = styled.input`
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: #059669;
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
  }

  &:disabled {
    background: #f9fafb;
    color: #6b7280;
  }
`;

const Select = styled.select`
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: #059669;
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
  }
`;

const CheckboxGroup = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
  margin-top: 8px;
`;

const CheckboxItem = styled.label`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #f9fafb;
    border-color: #059669;
  }

  input[type="checkbox"] {
    margin: 0;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
`;

const Button = styled.button`
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;

  &.primary {
    background: linear-gradient(135deg, #1e3a8a 0%, #059669 100%);
    color: white;

    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }
  }

  &.secondary {
    background: white;
    color: #374151;
    border-color: #d1d5db;

    &:hover {
      background: #f9fafb;
    }
  }
`;

const ErrorMessage = styled.div`
  color: #dc2626;
  font-size: 0.875rem;
  margin-top: 4px;
`;

const UsuarioModal = ({ isOpen, onClose, usuario = null, onSuccess }) => {
  const [formData, setFormData] = useState({
    nome: '',
    email: '',
    senha: '',
    tipo_usuario: 'cliente',
    codigo_cliente: '',
    ativo: true
  });
  const [clientes, setClientes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (isOpen) {
      loadClientes();
      if (usuario) {
        setFormData({
          nome: usuario.nome || '',
          email: usuario.email || '',
          senha: '',
          tipo_usuario: usuario.tipo_usuario || 'cliente',
          codigo_cliente: usuario.codigo_cliente || '',
          ativo: usuario.ativo !== undefined ? usuario.ativo : true
        });
      } else {
        setFormData({
          nome: '',
          email: '',
          senha: '',
          tipo_usuario: 'cliente',
          codigo_cliente: '',
          ativo: true
        });
      }
      setError('');
    }
  }, [isOpen, usuario]);

  const loadClientes = async () => {
    try {
      const response = await clienteService.listar();
      setClientes(response.data);
    } catch (error) {
      console.error('Erro ao carregar clientes:', error);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const dataToSend = { ...formData };

      // Validações básicas
      if (!dataToSend.nome.trim()) {
        setError('Nome é obrigatório');
        return;
      }
      if (!dataToSend.email.trim()) {
        setError('Email é obrigatório');
        return;
      }
      if (!usuario && !dataToSend.senha.trim()) {
        setError('Senha é obrigatória para novos usuários');
        return;
      }
      if (dataToSend.tipo_usuario === 'cliente' && !dataToSend.codigo_cliente) {
        setError('Cliente é obrigatório para usuários do tipo cliente');
        return;
      }

      // Se não há senha preenchida na edição, remover do payload
      if (usuario && !dataToSend.senha.trim()) {
        console.log('🔐 Senha vazia na edição, removendo do payload');
        delete dataToSend.senha;
      } else if (usuario && dataToSend.senha.trim()) {
        console.log('🔐 Nova senha fornecida para edição:', dataToSend.senha);
        console.log('🔐 Tamanho da senha:', dataToSend.senha.length);
      } else if (!usuario) {
        console.log('🔐 Criando novo usuário com senha:', dataToSend.senha);
      }

      // Garantir que ativo seja boolean
      dataToSend.ativo = Boolean(dataToSend.ativo);

      console.log('📤 Dados sendo enviados:', {
        ...dataToSend,
        senha: dataToSend.senha ? `*** (${dataToSend.senha.length} caracteres)` : 'não enviada'
      });

      console.log('🔍 Tipo de operação:', usuario ? 'EDIÇÃO' : 'CRIAÇÃO');

      if (usuario) {
        console.log('🔄 Atualizando usuário:', usuario.id);
        await usuarioService.atualizar(usuario.id, dataToSend);
        alert('Usuário atualizado com sucesso!');
      } else {
        console.log('➕ Criando novo usuário');
        const response = await usuarioService.criar(dataToSend);
        console.log('✅ Usuário criado:', response.data);
        alert('Usuário criado com sucesso!');
      }

      onSuccess();
      onClose();
    } catch (error) {
      console.error('❌ Erro ao salvar usuário:', error);
      console.error('📋 Detalhes do erro:', error.response?.data);
      setError(error.response?.data?.error || 'Erro ao salvar usuário');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={usuario ? 'Editar Usuário' : 'Novo Usuário'}
      maxWidth="700px"
    >
      <Form onSubmit={handleSubmit}>
        <FormGroup>
          <Label>Nome Completo *</Label>
          <Input
            type="text"
            name="nome"
            value={formData.nome}
            onChange={handleChange}
            required
            placeholder="Ex: João Silva"
          />
        </FormGroup>

        <FormGroup>
          <Label>Email *</Label>
          <Input
            type="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            required
            placeholder="Ex: <EMAIL>"
            disabled={!!usuario}
          />
        </FormGroup>

        <FormGroup>
          <Label>Senha {usuario ? '(deixe em branco para manter atual)' : '*'}</Label>
          <Input
            type="password"
            name="senha"
            value={formData.senha}
            onChange={handleChange}
            required={!usuario}
            placeholder={usuario ? 'Nova senha (opcional)' : 'Senha do usuário'}
          />
        </FormGroup>

        <FormGroup>
          <Label>Tipo de Usuário *</Label>
          <Select
            name="tipo_usuario"
            value={formData.tipo_usuario}
            onChange={handleChange}
            required
          >
            <option value="cliente">Cliente</option>
            <option value="admin">Administrador</option>
          </Select>
        </FormGroup>

        {formData.tipo_usuario === 'cliente' && (
          <FormGroup>
            <Label>Cliente *</Label>
            <Select
              name="codigo_cliente"
              value={formData.codigo_cliente}
              onChange={handleChange}
              required
            >
              <option value="">Selecione um cliente</option>
              {clientes.map(cliente => (
                <option key={cliente.codigo_cliente} value={cliente.codigo_cliente}>
                  {cliente.nome_fantasia} ({cliente.codigo_cliente})
                </option>
              ))}
            </Select>
          </FormGroup>
        )}



        {error && <ErrorMessage>{error}</ErrorMessage>}

        <ButtonGroup>
          <Button type="button" className="secondary" onClick={onClose}>
            Cancelar
          </Button>
          <Button type="submit" className="primary" disabled={loading}>
            {loading ? 'Salvando...' : (usuario ? 'Atualizar' : 'Criar')}
          </Button>
        </ButtonGroup>
      </Form>
    </Modal>
  );
};

export default UsuarioModal;
