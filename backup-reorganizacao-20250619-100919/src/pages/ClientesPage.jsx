import React, { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Box,
  Typography,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Visibility as VisibilityIcon,
  ToggleOn as ToggleOnIcon,
  ToggleOff as ToggleOffIcon,
} from '@mui/icons-material';
import { clienteService } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import Modal from '../components/Modal';
import ClienteModal from '../components/ClienteModal';
import { StandardContainer, StandardButton, StandardCard } from '../components/common';

// Todos os styled-components removidos - usando Material-UI

const ClientesPage = () => {
  const [clientes, setClientes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedCliente, setSelectedCliente] = useState(null);
  const [showClienteModal, setShowClienteModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const { canAccessClientes } = useAuth();

  useEffect(() => {
    if (canAccessClientes()) {
      loadClientes();
    }
  }, [canAccessClientes]);

  const loadClientes = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await clienteService.listar();
      setClientes(response.data);
    } catch (error) {
      console.error('Erro ao carregar clientes:', error);
      setError('Erro ao carregar clientes');
    } finally {
      setLoading(false);
    }
  };

  const handleAddCliente = () => {
    setSelectedCliente(null);
    setShowClienteModal(true);
  };

  const handleEditCliente = (cliente) => {
    setSelectedCliente(cliente);
    setShowClienteModal(true);
  };

  const handleViewDetails = (cliente) => {
    setSelectedCliente(cliente);
    setShowDetailModal(true);
  };

  const handleToggleStatus = async (cliente) => {
    const action = cliente.ativo ? 'inativar' : 'ativar';
    if (window.confirm(`Tem certeza que deseja ${action} o cliente "${cliente.nome_fantasia}"?`)) {
      try {
        await clienteService.atualizar(cliente.codigo_cliente, { 
          ...cliente, 
          ativo: !cliente.ativo 
        });
        loadClientes();
      } catch (error) {
        console.error('Erro ao alterar status do cliente:', error);
        alert('Erro ao alterar status do cliente');
      }
    }
  };

  if (!canAccessClientes()) {
    return (
      <StandardContainer title="Gestão de Clientes" subtitle="Gerenciamento de clientes">
        <Alert severity="error" sx={{ textAlign: 'center' }}>
          Acesso negado. Apenas administradores podem acessar esta página.
        </Alert>
      </StandardContainer>
    );
  }

  if (loading) {
    return (
      <StandardContainer title="Gestão de Clientes" subtitle="Gerenciamento de clientes">
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
          <CircularProgress size={40} />
        </Box>
      </StandardContainer>
    );
  }

  if (error) {
    return (
      <StandardContainer title="Gestão de Clientes" subtitle="Gerenciamento de clientes">
        <Alert severity="error" sx={{ textAlign: 'center' }}>
          {error}
        </Alert>
      </StandardContainer>
    );
  }

  return (
    <StandardContainer
      title="Gestão de Clientes"
      subtitle="Gerenciamento de clientes"
      headerAction={
        <StandardButton
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddCliente}
        >
          Novo Cliente
        </StandardButton>
      }
    >
      {clientes.length === 0 ? (
        <StandardCard sx={{ textAlign: 'center', py: 8 }}>
          <Typography variant="h1" sx={{ fontSize: '3rem', mb: 2 }}>👥</Typography>
          <Typography variant="h5" sx={{ mb: 1, color: 'text.primary' }}>
            Nenhum cliente encontrado
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Comece criando o primeiro cliente.
          </Typography>
        </StandardCard>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Código</TableCell>
                <TableCell>CNPJ</TableCell>
                <TableCell>Nome Fantasia</TableCell>
                <TableCell>Cidade</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Ações</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {clientes.map((cliente) => (
                <TableRow
                  key={cliente.codigo_cliente}
                  hover
                  sx={{ cursor: 'pointer' }}
                  onClick={() => handleViewDetails(cliente)}
                >
                  <TableCell>{cliente.codigo_cliente}</TableCell>
                  <TableCell>{cliente.cnpj}</TableCell>
                  <TableCell>{cliente.nome_fantasia}</TableCell>
                  <TableCell>{cliente.cidade}</TableCell>
                  <TableCell>
                    <Chip
                      label={cliente.ativo ? 'Ativo' : 'Inativo'}
                      color={cliente.ativo ? 'success' : 'error'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <StandardButton
                        variant="outlined"
                        color="warning"
                        size="small"
                        startIcon={<EditIcon />}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditCliente(cliente);
                        }}
                      >
                        Editar
                      </StandardButton>
                      <StandardButton
                        variant="outlined"
                        color={cliente.ativo ? 'error' : 'success'}
                        size="small"
                        startIcon={cliente.ativo ? <ToggleOffIcon /> : <ToggleOnIcon />}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleToggleStatus(cliente);
                        }}
                      >
                        {cliente.ativo ? 'Inativar' : 'Ativar'}
                      </StandardButton>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Modal de Cliente */}
      <ClienteModal
        isOpen={showClienteModal}
        onClose={() => setShowClienteModal(false)}
        cliente={selectedCliente}
        onSuccess={loadClientes}
      />

      {/* Modal de Detalhes */}
      <Modal
        isOpen={showDetailModal}
        onClose={() => setShowDetailModal(false)}
        title={`Detalhes: ${selectedCliente?.nome_fantasia}`}
        maxWidth="800px"
      >
        {selectedCliente && (
          <Box sx={{ p: 2 }}>
            <Typography variant="body1" sx={{ mb: 1 }}>
              <strong>Código:</strong> {selectedCliente.codigo_cliente}
            </Typography>
            <Typography variant="body1" sx={{ mb: 1 }}>
              <strong>CNPJ:</strong> {selectedCliente.cnpj}
            </Typography>
            <Typography variant="body1" sx={{ mb: 1 }}>
              <strong>Nome Fantasia:</strong> {selectedCliente.nome_fantasia}
            </Typography>
            <Typography variant="body1" sx={{ mb: 1 }}>
              <strong>Razão Social:</strong> {selectedCliente.razao_social}
            </Typography>
            <Typography variant="body1" sx={{ mb: 1 }}>
              <strong>Endereço:</strong> {selectedCliente.logradouro}, {selectedCliente.numero}
            </Typography>
            <Typography variant="body1" sx={{ mb: 1 }}>
              <strong>Cidade:</strong> {selectedCliente.cidade} - {selectedCliente.estado}
            </Typography>
            <Typography variant="body1" sx={{ mb: 1 }}>
              <strong>Status:</strong>
              <Chip
                label={selectedCliente.ativo ? 'Ativo' : 'Inativo'}
                color={selectedCliente.ativo ? 'success' : 'error'}
                size="small"
                sx={{ ml: 1 }}
              />
            </Typography>
          </Box>
        )}
      </Modal>
    </StandardContainer>
  );
};

export default ClientesPage;
