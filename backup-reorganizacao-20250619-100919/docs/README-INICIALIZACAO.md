# 🚀 Portal Evolution - Guia de Inicialização

## 📋 **Como Iniciar o Sistema**

### **Método 1: Script Automático (Recomendado)**

Execute o comando no PowerShell:

```powershell
.\start-portal.ps1
```

**OU** com caminho completo:

```powershell
powershell -ExecutionPolicy Bypass -File "C:\Users\<USER>\Documents\portalevo\portal-evolution\start-portal.ps1"
```

### **O que o script faz:**

1. ✅ **Verifica dependências** (Node.js, NPM)
2. ✅ **Finaliza processos** anteriores nas portas 3001 e 5173
3. ✅ **Inicia o Backend** (porta 3001)
4. ✅ **Inicia o Frontend** (porta 5173)
5. ✅ **Abre o navegador** automaticamente
6. ✅ **Exibe credenciais** de acesso

---

## 🌐 **Informações de Acesso**

### **URLs:**
- **Aplicação:** http://localhost:5173
- **API Backend:** http://localhost:3001
- **Health Check:** http://localhost:3001/api/health

### **Credenciais de Teste:**
- **👤 Admin:** `admin` / `adminnbr5410!`
- **👤 Cliente:** `<EMAIL>` / `cliente123`

---

## 🔧 **Funcionalidades por Usuário**

### **👨‍💼 ADMIN (admin)**
- ✅ **Dashboard Geral** - Visualizar todos os produtos
- ✅ **Cadastros** - Gerenciar sepultamentos
- ✅ **Clientes** - CRUD completo de clientes
- ✅ **Cadastro de Produtos** - CRUD completo de produtos
- ✅ **Usuários** - Gerenciar usuários do sistema
- ✅ **Logs** - Visualizar logs do sistema

### **👤 CLIENTE (<EMAIL>)**
- ✅ **Dashboard Geral** - Visualizar produtos (somente leitura)
- ✅ **Cadastros** - Gerenciar sepultamentos
- ❌ Sem acesso a clientes, produtos, usuários ou logs

---

## 🛠️ **Correções Implementadas**

### **✅ Aba Clientes**
- **CORRIGIDO:** Cliente não fica mais inativo ao editar
- **CORRIGIDO:** Status ativo/inativo preservado durante edição

### **✅ Aba Usuários**
- **CORRIGIDO:** Usuário não é mais apagado ao editar
- **CORRIGIDO:** Confirmação dupla para deletar usuários
- **CORRIGIDO:** Removida seção "Produtos com Acesso"

### **✅ Aba Cadastros (Sepultamentos)**
- **CORRIGIDO:** Removidos campos "Data do Óbito" e "Data de Nascimento"
- **CORRIGIDO:** Adicionado campo "Horário do Sepultamento" (obrigatório)
- **CORRIGIDO:** Removida seção "Responsável"
- **CORRIGIDO:** Mantido apenas campo "Observações"
- **CORRIGIDO:** Gavetas aparecem corretamente na seleção
- **CORRIGIDO:** Interface mostra denominações em vez de códigos

### **✅ Aba Produtos**
- **CORRIGIDO:** Cliente pode consultar produtos (somente leitura)
- **CORRIGIDO:** Botão "Ver Sepultados" para listar sepultamentos
- **CORRIGIDO:** Admin tem aba específica "Cadastro de Produtos"
- **CORRIGIDO:** Admin não vê mais tela branca

---

## 🗄️ **Dados de Teste Disponíveis**

- **400 gavetas** ativas (391 disponíveis, 9 ocupadas)
- **4 produtos** cadastrados
- **10 sepultamentos** de teste
- **Estrutura hierárquica** completa (Cliente → Produto → Bloco → Sub-bloco → Gavetas)

---

## 🚨 **Solução de Problemas**

### **Problema: "Node.js não encontrado"**
**Solução:** Instale o Node.js em https://nodejs.org/

### **Problema: "Arquivo backend não encontrado"**
**Solução:** Certifique-se de estar no diretório correto do projeto

### **Problema: "Porta já em uso"**
**Solução:** O script automaticamente finaliza processos anteriores

### **Problema: "Serviços não respondem"**
**Solução:** Aguarde alguns segundos - os serviços podem levar tempo para inicializar

---

## 🔄 **Como Parar o Sistema**

1. **No terminal do script:** Pressione qualquer tecla
2. **Forçar parada:** Feche o terminal ou use Ctrl+C
3. **Manual:** Finalize os processos Node.js no Gerenciador de Tarefas

---

## 📞 **Suporte**

Se encontrar problemas:

1. Verifique se o Node.js está instalado
2. Certifique-se de estar no diretório correto
3. Execute o script como administrador se necessário
4. Verifique se as portas 3001 e 5173 estão livres

---

## 🎯 **Sistema Totalmente Funcional**

✅ **Todas as correções solicitadas foram implementadas**  
✅ **Sistema testado e funcionando**  
✅ **Script de inicialização automática criado**  
✅ **Dados de teste carregados**  
✅ **Interface responsiva e amigável**

**🎉 O Portal Evolution está pronto para uso!**
