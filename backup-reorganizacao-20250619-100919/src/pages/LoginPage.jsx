import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  TextField,
  Typography,
  Alert,
  Container,
  InputAdornment,
  IconButton,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Person as PersonIcon,
  Lock as LockIcon,
  Email as EmailIcon,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { StandardCard, StandardButton } from '../components/common';
import { authService } from '../services/api';
import logoComFundo from '../assets/logo_com_fundo_branco.png';

const LoginPage = () => {
  const [email, setEmail] = useState('');
  const [senha, setSenha] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [forgotEmail, setForgotEmail] = useState('');
  const [forgotLoading, setForgotLoading] = useState(false);
  const [forgotMessage, setForgotMessage] = useState('');
  const { login, user } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (user) {
      // Limpar cache e forçar redirecionamento
      window.history.replaceState(null, '', '/dashboard');
      navigate('/dashboard', { replace: true });
    }
  }, [user, navigate]);

  // Monitorar mudanças no estado de erro (removido para evitar loops)

  const handleSubmit = async (e) => {
    e.preventDefault();

    setError('');
    setLoading(true);

    try {
      const result = await login(email, senha);

      if (result && result.success) {
        // Limpar cache e forçar redirecionamento imediato
        window.history.replaceState(null, '', '/dashboard');
        navigate('/dashboard', { replace: true });
      } else {
        const errorMsg = 'Email ou senha incorretos. Por favor, verifique suas credenciais e tente novamente.';
        setError(errorMsg);
      }
    } catch (error) {
      const errorMsg = 'Email ou senha incorretos. Por favor, verifique suas credenciais e tente novamente.';
      setError(errorMsg);
    } finally {
      setLoading(false);
    }
  };

  // Limpar erro quando usuário começar a digitar
  const handleEmailChange = (e) => {
    setEmail(e.target.value);
    if (error) {
      setError('');
    }
  };

  const handleSenhaChange = (e) => {
    setSenha(e.target.value);
    if (error) {
      setError('');
    }
  };

  const handleClickShowPassword = () => {
    setShowPassword(!showPassword);
  };



  const handleForgotPassword = async (e) => {
    e.preventDefault();
    setForgotLoading(true);
    setForgotMessage('');

    try {
      console.log('📧 Solicitando recuperação de senha para:', forgotEmail);

      const response = await authService.forgotPassword(forgotEmail);

      if (response.data && response.data.success) {
        console.log('✅ Email enviado com sucesso');
        setForgotMessage(
          `✅ Email enviado com sucesso!\n\n` +
          `📧 Suas credenciais foram enviadas para: ${forgotEmail}\n\n` +
          `📬 Verifique sua caixa de entrada e também a pasta de spam.\n\n` +
          `💡 Use as credenciais recebidas por email para fazer login no sistema.`
        );
      } else {
        setForgotMessage('❌ Erro inesperado. Tente novamente.');
      }
    } catch (error) {
      console.error('❌ Erro ao solicitar recuperação:', error);

      if (error.response?.status === 404) {
        setForgotMessage('❌ Email não encontrado no sistema.\n\nVerifique se o email está correto ou entre em contato com o administrador.');
      } else if (error.response?.status === 400) {
        setForgotMessage('❌ Email é obrigatório.');
      } else if (error.response?.status === 500) {
        setForgotMessage('❌ Erro ao enviar email.\n\nTente novamente mais tarde ou entre em contato com o suporte.');
      } else {
        setForgotMessage('❌ Erro ao solicitar recuperação de senha.\n\nTente novamente mais tarde ou entre em contato com o suporte.');
      }
    } finally {
      setForgotLoading(false);
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #1e3a8a 0%, #059669 100%)',
        padding: 2,
      }}
    >
      <Container maxWidth="sm">
        <StandardCard
          elevation={8}
          sx={{
            background: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(10px)',
            p: 6,
          }}
        >
            {/* Logo e Título */}
            <Box sx={{ textAlign: 'center', mb: 4 }}>
              {/* Logo da Evolution Tecnologia Funerária */}
              <Box sx={{ mb: 3 }}>
                <img
                  src={logoComFundo}
                  alt="Evolution Tecnologia Funerária"
                  style={{
                    maxWidth: '200px',
                    height: 'auto',
                    marginBottom: '16px'
                  }}
                />
              </Box>

              <Typography
                variant="h3"
                component="h1"
                sx={{
                  fontWeight: 700,
                  background: 'linear-gradient(135deg, #1e3a8a 0%, #059669 100%)',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  mb: 1,
                }}
              >
                Portal do Cliente
              </Typography>
              <Typography
                variant="body1"
                color="text.secondary"
                sx={{ mb: 4 }}
              >
                Sistema de Gestão dos Sepultados
              </Typography>
            </Box>



            {/* Formulário */}
            <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
              {error && (
                <Alert
                  severity="error"
                  sx={{
                    mb: 3,
                    '& .MuiAlert-message': {
                      width: '100%'
                    }
                  }}
                  onClose={() => setError('')}
                >
                  {error}
                </Alert>
              )}

              <TextField
                fullWidth
                label="Email ou Login"
                type="email"
                value={email}
                onChange={handleEmailChange}
                required
                disabled={loading}
                sx={{ mb: 3 }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <PersonIcon color="action" />
                    </InputAdornment>
                  ),
                }}
              />

              <TextField
                fullWidth
                label="Senha"
                type={showPassword ? 'text' : 'password'}
                value={senha}
                onChange={handleSenhaChange}
                required
                disabled={loading}
                sx={{ mb: 4 }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <LockIcon color="action" />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={handleClickShowPassword}
                        edge="end"
                        disabled={loading}
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />

              <Button
                type="submit"
                fullWidth
                variant="contained"
                size="large"
                disabled={loading}
                sx={{
                  py: 1.5,
                  background: 'linear-gradient(135deg, #1e3a8a 0%, #059669 100%)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #1e40af 0%, #047857 100%)',
                  },
                }}
              >
                {loading ? (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <CircularProgress size={20} color="inherit" />
                    Entrando...
                  </Box>
                ) : (
                  'Entrar'
                )}
              </Button>



              {/* Link Esqueci minha senha */}
              <Box sx={{ textAlign: 'center', mt: 2 }}>
                <Button
                  variant="text"
                  onClick={() => {
                    setShowForgotPassword(true);
                    setError(''); // Limpar erro ao abrir modal
                  }}
                  sx={{
                    color: 'primary.main',
                    textTransform: 'none',
                    fontSize: '0.875rem'
                  }}
                >
                  Esqueci minha senha
                </Button>
              </Box>
            </Box>


        </StandardCard>
      </Container>

      {/* Modal Esqueci minha senha */}
      <Dialog
        open={showForgotPassword}
        onClose={() => setShowForgotPassword(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <EmailIcon color="primary" />
            <Typography variant="h6">Recuperar Credenciais</Typography>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Digite seu email abaixo. Se existir um usuário cadastrado com este email,
            enviaremos suas credenciais de acesso para o Portal do Cliente Evolution.
          </Typography>

          <Box component="form" onSubmit={handleForgotPassword}>
            <TextField
              fullWidth
              label="Email"
              type="email"
              value={forgotEmail}
              onChange={(e) => setForgotEmail(e.target.value)}
              required
              disabled={forgotLoading}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <EmailIcon color="action" />
                  </InputAdornment>
                ),
              }}
              sx={{ mb: 2 }}
            />

            {forgotMessage && (
              <Alert
                severity={forgotMessage.includes('✅') ? 'success' : forgotMessage.includes('❌') ? 'error' : 'info'}
                sx={{
                  mb: 2,
                  '& .MuiAlert-message': {
                    whiteSpace: 'pre-line',
                    fontFamily: 'monospace',
                    fontSize: '0.875rem'
                  }
                }}
              >
                {forgotMessage}
              </Alert>
            )}
          </Box>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button
            onClick={() => {
              setShowForgotPassword(false);
              setForgotEmail('');
              setForgotMessage('');
            }}
            disabled={forgotLoading}
          >
            Cancelar
          </Button>
          <StandardButton
            onClick={handleForgotPassword}
            variant="contained"
            disabled={forgotLoading || !forgotEmail.trim()}
          >
            {forgotLoading ? (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CircularProgress size={16} color="inherit" />
                Enviando...
              </Box>
            ) : (
              'Enviar Credenciais'
            )}
          </StandardButton>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default LoginPage;
