#!/bin/bash

# Script completo para deploy da correção do sistema de logs
# Limpa imagens antigas, cria novas, atualiza Docker Swarm

set -e

echo "🚀 Deploy Completo - Correção Sistema de Logs"
echo "=============================================="

# Verificar se estamos no diretório correto
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ Erro: Execute este script no diretório raiz do projeto"
    exit 1
fi

# Função para backup do banco
backup_database() {
    echo "💾 Fazendo backup do banco de dados..."
    mkdir -p backups
    docker exec postgres_postgres.1.k6jai8b12yfbgxsnj4c3wcy58 pg_dump -U postgres dbetens > "backups/backup_logs_$(date +%Y%m%d_%H%M%S).sql"
    echo "✅ Backup criado em backups/"
}

# Função para limpar imagens antigas
cleanup_old_images() {
    echo "🧹 Limpando imagens Docker antigas..."
    
    # Parar containers se estiverem rodando
    echo "⏹️ Parando containers..."
    docker-compose down || true
    
    # Remover imagens antigas do portal
    echo "🗑️ Removendo imagens antigas..."
    docker rmi portal-evolution-backend:latest || true
    docker rmi portal-evolution-frontend:latest || true
    
    # Limpar imagens não utilizadas
    docker image prune -f
    
    echo "✅ Limpeza de imagens concluída"
}

# Função para construir novas imagens
build_new_images() {
    echo "🔨 Construindo novas imagens..."
    
    # Build do backend
    echo "📦 Construindo imagem do backend..."
    docker build -t portal-evolution-backend:latest ./portalcliente
    
    # Build do frontend
    echo "📦 Construindo imagem do frontend..."
    docker build -t portal-evolution-frontend:latest ./portalcliente
    
    echo "✅ Novas imagens construídas com sucesso"
}

# Função para atualizar Docker Swarm
update_swarm() {
    echo "🔄 Atualizando Docker Swarm..."
    
    # Verificar se o swarm está ativo
    if ! docker info | grep -q "Swarm: active"; then
        echo "⚠️ Docker Swarm não está ativo, iniciando..."
        docker swarm init || true
    fi
    
    # Remover stack existente se houver
    echo "🗑️ Removendo stack existente..."
    docker stack rm portal-evolution || true
    
    # Aguardar remoção completa
    echo "⏳ Aguardando remoção completa da stack..."
    sleep 10
    
    # Deploy da nova stack
    echo "🚀 Fazendo deploy da nova stack..."
    docker stack deploy -c docker-compose.yml portal-evolution
    
    echo "✅ Docker Swarm atualizado"
}

# Função para aguardar containers ficarem prontos
wait_for_containers() {
    echo "⏳ Aguardando containers ficarem prontos..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        echo "🔍 Tentativa $attempt/$max_attempts..."
        
        # Verificar se o backend está respondendo
        if curl -s http://localhost/api/health > /dev/null 2>&1; then
            echo "✅ Backend está respondendo"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            echo "❌ Timeout aguardando containers"
            return 1
        fi
        
        sleep 10
        ((attempt++))
    done
    
    # Aguardar mais um pouco para estabilizar
    sleep 15
}

# Função para testar sistema de logs
test_logs_system() {
    echo "🧪 Testando sistema de logs..."
    
    # Gerar token com o JWT_SECRET correto
    TOKEN=$(docker exec -it portal-evolution_portalevo-backend.1.ifddzed71uql7dk40wwgx8m09 node -e "
        const jwt = require('jsonwebtoken'); 
        console.log(jwt.sign({id: 1, email: 'admin', tipo_usuario: 'admin'}, 'portal_jwt_secret_key_muito_segura_2025', {expiresIn: '1h'}))
    " | tr -d '\r\n')
    
    # Testar endpoint de logs
    echo "📡 Testando endpoint GET /api/logs..."
    RESPONSE=$(curl -s -w "%{http_code}" -H "Authorization: Bearer $TOKEN" "http://localhost/api/logs" -o /tmp/logs_response.json)
    
    if [ "$RESPONSE" = "200" ]; then
        echo "✅ API de logs funcionando corretamente"
        echo "📊 Primeiros logs encontrados:"
        cat /tmp/logs_response.json | jq '.logs[0:3] | .[] | {id, acao, tabela_afetada, created_at}' 2>/dev/null || echo "Resposta recebida com sucesso"
        
        # Verificar se há logs
        TOTAL_LOGS=$(cat /tmp/logs_response.json | jq '.pagination.total' 2>/dev/null || echo "0")
        echo "📊 Total de logs encontrados: $TOTAL_LOGS"
        
    else
        echo "❌ Erro na API de logs. Status: $RESPONSE"
        echo "📄 Resposta:"
        cat /tmp/logs_response.json
        return 1
    fi
}

# Função para criar log de teste
create_test_log() {
    echo "📝 Criando log de teste..."
    
    # Fazer login para gerar um log
    LOGIN_RESPONSE=$(curl -s -X POST "http://localhost/api/auth/login" \
        -H "Content-Type: application/json" \
        -d '{"email":"admin","senha":"adminnbr5410!"}')
    
    if echo "$LOGIN_RESPONSE" | grep -q "token"; then
        echo "✅ Log de login criado com sucesso"
    else
        echo "⚠️ Não foi possível criar log de teste via login"
    fi
}

# Função para verificar limpeza automática
verify_cleanup() {
    echo "🧹 Verificando sistema de limpeza automática..."
    
    # Verificar logs antigos
    OLD_LOGS=$(docker exec -it postgres_postgres.1.k6jai8b12yfbgxsnj4c3wcy58 psql -U postgres -d dbetens -t -c "SELECT COUNT(*) FROM logs_auditoria WHERE created_at < CURRENT_DATE - INTERVAL '30 days';" | tr -d ' \r\n')
    
    echo "📊 Logs antigos (>30 dias): $OLD_LOGS"
    
    if [ "$OLD_LOGS" -gt 0 ]; then
        echo "🧹 Executando limpeza de logs antigos..."
        docker exec -it postgres_postgres.1.k6jai8b12yfbgxsnj4c3wcy58 psql -U postgres -d dbetens -c "DELETE FROM logs_auditoria WHERE created_at < CURRENT_DATE - INTERVAL '30 days';"
        echo "✅ Limpeza executada"
    else
        echo "✅ Nenhum log antigo para limpar"
    fi
}

# Função principal
main() {
    echo "🎯 Iniciando deploy completo..."
    
    # 1. Backup do banco
    backup_database
    
    # 2. Limpar imagens antigas
    cleanup_old_images
    
    # 3. Construir novas imagens
    build_new_images
    
    # 4. Atualizar Docker Swarm
    update_swarm
    
    # 5. Aguardar containers
    if wait_for_containers; then
        echo "✅ Containers prontos"
    else
        echo "❌ Erro aguardando containers"
        exit 1
    fi
    
    # 6. Testar sistema
    if test_logs_system; then
        echo "✅ Sistema de logs funcionando"
    else
        echo "❌ Erro no sistema de logs"
        exit 1
    fi
    
    # 7. Criar log de teste
    create_test_log
    
    # 8. Verificar limpeza automática
    verify_cleanup
    
    # 9. Mostrar status final
    echo ""
    echo "🎉 Deploy completo realizado com sucesso!"
    echo ""
    echo "✅ Melhorias implementadas:"
    echo "   ✓ Modal de detalhes melhorado com formatação avançada"
    echo "   ✓ Busca por texto em todos os campos de logs"
    echo "   ✓ Formatação de dados (CPF, CNPJ, telefone, valores)"
    echo "   ✓ Comparação visual de dados antes/depois"
    echo "   ✓ Middleware de logging automático aprimorado"
    echo "   ✓ Filtros com feedback visual e busca manual"
    echo "   ✓ Performance otimizada com índices de busca"
    echo "   ✓ Limpeza automática de logs (30 dias)"
    echo "   ✓ Imagens Docker atualizadas"
    echo "   ✓ Docker Swarm atualizado"
    echo ""
    echo "🔍 Para verificar:"
    echo "   1. Acesse: http://localhost (ou portal.evo-eden.site)"
    echo "   2. Faça login como admin"
    echo "   3. Vá na aba 'Logs'"
    echo "   4. Verifique se os logs carregam corretamente"
    echo ""
    echo "📊 Status dos containers:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
}

# Executar função principal
main "$@"
