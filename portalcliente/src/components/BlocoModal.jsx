import React, { useState, useEffect } from 'react';
import { Button } from '@mui/material';
import styled from 'styled-components';
import Modal from './Modal';
import { produtoService } from '../services/api';

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const StyledFormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const Label = styled.label`
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
`;

const Input = styled.input`
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  &:disabled {
    background-color: #f9fafb;
    color: #6b7280;
  }
`;

const TextArea = styled.textarea`
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  min-height: 100px;
  resize: vertical;
  font-family: inherit;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
`;

const StyledButtonGroup = styled.div`
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
`;

const StyledButton = styled.button`
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;

  &.primary {
    background: #3b82f6;
    color: white;

    &:hover {
      background: #2563eb;
    }

    &:disabled {
      background: #9ca3af;
      cursor: not-allowed;
    }
  }

  &.secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;

    &:hover {
      background: #e5e7eb;
    }
  }

  &.danger {
    background: #ef4444;
    color: white;

    &:hover {
      background: #dc2626;
    }

    &:disabled {
      background: #9ca3af;
      cursor: not-allowed;
    }
  }
`;

const ErrorMessage = styled.div`
  background: #fef2f2;
  color: #dc2626;
  padding: 12px;
  border-radius: 8px;
  font-size: 0.875rem;
`;

const BlocoModal = ({ isOpen, onClose, bloco, produtoId, onSuccess }) => {
  const [formData, setFormData] = useState({
    codigo_bloco: '',
    nome: '',
    descricao: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (isOpen) {
      if (bloco) {
        setFormData({
          codigo_bloco: bloco.codigo_bloco || '',
          nome: bloco.nome || '',
          descricao: bloco.descricao || ''
        });
      } else {
        setFormData({
          codigo_bloco: '',
          nome: '',
          descricao: ''
        });
      }
      setError('');
    }
  }, [isOpen, bloco]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      if (bloco) {
        await produtoService.atualizarBloco(bloco.id, formData);
        alert('Bloco atualizado com sucesso!');
      } else {
        await produtoService.criarBloco(produtoId, formData);
        alert('Bloco criado com sucesso!');
      }

      onSuccess();
      onClose();
    } catch (error) {
      console.error('Erro ao salvar bloco:', error);

      let errorMessage = 'Erro ao salvar bloco';

      if (error.response?.status === 400) {
        const errorData = error.response.data;

        if (errorData.dependencias || errorData.acao_necessaria) {
          // Erro de validação hierárquica
          errorMessage = `${errorData.error}\n\n${errorData.detalhes || ''}`;

          if (errorData.acao_necessaria) {
            errorMessage += `\n\n🔧 ${errorData.acao_necessaria}`;
          }

          if (errorData.sub_blocos_encontrados) {
            errorMessage += `\n\n📦 ${errorData.sub_blocos_encontrados} sub-bloco(s) encontrado(s)`;
          }
        } else {
          errorMessage = errorData.error || 'Dados inválidos para salvar o bloco';
        }
      } else if (error.response?.status === 403) {
        errorMessage = 'Acesso negado. Apenas administradores podem editar blocos.';
      } else if (error.response?.status === 404) {
        errorMessage = 'Bloco não encontrado. Pode ter sido removido por outro usuário.';
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!bloco || !window.confirm('Tem certeza que deseja deletar este bloco? Esta ação não pode ser desfeita.')) {
      return;
    }

    setLoading(true);
    setError('');

    try {
      await produtoService.deletarBloco(bloco.id);
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Erro ao deletar bloco:', error);

      let errorMessage = 'Erro ao deletar bloco';

      if (error.response?.status === 400) {
        const errorData = error.response.data;

        if (errorData.dependencias) {
          // Erro de validação hierárquica
          errorMessage = `❌ ${errorData.error}\n\n📋 ${errorData.detalhes}\n\n🔧 ${errorData.acao_necessaria}`;

          if (errorData.passos_obrigatorios && errorData.passos_obrigatorios.length > 0) {
            errorMessage += '\n\nPassos obrigatórios:\n' +
              errorData.passos_obrigatorios.map((passo, index) => `${index + 1}. ${passo}`).join('\n');
          }

          if (errorData.sub_blocos_nomes) {
            errorMessage += `\n\n📦 Sub-blocos encontrados: ${errorData.sub_blocos_nomes}`;
          }
        } else {
          errorMessage = errorData.error || 'Não é possível deletar este bloco';
        }
      } else if (error.response?.status === 403) {
        errorMessage = 'Acesso negado. Apenas administradores podem deletar blocos.';
      } else if (error.response?.status === 404) {
        errorMessage = 'Bloco não encontrado. Pode ter sido removido por outro usuário.';
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={bloco ? 'Editar Bloco' : 'Novo Bloco'}
      maxWidth="600px"
    >
      <Form onSubmit={handleSubmit}>
        <StyledFormGroup>
          <Label>Código do Bloco *</Label>
          <Input
            type="text"
            name="codigo_bloco"
            value={formData.codigo_bloco}
            onChange={handleChange}
            required
            placeholder="Ex: BL01"
            disabled={!!bloco}
          />
        </StyledFormGroup>

        <StyledFormGroup>
          <Label>Denominação *</Label>
          <Input
            type="text"
            name="nome"
            value={formData.nome}
            onChange={handleChange}
            required
            placeholder="Ex: Bloco Principal"
          />
        </StyledFormGroup>

        <StyledFormGroup>
          <Label>Observações</Label>
          <TextArea
            name="descricao"
            value={formData.descricao}
            onChange={handleChange}
            placeholder="Detalhes sobre o bloco..."
          />
        </StyledFormGroup>

        {error && <ErrorMessage>{error}</ErrorMessage>}

        <StyledButtonGroup>
          <StyledButton type="button" className="secondary" onClick={onClose}>
            Cancelar
          </StyledButton>
          {bloco && (
            <StyledButton
              type="button"
              className="danger"
              onClick={handleDelete}
              disabled={loading}
            >
              {loading ? 'Deletando...' : 'Deletar'}
            </StyledButton>
          )}
          <StyledButton type="submit" className="primary" disabled={loading}>
            {loading ? 'Salvando...' : (bloco ? 'Atualizar' : 'Criar')}
          </StyledButton>
        </StyledButtonGroup>
      </Form>
    </Modal>
  );
};

export default BlocoModal;
