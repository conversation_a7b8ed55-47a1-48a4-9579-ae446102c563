import React from 'react';
import { Tabs, Tab, Box, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledTabs = styled(Tabs)(({ theme }) => ({
  '& .MuiTabs-indicator': {
    height: '3px',
    backgroundColor: theme.palette.primary.main,
    borderRadius: '3px 3px 0 0',
  },
  '& .MuiTabs-flexContainer': {
    borderBottom: `1px solid ${theme.palette.divider}`,
  },
}));

const StyledTab = styled(Tab)(({ theme }) => ({
  fontSize: '16px',
  fontWeight: 500,
  minHeight: '48px',
  padding: '12px 24px',
  textTransform: 'none',
  color: theme.palette.text.secondary,
  transition: 'all 0.3s ease',
  
  '&:hover': {
    color: theme.palette.primary.main,
    backgroundColor: `${theme.palette.primary.main}08`,
  },
  
  '&.Mui-selected': {
    color: theme.palette.primary.main,
    fontWeight: 600,
  },
  
  '&.Mui-disabled': {
    opacity: 0.5,
    cursor: 'not-allowed',
  },
  
  // Responsividade
  [theme.breakpoints.down('sm')]: {
    fontSize: '14px',
    padding: '12px 16px',
    minWidth: 'auto',
  },
}));

const TabPanel = ({ children, value, index, ...other }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tabpanel-${index}`}
      aria-labelledby={`tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

const StandardTabs = ({
  tabs,
  value,
  onChange,
  variant = 'standard',
  scrollButtons = 'auto',
  allowScrollButtonsMobile = false,
  centered = false,
  sx = {},
  ...props
}) => {
  const a11yProps = (index) => {
    return {
      id: `tab-${index}`,
      'aria-controls': `tabpanel-${index}`,
    };
  };

  return (
    <Box sx={{ width: '100%', ...sx }}>
      <StyledTabs
        value={value}
        onChange={onChange}
        variant={variant}
        scrollButtons={scrollButtons}
        allowScrollButtonsMobile={allowScrollButtonsMobile}
        centered={centered}
        {...props}
      >
        {tabs.map((tab, index) => (
          <StyledTab
            key={index}
            label={tab.label}
            icon={tab.icon}
            iconPosition={tab.iconPosition || 'start'}
            disabled={tab.disabled}
            {...a11yProps(index)}
          />
        ))}
      </StyledTabs>
      
      {tabs.map((tab, index) => (
        <TabPanel key={index} value={value} index={index}>
          {tab.content}
        </TabPanel>
      ))}
    </Box>
  );
};

// Componente simplificado para casos básicos
export const SimpleTabs = ({
  tabs,
  value,
  onChange,
  ...props
}) => {
  return (
    <Box sx={{ width: '100%' }}>
      <StyledTabs
        value={value}
        onChange={onChange}
        {...props}
      >
        {tabs.map((tab, index) => (
          <StyledTab
            key={index}
            label={tab.label}
            icon={tab.icon}
          />
        ))}
      </StyledTabs>
    </Box>
  );
};

export { TabPanel };
export default StandardTabs;
