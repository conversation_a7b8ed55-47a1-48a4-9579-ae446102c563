# 📋 Documentação: Sistema de Ativar/Inativar Produtos

## 🎯 Objetivo
Permitir que administradores ativem ou inativem produtos mantendo **100% da integridade** dos dados subsequentes (blocos, sub-blocos, gavetas, sepultamentos).

## 🔧 Funcionalidades Implementadas

### ✅ Backend - API Endpoints

#### `PATCH /api/produtos/:id/toggle-status`
- **Autenticação:** Obrigatória (apenas admins)
- **Função:** Alterna status ativo/inativo do produto
- **Integridade:** Preserva TODOS os dados relacionados

**Exemplo de Request:**
```bash
PATCH /api/produtos/44/toggle-status
Authorization: Bearer <token>
```

**Exemplo de Response:**
```json
{
  "message": "Produto inativado com sucesso",
  "produto": {
    "id": 44,
    "denominacao": "Cemitério Central",
    "ativo": false,
    "updated_at": "2025-01-16T10:30:00Z"
  },
  "integridade": {
    "dados_preservados": {
      "blocos": 5,
      "subBlocos": 12,
      "gavetas": 240,
      "sepultamentos": 89,
      "sepultamentosAtivos": 67
    },
    "garantia": "Todos os dados subsequentes foram mantidos intactos"
  }
}
```

### ✅ Frontend - Interface

#### Página de Produtos (`/cadastros-produtos`)
- **Listagem:** Mostra TODOS os produtos (ativos e inativos)
- **Indicadores Visuais:** 
  - Produtos inativos com fundo vermelho claro
  - Chips de status coloridos
  - Contadores no título da página
- **Botões de Toggle:**
  - Produtos ativos: Botão "Inativar" (ícone ToggleOff)
  - Produtos inativos: Botão "Ativar" (ícone ToggleOn)

#### Modal de Confirmação
- **Informações Detalhadas:** Consequências da ação
- **Garantias de Integridade:** Lista de dados que serão preservados
- **Feedback Visual:** Ícones e cores apropriadas

## 🛡️ Garantias de Integridade

### ❌ O que NÃO é afetado pelo toggle:
1. **Blocos** - Mantidos intactos
2. **Sub-blocos** - Mantidos intactos  
3. **Gavetas** - Mantidas intactas
4. **Sepultamentos** - Mantidos intactos
5. **Ranges de numeração** - Mantidos intactos
6. **Relacionamentos** - Preservados

### ✅ O que é afetado pelo toggle:
1. **Apenas o campo `ativo`** do produto
2. **Timestamp `updated_at`** do produto
3. **Logs de auditoria** (criados)

## 🔍 Validações Implementadas

### 1. Autenticação e Autorização
```javascript
// Verificar se usuário está autenticado
if (!req.user) {
  return res.status(401).json({ error: 'Usuário não autenticado' });
}

// Verificar se é admin
if (req.user.tipo_usuario !== 'admin') {
  return res.status(403).json({ error: 'Acesso negado. Apenas administradores.' });
}
```

### 2. Validação de Integridade
```javascript
// Verificar consistência de dados relacionados
const validacaoIntegridade = await validarIntegridadeCompleta(produto);
if (!validacaoIntegridade.valido) {
  return res.status(400).json({ 
    error: 'Falha na validação de integridade: ' + validacaoIntegridade.erro 
  });
}
```

### 3. Verificação de Dependências
```javascript
// Contar dados que serão preservados
const dadosSubsequentes = await verificarDadosSubsequentes(produto);
console.log('📋 Dados que serão preservados:', dadosSubsequentes);
```

## 📊 Logs de Auditoria

### Estrutura do Log
```json
{
  "acao": "INATIVACAO",
  "produto": {
    "id": 44,
    "denominacao": "Cemitério Central",
    "codigo_cliente": "CLI001",
    "codigo_estacao": "EST001"
  },
  "status_anterior": true,
  "status_novo": false,
  "dados_preservados": {
    "blocos": 5,
    "subBlocos": 12,
    "gavetas": 240,
    "sepultamentos": 89,
    "sepultamentosAtivos": 67
  },
  "timestamp": "2025-01-16T10:30:00.000Z",
  "usuario": {
    "id": 1,
    "email": "<EMAIL>",
    "tipo": "admin"
  },
  "garantias_integridade": [
    "5 blocos mantidos intactos",
    "12 sub-blocos mantidos intactos",
    "240 gavetas mantidas intactas",
    "89 sepultamentos mantidos intactos",
    "67 sepultamentos ativos preservados"
  ]
}
```

## 🚨 Tratamento de Erros

### Códigos de Status HTTP
- **200:** Sucesso
- **400:** Dados inválidos ou falha na validação
- **401:** Não autenticado
- **403:** Não autorizado (não é admin)
- **404:** Produto não encontrado
- **500:** Erro interno do servidor

### Mensagens de Erro Frontend
```javascript
switch (status) {
  case 401:
    errorMessage = 'Sessão expirada. Faça login novamente.';
    break;
  case 403:
    errorMessage = 'Acesso negado. Apenas administradores podem alterar status de produtos.';
    break;
  case 404:
    errorMessage = 'Produto não encontrado. Pode ter sido removido por outro usuário.';
    break;
  // ... outros casos
}
```

## 🧪 Testes de Integridade

### Script de Teste
```bash
# Executar testes automatizados
node portalcliente/test-ativar-inativar.js
```

### Validações Testadas
1. ✅ Listagem de todos os produtos
2. ✅ Preservação de dados subsequentes
3. ✅ Logs de auditoria detalhados
4. ✅ Validação de integridade
5. ✅ Tratamento de erros

## 📝 Fluxo Completo

### 1. Usuário Clica em Toggle
```
Frontend → Validação → Modal de Confirmação → API Request
```

### 2. Backend Processa
```
Auth → Validação → Verificação Integridade → Update → Log → Response
```

### 3. Frontend Atualiza
```
Response → Feedback → Reload Lista → Atualização Visual
```

## 🔧 Configuração e Deploy

### Arquivos Modificados
- `portalcliente/server/routes/produtos_new.js` - Endpoint principal
- `portalcliente/src/pages/CadastrosProdutosPage.jsx` - Interface
- `portalcliente/src/components/ConfirmationModal.jsx` - Modal
- `portalcliente/test-ativar-inativar.js` - Testes

### Deploy
```bash
# Remover imagens antigas
docker rmi portal-evolution-frontend:latest portal-evolution-backend:latest

# Construir novas imagens
docker build -f portalcliente/Dockerfile -t portal-evolution-frontend:latest ./portalcliente
docker build -f portalcliente/Dockerfile.backend -t portal-evolution-backend:latest ./portalcliente

# Deploy
docker stack rm portal-evolution
sleep 10
docker stack deploy -c docker-compose.yml portal-evolution
```

## ✅ Status Final
- **Backend:** ✅ Funcionando
- **Frontend:** ✅ Funcionando
- **Integridade:** ✅ Garantida
- **Logs:** ✅ Detalhados
- **Testes:** ✅ Passando
- **Deploy:** ✅ Realizado

---
**Documentação criada em:** 16/01/2025  
**Versão:** 1.0  
**Status:** Produção
