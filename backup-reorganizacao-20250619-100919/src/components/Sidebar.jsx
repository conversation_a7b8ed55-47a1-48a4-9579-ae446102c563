import React, { useState } from 'react';
import { NavLink, useLocation, useNavigate } from 'react-router-dom';
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  Box,
  Divider,
  IconButton,
  useTheme,
  useMediaQuery,
  Avatar,
} from '@mui/material';
import { StandardButton } from '../components/common';
import {
  Home as HomeIcon,
  Business as BusinessIcon,
  People as PeopleIcon,
  Factory as FactoryIcon,
  Person as PersonIcon,
  Assignment as AssignmentIcon,
  Assessment as ReportIcon,
  Menu as MenuIcon,
  Close as CloseIcon,
  ExitToApp as LogoutIcon,
  AccountBox as SepultamentosIcon,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import logoSemFundo from '../assets/logo_sem_fundo_branco.png';

const DRAWER_WIDTH = 280;

// Ícones para cada menu item
const getMenuIcon = (iconKey) => {
  const icons = {
    home: <HomeIcon />,
    produtos: <BusinessIcon />,
    sepultamentos: <SepultamentosIcon />,
    relatorios: <ReportIcon />, // NOVO ÍCONE: Relatórios
    clientes: <PeopleIcon />,
    'cadastro-produtos': <FactoryIcon />,
    usuarios: <PersonIcon />,
    logs: <AssignmentIcon />,
  };
  return icons[iconKey] || <HomeIcon />;
};

const Sidebar = ({ mobileOpen, onMobileClose }) => {
  const { user, logout, canAccessClientes, canAccessUsuarios, canAccessLogs, canEditProdutos } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const menuItems = [
    { path: '/dashboard/', iconKey: 'home', label: 'Início', show: true },
    // Book de Sepultamentos removido para admin, mantido apenas para clientes
    { path: '/dashboard/book-sepultamentos', iconKey: 'sepultamentos', label: 'Book de Sepultamentos', show: !canEditProdutos() },
    { path: '/dashboard/relatorios', iconKey: 'relatorios', label: 'Relatórios', show: true }, // NOVA ABA: Relatórios para todos
    { path: '/dashboard/cadastros-produtos', iconKey: 'produtos', label: 'Cadastros dos Produtos', show: canEditProdutos() }, // CORREÇÃO: Apenas para admins
    { path: '/dashboard/clientes', iconKey: 'clientes', label: 'Clientes', show: canAccessClientes() },
    // "Cadastro de Produtos" removido para admin - mantemos apenas "Cadastros dos Produtos"
    { path: '/dashboard/usuarios', iconKey: 'usuarios', label: 'Usuários', show: canAccessUsuarios() },
    { path: '/dashboard/logs', iconKey: 'logs', label: 'Logs', show: canAccessLogs() },
  ];

  const handleLogout = () => {
    if (window.confirm('Tem certeza que deseja sair?')) {
      logout();
      navigate('/login');
    }
  };

  const getUserInitials = (name) => {
    if (!name) return 'U';
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  const drawerContent = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header/Logo */}
      <Box sx={{ p: 3, textAlign: 'center', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
        {/* Logo da Evolution Tecnologia Funerária */}
        <Box sx={{ mb: 2 }}>
          <img
            src={logoSemFundo}
            alt="Evolution Tecnologia Funerária"
            style={{
              maxWidth: '120px',
              height: 'auto',
              filter: 'brightness(0) invert(1)', // Torna a logo branca para contrastar com o fundo azul
            }}
          />
        </Box>

        <Typography variant="h6" sx={{ fontWeight: 700, color: '#10b981', mb: 0.5 }}>
          Portal do Cliente
        </Typography>
        <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
          Sistema de Gestão dos Sepultados
        </Typography>
      </Box>

      {/* Navigation */}
      <Box sx={{ flex: 1, py: 2 }}>
        <List>
          {menuItems.filter(item => item.show).map((item) => {
            // Lógica simplificada para detectar rota ativa
            let isActive = false;

            if (item.path === '/dashboard/') {
              // Para o botão Início, verificar se estamos na rota raiz do dashboard
              isActive = location.pathname === '/dashboard' || location.pathname === '/dashboard/';
            } else {
              // Para outros botões, verificação exata
              isActive = location.pathname === item.path;
            }

            return (
              <ListItem key={item.path} disablePadding>
                <ListItemButton
                  onClick={() => {
                    navigate(item.path);
                    if (isMobile) onMobileClose();
                  }}
                  sx={{
                    mx: 1,
                    borderRadius: 1,
                    color: isActive ? '#10b981' : 'rgba(255, 255, 255, 0.8)',
                    borderLeft: isActive ? '3px solid #10b981' : '3px solid transparent',
                    backgroundColor: isActive ? 'rgba(16, 185, 129, 0.2)' : 'transparent',
                    '&:hover': {
                      backgroundColor: isActive ? 'rgba(16, 185, 129, 0.3)' : 'rgba(255, 255, 255, 0.1)',
                      color: isActive ? '#10b981' : 'white',
                    },
                  }}
                >
                  <ListItemIcon sx={{ color: 'inherit', minWidth: 40 }}>
                    {getMenuIcon(item.iconKey)}
                  </ListItemIcon>
                  <ListItemText 
                    primary={item.label} 
                    primaryTypographyProps={{ 
                      fontSize: '0.875rem',
                      fontWeight: 500 
                    }}
                  />
                </ListItemButton>
              </ListItem>
            );
          })}
        </List>
      </Box>

      <Divider sx={{ borderColor: 'rgba(255, 255, 255, 0.1)' }} />

      {/* User Info */}
      <Box sx={{ p: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Avatar 
            sx={{ 
              bgcolor: theme.palette.secondary.main,
              width: 40,
              height: 40,
              mr: 1.5,
              fontSize: '0.875rem',
              fontWeight: 600
            }}
          >
            {getUserInitials(user?.nome)}
          </Avatar>
          <Box sx={{ flex: 1 }}>
            <Typography variant="body2" sx={{ fontWeight: 500, color: 'white' }}>
              {user?.nome || 'Usuário'}
            </Typography>
            <Typography variant="caption" sx={{ color: 'rgba(255, 255, 255, 0.7)', textTransform: 'capitalize' }}>
              {user?.tipo_usuario || 'cliente'}
            </Typography>
          </Box>
        </Box>
        
        <StandardButton
          fullWidth
          variant="outlined"
          startIcon={<LogoutIcon />}
          onClick={handleLogout}
          sx={{
            color: '#fca5a5',
            borderColor: 'rgba(220, 38, 38, 0.3)',
            backgroundColor: 'rgba(220, 38, 38, 0.1)',
            '&:hover': {
              backgroundColor: 'rgba(220, 38, 38, 0.2)',
              borderColor: 'rgba(220, 38, 38, 0.5)',
              color: '#fecaca',
            },
          }}
        >
          Sair
        </StandardButton>
      </Box>
    </Box>
  );

  return (
    <Box component="nav" sx={{ width: { md: DRAWER_WIDTH }, flexShrink: { md: 0 } }}>
      {/* Mobile drawer */}
      <Drawer
        variant="temporary"
        open={mobileOpen}
        onClose={onMobileClose}
        ModalProps={{ keepMounted: true }}
        sx={{
          display: { xs: 'block', md: 'none' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: DRAWER_WIDTH,
            background: 'linear-gradient(180deg, #1e3a8a 0%, #1e40af 100%)',
            color: 'white',
          },
        }}
      >
        {drawerContent}
      </Drawer>

      {/* Desktop drawer */}
      <Drawer
        variant="permanent"
        sx={{
          display: { xs: 'none', md: 'block' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: DRAWER_WIDTH,
            background: 'linear-gradient(180deg, #1e3a8a 0%, #1e40af 100%)',
            color: 'white',
            border: 'none',
          },
        }}
        open
      >
        {drawerContent}
      </Drawer>
    </Box>
  );
};

export default Sidebar;
