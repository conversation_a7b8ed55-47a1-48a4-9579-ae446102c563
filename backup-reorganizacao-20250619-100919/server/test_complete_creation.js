const { Pool } = require('pg');
require('dotenv').config();

// Configuração do banco de dados
const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
});

async function testCompleteCreation() {
  console.log('🧪 TESTE COMPLETO - Criação de Dados com Referências Hierárquicas');
  console.log('=' .repeat(80));

  const client = await pool.connect();
  
  try {
    // 1. Primeiro, verificar se as colunas realmente existem
    console.log('🔍 1. Verificando estrutura atual das tabelas...');
    
    const tables = ['blocos', 'sub_blocos', 'gavetas', 'numeracoes_gavetas', 'sepultamentos'];
    
    for (const tableName of tables) {
      console.log(`\n📋 Estrutura da tabela: ${tableName.toUpperCase()}`);
      
      const columnsResult = await client.query(`
        SELECT 
          column_name,
          data_type,
          is_nullable,
          column_default
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = $1
        ORDER BY ordinal_position
      `, [tableName]);
      
      console.log(`   Total de colunas: ${columnsResult.rows.length}`);
      
      // Verificar colunas hierárquicas específicas
      const hierarchicalCols = ['codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco'];
      const foundHierarchical = columnsResult.rows.filter(row => 
        hierarchicalCols.includes(row.column_name)
      );
      
      console.log(`   Colunas hierárquicas encontradas: ${foundHierarchical.length}`);
      foundHierarchical.forEach(col => {
        console.log(`      ✅ ${col.column_name} (${col.data_type})`);
      });
      
      const missingHierarchical = hierarchicalCols.filter(hCol => 
        !foundHierarchical.some(fCol => fCol.column_name === hCol)
      );
      
      if (missingHierarchical.length > 0) {
        console.log(`   ❌ Colunas hierárquicas faltando: ${missingHierarchical.join(', ')}`);
      }
    }
    
    // 2. Criar dados de teste completos
    console.log('\n🏗️  2. Criando dados de teste completos...');
    
    await client.query('BEGIN');
    
    const testData = {
      codigo_cliente: 'TEST_HIER_001',
      cnpj: '12.345.678/0001-99',
      nome_fantasia: 'Teste Hierárquico Ltda',
      razao_social: 'Teste Hierárquico Ltda',
      codigo_estacao: 'EST_TESTE_001',
      denominacao_produto: 'Estação de Teste Hierárquico',
      codigo_bloco: 'BL_TESTE',
      nome_bloco: 'Bloco de Teste Hierárquico',
      codigo_sub_bloco: 'SB_TESTE',
      nome_sub_bloco: 'Sub-bloco de Teste Hierárquico'
    };
    
    // 2.1. Criar cliente
    console.log(`   👤 Criando cliente: ${testData.codigo_cliente}`);
    await client.query(`
      INSERT INTO clientes (codigo_cliente, cnpj, nome_fantasia, razao_social, nome)
      VALUES ($1, $2, $3, $4, $5)
      ON CONFLICT (codigo_cliente) DO UPDATE SET
        nome_fantasia = EXCLUDED.nome_fantasia
    `, [testData.codigo_cliente, testData.cnpj, testData.nome_fantasia, testData.razao_social, testData.nome_fantasia]);
    
    // 2.2. Criar produto
    console.log(`   📦 Criando produto: ${testData.codigo_estacao}`);
    await client.query(`
      INSERT INTO produtos (codigo_cliente, codigo_estacao, denominacao, meses_para_exumar, nome, tipo)
      VALUES ($1, $2, $3, 24, $4, 'ETEN')
      ON CONFLICT (codigo_cliente, codigo_estacao) DO UPDATE SET
        denominacao = EXCLUDED.denominacao
    `, [testData.codigo_cliente, testData.codigo_estacao, testData.denominacao_produto, testData.denominacao_produto]);
    
    // Buscar ID do produto para usar nas próximas inserções
    const produtoResult = await client.query(`
      SELECT id FROM produtos WHERE codigo_cliente = $1 AND codigo_estacao = $2
    `, [testData.codigo_cliente, testData.codigo_estacao]);
    
    if (produtoResult.rows.length === 0) {
      throw new Error('Produto não foi criado corretamente');
    }
    
    const produtoId = produtoResult.rows[0].id;
    console.log(`   ✅ Produto criado com ID: ${produtoId}`);
    
    // 2.3. Criar bloco
    console.log(`   🧱 Criando bloco: ${testData.codigo_bloco}`);
    await client.query(`
      INSERT INTO blocos (produto_id, codigo_bloco, nome, codigo_cliente, codigo_estacao, denominacao)
      VALUES ($1, $2, $3, $4, $5, $6)
      ON CONFLICT (produto_id, codigo_bloco) DO UPDATE SET
        nome = EXCLUDED.nome,
        codigo_cliente = EXCLUDED.codigo_cliente,
        codigo_estacao = EXCLUDED.codigo_estacao,
        denominacao = EXCLUDED.denominacao
    `, [produtoId, testData.codigo_bloco, testData.nome_bloco, testData.codigo_cliente, testData.codigo_estacao, testData.nome_bloco]);
    
    // Buscar ID do bloco
    const blocoResult = await client.query(`
      SELECT id FROM blocos WHERE produto_id = $1 AND codigo_bloco = $2
    `, [produtoId, testData.codigo_bloco]);
    
    if (blocoResult.rows.length === 0) {
      throw new Error('Bloco não foi criado corretamente');
    }
    
    const blocoId = blocoResult.rows[0].id;
    console.log(`   ✅ Bloco criado com ID: ${blocoId}`);
    
    // 2.4. Criar sub-bloco
    console.log(`   🔲 Criando sub-bloco: ${testData.codigo_sub_bloco}`);
    await client.query(`
      INSERT INTO sub_blocos (bloco_id, codigo_sub_bloco, nome, codigo_cliente, codigo_estacao, codigo_bloco, denominacao)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      ON CONFLICT (bloco_id, codigo_sub_bloco) DO UPDATE SET
        nome = EXCLUDED.nome,
        codigo_cliente = EXCLUDED.codigo_cliente,
        codigo_estacao = EXCLUDED.codigo_estacao,
        codigo_bloco = EXCLUDED.codigo_bloco,
        denominacao = EXCLUDED.denominacao
    `, [blocoId, testData.codigo_sub_bloco, testData.nome_sub_bloco, testData.codigo_cliente, testData.codigo_estacao, testData.codigo_bloco, testData.nome_sub_bloco]);
    
    // Buscar ID do sub-bloco
    const subBlocoResult = await client.query(`
      SELECT id FROM sub_blocos WHERE bloco_id = $1 AND codigo_sub_bloco = $2
    `, [blocoId, testData.codigo_sub_bloco]);
    
    if (subBlocoResult.rows.length === 0) {
      throw new Error('Sub-bloco não foi criado corretamente');
    }
    
    const subBlocoId = subBlocoResult.rows[0].id;
    console.log(`   ✅ Sub-bloco criado com ID: ${subBlocoId}`);
    
    // 2.5. Criar gavetas (1-10)
    console.log(`   📊 Criando gavetas 1-10...`);
    for (let i = 1; i <= 10; i++) {
      await client.query(`
        INSERT INTO gavetas (sub_bloco_id, numero_gaveta, posicao_x, posicao_y, codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco)
        VALUES ($1, $2, 1, 1, $3, $4, $5, $6)
        ON CONFLICT (sub_bloco_id, numero_gaveta) DO UPDATE SET
          codigo_cliente = EXCLUDED.codigo_cliente,
          codigo_estacao = EXCLUDED.codigo_estacao,
          codigo_bloco = EXCLUDED.codigo_bloco,
          codigo_sub_bloco = EXCLUDED.codigo_sub_bloco
      `, [subBlocoId, i, testData.codigo_cliente, testData.codigo_estacao, testData.codigo_bloco, testData.codigo_sub_bloco]);
    }
    console.log(`   ✅ 10 gavetas criadas`);
    
    // 2.6. Criar 5 sepultamentos
    console.log(`   ⚰️  Criando 5 sepultamentos...`);
    const sepultados = [
      { nome: 'João Silva Teste', gaveta: 1, data: '2024-01-15' },
      { nome: 'Maria Santos Teste', gaveta: 2, data: '2024-01-20' },
      { nome: 'Pedro Oliveira Teste', gaveta: 3, data: '2024-01-25' },
      { nome: 'Ana Costa Teste', gaveta: 4, data: '2024-02-01' },
      { nome: 'Carlos Lima Teste', gaveta: 5, data: '2024-02-05' }
    ];
    
    for (const sepultado of sepultados) {
      // Buscar ID da gaveta
      const gavetaResult = await client.query(`
        SELECT id FROM gavetas WHERE sub_bloco_id = $1 AND numero_gaveta = $2
      `, [subBlocoId, sepultado.gaveta]);
      
      if (gavetaResult.rows.length > 0) {
        const gavetaId = gavetaResult.rows[0].id;
        
        await client.query(`
          INSERT INTO sepultamentos (
            gaveta_id, nome_sepultado, codigo_cliente, codigo_bloco, codigo_sub_bloco, 
            numero_gaveta, data_sepultamento, codigo_estacao, horario_sepultamento
          )
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, '14:00')
        `, [
          gavetaId, 
          sepultado.nome, 
          testData.codigo_cliente, 
          testData.codigo_bloco, 
          testData.codigo_sub_bloco, 
          sepultado.gaveta, 
          sepultado.data,
          testData.codigo_estacao
        ]);
        
        // Marcar gaveta como ocupada
        await client.query(`
          UPDATE gavetas SET disponivel = false WHERE id = $1
        `, [gavetaId]);
        
        console.log(`      ✅ ${sepultado.nome} - Gaveta ${sepultado.gaveta}`);
      }
    }
    
    await client.query('COMMIT');
    console.log(`   🎉 Todos os dados criados com sucesso!`);
    
    // 3. Verificar dados criados usando consultas hierárquicas
    console.log('\n🔍 3. Verificando dados criados com consultas hierárquicas...');
    
    // 3.1. Verificar estrutura hierárquica completa
    const hierarchyCheck = await client.query(`
      SELECT 
        c.codigo_cliente,
        c.nome_fantasia,
        p.codigo_estacao,
        p.denominacao as produto_nome,
        b.codigo_bloco,
        b.denominacao as bloco_nome,
        sb.codigo_sub_bloco,
        sb.denominacao as sub_bloco_nome,
        COUNT(g.numero_gaveta) as total_gavetas,
        COUNT(s.id) as total_sepultamentos
      FROM clientes c
      JOIN produtos p ON c.codigo_cliente = p.codigo_cliente
      JOIN blocos b ON p.codigo_cliente = b.codigo_cliente AND p.codigo_estacao = b.codigo_estacao
      JOIN sub_blocos sb ON b.codigo_cliente = sb.codigo_cliente 
                         AND b.codigo_estacao = sb.codigo_estacao 
                         AND b.codigo_bloco = sb.codigo_bloco
      LEFT JOIN gavetas g ON sb.codigo_cliente = g.codigo_cliente 
                          AND sb.codigo_estacao = g.codigo_estacao 
                          AND sb.codigo_bloco = g.codigo_bloco 
                          AND sb.codigo_sub_bloco = g.codigo_sub_bloco
      LEFT JOIN sepultamentos s ON g.codigo_cliente = s.codigo_cliente 
                                AND g.codigo_estacao = s.codigo_estacao 
                                AND g.codigo_bloco = s.codigo_bloco 
                                AND g.codigo_sub_bloco = s.codigo_sub_bloco 
                                AND g.numero_gaveta = s.numero_gaveta
                                AND s.ativo = true
      WHERE c.codigo_cliente = $1
      GROUP BY c.codigo_cliente, c.nome_fantasia, p.codigo_estacao, p.denominacao, 
               b.codigo_bloco, b.denominacao, sb.codigo_sub_bloco, sb.denominacao
    `, [testData.codigo_cliente]);
    
    console.log(`   📊 Estrutura hierárquica encontrada: ${hierarchyCheck.rows.length} registros`);
    hierarchyCheck.rows.forEach(row => {
      console.log(`      📋 ${row.codigo_cliente}/${row.codigo_estacao}/${row.codigo_bloco}/${row.codigo_sub_bloco}`);
      console.log(`         Cliente: ${row.nome_fantasia}`);
      console.log(`         Produto: ${row.produto_nome}`);
      console.log(`         Bloco: ${row.bloco_nome}`);
      console.log(`         Sub-bloco: ${row.sub_bloco_nome}`);
      console.log(`         Gavetas: ${row.total_gavetas}, Sepultamentos: ${row.total_sepultamentos}`);
    });
    
    // 3.2. Verificar gavetas por códigos
    const gavetasCheck = await client.query(`
      SELECT 
        codigo_cliente,
        codigo_estacao,
        codigo_bloco,
        codigo_sub_bloco,
        numero_gaveta,
        disponivel
      FROM gavetas 
      WHERE codigo_cliente = $1 
      AND codigo_estacao = $2
      AND codigo_bloco = $3
      AND codigo_sub_bloco = $4
      ORDER BY numero_gaveta
    `, [testData.codigo_cliente, testData.codigo_estacao, testData.codigo_bloco, testData.codigo_sub_bloco]);
    
    console.log(`\n   📊 Gavetas encontradas por códigos: ${gavetasCheck.rows.length}`);
    gavetasCheck.rows.forEach(row => {
      const status = row.disponivel ? '🟢 Disponível' : '🔴 Ocupada';
      console.log(`      ${status} Gaveta ${row.numero_gaveta} - ${row.codigo_cliente}/${row.codigo_estacao}/${row.codigo_bloco}/${row.codigo_sub_bloco}`);
    });
    
    // 3.3. Verificar sepultamentos por códigos
    const sepultamentosCheck = await client.query(`
      SELECT 
        codigo_cliente,
        codigo_estacao,
        codigo_bloco,
        codigo_sub_bloco,
        numero_gaveta,
        nome_sepultado,
        data_sepultamento
      FROM sepultamentos 
      WHERE codigo_cliente = $1 
      AND codigo_estacao = $2
      AND codigo_bloco = $3
      AND codigo_sub_bloco = $4
      AND ativo = true
      ORDER BY numero_gaveta
    `, [testData.codigo_cliente, testData.codigo_estacao, testData.codigo_bloco, testData.codigo_sub_bloco]);
    
    console.log(`\n   ⚰️  Sepultamentos encontrados por códigos: ${sepultamentosCheck.rows.length}`);
    sepultamentosCheck.rows.forEach(row => {
      console.log(`      ${row.nome_sepultado} - Gaveta ${row.numero_gaveta} (${row.data_sepultamento})`);
      console.log(`         Localização: ${row.codigo_cliente}/${row.codigo_estacao}/${row.codigo_bloco}/${row.codigo_sub_bloco}`);
    });
    
    console.log('\n🎉 TESTE COMPLETO FINALIZADO!');
    console.log('=' .repeat(80));
    
    if (hierarchyCheck.rows.length > 0 && gavetasCheck.rows.length > 0 && sepultamentosCheck.rows.length > 0) {
      console.log('✅ SUCESSO TOTAL!');
      console.log('✅ Colunas hierárquicas funcionando perfeitamente!');
      console.log('✅ Consultas por códigos operacionais!');
      console.log('✅ Sistema pronto para importação de dados!');
    } else {
      console.log('❌ FALHA DETECTADA!');
      console.log('❌ Algumas consultas não retornaram dados esperados');
    }
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Erro durante o teste:', error);
    console.error('Stack:', error.stack);
  } finally {
    client.release();
    await pool.end();
  }
}

// Executar teste completo
if (require.main === module) {
  testCompleteCreation().catch(error => {
    console.error('❌ Erro fatal:', error);
    process.exit(1);
  });
}

module.exports = { testCompleteCreation };
