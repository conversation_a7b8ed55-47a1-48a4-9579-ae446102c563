const express = require('express');
const { Pool } = require('pg');
const path = require('path');

const app = express();
const port = 3001;

// Configuração do banco de dados
const pool = new Pool({
  user: 'postgres',
  host: '************',
  database: 'dbetens',
  password: 'ab3780bd73ee4e2804d566ce6fd96209',
  port: 5432,
});

// Middleware
app.use(express.static('public'));
app.use(express.json());

// Rota principal - página de inspeção
app.get('/', (req, res) => {
  res.send(`
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inspetor de Banco de Dados - Colunas Hierárquicas</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 10px;
        }
        .table-section {
            margin: 30px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }
        .table-header {
            background: #007bff;
            color: white;
            padding: 15px;
            font-weight: bold;
            font-size: 18px;
        }
        .table-content {
            padding: 15px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .status-found {
            color: #28a745;
            font-weight: bold;
        }
        .status-missing {
            color: #dc3545;
            font-weight: bold;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .refresh-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
        }
        .refresh-btn:hover {
            background: #218838;
        }
        .summary {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .expected-columns {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Inspetor de Colunas Hierárquicas</h1>
        <p><strong>Banco:</strong> dbetens @ ************:5432</p>
        
        <button class="refresh-btn" onclick="loadTableData()">🔄 Atualizar Dados</button>
        
        <div class="summary" id="summary">
            <div class="loading">Carregando dados...</div>
        </div>

        <div class="expected-columns">
            <h3>📋 Colunas Hierárquicas Esperadas por Tabela:</h3>
            <ul>
                <li><strong>blocos:</strong> codigo_cliente, codigo_estacao, codigo_bloco</li>
                <li><strong>sub_blocos:</strong> codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco</li>
                <li><strong>gavetas:</strong> codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco</li>
                <li><strong>numeracoes_gavetas:</strong> codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco</li>
                <li><strong>sepultamentos:</strong> codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco</li>
                <li><strong>logs_auditoria:</strong> codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco</li>
            </ul>
        </div>
        
        <div id="tables-container">
            <div class="loading">Carregando estrutura das tabelas...</div>
        </div>
    </div>

    <script>
        async function loadTableData() {
            try {
                document.getElementById('tables-container').innerHTML = '<div class="loading">Carregando estrutura das tabelas...</div>';
                document.getElementById('summary').innerHTML = '<div class="loading">Carregando dados...</div>';
                
                const response = await fetch('/api/inspect-tables');
                const data = await response.json();
                
                displaySummary(data.summary);
                displayTables(data.tables);
            } catch (error) {
                console.error('Erro ao carregar dados:', error);
                document.getElementById('tables-container').innerHTML = '<div style="color: red;">Erro ao carregar dados: ' + error.message + '</div>';
            }
        }
        
        function displaySummary(summary) {
            const summaryHtml = \`
                <h3>📊 Resumo Geral</h3>
                <p><strong>Total de tabelas verificadas:</strong> \${summary.totalTables}</p>
                <p><strong>Tabelas com todas as colunas:</strong> <span class="status-found">\${summary.tablesWithAllColumns}</span></p>
                <p><strong>Tabelas com colunas faltando:</strong> <span class="status-missing">\${summary.tablesWithMissingColumns}</span></p>
                <p><strong>Total de colunas hierárquicas encontradas:</strong> \${summary.totalHierarchicalColumns}</p>
                <p><strong>Status geral:</strong> \${summary.allComplete ? '<span class="status-found">✅ COMPLETO</span>' : '<span class="status-missing">❌ INCOMPLETO</span>'}</p>
            \`;
            document.getElementById('summary').innerHTML = summaryHtml;
        }
        
        function displayTables(tables) {
            let html = '';
            
            tables.forEach(table => {
                const expectedColumns = getExpectedColumns(table.name);
                const foundColumns = table.columns.filter(col => expectedColumns.includes(col.column_name));
                const missingColumns = expectedColumns.filter(col => !foundColumns.some(found => found.column_name === col));
                
                html += \`
                    <div class="table-section">
                        <div class="table-header">
                            📋 Tabela: \${table.name.toUpperCase()}
                            \${missingColumns.length === 0 ? '<span style="float: right;">✅ COMPLETA</span>' : '<span style="float: right;">❌ INCOMPLETA</span>'}
                        </div>
                        <div class="table-content">
                            <p><strong>Total de colunas:</strong> \${table.columns.length}</p>
                            <p><strong>Colunas hierárquicas encontradas:</strong> <span class="status-found">\${foundColumns.length}</span></p>
                            <p><strong>Colunas hierárquicas faltando:</strong> <span class="status-missing">\${missingColumns.length}</span></p>
                            
                            \${foundColumns.length > 0 ? \`
                                <h4 style="color: #28a745;">✅ Colunas Hierárquicas Encontradas:</h4>
                                <table>
                                    <thead>
                                        <tr>
                                            <th>Nome da Coluna</th>
                                            <th>Tipo de Dados</th>
                                            <th>Permite NULL</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        \${foundColumns.map(col => \`
                                            <tr>
                                                <td><strong>\${col.column_name}</strong></td>
                                                <td>\${col.data_type}</td>
                                                <td>\${col.is_nullable === 'YES' ? 'Sim' : 'Não'}</td>
                                            </tr>
                                        \`).join('')}
                                    </tbody>
                                </table>
                            \` : ''}
                            
                            \${missingColumns.length > 0 ? \`
                                <h4 style="color: #dc3545;">❌ Colunas Hierárquicas Faltando:</h4>
                                <ul>
                                    \${missingColumns.map(col => \`<li><strong>\${col}</strong></li>\`).join('')}
                                </ul>
                            \` : ''}
                            
                            <details>
                                <summary><strong>Ver todas as colunas da tabela (\${table.columns.length})</strong></summary>
                                <table style="margin-top: 10px;">
                                    <thead>
                                        <tr>
                                            <th>Nome da Coluna</th>
                                            <th>Tipo de Dados</th>
                                            <th>Permite NULL</th>
                                            <th>Hierárquica?</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        \${table.columns.map(col => \`
                                            <tr>
                                                <td>\${col.column_name}</td>
                                                <td>\${col.data_type}</td>
                                                <td>\${col.is_nullable === 'YES' ? 'Sim' : 'Não'}</td>
                                                <td>\${expectedColumns.includes(col.column_name) ? '<span class="status-found">✅ Sim</span>' : 'Não'}</td>
                                            </tr>
                                        \`).join('')}
                                    </tbody>
                                </table>
                            </details>
                        </div>
                    </div>
                \`;
            });
            
            document.getElementById('tables-container').innerHTML = html;
        }
        
        function getExpectedColumns(tableName) {
            const expectedColumns = {
                'blocos': ['codigo_cliente', 'codigo_estacao', 'codigo_bloco'],
                'sub_blocos': ['codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco'],
                'gavetas': ['codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco'],
                'numeracoes_gavetas': ['codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco'],
                'sepultamentos': ['codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco'],
                'logs_auditoria': ['codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco']
            };
            return expectedColumns[tableName] || [];
        }
        
        // Carregar dados ao inicializar a página
        window.onload = loadTableData;
    </script>
</body>
</html>
  `);
});

// API para inspecionar tabelas
app.get('/api/inspect-tables', async (req, res) => {
  const client = await pool.connect();
  
  try {
    const tables = ['blocos', 'sub_blocos', 'gavetas', 'numeracoes_gavetas', 'sepultamentos', 'logs_auditoria'];
    const expectedColumns = {
      'blocos': ['codigo_cliente', 'codigo_estacao', 'codigo_bloco'],
      'sub_blocos': ['codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco'],
      'gavetas': ['codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco'],
      'numeracoes_gavetas': ['codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco'],
      'sepultamentos': ['codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco'],
      'logs_auditoria': ['codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco']
    };
    
    const result = {
      tables: [],
      summary: {
        totalTables: tables.length,
        tablesWithAllColumns: 0,
        tablesWithMissingColumns: 0,
        totalHierarchicalColumns: 0,
        allComplete: true
      }
    };
    
    for (const tableName of tables) {
      // Buscar colunas da tabela
      const columnsResult = await client.query(`
        SELECT 
          column_name,
          data_type,
          is_nullable,
          column_default
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = $1
        ORDER BY ordinal_position
      `, [tableName]);
      
      const tableData = {
        name: tableName,
        columns: columnsResult.rows
      };
      
      // Verificar colunas hierárquicas
      const expected = expectedColumns[tableName] || [];
      const found = columnsResult.rows.filter(col => expected.includes(col.column_name));
      const missing = expected.filter(exp => !found.some(f => f.column_name === exp));
      
      result.summary.totalHierarchicalColumns += found.length;
      
      if (missing.length === 0) {
        result.summary.tablesWithAllColumns++;
      } else {
        result.summary.tablesWithMissingColumns++;
        result.summary.allComplete = false;
      }
      
      result.tables.push(tableData);
    }
    
    res.json(result);
    
  } catch (error) {
    console.error('Erro ao inspecionar tabelas:', error);
    res.status(500).json({ error: error.message });
  } finally {
    client.release();
  }
});

// API para adicionar colunas faltantes
app.post('/api/add-missing-columns', async (req, res) => {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    const results = [];
    
    // Adicionar colunas em blocos
    try {
      await client.query('ALTER TABLE blocos ADD COLUMN IF NOT EXISTS codigo_cliente VARCHAR(50)');
      await client.query('ALTER TABLE blocos ADD COLUMN IF NOT EXISTS codigo_estacao VARCHAR(50)');
      await client.query('ALTER TABLE blocos ADD COLUMN IF NOT EXISTS denominacao VARCHAR(255)');
      results.push('✅ Colunas adicionadas em blocos');
    } catch (error) {
      results.push(`❌ Erro em blocos: ${error.message}`);
    }
    
    // Adicionar colunas em sub_blocos
    try {
      await client.query('ALTER TABLE sub_blocos ADD COLUMN IF NOT EXISTS codigo_cliente VARCHAR(50)');
      await client.query('ALTER TABLE sub_blocos ADD COLUMN IF NOT EXISTS codigo_estacao VARCHAR(50)');
      await client.query('ALTER TABLE sub_blocos ADD COLUMN IF NOT EXISTS codigo_bloco VARCHAR(50)');
      await client.query('ALTER TABLE sub_blocos ADD COLUMN IF NOT EXISTS denominacao VARCHAR(255)');
      results.push('✅ Colunas adicionadas em sub_blocos');
    } catch (error) {
      results.push(`❌ Erro em sub_blocos: ${error.message}`);
    }
    
    // Adicionar colunas em gavetas
    try {
      await client.query('ALTER TABLE gavetas ADD COLUMN IF NOT EXISTS codigo_cliente VARCHAR(50)');
      await client.query('ALTER TABLE gavetas ADD COLUMN IF NOT EXISTS codigo_estacao VARCHAR(50)');
      await client.query('ALTER TABLE gavetas ADD COLUMN IF NOT EXISTS codigo_bloco VARCHAR(50)');
      await client.query('ALTER TABLE gavetas ADD COLUMN IF NOT EXISTS codigo_sub_bloco VARCHAR(50)');
      results.push('✅ Colunas adicionadas em gavetas');
    } catch (error) {
      results.push(`❌ Erro em gavetas: ${error.message}`);
    }
    
    // Adicionar colunas em numeracoes_gavetas
    try {
      await client.query('ALTER TABLE numeracoes_gavetas ADD COLUMN IF NOT EXISTS codigo_cliente VARCHAR(50)');
      await client.query('ALTER TABLE numeracoes_gavetas ADD COLUMN IF NOT EXISTS codigo_estacao VARCHAR(50)');
      await client.query('ALTER TABLE numeracoes_gavetas ADD COLUMN IF NOT EXISTS codigo_bloco VARCHAR(50)');
      await client.query('ALTER TABLE numeracoes_gavetas ADD COLUMN IF NOT EXISTS codigo_sub_bloco VARCHAR(50)');
      results.push('✅ Colunas adicionadas em numeracoes_gavetas');
    } catch (error) {
      results.push(`❌ Erro em numeracoes_gavetas: ${error.message}`);
    }
    
    // Adicionar colunas em logs_auditoria
    try {
      await client.query('ALTER TABLE logs_auditoria ADD COLUMN IF NOT EXISTS codigo_cliente VARCHAR(50)');
      await client.query('ALTER TABLE logs_auditoria ADD COLUMN IF NOT EXISTS codigo_estacao VARCHAR(50)');
      await client.query('ALTER TABLE logs_auditoria ADD COLUMN IF NOT EXISTS codigo_bloco VARCHAR(50)');
      await client.query('ALTER TABLE logs_auditoria ADD COLUMN IF NOT EXISTS codigo_sub_bloco VARCHAR(50)');
      await client.query('ALTER TABLE logs_auditoria ADD COLUMN IF NOT EXISTS descricao TEXT');
      results.push('✅ Colunas adicionadas em logs_auditoria');
    } catch (error) {
      results.push(`❌ Erro em logs_auditoria: ${error.message}`);
    }
    
    await client.query('COMMIT');
    
    res.json({ 
      success: true, 
      message: 'Colunas adicionadas com sucesso!',
      results: results
    });
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Erro ao adicionar colunas:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message 
    });
  } finally {
    client.release();
  }
});

// Iniciar servidor
app.listen(port, () => {
  console.log(`🔍 Inspetor de Banco de Dados rodando em http://localhost:${port}`);
  console.log(`📋 Verificando colunas hierárquicas no PostgreSQL`);
  console.log(`🗄️  Banco: dbetens @ ************:5432`);
});

module.exports = app;
