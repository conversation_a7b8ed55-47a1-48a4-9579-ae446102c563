const express = require('express');
const { query } = require('../database/connection');

const router = express.Router();

// Rota para buscar dados de relatórios de sepultamentos
router.get('/sepultamentos', async (req, res) => {
  try {
    const userId = req.user.id;
    const userType = req.user.tipo_usuario;
    const { produto_id, data_inicio, data_fim } = req.query;

    console.log('📊 Buscando dados de relatório:', {
      userId,
      userType,
      produto_id,
      data_inicio,
      data_fim
    });

    if (!produto_id || !data_inicio || !data_fim) {
      return res.status(400).json({
        error: 'Parâmetros obrigatórios: produto_id, data_inicio, data_fim'
      });
    }

    let whereClause = '';
    let params = [produto_id, data_inicio, data_fim];

    // Filtrar por cliente se for usuário cliente
    if (userType === 'cliente') {
      whereClause = 'AND s.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $4)';
      params.push(userId);
    }

    // Buscar sepultamentos do período
    const sepultamentosQuery = `
      SELECT 
        s.*,
        p.denominacao as produto_denominacao,
        p.nome as produto_nome
      FROM sepultamentos s
      JOIN produtos p ON p.codigo_estacao = s.codigo_estacao
      WHERE s.codigo_estacao = $1
        AND s.data_sepultamento >= $2
        AND s.data_sepultamento <= $3
        AND s.ativo = true
        ${whereClause}
      ORDER BY s.data_sepultamento DESC
    `;

    const sepultamentosResult = await query(sepultamentosQuery, params);

    // Buscar gavetas do produto
    const gavetasQuery = `
      SELECT COUNT(*) as total
      FROM gavetas g
      WHERE g.codigo_estacao = $1
        ${userType === 'cliente' ? 'AND g.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $2)' : ''}
    `;

    const gavetasParams = userType === 'cliente' ? [produto_id, userId] : [produto_id];
    const gavetasResult = await query(gavetasQuery, gavetasParams);

    // Buscar produto
    const produtoQuery = `
      SELECT *
      FROM produtos
      WHERE codigo_estacao = $1
        ${userType === 'cliente' ? 'AND codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $2)' : ''}
    `;

    const produtoParams = userType === 'cliente' ? [produto_id, userId] : [produto_id];
    const produtoResult = await query(produtoQuery, produtoParams);

    res.json({
      sepultamentos: sepultamentosResult.rows,
      produto: produtoResult.rows[0] || null,
      totalGavetas: parseInt(gavetasResult.rows[0]?.total || 0),
      periodo: {
        inicio: data_inicio,
        fim: data_fim
      }
    });

  } catch (error) {
    console.error('❌ Erro ao buscar dados de relatório:', error);
    res.status(500).json({
      error: 'Erro interno do servidor',
      message: 'Erro ao buscar dados de relatório'
    });
  }
});

module.exports = router;
