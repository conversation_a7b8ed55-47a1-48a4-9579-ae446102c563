import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Grid,
  Typography,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  LinearProgress,
  IconButton,
  Container,
  Paper,
  Skeleton
} from '@mui/material';
import {
  Business as BusinessIcon,
  LocationOn as LocationIcon,
  CalendarToday as CalendarIcon,
  Assessment as AssessmentIcon,
  Edit as EditIcon,
  Visibility as VisibilityIcon
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { produtoService } from '../services/api';

const BookSepultamentosPage = () => {
  const [produtos, setProdutos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { user, isAdmin } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    loadProdutos();
  }, []);

  const loadProdutos = async () => {
    try {
      setLoading(true);
      setError(null);

      // Usar API de estatísticas para obter dados completos
      const response = await produtoService.estatisticas();
      setProdutos(response.data || []);
    } catch (error) {
      console.error('Erro ao carregar produtos:', error);
      setError('Erro ao carregar produtos');
    } finally {
      setLoading(false);
    }
  };

  const handleViewSepultamentos = (produto) => {
    navigate(`/dashboard/book-sepultamentos/${produto.codigo_estacao}`, {
      state: { produto }
    });
  };

  const getStatusColor = (ativo) => {
    return ativo ? 'success' : 'error';
  };

  const getStatusText = (ativo) => {
    return ativo ? 'Ativa' : 'Inativa';
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Não informado';
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const ProductCardSkeleton = () => (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <CardContent sx={{ flexGrow: 1 }}>
        <Skeleton variant="text" width="60%" height={32} />
        <Skeleton variant="text" width="80%" height={24} sx={{ mt: 1 }} />
        <Skeleton variant="text" width="70%" height={24} sx={{ mt: 1 }} />
        <Box sx={{ mt: 2 }}>
          <Skeleton variant="rectangular" height={60} />
        </Box>
        <Box sx={{ mt: 2 }}>
          <Skeleton variant="rectangular" height={8} />
          <Skeleton variant="text" width="50%" height={20} sx={{ mt: 1 }} />
        </Box>
      </CardContent>
      <CardActions>
        <Skeleton variant="rectangular" width={120} height={36} />
      </CardActions>
    </Card>
  );

  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ py: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom sx={{ mb: 4 }}>
          Book de Sepultamentos
        </Typography>
        <Grid container spacing={3}>
          {[1, 2, 3, 4, 5, 6].map((item) => (
            <Grid item xs={12} sm={6} md={4} key={item}>
              <ProductCardSkeleton />
            </Grid>
          ))}
        </Grid>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="xl" sx={{ py: 4 }}>
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h6" color="error" gutterBottom>
            {error}
          </Typography>
          <Button variant="contained" onClick={loadProdutos} sx={{ mt: 2 }}>
            Tentar Novamente
          </Button>
        </Paper>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Book de Sepultamentos
        </Typography>
        <Typography variant="body1" color="text.secondary">
          {isAdmin() 
            ? 'Visualize todos os produtos e seus sepultamentos' 
            : 'Gerencie os sepultamentos dos seus produtos'
          }
        </Typography>
      </Box>

      {produtos.length === 0 ? (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <BusinessIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            Nenhum produto encontrado
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {isAdmin() 
              ? 'Nenhum produto foi cadastrado no sistema ainda.' 
              : 'Você não possui produtos associados à sua conta.'
            }
          </Typography>
        </Paper>
      ) : (
        <Grid container spacing={3} sx={{ maxWidth: '1400px', margin: '0 auto' }}>
          {produtos.map((produto) => (
            <Grid item xs={12} sm={6} md={4} key={produto.id}>
              <Card
                sx={{
                  height: '480px',  // Altura aumentada para acomodar mais conteúdo
                  width: '100%',    // Largura fixa
                  display: 'flex',
                  flexDirection: 'column',
                  transition: 'all 0.3s ease-in-out',
                  border: '1px solid',
                  borderColor: 'divider',
                  borderRadius: 2,
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 4,
                    borderColor: 'primary.main'
                  }
                }}
              >
                <CardContent sx={{ flexGrow: 1, p: 3 }}>
                  {/* Header do Card */}
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                    <BusinessIcon sx={{ mr: 1, color: 'primary.main', mt: 0.5 }} />
                    <Box sx={{ flexGrow: 1, mr: 1 }}>
                      <Typography
                        variant="h6"
                        component="h2"
                        sx={{
                          fontWeight: 600,
                          lineHeight: 1.3,
                          wordBreak: 'break-word',
                          hyphens: 'auto',
                          fontSize: '1.1rem'
                        }}
                      >
                        {produto.denominacao || produto.nome}
                      </Typography>
                    </Box>
                    <Chip
                      label={getStatusText(produto.ativo)}
                      color={getStatusColor(produto.ativo)}
                      size="small"
                    />
                  </Box>

                  {/* Informações do Produto */}
                  <Box sx={{ mb: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1.5 }}>
                      <LocationIcon sx={{ fontSize: 18, mr: 1, color: 'text.secondary' }} />
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{
                          wordBreak: 'break-word',
                          fontSize: '0.875rem'
                        }}
                      >
                        Código: {produto.codigo_estacao}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1.5 }}>
                      <CalendarIcon sx={{ fontSize: 18, mr: 1, color: 'text.secondary' }} />
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{ fontSize: '0.875rem' }}
                      >
                        Exumação: {produto.meses_para_exumar || 24} meses
                      </Typography>
                    </Box>
                  </Box>

                  {/* Estatísticas */}
                  <Box sx={{ mb: 3 }}>
                    <Grid container spacing={2}>
                      <Grid item xs={6}>
                        <Box sx={{
                          p: 1.5,
                          backgroundColor: 'grey.50',
                          borderRadius: 1,
                          textAlign: 'center'
                        }}>
                          <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                            Blocos
                          </Typography>
                          <Typography variant="h6" sx={{ fontWeight: 700, color: 'primary.main' }}>
                            {produto.total_blocos || 0}
                          </Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={6}>
                        <Box sx={{
                          p: 1.5,
                          backgroundColor: 'grey.50',
                          borderRadius: 1,
                          textAlign: 'center'
                        }}>
                          <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                            Total Gavetas
                          </Typography>
                          <Typography variant="h6" sx={{ fontWeight: 700, color: 'primary.main' }}>
                            {produto.total_gavetas || 0}
                          </Typography>
                        </Box>
                      </Grid>
                    </Grid>
                  </Box>

                  {/* Gráfico de Ocupação */}
                  <Box sx={{ mb: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1.5 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                        Ocupação
                      </Typography>
                      <Typography variant="body2" fontWeight="bold" color="primary.main">
                        {produto.percentual_ocupacao || 0}%
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={produto.percentual_ocupacao || 0}
                      sx={{
                        height: 10,
                        borderRadius: 5,
                        backgroundColor: 'grey.200',
                        '& .MuiLinearProgress-bar': {
                          backgroundColor: 'success.main',
                          borderRadius: 5
                        }
                      }}
                    />
                    <Typography
                      variant="caption"
                      color="text.secondary"
                      sx={{
                        mt: 1,
                        display: 'block',
                        fontSize: '0.75rem',
                        lineHeight: 1.4,
                        wordBreak: 'break-word'
                      }}
                    >
                      {produto.gavetas_ocupadas || 0} ocupadas de {produto.total_gavetas || 0} gavetas
                    </Typography>
                  </Box>
                </CardContent>

                <CardActions sx={{ p: 2, pt: 0 }}>
                  <Button
                    variant="contained"
                    fullWidth
                    startIcon={<VisibilityIcon />}
                    onClick={() => handleViewSepultamentos(produto)}
                    sx={{
                      textTransform: 'none',
                      fontWeight: 600
                    }}
                  >
                    Ver Sepultamentos
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}
    </Container>
  );
};

export default BookSepultamentosPage;
