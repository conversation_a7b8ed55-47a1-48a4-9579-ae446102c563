const { query } = require('../database/connection');

// Função para extrair referências hierárquicas dos dados
const extrairReferenciasHierarquicas = (contexto = {}, dadosNovos = {}, dadosAnteriores = {}) => {
  // Prioridade: contexto explícito > dados novos > dados anteriores
  const referencias = {
    codigo_cliente: contexto.codigo_cliente || dadosNovos?.codigo_cliente || dadosAnteriores?.codigo_cliente || null,
    codigo_estacao: contexto.codigo_estacao || dadosNovos?.codigo_estacao || dadosAnteriores?.codigo_estacao || null,
    codigo_bloco: contexto.codigo_bloco || dadosNovos?.codigo_bloco || dadosAnteriores?.codigo_bloco || null,
    codigo_sub_bloco: contexto.codigo_sub_bloco || dadosNovos?.codigo_sub_bloco || dadosAnteriores?.codigo_sub_bloco || null
  };

  return referencias;
};
const axios = require('axios');

const WEBHOOK_URL = 'https://webhook.vps.evo-eden.site/webhook/c8e01995-0591-4989-87a1-77d6d51a97e9';

// Função para gerar descrição automática baseada na ação
const gerarDescricao = (acao, tabelaAfetada) => {
  switch (acao) {
    case 'LOGIN':
      return 'Usuário realizou login no sistema';
    case 'LOGOUT':
      return 'Usuário realizou logout do sistema';
    case 'CREATE':
      if (tabelaAfetada === 'usuarios') {
        return 'Usuário realizou cadastro de novo usuário';
      } else if (tabelaAfetada === 'clientes') {
        return 'Usuário realizou cadastro de novo cliente';
      } else if (tabelaAfetada === 'produtos') {
        return 'Usuário realizou cadastro de novo produto';
      } else if (tabelaAfetada === 'sepultamentos') {
        return 'Usuário realizou novo sepultamento';
      } else if (tabelaAfetada === 'blocos') {
        return 'Usuário realizou cadastro de novo bloco';
      } else if (tabelaAfetada === 'sub_blocos') {
        return 'Usuário realizou cadastro de novo sub-bloco';
      } else {
        return `Usuário realizou cadastro em ${tabelaAfetada}`;
      }
    case 'EDIT':
    case 'UPDATE':
      if (tabelaAfetada === 'usuarios') {
        return 'Usuário realizou edição de usuário';
      } else if (tabelaAfetada === 'clientes') {
        return 'Usuário realizou edição de cliente';
      } else if (tabelaAfetada === 'produtos') {
        return 'Usuário realizou edição de produto';
      } else if (tabelaAfetada === 'sepultamentos') {
        return 'Usuário realizou edição de sepultamento';
      } else if (tabelaAfetada === 'blocos') {
        return 'Usuário realizou edição de bloco';
      } else if (tabelaAfetada === 'sub_blocos') {
        return 'Usuário realizou edição de sub-bloco';
      } else {
        return `Usuário realizou edição em ${tabelaAfetada}`;
      }
    case 'DELETE':
      if (tabelaAfetada === 'usuarios') {
        return 'Usuário realizou exclusão de usuário';
      } else if (tabelaAfetada === 'clientes') {
        return 'Usuário realizou exclusão de cliente';
      } else if (tabelaAfetada === 'produtos') {
        return 'Usuário realizou exclusão de produto';
      } else if (tabelaAfetada === 'sepultamentos') {
        return 'Usuário realizou exclusão de sepultamento';
      } else if (tabelaAfetada === 'blocos') {
        return 'Usuário realizou exclusão de bloco';
      } else if (tabelaAfetada === 'sub_blocos') {
        return 'Usuário realizou exclusão de sub-bloco';
      } else {
        return `Usuário realizou exclusão em ${tabelaAfetada}`;
      }
    case 'EXUMAR':
      return 'Usuário realizou exumação de sepultamento';
    case 'RECUPERACAO_SENHA':
      return 'Usuário solicitou recuperação de senha';
    default:
      return `Usuário realizou ação: ${acao}`;
  }
};

// Função para registrar ações no log de auditoria com referências hierárquicas
const logAction = async (usuarioId, acao, tabelaAfetada, registroId, dadosAnteriores, dadosNovos, ipAddress, userAgent, contextoHierarquico = {}) => {
  try {
    // Gerar descrição automática
    const descricao = gerarDescricao(acao, tabelaAfetada);

    // Extrair referências hierárquicas do contexto ou dos dados
    const { codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco } = extrairReferenciasHierarquicas(
      contextoHierarquico,
      dadosNovos,
      dadosAnteriores
    );

    await query(
      `INSERT INTO logs_auditoria
       (usuario_id, codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, acao, tabela_afetada, registro_id, dados_anteriores, dados_novos, ip_address, user_agent, descricao)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)`,
      [
        usuarioId,
        codigo_cliente,
        codigo_estacao,
        codigo_bloco,
        codigo_sub_bloco,
        acao,
        tabelaAfetada,
        registroId,
        dadosAnteriores ? JSON.stringify(dadosAnteriores) : null,
        dadosNovos ? JSON.stringify(dadosNovos) : null,
        ipAddress,
        userAgent,
        descricao
      ]
    );

    console.log(`📝 Log registrado: ${acao} em ${tabelaAfetada} por usuário ${usuarioId}`);
  } catch (error) {
    console.error('❌ Erro ao registrar log:', error);
  }
};

// Função para enviar dados para o webhook
const sendWebhook = async (data) => {
  try {
    const response = await axios.post(WEBHOOK_URL, {
      timestamp: new Date().toISOString(),
      source: 'Portal Evolution',
      ...data
    }, {
      timeout: 5000,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('📨 Webhook enviado com sucesso:', response.status);
    return true;
  } catch (error) {
    console.error('❌ Erro ao enviar webhook:', error.message);
    return false;
  }
};

// Função para registrar sepultamento e enviar webhook
const logSepultamento = async (usuarioId, acao, dadosSepultamento, ipAddress, userAgent) => {
  try {
    // Criar descrição da atividade conforme logica_logs.md
    let descricaoAtividade = '';

    switch (acao) {
      case 'CREATE':
        descricaoAtividade = `Criado novo sepultamento..
- Nome do Sepultado: ${dadosSepultamento.nome_sepultado}
- Cliente (codigo_cliente): ${dadosSepultamento.codigo_cliente}
- Código do Bloco: ${dadosSepultamento.codigo_bloco}
- Código do Sub-bloco: ${dadosSepultamento.codigo_sub_bloco}
- Número da Gaveta: ${dadosSepultamento.numero_gaveta}
- Data do Sepultamento: ${dadosSepultamento.data_sepultamento}
- Horário: ${dadosSepultamento.horario_sepultamento || 'Não informado'}
- Localização: ${dadosSepultamento.localizacao || 'Não informada'}
- Observações: ${dadosSepultamento.observacoes || 'Sem observações'}`;
        break;

      case 'EDIT':
        descricaoAtividade = `Editado sepultamento..
- Nome do Sepultado: ${dadosSepultamento.nome_sepultado}
- Cliente (codigo_cliente): ${dadosSepultamento.codigo_cliente}
- Código do Bloco: ${dadosSepultamento.codigo_bloco}
- Código do Sub-bloco: ${dadosSepultamento.codigo_sub_bloco}
- Número da Gaveta: ${dadosSepultamento.numero_gaveta}
- Data do Sepultamento: ${dadosSepultamento.data_sepultamento}
- Horário: ${dadosSepultamento.horario_sepultamento || 'Não informado'}
- Localização: ${dadosSepultamento.localizacao || 'Não informada'}
- Observações: ${dadosSepultamento.observacoes || 'Sem observações'}`;
        break;

      case 'DELETE':
        descricaoAtividade = `Deletado sepultamento..
- Nome do Sepultado: ${dadosSepultamento.nome_sepultado}
- Cliente (codigo_cliente): ${dadosSepultamento.codigo_cliente}
- Código do Bloco: ${dadosSepultamento.codigo_bloco}
- Código do Sub-bloco: ${dadosSepultamento.codigo_sub_bloco}
- Número da Gaveta: ${dadosSepultamento.numero_gaveta}
- Data do Sepultamento: ${dadosSepultamento.data_sepultamento}
- Horário: ${dadosSepultamento.horario_sepultamento || 'Não informado'}
- Localização: ${dadosSepultamento.localizacao || 'Não informada'}
- Observações: ${dadosSepultamento.observacoes || 'Sem observações'}`;
        break;
    }

    // Adicionar descrição aos dados novos
    const dadosComDescricao = {
      ...dadosSepultamento,
      descricao_atividade: descricaoAtividade
    };

    // Registrar no log local com referências hierárquicas
    const contexto = {
      codigo_cliente: dadosSepultamento.codigo_cliente,
      codigo_estacao: dadosSepultamento.codigo_estacao,
      codigo_bloco: dadosSepultamento.codigo_bloco,
      codigo_sub_bloco: dadosSepultamento.codigo_sub_bloco
    };

    await logAction(
      usuarioId,
      acao,
      'sepultamentos',
      dadosSepultamento.id,
      acao === 'EDIT' ? dadosSepultamento.dadosAnteriores : null,
      dadosComDescricao,
      ipAddress,
      userAgent,
      contexto
    );

    // Enviar para webhook
    const webhookData = {
      acao,
      tipo: 'sepultamento',
      dados: {
        id: dadosSepultamento.id,
        nome_sepultado: dadosSepultamento.nome_sepultado,
        codigo_cliente: dadosSepultamento.codigo_cliente,
        codigo_bloco: dadosSepultamento.codigo_bloco,
        codigo_sub_bloco: dadosSepultamento.codigo_sub_bloco,
        numero_gaveta: dadosSepultamento.numero_gaveta,
        data_sepultamento: dadosSepultamento.data_sepultamento,
        data_exumacao: dadosSepultamento.data_exumacao,
        localizacao: dadosSepultamento.localizacao,
        observacoes: dadosSepultamento.observacoes
      },
      usuario: {
        id: usuarioId
      },
      metadata: {
        ip_address: ipAddress,
        user_agent: userAgent
      }
    };

    await sendWebhook(webhookData);
  } catch (error) {
    console.error('❌ Erro ao registrar sepultamento:', error);
  }
};

// Função para registrar exumação e enviar webhook
const logExumacao = async (usuarioId, dadosExumacao, ipAddress, userAgent) => {
  try {
    // Criar descrição da atividade para exumação
    const descricaoAtividade = `Exumação realizada..
- Nome do Sepultado: ${dadosExumacao.nome_sepultado}
- Cliente (codigo_cliente): ${dadosExumacao.codigo_cliente}
- Código do Bloco: ${dadosExumacao.codigo_bloco}
- Código do Sub-bloco: ${dadosExumacao.codigo_sub_bloco}
- Número da Gaveta: ${dadosExumacao.numero_gaveta}
- Data do Sepultamento: ${dadosExumacao.data_sepultamento}
- Data da Exumação: ${dadosExumacao.data_exumacao}
- Observações da Exumação: ${dadosExumacao.observacoes || 'Sem observações'}`;

    // Adicionar descrição aos dados novos
    const dadosComDescricao = {
      ...dadosExumacao,
      descricao_atividade: descricaoAtividade
    };

    // Registrar no log local com referências hierárquicas
    const contexto = {
      codigo_cliente: dadosExumacao.codigo_cliente,
      codigo_estacao: dadosExumacao.codigo_estacao,
      codigo_bloco: dadosExumacao.codigo_bloco,
      codigo_sub_bloco: dadosExumacao.codigo_sub_bloco
    };

    await logAction(
      usuarioId,
      'EXUMAR',
      'sepultamentos',
      dadosExumacao.id,
      dadosExumacao.dadosAnteriores,
      dadosComDescricao,
      ipAddress,
      userAgent,
      contexto
    );

    // Enviar para webhook
    const webhookData = {
      acao: 'EXUMACAO',
      tipo: 'exumacao',
      dados: {
        id: dadosExumacao.id,
        nome_sepultado: dadosExumacao.nome_sepultado,
        codigo_cliente: dadosExumacao.codigo_cliente,
        codigo_bloco: dadosExumacao.codigo_bloco,
        codigo_sub_bloco: dadosExumacao.codigo_sub_bloco,
        numero_gaveta: dadosExumacao.numero_gaveta,
        data_sepultamento: dadosExumacao.data_sepultamento,
        data_exumacao: dadosExumacao.data_exumacao,
        localizacao: dadosExumacao.localizacao,
        observacoes: dadosExumacao.observacoes
      },
      usuario: {
        id: usuarioId
      },
      metadata: {
        ip_address: ipAddress,
        user_agent: userAgent
      }
    };

    await sendWebhook(webhookData);
  } catch (error) {
    console.error('❌ Erro ao registrar exumação:', error);
  }
};

// Função para registrar logs de produtos
const logProduto = async (usuarioId, acao, dadosProduto, dadosAnteriores, ipAddress, userAgent) => {
  try {
    // Criar descrição da atividade conforme logica_logs.md
    let descricaoAtividade = '';

    switch (acao) {
      case 'CREATE':
        descricaoAtividade = `Criado novo produto..
- Denominação (codigo_estacao): ${dadosProduto.denominacao} (${dadosProduto.codigo_estacao})
- Cliente (codigo_cliente): ${dadosProduto.codigo_cliente}
- Meses para Exumação: ${dadosProduto.meses_para_exumar} meses para exumar
- Observação: ${dadosProduto.observacao || 'Sem observação'}`;
        break;

      case 'EDIT':
        descricaoAtividade = `Editado produto..
- Denominação (codigo_estacao): ${dadosProduto.denominacao} (${dadosProduto.codigo_estacao})
- Cliente (codigo_cliente): ${dadosProduto.codigo_cliente}
- Meses para Exumação: ${dadosProduto.meses_para_exumar} meses para exumar
- Observação: ${dadosProduto.observacao || 'Sem observação'}`;
        break;

      case 'DELETE':
        descricaoAtividade = `Deletado produto..
- Denominação (codigo_estacao): ${dadosProduto.denominacao} (${dadosProduto.codigo_estacao})
- Cliente (codigo_cliente): ${dadosProduto.codigo_cliente}
- Meses para Exumação: ${dadosProduto.meses_para_exumar} meses para exumar
- Observação: ${dadosProduto.observacao || 'Sem observação'}`;
        break;
    }

    // Adicionar descrição aos dados novos
    const dadosComDescricao = {
      ...dadosProduto,
      descricao_atividade: descricaoAtividade
    };

    // Registrar no log local com referências hierárquicas
    const contexto = {
      codigo_cliente: dadosProduto.codigo_cliente,
      codigo_estacao: dadosProduto.codigo_estacao
    };

    await logAction(
      usuarioId,
      acao,
      'produtos',
      null, // Não usar ID, usar referências por códigos
      dadosAnteriores,
      dadosComDescricao,
      ipAddress,
      userAgent,
      contexto
    );

    console.log(`📝 Log de produto registrado: ${acao} - ${dadosProduto.denominacao}`);
  } catch (error) {
    console.error('❌ Erro ao registrar log de produto:', error);
  }
};

// Função para registrar logs de blocos
const logBloco = async (usuarioId, acao, dadosBloco, dadosAnteriores, ipAddress, userAgent) => {
  try {
    // Criar descrição da atividade
    let descricaoAtividade = '';

    switch (acao) {
      case 'CREATE':
        descricaoAtividade = `Criado novo bloco..
- Nome: ${dadosBloco.nome}
- Código do Bloco: ${dadosBloco.codigo_bloco}
- Descrição: ${dadosBloco.descricao || 'Sem descrição'}`;
        break;

      case 'EDIT':
        descricaoAtividade = `Editado bloco..
- Nome: ${dadosBloco.nome}
- Código do Bloco: ${dadosBloco.codigo_bloco}
- Descrição: ${dadosBloco.descricao || 'Sem descrição'}`;
        break;

      case 'DELETE':
        descricaoAtividade = `Deletado bloco..
- Nome: ${dadosBloco.nome}
- Código do Bloco: ${dadosBloco.codigo_bloco}
- Descrição: ${dadosBloco.descricao || 'Sem descrição'}`;
        break;
    }

    // Adicionar descrição aos dados novos
    const dadosComDescricao = {
      ...dadosBloco,
      descricao_atividade: descricaoAtividade
    };

    // Registrar no log local com referências hierárquicas
    const contexto = {
      codigo_cliente: dadosBloco.codigo_cliente,
      codigo_estacao: dadosBloco.codigo_estacao,
      codigo_bloco: dadosBloco.codigo_bloco
    };

    await logAction(
      usuarioId,
      acao,
      'blocos',
      null, // Não usar ID, usar referências por códigos
      dadosAnteriores,
      dadosComDescricao,
      ipAddress,
      userAgent,
      contexto
    );

    console.log(`📝 Log de bloco registrado: ${acao} - ${dadosBloco.nome}`);
  } catch (error) {
    console.error('❌ Erro ao registrar log de bloco:', error);
  }
};

// Função para registrar logs de sub-blocos
const logSubBloco = async (usuarioId, acao, dadosSubBloco, dadosAnteriores, ipAddress, userAgent) => {
  try {
    // Criar descrição da atividade
    let descricaoAtividade = '';

    switch (acao) {
      case 'CREATE':
        descricaoAtividade = `Criado novo sub-bloco..
- Nome: ${dadosSubBloco.nome}
- Código do Sub-bloco: ${dadosSubBloco.codigo_sub_bloco}
- Código do Bloco: ${dadosSubBloco.codigo_bloco}
- Denominação: ${dadosSubBloco.denominacao || 'Sem denominação'}`;
        break;

      case 'EDIT':
        descricaoAtividade = `Editado sub-bloco..
- Nome: ${dadosSubBloco.nome}
- Código do Sub-bloco: ${dadosSubBloco.codigo_sub_bloco}
- Código do Bloco: ${dadosSubBloco.codigo_bloco}
- Denominação: ${dadosSubBloco.denominacao || 'Sem denominação'}`;
        break;

      case 'DELETE':
        descricaoAtividade = `Deletado sub-bloco..
- Nome: ${dadosSubBloco.nome}
- Código do Sub-bloco: ${dadosSubBloco.codigo_sub_bloco}
- Código do Bloco: ${dadosSubBloco.codigo_bloco}
- Denominação: ${dadosSubBloco.denominacao || 'Sem denominação'}`;
        break;
    }

    // Adicionar descrição aos dados novos
    const dadosComDescricao = {
      ...dadosSubBloco,
      descricao_atividade: descricaoAtividade
    };

    // Registrar no log local com referências hierárquicas
    const contexto = {
      codigo_cliente: dadosSubBloco.codigo_cliente,
      codigo_estacao: dadosSubBloco.codigo_estacao,
      codigo_bloco: dadosSubBloco.codigo_bloco,
      codigo_sub_bloco: dadosSubBloco.codigo_sub_bloco
    };

    await logAction(
      usuarioId,
      acao,
      'sub_blocos',
      null, // Não usar ID, usar referências por códigos
      dadosAnteriores,
      dadosComDescricao,
      ipAddress,
      userAgent,
      contexto
    );

    console.log(`📝 Log de sub-bloco registrado: ${acao} - ${dadosSubBloco.nome}`);
  } catch (error) {
    console.error('❌ Erro ao registrar log de sub-bloco:', error);
  }
};

module.exports = {
  logAction,
  sendWebhook,
  logSepultamento,
  logExumacao,
  logProduto,
  logBloco,
  logSubBloco
};
