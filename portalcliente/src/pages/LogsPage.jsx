import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Alert,
  Pagination,
  CircularProgress,
  Grid,
  Paper,
  Chip,
} from '@mui/material';
import {
  Search as SearchIcon,
  Clear as ClearIcon,
  History as HistoryIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  AccessTime as TimeIcon,
  Info as InfoIcon,
  Description as DescriptionIcon,
  Compare as CompareIcon,
  Add as AddIcon,
  Remove as RemoveIcon,
  Delete as DeleteIcon,
  Storage as DataIcon,
} from '@mui/icons-material';
import { logService, clienteService, produtoService, usuarioService } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import { useAsyncOperation } from '../contexts/LoadingContext';
import Modal from '../components/Modal';
import {
  StandardContainer,
  StandardButton,
  StandardCard,
  StandardForm,
  StandardTextField,
  StandardSelect,
  StandardTable,
  StatusChip,
  FormGrid,
  FormGridItem,
  FormSection,
  ContentSection,
} from '../components/common';

const LogsPage = () => {
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({});
  const [filters, setFilters] = useState({
    codigo_cliente: '',
    codigo_estacao: '',
    usuario_id: '',
    data_inicio: '',
    data_fim: '',
    acao: '',
    busca_texto: '',
    page: 1,
    limit: 50
  });

  const [clientes, setClientes] = useState([]);
  const [produtos, setProdutos] = useState([]);
  const [usuarios, setUsuarios] = useState([]);
  const [selectedLog, setSelectedLog] = useState(null);
  const { executeAsync } = useAsyncOperation();
  const [showDetailModal, setShowDetailModal] = useState(false);

  const { user } = useAuth();

  useEffect(() => {
    loadLogs();
    loadFilterOptions();
  }, []);

  // Não aplicar filtros automaticamente - apenas quando clicar em BUSCAR
  // useEffect(() => {
  //   if (filters.data_inicio || filters.data_fim) {
  //     loadLogs();
  //   }
  // }, [filters.data_inicio, filters.data_fim]);

  const loadLogs = async () => {
    try {
      setLoading(true);

      // Preparar filtros para envio
      const filtrosParaEnvio = { ...filters };

      // Se não há filtros de data, carregar apenas as últimas 50 ocorrências
      if (!filters.data_inicio && !filters.data_fim) {
        filtrosParaEnvio.ultimas_50 = true;
      }

      console.log('📋 Carregando logs com filtros:', filtrosParaEnvio);

      if (filtrosParaEnvio.busca_texto) {
        console.log('🔍 Busca por texto ativa:', filtrosParaEnvio.busca_texto);
      }

      const response = await logService.listar(filtrosParaEnvio);
      setLogs(response.data.logs || response.data);
      setPagination(response.data.pagination || {});
    } catch (error) {
      console.error('Erro ao carregar logs:', error);
      alert('Erro ao carregar logs');
    } finally {
      setLoading(false);
    }
  };

  const loadFilterOptions = async () => {
    try {
      const clientesResponse = await clienteService.listar();
      setClientes(clientesResponse.data);

      const produtosResponse = await produtoService.listar();
      setProdutos(produtosResponse.data);

      const usuariosResponse = await usuarioService.listar();
      setUsuarios(usuariosResponse.data);
    } catch (error) {
      console.error('Erro ao carregar opções de filtro:', error);
    }
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value,
      page: 1
    }));
  };

  const handleBuscar = () => {
    console.log('🔍 Executando busca com filtros:', filters);
    loadLogs();
  };

  const handleLimparFiltros = () => {
    const filtrosLimpos = {
      codigo_cliente: '',
      codigo_estacao: '',
      usuario_id: '',
      data_inicio: '',
      data_fim: '',
      acao: '',
      busca_texto: '',
      page: 1,
      limit: 50
    };
    setFilters(filtrosLimpos);
    console.log('🧹 Filtros limpos, carregando logs padrão...');

    // Carregar logs padrão após limpar filtros
    setTimeout(() => {
      loadLogs();
    }, 100);
  };

  const handleApplyFilters = () => {
    loadLogs();
  };

  const handleClearFilters = () => {
    setFilters({
      codigo_cliente: '',
      codigo_estacao: '',
      usuario_id: '',
      data_inicio: '',
      data_fim: '',
      acao: '',
      page: 1,
      limit: 50
    });
  };

  const handlePageChange = (event, newPage) => {
    setFilters(prev => ({ ...prev, page: newPage }));
    setTimeout(loadLogs, 100);
  };

  // Verificar se há filtros ativos
  const temFiltrosAtivos = () => {
    return filters.codigo_cliente || filters.codigo_estacao || filters.usuario_id ||
           filters.data_inicio || filters.data_fim || filters.acao || filters.busca_texto;
  };

  // Contar filtros ativos
  const contarFiltrosAtivos = () => {
    let count = 0;
    if (filters.codigo_cliente) count++;
    if (filters.codigo_estacao) count++;
    if (filters.usuario_id) count++;
    if (filters.data_inicio) count++;
    if (filters.data_fim) count++;
    if (filters.acao) count++;
    if (filters.busca_texto) count++;
    return count;
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const brasilDate = new Date(date.getTime() - (3 * 60 * 60 * 1000));
    return brasilDate.toLocaleString('pt-BR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const getActionLabel = (action) => {
    const labels = {
      'CREATE': 'Cadastro',
      'EDIT': 'Edição',
      'DELETE': 'Deleção',
      'EXUMAR': 'Exumação'
    };
    return labels[action] || action;
  };

  const getActionColor = (action) => {
    const colors = {
      'CREATE': 'success',
      'EDIT': 'warning',
      'DELETE': 'error',
      'EXUMAR': 'secondary'
    };
    return colors[action] || 'default';
  };

  const handleLogClick = (log) => {
    setSelectedLog(log);
    setShowDetailModal(true);
  };

  const formatJsonData = (data) => {
    if (!data) return 'Nenhum dado disponível';
    try {
      return JSON.stringify(data, null, 2);
    } catch (error) {
      return 'Erro ao formatar dados';
    }
  };

  const formatJsonDataForComparison = (data) => {
    if (!data) return 'Nenhum dado disponível';
    if (typeof data === 'string') return data;

    try {
      const parsed = typeof data === 'object' ? data : JSON.parse(data);

      // Formatação especial para objetos com campos conhecidos
      if (parsed && typeof parsed === 'object') {
        let formatted = '';
        Object.entries(parsed).forEach(([key, value]) => {
          if (value !== null && value !== undefined && value !== '') {
            const displayKey = formatFieldName(key);
            const displayValue = formatFieldValue(key, value);
            formatted += `${displayKey}: ${displayValue}\n`;
          }
        });
        return formatted || JSON.stringify(parsed, null, 2);
      }

      return JSON.stringify(parsed, null, 2);
    } catch (e) {
      return data.toString();
    }
  };

  // Formatar nomes de campos
  const formatFieldName = (fieldName) => {
    const fieldMap = {
      'nome': 'Nome',
      'email': 'E-mail',
      'telefone': 'Telefone',
      'cpf': 'CPF',
      'cnpj': 'CNPJ',
      'endereco': 'Endereço',
      'cidade': 'Cidade',
      'estado': 'Estado',
      'cep': 'CEP',
      'data_nascimento': 'Data de Nascimento',
      'data_obito': 'Data de Óbito',
      'created_at': 'Criado em',
      'updated_at': 'Atualizado em',
      'valor': 'Valor',
      'preco': 'Preço',
      'status': 'Status',
      'ativo': 'Ativo',
      'denominacao': 'Denominação',
      'codigo_cliente': 'Código do Cliente',
      'codigo_estacao': 'Código da Estação',
      'codigo_bloco': 'Código do Bloco',
      'codigo_sub_bloco': 'Código do Sub-bloco'
    };

    return fieldMap[fieldName] || fieldName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  // Formatar valores de campos
  const formatFieldValue = (fieldName, value) => {
    if (value === null || value === undefined) return 'N/A';

    // Datas
    if (fieldName.includes('data') || fieldName.includes('created') || fieldName.includes('updated')) {
      return formatDateTime(value);
    }

    // Valores monetários
    if (fieldName.includes('valor') || fieldName.includes('preco')) {
      return formatCurrency(value);
    }

    // Booleanos
    if (typeof value === 'boolean') {
      return value ? 'Sim' : 'Não';
    }

    // CPF/CNPJ
    if (fieldName === 'cpf' && value) {
      return formatCPF(value);
    }
    if (fieldName === 'cnpj' && value) {
      return formatCNPJ(value);
    }

    // Telefone
    if (fieldName.includes('telefone') && value) {
      return formatPhone(value);
    }

    // CEP
    if (fieldName === 'cep' && value) {
      return formatCEP(value);
    }

    // Objetos
    if (typeof value === 'object') {
      return JSON.stringify(value, null, 2);
    }

    return value.toString();
  };

  // Formatadores específicos
  const formatCurrency = (value) => {
    if (!value || isNaN(value)) return value;
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const formatCPF = (cpf) => {
    if (!cpf) return cpf;
    const cleaned = cpf.replace(/\D/g, '');
    return cleaned.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
  };

  const formatCNPJ = (cnpj) => {
    if (!cnpj) return cnpj;
    const cleaned = cnpj.replace(/\D/g, '');
    return cleaned.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
  };

  const formatPhone = (phone) => {
    if (!phone) return phone;
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 11) {
      return cleaned.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
    } else if (cleaned.length === 10) {
      return cleaned.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
    }
    return phone;
  };

  const formatCEP = (cep) => {
    if (!cep) return cep;
    const cleaned = cep.replace(/\D/g, '');
    return cleaned.replace(/(\d{5})(\d{3})/, '$1-$2');
  };

  // Função para formatar data e hora
  const formatDateTime = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString('pt-BR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  // Função para buscar logs
  const handleSearch = () => {
    loadLogs();
  };

  if (loading && logs.length === 0) {
    return (
      <StandardContainer title="📋 Logs de Auditoria" subtitle="Histórico de atividades do sistema">
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
          <CircularProgress size={40} />
        </Box>
      </StandardContainer>
    );
  }

  return (
    <StandardContainer
      title="📋 Logs de Auditoria"
      subtitle="Histórico de atividades do sistema"
    >
      <ContentSection
        title={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            Filtros de Pesquisa
            {temFiltrosAtivos() && (
              <Chip
                label={`${contarFiltrosAtivos()} filtro${contarFiltrosAtivos() > 1 ? 's' : ''} ativo${contarFiltrosAtivos() > 1 ? 's' : ''}`}
                size="small"
                color="primary"
                variant="outlined"
              />
            )}
          </Box>
        }
        subtitle="Configure os filtros para encontrar logs específicos"
      >
        <StandardForm>
          <FormSection>
            <FormGrid spacing={3}>
              <FormGridItem xs={12} sm={6} md={4}>
                <StandardSelect
                  label="Cliente"
                  value={filters.codigo_cliente}
                  onChange={(e) => handleFilterChange('codigo_cliente', e.target.value)}
                  options={[
                    { value: '', label: 'Todos os clientes' },
                    ...clientes.map(cliente => ({
                      value: cliente.codigo_cliente,
                      label: `${cliente.nome_fantasia} (${cliente.codigo_cliente})`
                    }))
                  ]}
                  size="small"
                />
              </FormGridItem>

              <FormGridItem xs={12} sm={6} md={4}>
                <StandardSelect
                  label="Estação/Produto"
                  value={filters.codigo_estacao}
                  onChange={(e) => handleFilterChange('codigo_estacao', e.target.value)}
                  options={[
                    { value: '', label: 'Todas as estações' },
                    ...produtos.map(produto => ({
                      value: produto.codigo_estacao,
                      label: `${produto.denominacao} (${produto.codigo_estacao})`
                    }))
                  ]}
                  size="small"
                />
              </FormGridItem>

              <FormGridItem xs={12} sm={6} md={4}>
                <StandardSelect
                  label="Usuário"
                  value={filters.usuario_id}
                  onChange={(e) => handleFilterChange('usuario_id', e.target.value)}
                  options={[
                    { value: '', label: 'Todos os usuários' },
                    ...usuarios.map(usuario => ({
                      value: usuario.id,
                      label: `${usuario.nome} (${usuario.email})`
                    }))
                  ]}
                  size="small"
                />
              </FormGridItem>

              <FormGridItem xs={12} sm={6} md={3}>
                <StandardTextField
                  label="Data Início"
                  type="date"
                  value={filters.data_inicio}
                  onChange={(e) => handleFilterChange('data_inicio', e.target.value)}
                  InputLabelProps={{ shrink: true }}
                  size="small"
                />
              </FormGridItem>

              <FormGridItem xs={12} sm={6} md={3}>
                <StandardTextField
                  label="Data Fim"
                  type="date"
                  value={filters.data_fim}
                  onChange={(e) => handleFilterChange('data_fim', e.target.value)}
                  InputLabelProps={{ shrink: true }}
                  size="small"
                />
              </FormGridItem>

              <FormGridItem xs={12} sm={6} md={3}>
                <StandardSelect
                  label="Ação"
                  value={filters.acao}
                  onChange={(e) => handleFilterChange('acao', e.target.value)}
                  options={[
                    { value: '', label: 'Todas as ações' },
                    { value: 'CREATE', label: 'Criação' },
                    { value: 'UPDATE', label: 'Atualização' },
                    { value: 'DELETE', label: 'Exclusão' },
                    { value: 'LOGIN', label: 'Login' },
                    { value: 'LOGOUT', label: 'Logout' },
                  ]}
                  size="small"
                />
              </FormGridItem>

              <FormGridItem xs={12} md={6}>
                <StandardTextField
                  label="Busca por Texto"
                  placeholder="Digite qualquer texto para buscar nos detalhes dos logs..."
                  value={filters.busca_texto}
                  onChange={(e) => handleFilterChange('busca_texto', e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                    ),
                  }}
                  size="small"
                  helperText="Busca em descrições, dados anteriores e novos"
                />
              </FormGridItem>

              <FormGridItem xs={12} sm={6} md={3}>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <StandardButton
                    variant="contained"
                    startIcon={<SearchIcon />}
                    onClick={handleBuscar}
                    disabled={loading}
                    size="small"
                    sx={{ minWidth: 120 }}
                  >
                    {loading ? 'Buscando...' : 'BUSCAR'}
                  </StandardButton>
                  <StandardButton
                    variant="outlined"
                    startIcon={<ClearIcon />}
                    onClick={handleLimparFiltros}
                    disabled={loading}
                    size="small"
                    sx={{ minWidth: 120 }}
                  >
                    Limpar
                  </StandardButton>
                </Box>
              </FormGridItem>
            </FormGrid>
          </FormSection>
        </StandardForm>
      </ContentSection>

      {/* Seção de Resultados */}
      <ContentSection
        title="Histórico de Logs"
        subtitle={
          !filters.data_inicio && !filters.data_fim
            ? `Exibindo as últimas ${logs.length} ocorrências`
            : `Encontrados ${pagination.total || logs.length} registros`
        }
      >
        <StandardTable
          columns={[
            {
              id: 'created_at',
              label: 'Data e Hora',
              minWidth: 180,
              render: (value) => (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <TimeIcon sx={{ mr: 1, color: 'text.secondary', fontSize: 16 }} />
                  <Typography variant="body2">
                    {formatDateTime(value)}
                  </Typography>
                </Box>
              ),
            },
            {
              id: 'acao',
              label: 'Ação',
              minWidth: 120,
              render: (value) => {
                const getActionColor = (action) => {
                  switch (action) {
                    case 'CREATE': return 'success';
                    case 'UPDATE': return 'warning';
                    case 'DELETE': return 'error';
                    case 'LOGIN': return 'info';
                    case 'LOGOUT': return 'default';
                    default: return 'primary';
                  }
                };

                return (
                  <StatusChip
                    status={getActionLabel(value)}
                    color={getActionColor(value)}
                  />
                );
              },
            },
            {
              id: 'nome_usuario',
              label: 'Usuário',
              minWidth: 200,
              render: (value, row) => (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <PersonIcon sx={{ mr: 1, color: 'text.secondary', fontSize: 16 }} />
                  <Box>
                    <Typography variant="body2" fontWeight="medium">
                      {value || 'Sistema'}
                    </Typography>
                    {row.email_usuario && (
                      <Typography variant="caption" color="text.secondary">
                        {row.email_usuario}
                      </Typography>
                    )}
                  </Box>
                </Box>
              ),
            },
            {
              id: 'cliente_nome',
              label: 'Cliente',
              minWidth: 150,
              render: (value) => value ? (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <BusinessIcon sx={{ mr: 1, color: 'text.secondary', fontSize: 16 }} />
                  <Typography variant="body2">
                    {value}
                  </Typography>
                </Box>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  -
                </Typography>
              ),
            },
            {
              id: 'descricao',
              label: 'Descrição',
              minWidth: 300,
              render: (value) => (
                <Typography
                  variant="body2"
                  sx={{
                    maxWidth: 300,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  }}
                  title={value}
                >
                  {value}
                </Typography>
              ),
            },
            {
              id: 'actions',
              label: 'Ações',
              minWidth: 100,
              align: 'center',
              render: (_, row) => (
                <StandardButton
                  variant="outlined"
                  size="small"
                  startIcon={<InfoIcon />}
                  onClick={() => handleLogClick(row)}
                >
                  DETALHES
                </StandardButton>
              ),
            },
          ]}
          data={logs}
          loading={loading}
          emptyMessage={
            <Box sx={{ textAlign: 'center', py: 8 }}>
              <HistoryIcon sx={{ fontSize: '3rem', mb: 2, color: 'text.secondary' }} />
              <Typography variant="h5" sx={{ mb: 1, color: 'text.primary' }}>
                Nenhum log encontrado
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Ajuste os filtros para encontrar os logs desejados.
              </Typography>
            </Box>
          }
        />

        {/* Paginação */}
        {pagination.totalPages > 1 && (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
            <Pagination
              count={pagination.totalPages}
              page={pagination.page}
              onChange={handlePageChange}
              color="primary"
              size="large"
            />
          </Box>
        )}
      </ContentSection>


      {/* Modal de Detalhes */}
      <Modal
        isOpen={showDetailModal}
        onClose={() => setShowDetailModal(false)}
        title={`Detalhes do Log - ${selectedLog?.acao ? getActionLabel(selectedLog.acao) : ''}`}
        maxWidth="800px"
      >
        {selectedLog && (
          <Box sx={{ maxHeight: '70vh', overflow: 'auto' }}>
            {/* Header com informações principais */}
            <Box sx={{ mb: 3, p: 2, backgroundColor: 'primary.50', borderRadius: 1 }}>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} md={3}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <TimeIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Box>
                      <Typography variant="caption" color="text.secondary">
                        Data e Hora
                      </Typography>
                      <Typography variant="body2" fontWeight="medium">
                        {formatDateTime(selectedLog.created_at)}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={12} md={3}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <PersonIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Box>
                      <Typography variant="caption" color="text.secondary">
                        Usuário
                      </Typography>
                      <Typography variant="body2" fontWeight="medium">
                        {selectedLog.nome_usuario || 'Sistema'}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={12} md={3}>
                  <Box>
                    <Typography variant="caption" color="text.secondary">
                      Ação
                    </Typography>
                    <Box sx={{ mt: 0.5 }}>
                      <StatusChip
                        status={getActionLabel(selectedLog.acao)}
                        color={getActionColor(selectedLog.acao)}
                      />
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={12} md={3}>
                  <Box>
                    <Typography variant="caption" color="text.secondary">
                      Tabela
                    </Typography>
                    <Typography variant="body2" fontWeight="medium">
                      {selectedLog.tabela_afetada}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Box>

            {/* Informações Técnicas */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                <InfoIcon sx={{ mr: 1 }} />
                Informações Técnicas
              </Typography>
              <Grid container spacing={2}>
                {selectedLog.registro_id && (
                  <Grid item xs={12} md={4}>
                    <Typography variant="caption" color="text.secondary">
                      ID do Registro
                    </Typography>
                    <Typography variant="body2" fontWeight="medium">
                      {selectedLog.registro_id}
                    </Typography>
                  </Grid>
                )}
                {selectedLog.ip_address && (
                  <Grid item xs={12} md={4}>
                    <Typography variant="caption" color="text.secondary">
                      Endereço IP
                    </Typography>
                    <Typography variant="body2" fontWeight="medium">
                      {selectedLog.ip_address}
                    </Typography>
                  </Grid>
                )}
                {selectedLog.codigo_cliente && (
                  <Grid item xs={12} md={4}>
                    <Typography variant="caption" color="text.secondary">
                      Código Cliente
                    </Typography>
                    <Typography variant="body2" fontWeight="medium">
                      {selectedLog.codigo_cliente}
                    </Typography>
                  </Grid>
                )}
              </Grid>
            </Box>

            {/* Descrição */}
            {selectedLog.descricao && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                  <DescriptionIcon sx={{ mr: 1 }} />
                  Descrição da Ação
                </Typography>
                <Paper sx={{ p: 2, backgroundColor: 'grey.50', borderLeft: '4px solid', borderColor: 'primary.main' }}>
                  <Typography variant="body2">
                    {selectedLog.descricao}
                  </Typography>
                </Paper>
              </Box>
            )}

            {/* Comparação de Dados para UPDATE */}
            {selectedLog.acao === 'UPDATE' && selectedLog.dados_anteriores && selectedLog.dados_novos && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                  <CompareIcon sx={{ mr: 1 }} />
                  Comparação de Alterações
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2" sx={{ mb: 1, color: 'error.main', display: 'flex', alignItems: 'center' }}>
                      <RemoveIcon sx={{ mr: 1 }} />
                      Dados Anteriores
                    </Typography>
                    <Paper sx={{ p: 2, backgroundColor: 'error.50', maxHeight: 400, overflow: 'auto', border: '1px solid', borderColor: 'error.200' }}>
                      <Typography variant="body2" component="pre" sx={{ fontSize: '0.75rem', fontFamily: 'monospace', whiteSpace: 'pre-wrap' }}>
                        {formatJsonDataForComparison(selectedLog.dados_anteriores)}
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2" sx={{ mb: 1, color: 'success.main', display: 'flex', alignItems: 'center' }}>
                      <AddIcon sx={{ mr: 1 }} />
                      Dados Novos
                    </Typography>
                    <Paper sx={{ p: 2, backgroundColor: 'success.50', maxHeight: 400, overflow: 'auto', border: '1px solid', borderColor: 'success.200' }}>
                      <Typography variant="body2" component="pre" sx={{ fontSize: '0.75rem', fontFamily: 'monospace', whiteSpace: 'pre-wrap' }}>
                        {formatJsonDataForComparison(selectedLog.dados_novos)}
                      </Typography>
                    </Paper>
                  </Grid>
                </Grid>
              </Box>
            )}

            {/* Dados para CREATE */}
            {selectedLog.acao === 'CREATE' && selectedLog.dados_novos && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                  <AddIcon sx={{ mr: 1, color: 'success.main' }} />
                  Dados Criados
                </Typography>
                <Paper sx={{ p: 2, backgroundColor: 'success.50', maxHeight: 400, overflow: 'auto', border: '1px solid', borderColor: 'success.200' }}>
                  <Typography variant="body2" component="pre" sx={{ fontSize: '0.75rem', fontFamily: 'monospace', whiteSpace: 'pre-wrap' }}>
                    {formatJsonDataForComparison(selectedLog.dados_novos)}
                  </Typography>
                </Paper>
              </Box>
            )}

            {/* Dados para DELETE */}
            {selectedLog.acao === 'DELETE' && selectedLog.dados_anteriores && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                  <DeleteIcon sx={{ mr: 1, color: 'error.main' }} />
                  Dados Excluídos
                </Typography>
                <Paper sx={{ p: 2, backgroundColor: 'error.50', maxHeight: 400, overflow: 'auto', border: '1px solid', borderColor: 'error.200' }}>
                  <Typography variant="body2" component="pre" sx={{ fontSize: '0.75rem', fontFamily: 'monospace', whiteSpace: 'pre-wrap' }}>
                    {formatJsonDataForComparison(selectedLog.dados_anteriores)}
                  </Typography>
                </Paper>
              </Box>
            )}

            {/* Outros tipos de dados */}
            {!['CREATE', 'UPDATE', 'DELETE'].includes(selectedLog.acao) && (selectedLog.dados_novos || selectedLog.dados_anteriores) && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                  <DataIcon sx={{ mr: 1 }} />
                  Dados da Ação
                </Typography>
                {selectedLog.dados_novos && (
                  <Paper sx={{ p: 2, backgroundColor: 'info.50', maxHeight: 400, overflow: 'auto', border: '1px solid', borderColor: 'info.200' }}>
                    <Typography variant="body2" component="pre" sx={{ fontSize: '0.75rem', fontFamily: 'monospace', whiteSpace: 'pre-wrap' }}>
                      {formatJsonDataForComparison(selectedLog.dados_novos)}
                    </Typography>
                  </Paper>
                )}
              </Box>
            )}
          </Box>
        )}
      </Modal>
    </StandardContainer>
  );
};

export default LogsPage;
