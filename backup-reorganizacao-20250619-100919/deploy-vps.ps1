# ===================================
# PORTAL EVOLUTION - DEPLOY VPS
# ===================================
# Script para deploy na VPS com senha SSH
# Execute: .\deploy-vps.ps1

param(
    [switch]$Force,      # Forcar deploy sem confirmacao
    [string]$Message = "Deploy automatico via script"  # Mensagem do commit
)

# Funcao para log
function Write-Log {
    param([string]$Message, [string]$Color = "Green")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] $Message" -ForegroundColor $Color
}

function Write-Info {
    param([string]$Message)
    Write-Log $Message "Cyan"
}

function Write-Success {
    param([string]$Message)
    Write-Log $Message "Green"
}

function Write-Warning {
    param([string]$Message)
    Write-Log $Message "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-Log $Message "Red"
}

# Definir diretorio do script
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $scriptPath

Write-Host ""
Write-Host "PORTAL EVOLUTION - DEPLOY VPS" -ForegroundColor Cyan
Write-Host "==============================" -ForegroundColor Cyan
Write-Host ""

# Verificar se esta no diretorio correto
if (-not (Test-Path "configuracao.env")) {
    Write-Error "Arquivo configuracao.env nao encontrado!"
    exit 1
}

# Carregar configuracoes
Write-Info "Carregando configuracoes..."
$config = @{}
Get-Content "configuracao.env" | ForEach-Object {
    if ($_ -match "^([^#][^=]+)=(.*)$") {
        $config[$matches[1].Trim()] = $matches[2].Trim()
    }
}

$VPS_HOST = $config["VPS_HOST"]
$VPS_USER = $config["VPS_USER"]
$VPS_PASSWORD = $config["VPS_PASSWORD"]
$DOMAIN = $config["DOMAIN"]

Write-Success "Configuracoes carregadas:"
Write-Host "   VPS: $VPS_USER@$VPS_HOST" -ForegroundColor White
Write-Host "   Dominio: $DOMAIN" -ForegroundColor White
Write-Host ""

# Verificar Git
Write-Info "Verificando Git..."
try {
    $gitStatus = git status --porcelain 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Git nao encontrado ou nao e um repositorio Git!"
        exit 1
    }
    Write-Success "Git OK"
} catch {
    Write-Error "Erro ao verificar Git: $_"
    exit 1
}

# Verificar se ha alteracoes
$hasChanges = $false
if ($gitStatus) {
    $hasChanges = $true
    Write-Warning "Alteracoes detectadas:"
    $gitStatus | ForEach-Object { Write-Host "   $_" -ForegroundColor Yellow }
    Write-Host ""
}

# Se ha alteracoes e nao e forcado, perguntar sobre commit
if ($hasChanges -and -not $Force) {
    Write-Host ""
    Write-Warning "Ha alteracoes nao commitadas."
    Write-Host "Deseja fazer commit e push antes do deploy? (s/N): " -NoNewline -ForegroundColor Yellow
    $commitChoice = Read-Host
    
    if ($commitChoice -eq "s" -or $commitChoice -eq "S") {
        Write-Info "Fazendo commit das alteracoes..."
        
        # Adicionar todos os arquivos
        git add .
        
        # Fazer commit
        git commit -m $Message
        
        # Fazer push
        Write-Info "Fazendo push para o repositorio..."
        git push origin master
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Codigo enviado para o repositorio!"
        } else {
            Write-Error "Erro ao fazer push!"
            exit 1
        }
    }
} elseif ($hasChanges -and $Force) {
    Write-Info "Modo forcado - fazendo commit automatico..."
    git add .
    git commit -m $Message
    git push origin master
    Write-Success "Codigo enviado para o repositorio!"
}

Write-Host ""
Write-Info "Iniciando deploy na VPS..."

# Criar script temporario para executar na VPS
$tempScript = "deploy_temp_$(Get-Date -Format 'yyyyMMdd_HHmmss').sh"

$deployCommands = @"
#!/bin/bash
set -e

echo "=== PORTAL EVOLUTION - DEPLOY AUTOMATICO ==="
echo "Iniciado em: \$(date)"
echo ""

# Navegar para o diretorio do projeto
cd /home/<USER>/portalevo/portalcliente || {
    echo "ERRO: Diretorio /home/<USER>/portalevo/portalcliente nao encontrado!"
    echo "Projeto deve estar clonado em /home/<USER>/portalevo"
    exit 1
}

echo "Diretorio atual: \$(pwd)"

# Atualizar codigo
echo "Atualizando codigo via git pull..."
git pull origin master || {
    echo "ERRO: Falha no git pull"
    exit 1
}

echo "Codigo atualizado com sucesso!"

# Verificar se arquivos necessarios existem
echo "Verificando arquivos necessarios..."
if [ ! -f "docker-compose.prod.yml" ]; then
    echo "ERRO: docker-compose.prod.yml nao encontrado!"
    exit 1
fi

echo "Arquivos necessarios encontrados!"

# Tornar scripts executaveis
chmod +x *.sh

# Verificar se Docker esta funcionando
echo "Verificando Docker..."
if ! docker info > /dev/null 2>&1; then
    echo "ERRO: Docker nao esta acessivel para o usuario vscode"
    echo "Tentando usar sudo para comandos Docker..."

    # Tentar com sudo
    if sudo docker info > /dev/null 2>&1; then
        echo "Docker acessivel com sudo"
        DOCKER_CMD="sudo docker"
    else
        echo "ERRO: Docker nao esta funcionando nem com sudo"
        exit 1
    fi
else
    echo "Docker acessivel diretamente"
    DOCKER_CMD="docker"
fi

# Verificar se Docker Swarm esta ativo
echo "Verificando Docker Swarm..."
if ! \$DOCKER_CMD info | grep -q "Swarm: active"; then
    echo "Docker Swarm nao esta ativo. Inicializando..."
    \$DOCKER_CMD swarm init --advertise-addr \$(hostname -I | awk '{print \$1}') || true
fi

# Verificar se rede redeinterna existe
if ! \$DOCKER_CMD network ls | grep -q "redeinterna"; then
    echo "Rede 'redeinterna' nao encontrada. Criando..."
    \$DOCKER_CMD network create --driver overlay --attachable redeinterna || true
fi

# Parar stack se existir
echo "Removendo stack anterior (se existir)..."
\$DOCKER_CMD stack rm portal-evolution 2>/dev/null || true

# Aguardar remocao completa
echo "Aguardando remocao completa..."
sleep 15

# Build das imagens
echo "Construindo imagens..."

# Build backend
echo "Construindo backend..."
if \$DOCKER_CMD build -f Dockerfile.backend -t portal-evolution-backend:latest .; then
    echo "Backend construido com sucesso"
else
    echo "ERRO: Falha ao construir backend"
    exit 1
fi

# Build frontend
echo "Construindo frontend..."
if \$DOCKER_CMD build -f Dockerfile.frontend -t portal-evolution-frontend:latest .; then
    echo "Frontend construido com sucesso"
else
    echo "ERRO: Falha ao construir frontend"
    exit 1
fi

# Deploy da stack
echo "Fazendo deploy da stack..."
if \$DOCKER_CMD stack deploy -c docker-compose.prod.yml portal-evolution; then
    echo "Stack deployada com sucesso"
else
    echo "ERRO: Falha no deploy da stack"
    exit 1
fi

# Aguardar servicos iniciarem
echo "Aguardando servicos iniciarem..."
sleep 45

# Verificar status dos servicos
echo "Verificando status dos servicos..."
\$DOCKER_CMD stack services portal-evolution

echo ""
echo "=== DEPLOY CONCLUIDO ==="
echo "Finalizado em: \$(date)"
"@

# Salvar script temporario (sem BOM)
$deployCommands | Out-File -FilePath $tempScript -Encoding ASCII

Write-Info "Script de deploy criado: $tempScript"

# Funcao para executar comando SSH com senha
function Invoke-SSHCommand {
    param(
        [string]$Command,
        [string]$Host,
        [string]$User,
        [string]$Password
    )
    
    # Criar script expect temporario para lidar com senha
    $expectScript = @"
spawn ssh -o StrictHostKeyChecking=no $User@$Host "$Command"
expect "password:"
send "$Password\r"
expect eof
"@
    
    $expectFile = "ssh_temp_$(Get-Date -Format 'yyyyMMdd_HHmmss').exp"
    $expectScript | Out-File -FilePath $expectFile -Encoding ASCII
    
    try {
        # Tentar usar expect se disponivel
        $result = expect $expectFile 2>$null
        if ($LASTEXITCODE -eq 0) {
            Remove-Item $expectFile -Force
            return $true
        }
    } catch {
        # Se expect nao estiver disponivel, usar metodo alternativo
    }
    
    Remove-Item $expectFile -Force -ErrorAction SilentlyContinue
    
    # Metodo alternativo: usar plink se disponivel
    try {
        $result = echo $Password | plink -ssh -batch -pw $Password $User@$Host $Command 2>$null
        if ($LASTEXITCODE -eq 0) {
            return $true
        }
    } catch {
        # Plink nao disponivel
    }
    
    # Ultimo recurso: tentar SSH interativo
    Write-Warning "Metodos automaticos nao disponiveis. Sera necessario inserir senha manualmente."
    $result = ssh -o StrictHostKeyChecking=no $User@$Host $Command
    return ($LASTEXITCODE -eq 0)
}

# Testar conectividade
Write-Info "Testando conectividade SSH..."
Write-Host "Tentando conectar em $VPS_USER@$VPS_HOST..." -ForegroundColor Gray

# Metodo simples: usar SSH interativo
Write-Warning "ATENCAO: Sera solicitada a senha SSH durante o processo."
Write-Host "Senha da VPS: $VPS_PASSWORD" -ForegroundColor Yellow
Write-Host ""

# Copiar script para VPS
Write-Info "Copiando script de deploy para VPS..."
Write-Host "Executando: scp $tempScript $VPS_USER@${VPS_HOST}:/tmp/" -ForegroundColor Gray

$scpResult = scp -o StrictHostKeyChecking=no $tempScript $VPS_USER@${VPS_HOST}:/tmp/

if ($LASTEXITCODE -eq 0) {
    Write-Success "Script copiado com sucesso!"
    
    # Executar script na VPS
    Write-Info "Executando deploy na VPS..."
    Write-Host "Executando: ssh $VPS_USER@$VPS_HOST 'bash /tmp/$tempScript'" -ForegroundColor Gray
    
    $sshResult = ssh -o StrictHostKeyChecking=no $VPS_USER@$VPS_HOST "bash /tmp/$tempScript"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Deploy executado com sucesso!"
        
        # Limpar arquivo temporario na VPS
        ssh -o StrictHostKeyChecking=no $VPS_USER@$VPS_HOST "rm -f /tmp/$tempScript" 2>$null
        
        # Aguardar aplicacao inicializar
        Write-Info "Aguardando aplicacao inicializar..."
        Start-Sleep -Seconds 45
        
        # Testar aplicacao
        Write-Info "Testando aplicacao..."
        try {
            $response = Invoke-WebRequest -Uri "https://$DOMAIN" -TimeoutSec 30 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Success "Aplicacao funcionando!"
                Write-Host ""
                Write-Host "DEPLOY CONCLUIDO COM SUCESSO!" -ForegroundColor Green
                Write-Host "=============================" -ForegroundColor Green
                Write-Host ""
                Write-Host "ACESSO A APLICACAO:" -ForegroundColor Cyan
                Write-Host "   Portal: https://$DOMAIN" -ForegroundColor White
                Write-Host "   API: https://$DOMAIN/api/health" -ForegroundColor White
                Write-Host ""
                Write-Host "CREDENCIAIS:" -ForegroundColor Cyan
                Write-Host "   Admin: <EMAIL> / adminnbr5410!" -ForegroundColor White
                Write-Host "   Cliente: <EMAIL> / 54321" -ForegroundColor White
                Write-Host ""
                
                # Abrir navegador
                Write-Info "Abrindo navegador..."
                Start-Process "https://$DOMAIN"
                
            } else {
                Write-Warning "Aplicacao deployada mas retornou status: $($response.StatusCode)"
            }
        } catch {
            Write-Warning "Aplicacao deployada mas nao foi possivel testar: $_"
            Write-Host "Tente acessar manualmente: https://$DOMAIN" -ForegroundColor Yellow
        }
        
    } else {
        Write-Error "Erro durante execucao do deploy na VPS!"
        Write-Host "Verifique os logs na VPS:" -ForegroundColor Yellow
        Write-Host "   ssh $VPS_USER@$VPS_HOST" -ForegroundColor Yellow
        Write-Host "   cd /root/portalevo/portalcliente" -ForegroundColor Yellow
        Write-Host "   docker stack services portal-evolution" -ForegroundColor Yellow
    }
    
} else {
    Write-Error "Erro ao copiar script para VPS!"
    Write-Host "Verifique:" -ForegroundColor Yellow
    Write-Host "   - Conectividade de rede" -ForegroundColor Yellow
    Write-Host "   - Credenciais SSH" -ForegroundColor Yellow
    Write-Host "   - Espaco em disco na VPS" -ForegroundColor Yellow
}

# Limpar arquivo temporario local
Remove-Item $tempScript -Force -ErrorAction SilentlyContinue

Write-Host ""
Write-Info "Deploy finalizado!"
