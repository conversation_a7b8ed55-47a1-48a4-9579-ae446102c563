#!/bin/bash

# Script para configurar VPS para o Portal Evolution
# Execute na VPS como root: bash configurar-vps.sh

set -e

echo "🚀 Configurando VPS para Portal Evolution..."

# Cores
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
    exit 1
}

# Verificar se está rodando como root
if [[ $EUID -ne 0 ]]; then
   error "Execute este script como root (sudo bash configurar-vps.sh)"
fi

# Atualizar sistema
log "Atualizando sistema..."
apt update && apt upgrade -y

# Instalar dependências
log "Instalando dependências..."
apt install -y \
    curl \
    wget \
    git \
    unzip \
    software-properties-common \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release \
    ufw \
    htop \
    nano

# Configurar firewall básico
log "Configurando firewall..."
ufw --force reset
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp
ufw --force enable

# Instalar Docker
log "Instalando Docker..."
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
apt update
apt install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

# Configurar Docker
log "Configurando Docker..."
systemctl enable docker
systemctl start docker

# Criar diretório do projeto
log "Criando diretório do projeto..."
mkdir -p /opt/portal-evolution
cd /opt/portal-evolution

# Configurar Git para usar token
log "Configurando Git..."
git config --global credential.helper store

# Criar diretório temporário para download
log "Baixando código do repositório..."
if [[ ! -d ".git" ]]; then
    # Baixar como ZIP para evitar problemas de autenticação
    wget -O repo.zip https://github.com/MauricioFilh/portalevo/archive/refs/heads/master.zip
    unzip -q repo.zip
    mv portalevo-master/* .
    mv portalevo-master/.* . 2>/dev/null || true
    rm -rf portalevo-master repo.zip

    # Inicializar git
    git init
    git remote add origin https://github.com/MauricioFilh/portalevo.git
    git add .
    git commit -m "Initial setup on VPS"

    warn "Repositório baixado como ZIP. Para usar git pull, configure token:"
    warn "git config --global credential.helper store"
    warn "git pull origin master (digite seu token quando solicitado)"
else
    log "Repositório já existe, tentando atualizar..."
    git pull origin master || warn "Erro ao fazer git pull. Configure token se necessário."
fi

# Navegar para diretório do portal
cd portalcliente

# Tornar scripts executáveis
chmod +x *.sh

# Criar serviço systemd para auto-start
log "Configurando auto-start..."
cat > /etc/systemd/system/portal-evolution.service << 'EOF'
[Unit]
Description=Portal Evolution
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/opt/portal-evolution/portalcliente
ExecStart=/usr/bin/docker-compose -f docker-compose.prod.yml --env-file configuracao.env up -d
ExecStop=/usr/bin/docker-compose -f docker-compose.prod.yml down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF

# Habilitar serviço
systemctl daemon-reload
systemctl enable portal-evolution.service

# Configurar logrotate
log "Configurando logrotate..."
cat > /etc/logrotate.d/portal-evolution << 'EOF'
/opt/portal-evolution/portalcliente/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
}
EOF

# Criar script de backup
log "Criando script de backup..."
cat > /opt/portal-evolution/backup.sh << 'EOF'
#!/bin/bash
# Script de backup automático

BACKUP_DIR="/opt/portal-evolution/backups"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# Backup do banco de dados
docker exec portal_database pg_dump -U portal_user portal_evolution > $BACKUP_DIR/db_backup_$DATE.sql

# Manter apenas últimos 7 backups
find $BACKUP_DIR -name "db_backup_*.sql" -mtime +7 -delete

echo "Backup concluído: $BACKUP_DIR/db_backup_$DATE.sql"
EOF

chmod +x /opt/portal-evolution/backup.sh

# Configurar cron para backup diário
log "Configurando backup automático..."
(crontab -l 2>/dev/null; echo "0 2 * * * /opt/portal-evolution/backup.sh >> /var/log/portal-backup.log 2>&1") | crontab -

# Configurar monitoramento básico
cat > /opt/portal-evolution/monitor.sh << 'EOF'
#!/bin/bash
# Script de monitoramento

cd /opt/portal-evolution/portalcliente

echo "=== STATUS DOS CONTAINERS ==="
docker-compose -f docker-compose.prod.yml ps

echo "=== SAÚDE DA APLICAÇÃO ==="
curl -s http://localhost/health || echo "Frontend: ERRO"
curl -s http://localhost/api/health || echo "Backend: ERRO"

echo "=== USO DE RECURSOS ==="
docker stats --no-stream

echo "=== ESPAÇO EM DISCO ==="
df -h
EOF

chmod +x /opt/portal-evolution/monitor.sh

log "✅ Configuração da VPS concluída!"
log ""
log "📋 Próximos passos:"
log "1. Edite o arquivo configuracao.env em /opt/portal-evolution/portalcliente/"
log "2. Execute: cd /opt/portal-evolution/portalcliente && bash deploy.sh"
log ""
log "🔧 Comandos úteis:"
log "   Iniciar: systemctl start portal-evolution"
log "   Parar: systemctl stop portal-evolution"
log "   Status: systemctl status portal-evolution"
log "   Logs: journalctl -u portal-evolution -f"
log "   Monitor: bash /opt/portal-evolution/monitor.sh"
log ""
log "📁 Diretórios importantes:"
log "   Projeto: /opt/portal-evolution/portalcliente/"
log "   Backups: /opt/portal-evolution/backups/"
log "   Logs: /var/log/portal-*.log"
log ""
warn "⚠️  Configure o arquivo configuracao.env antes de fazer o deploy!"
log ""
log "🎉 VPS pronta para o Portal Evolution!"
