#!/bin/bash

# Script para atualizar o sistema via git pull
# Execute: bash atualizar.sh

set -e

echo "🔄 Atualizando Portal Evolution..."

# Cores para output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
    exit 1
}

# Verificar se está no diretório correto
if [[ ! -f "configuracao.env" ]]; then
    error "Execute este script no diretório portalcliente"
fi

# Carregar configurações
source configuracao.env

log "Atualizando código via git pull..."

# Fazer backup dos containers atuais
log "Fazendo backup dos containers atuais..."
docker-compose -f docker-compose.prod.yml ps > backup_containers_$(date +%Y%m%d_%H%M%S).txt

# Atualizar código
log "Atualizando código..."

# Tentar git pull primeiro
if git pull origin master 2>/dev/null; then
    log "Git pull executado com sucesso"
else
    warn "Git pull falhou, usando método alternativo..."

    # Método alternativo: baixar como ZIP
    cd /opt/portal-evolution

    # Backup da configuração
    cp portalcliente/configuracao.env configuracao.env.backup

    # Baixar nova versão
    wget -O repo.zip https://github.com/MauricioFilh/portalevo/archive/refs/heads/master.zip
    unzip -q repo.zip

    # Atualizar código
    rm -rf portalcliente
    mv portalevo-master/portalcliente .
    rm -rf portalevo-master repo.zip

    # Restaurar configuração
    mv configuracao.env.backup portalcliente/configuracao.env

    # Tornar scripts executáveis
    chmod +x portalcliente/*.sh

    cd portalcliente
    log "Código atualizado via download ZIP"
fi

# Reconstruir e reiniciar containers
log "Reconstruindo containers..."
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml --env-file configuracao.env up -d --build

# Aguardar containers iniciarem
log "Aguardando containers iniciarem..."
sleep 30

# Verificar status
log "Verificando status..."
docker-compose -f docker-compose.prod.yml ps

# Testar saúde
log "Testando saúde da aplicação..."
if curl -f http://localhost/health > /dev/null 2>&1; then
    log "✅ Frontend OK"
else
    warn "❌ Frontend com problema"
fi

if curl -f http://localhost/api/health > /dev/null 2>&1; then
    log "✅ Backend OK"
else
    warn "❌ Backend com problema"
fi

# Limpar imagens antigas
log "Limpando imagens antigas..."
docker image prune -f

log "🎉 Atualização concluída!"
log ""
log "📋 Para verificar logs:"
log "   docker-compose -f docker-compose.prod.yml logs -f"
log ""
log "📋 Para verificar status:"
log "   docker-compose -f docker-compose.prod.yml ps"
