const express = require('express');
const { query } = require('../database/connection');
const { logSubBloco } = require('../utils/logger');

const router = express.Router();

// Criar novo sub-bloco
router.post('/:codigo_cliente/:codigo_estacao/:codigo_bloco/sub-blocos', async (req, res) => {
  if (req.user.tipo_usuario !== 'admin') {
    return res.status(403).json({ error: 'Acesso negado. Apenas administradores.' });
  }

  try {
    const { codigo_cliente, codigo_estacao, codigo_bloco } = req.params;
    const { codigo_sub_bloco, denominacao, numeracao_gavetas } = req.body;

    console.log('🔍 Dados recebidos para criação de sub-bloco:', {
      codigo_cliente,
      codigo_estacao,
      codigo_bloco,
      codigo_sub_bloco,
      denominacao,
      numeracao_gavetas
    });

    if (!codigo_sub_bloco || !denominacao) {
      return res.status(400).json({ error: 'Código do sub-bloco e denominação são obrigatórios' });
    }

    // Verificar se o sub-bloco já existe
    const existingSubBloco = await query(
      'SELECT codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco FROM sub_blocos WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 AND codigo_sub_bloco = $4',
      [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco]
    );

    if (existingSubBloco.rows.length > 0) {
      return res.status(400).json({ error: 'Código do sub-bloco já existe para este bloco' });
    }

    // Criar sub-bloco
    const result = await query(`
      INSERT INTO sub_blocos (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, denominacao, nome, descricao, ativo)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, denominacao, denominacao, denominacao, true]);

    const novoSubBloco = result.rows[0];
    console.log('✅ Sub-bloco inserido no banco de dados:', novoSubBloco);

    // Se foi fornecida numeração de gavetas, criar as numerações e gavetas
    if (numeracao_gavetas && Array.isArray(numeracao_gavetas) && numeracao_gavetas.length > 0) {
      console.log('🔢 Criando numerações de gavetas:', numeracao_gavetas);
      for (const range of numeracao_gavetas) {
        const { numero_inicio, numero_fim } = range;
        
        if (numero_inicio && numero_fim && numero_inicio <= numero_fim) {
          // Inserir numeração
          await query(`
            INSERT INTO numeracoes_gavetas (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_inicio, numero_fim, ativo)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
          `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_inicio, numero_fim, true]);

          // Criar gavetas individuais
          for (let numero = numero_inicio; numero <= numero_fim; numero++) {
            await query(`
              INSERT INTO gavetas (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta, posicao_x, posicao_y, disponivel, ativo)
              VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero, numero, 1, true, true]);
          }
        }
      }
    }

    // Registrar log da criação
    await logSubBloco(
      req.user.id,
      'CREATE',
      novoSubBloco,
      null,
      req.ip,
      req.get('User-Agent')
    );

    console.log('✅ Log de criação registrado');

    res.status(201).json({
      message: 'Sub-bloco cadastrado com sucesso',
      subBloco: novoSubBloco
    });

  } catch (error) {
    console.error('❌ Erro ao criar sub-bloco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar sub-bloco
router.put('/:codigo_cliente/:codigo_estacao/:codigo_bloco/:codigo_sub_bloco', async (req, res) => {
  if (req.user.tipo_usuario !== 'admin') {
    return res.status(403).json({ error: 'Acesso negado. Apenas administradores.' });
  }

  try {
    const { codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco } = req.params;
    const { denominacao, ativo, numeracao_gavetas } = req.body;

    const dadosAnteriores = await query(
      'SELECT * FROM sub_blocos WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 AND codigo_sub_bloco = $4', 
      [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco]
    );

    if (dadosAnteriores.rows.length === 0) {
      return res.status(404).json({ error: 'Sub-bloco não encontrado' });
    }

    // Atualizar sub-bloco
    const result = await query(`
      UPDATE sub_blocos
      SET denominacao = $1, ativo = $2, updated_at = CURRENT_TIMESTAMP
      WHERE codigo_cliente = $3 AND codigo_estacao = $4 AND codigo_bloco = $5 AND codigo_sub_bloco = $6
      RETURNING *
    `, [
      denominacao || dadosAnteriores.rows[0].denominacao,
      ativo !== undefined ? ativo : dadosAnteriores.rows[0].ativo,
      codigo_cliente,
      codigo_estacao,
      codigo_bloco,
      codigo_sub_bloco
    ]);

    const subBlocoAtualizado = result.rows[0];

    // Se foi fornecida nova numeração de gavetas, atualizar
    if (numeracao_gavetas && Array.isArray(numeracao_gavetas)) {
      // Remover numerações e gavetas existentes
      await query(`
        DELETE FROM gavetas 
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 AND codigo_sub_bloco = $4
      `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco]);

      await query(`
        DELETE FROM numeracoes_gavetas 
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 AND codigo_sub_bloco = $4
      `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco]);

      // Criar novas numerações e gavetas
      for (const range of numeracao_gavetas) {
        const { numero_inicio, numero_fim } = range;
        
        if (numero_inicio && numero_fim && numero_inicio <= numero_fim) {
          // Inserir numeração
          await query(`
            INSERT INTO numeracoes_gavetas (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_inicio, numero_fim, ativo)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
          `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_inicio, numero_fim, true]);

          // Criar gavetas individuais
          for (let numero = numero_inicio; numero <= numero_fim; numero++) {
            await query(`
              INSERT INTO gavetas (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta, posicao_x, posicao_y, disponivel, ativo)
              VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero, numero, 1, true, true]);
          }
        }
      }
    }

    // Registrar log da edição
    await logSubBloco(
      req.user.id,
      'EDIT',
      subBlocoAtualizado,
      dadosAnteriores.rows[0],
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      message: 'Sub-bloco atualizado com sucesso',
      subBloco: subBlocoAtualizado
    });

  } catch (error) {
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Listar gavetas de um sub-bloco
router.get('/:codigo_cliente/:codigo_estacao/:codigo_bloco/:codigo_sub_bloco/gavetas', async (req, res) => {
  try {
    const { codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco } = req.params;

    // Se não for admin, verificar se pertence ao cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      if (req.user.codigo_cliente !== codigo_cliente) {
        return res.status(403).json({ error: 'Acesso negado' });
      }
    }

    const result = await query(`
      SELECT 
        g.*,
        s.nome_sepultado,
        s.data_sepultamento,
        s.data_exumacao
      FROM gavetas g
      LEFT JOIN sepultamentos s ON g.codigo_cliente = s.codigo_cliente 
                                AND g.codigo_estacao = s.codigo_estacao 
                                AND g.codigo_bloco = s.codigo_bloco 
                                AND g.codigo_sub_bloco = s.codigo_sub_bloco 
                                AND g.numero_gaveta = s.numero_gaveta 
                                AND s.ativo = true
      WHERE g.codigo_cliente = $1 AND g.codigo_estacao = $2 AND g.codigo_bloco = $3 AND g.codigo_sub_bloco = $4 AND g.ativo = true
      ORDER BY g.numero_gaveta
    `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco]);

    res.json(result.rows);
  } catch (error) {
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Listar numerações de gavetas de um sub-bloco
router.get('/:codigo_cliente/:codigo_estacao/:codigo_bloco/:codigo_sub_bloco/numeracoes', async (req, res) => {
  try {
    const { codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco } = req.params;

    // Se não for admin, verificar se pertence ao cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      if (req.user.codigo_cliente !== codigo_cliente) {
        return res.status(403).json({ error: 'Acesso negado' });
      }
    }

    const result = await query(`
      SELECT *
      FROM numeracoes_gavetas
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 AND codigo_sub_bloco = $4 AND ativo = true
      ORDER BY numero_inicio
    `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco]);

    res.json(result.rows);
  } catch (error) {
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
