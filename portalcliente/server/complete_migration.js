const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
const { updateLogsTable } = require('./update_logs_table');
require('dotenv').config();

// Configuração do banco de dados
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'portal_evolution',
  password: process.env.DB_PASSWORD || 'postgres',
  port: process.env.DB_PORT || 5432,
});

async function completeMigration() {
  console.log('🚀 Portal Evolution - Migração Completa para Referências Hierárquicas');
  console.log('=' .repeat(80));
  
  const client = await pool.connect();
  
  try {
    // 1. Verificar se a migração principal já foi executada
    console.log('🔍 Verificando status da migração principal...');
    
    const checkMainMigration = await client.query(`
      SELECT column_name
      FROM information_schema.key_column_usage k
      JOIN information_schema.table_constraints t ON k.constraint_name = t.constraint_name
      WHERE t.table_name = 'produtos' 
      AND t.constraint_type = 'PRIMARY KEY'
      AND k.column_name = 'codigo_cliente'
    `);
    
    if (checkMainMigration.rows.length === 0) {
      console.log('🔄 Executando migração principal...');
      
      // Ler e executar o script de migração principal
      const migrationPath = path.join(__dirname, 'database', 'migration_to_codes.sql');
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      
      await client.query(migrationSQL);
      console.log('✅ Migração principal concluída');
    } else {
      console.log('✅ Migração principal já foi executada');
    }
    
    client.release();
    
    // 2. Atualizar tabela de logs
    console.log('\n🔄 Atualizando tabela de logs...');
    await updateLogsTable();
    
    // 3. Verificação final
    console.log('\n🔍 Verificação final da estrutura...');
    
    const finalClient = await pool.connect();
    
    // Verificar estrutura de todas as tabelas
    const tablesStructure = await finalClient.query(`
      SELECT 
        t.table_name,
        c.column_name,
        c.data_type,
        c.is_nullable,
        CASE 
          WHEN pk.column_name IS NOT NULL THEN 'PK'
          WHEN fk.column_name IS NOT NULL THEN 'FK'
          ELSE ''
        END as key_type
      FROM information_schema.tables t
      JOIN information_schema.columns c ON t.table_name = c.table_name
      LEFT JOIN (
        SELECT ku.table_name, ku.column_name
        FROM information_schema.table_constraints tc
        JOIN information_schema.key_column_usage ku ON tc.constraint_name = ku.constraint_name
        WHERE tc.constraint_type = 'PRIMARY KEY'
      ) pk ON c.table_name = pk.table_name AND c.column_name = pk.column_name
      LEFT JOIN (
        SELECT ku.table_name, ku.column_name
        FROM information_schema.table_constraints tc
        JOIN information_schema.key_column_usage ku ON tc.constraint_name = ku.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
      ) fk ON c.table_name = fk.table_name AND c.column_name = fk.column_name
      WHERE t.table_schema = 'public' 
      AND t.table_name IN ('clientes', 'usuarios', 'produtos', 'blocos', 'sub_blocos', 'gavetas', 'numeracoes_gavetas', 'sepultamentos', 'logs_auditoria')
      ORDER BY t.table_name, c.ordinal_position
    `);
    
    // Agrupar por tabela
    const tablesByName = {};
    tablesStructure.rows.forEach(row => {
      if (!tablesByName[row.table_name]) {
        tablesByName[row.table_name] = [];
      }
      tablesByName[row.table_name].push(row);
    });
    
    console.log('\n🏗️  Estrutura final das tabelas:');
    Object.keys(tablesByName).forEach(tableName => {
      console.log(`\n   📋 ${tableName.toUpperCase()}:`);
      tablesByName[tableName].forEach(column => {
        const keyInfo = column.key_type ? ` (${column.key_type})` : '';
        const nullInfo = column.is_nullable === 'NO' ? ' NOT NULL' : '';
        console.log(`      - ${column.column_name} ${column.data_type}${nullInfo}${keyInfo}`);
      });
    });
    
    // Verificar dados
    const dataStats = await finalClient.query(`
      SELECT 
        'clientes' as tabela, COUNT(*) as registros FROM clientes
      UNION ALL
      SELECT 
        'usuarios' as tabela, COUNT(*) as registros FROM usuarios
      UNION ALL
      SELECT 
        'produtos' as tabela, COUNT(*) as registros FROM produtos
      UNION ALL
      SELECT 
        'blocos' as tabela, COUNT(*) as registros FROM blocos
      UNION ALL
      SELECT 
        'sub_blocos' as tabela, COUNT(*) as registros FROM sub_blocos
      UNION ALL
      SELECT 
        'gavetas' as tabela, COUNT(*) as registros FROM gavetas
      UNION ALL
      SELECT 
        'numeracoes_gavetas' as tabela, COUNT(*) as registros FROM numeracoes_gavetas
      UNION ALL
      SELECT 
        'sepultamentos' as tabela, COUNT(*) as registros FROM sepultamentos
      UNION ALL
      SELECT 
        'logs_auditoria' as tabela, COUNT(*) as registros FROM logs_auditoria
    `);
    
    console.log('\n📊 Dados após migração:');
    dataStats.rows.forEach(row => {
      console.log(`   ${row.tabela}: ${row.registros} registros`);
    });
    
    // Verificar referências hierárquicas nos logs
    const logsWithHierarchy = await finalClient.query(`
      SELECT 
        COUNT(*) as total_logs,
        COUNT(codigo_cliente) as logs_com_cliente,
        COUNT(codigo_estacao) as logs_com_estacao,
        COUNT(codigo_bloco) as logs_com_bloco,
        COUNT(codigo_sub_bloco) as logs_com_sub_bloco
      FROM logs_auditoria
    `);
    
    console.log('\n📝 Referências hierárquicas nos logs:');
    const logStats = logsWithHierarchy.rows[0];
    console.log(`   Total de logs: ${logStats.total_logs}`);
    console.log(`   Com codigo_cliente: ${logStats.logs_com_cliente}`);
    console.log(`   Com codigo_estacao: ${logStats.logs_com_estacao}`);
    console.log(`   Com codigo_bloco: ${logStats.logs_com_bloco}`);
    console.log(`   Com codigo_sub_bloco: ${logStats.logs_com_sub_bloco}`);
    
    finalClient.release();
    
    console.log('\n🎉 MIGRAÇÃO COMPLETA FINALIZADA COM SUCESSO!');
    console.log('=' .repeat(80));
    console.log('✅ Todas as tabelas agora têm referências hierárquicas apropriadas:');
    console.log('   📋 clientes: codigo_cliente (base)');
    console.log('   👥 usuarios: codigo_cliente');
    console.log('   📦 produtos: codigo_cliente + codigo_estacao');
    console.log('   🧱 blocos: codigo_cliente + codigo_estacao + codigo_bloco');
    console.log('   🔲 sub_blocos: codigo_cliente + codigo_estacao + codigo_bloco + codigo_sub_bloco');
    console.log('   📊 gavetas: codigo_cliente + codigo_estacao + codigo_bloco + codigo_sub_bloco');
    console.log('   🔢 numeracoes_gavetas: codigo_cliente + codigo_estacao + codigo_bloco + codigo_sub_bloco');
    console.log('   ⚰️  sepultamentos: codigo_cliente + codigo_estacao + codigo_bloco + codigo_sub_bloco');
    console.log('   📝 logs_auditoria: codigo_cliente + codigo_estacao + codigo_bloco + codigo_sub_bloco (contexto)');
    console.log('\n🚀 O sistema está pronto para importação de dados de outras fontes!');
    console.log('📥 Todas as tabelas podem ser facilmente relacionadas usando os códigos hierárquicos.');
    
  } catch (error) {
    console.error('❌ Erro durante a migração completa:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Executar apenas se chamado diretamente
if (require.main === module) {
  completeMigration().catch(error => {
    console.error('❌ Erro fatal:', error);
    process.exit(1);
  });
}

module.exports = { completeMigration };
