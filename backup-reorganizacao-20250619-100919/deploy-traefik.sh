#!/bin/bash

# ===================================
# DEPLOY PORTAL EVOLUTION COM TRAEFIK
# ===================================
# Script para deploy no Docker Swarm com Traefik
# Execute: bash deploy-traefik.sh

set -e

echo "🚀 Iniciando deploy do Portal Evolution com Traefik..."

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Verificar se arquivo de configuração existe
if [[ ! -f "configuracao.env" ]]; then
    error "Arquivo configuracao.env não encontrado! Configure primeiro."
fi

# Carregar configurações
source configuracao.env

# Validar configurações obrigatórias
if [[ -z "$DOMAIN" ]]; then
    error "DOMAIN não configurado em configuracao.env"
fi

log "Configurações carregadas:"
info "Domínio: $DOMAIN"
info "Banco: $DB_NAME"
info "SSL: ${ENABLE_SSL:-false}"

# Verificar se Docker Swarm está ativo
if ! docker info | grep -q "Swarm: active"; then
    error "Docker Swarm não está ativo! Execute: docker swarm init"
fi

# Verificar se rede redeinterna existe
if ! docker network ls | grep -q "redeinterna"; then
    error "Rede 'redeinterna' não encontrada! Verifique a configuração do Traefik."
fi

# Verificar se Traefik está rodando
if ! docker service ls | grep -q "traefik_traefik"; then
    error "Traefik não está rodando! Verifique a configuração."
fi

log "Infraestrutura validada!"

# Parar stack se existir
log "Removendo stack anterior (se existir)..."
docker stack rm portal-evolution 2>/dev/null || true

# Aguardar remoção completa
log "Aguardando remoção completa..."
sleep 10

# Limpar imagens antigas
log "Limpando imagens antigas..."
docker system prune -f

# Build das imagens
log "Construindo imagens..."
docker build -f Dockerfile.backend -t portal-evolution-backend:latest .
docker build -f Dockerfile.frontend -t portal-evolution-frontend:latest .

# Deploy da stack
log "Fazendo deploy da stack..."
docker stack deploy -c docker-compose.prod.yml portal-evolution

# Aguardar serviços iniciarem
log "Aguardando serviços iniciarem..."
sleep 30

# Verificar status dos serviços
log "Verificando status dos serviços..."
docker stack services portal-evolution

# Aguardar mais um pouco para estabilizar
sleep 30

# Testar saúde da aplicação
log "Testando saúde da aplicação..."

# Testar backend
if curl -f -s https://$DOMAIN/api/health > /dev/null; then
    log "✅ Backend funcionando: https://$DOMAIN/api/health"
else
    warn "❌ Backend não está respondendo ainda"
fi

# Testar frontend
if curl -f -s https://$DOMAIN/health > /dev/null; then
    log "✅ Frontend funcionando: https://$DOMAIN"
else
    warn "❌ Frontend não está respondendo ainda"
fi

log "🎉 Deploy concluído!"
log ""
log "📋 Acesse a aplicação:"
log "   🌐 Portal: https://$DOMAIN"
log "   🔧 API: https://$DOMAIN/api/health"
log ""
log "👥 Credenciais padrão:"
log "   Admin: <EMAIL> / adminnbr5410!"
log "   Cliente: <EMAIL> / 54321"
log ""
log "📊 Monitoramento:"
log "   Stack: docker stack services portal-evolution"
log "   Logs Backend: docker service logs portal-evolution_portal_backend"
log "   Logs Frontend: docker service logs portal-evolution_portal_frontend"
log "   Logs Database: docker service logs portal-evolution_portal_database"
