# Guia para Adicionar Colunas Hierárquicas

## 🎯 Objetivo

Este guia mostra como adicionar as **colunas de referência hierárquica** às tabelas existentes do PostgreSQL, mantendo a estrutura atual e os dados existentes.

## 📋 O que será Adicionado

### Colunas por Tabela

| Tabela | Colunas a Adicionar | Propósito |
|--------|---------------------|-----------|
| `blocos` | `codigo_cliente`, `codigo_estacao`, `denominacao` | Referências hierárquicas |
| `sub_blocos` | `codigo_cliente`, `codigo_estacao`, `codigo_bloco`, `denominacao` | Referências hierárquicas |
| `gavetas` | `codigo_cliente`, `codigo_estacao`, `codigo_bloco`, `codigo_sub_bloco` | Referências hierár<PERSON>cas |
| `numeracoes_gavetas` | `codigo_cliente`, `codigo_estacao`, `codigo_bloco`, `codigo_sub_bloco` | Referências hierárquicas |
| `sepultamentos` | ✅ **Já possui** todas as colunas necessárias | - |
| `logs_auditoria` | `codigo_cliente`, `codigo_estacao`, `codigo_bloco`, `codigo_sub_bloco`, `descricao` | Contexto hierárquico |

## 🚀 Como Executar

### Pré-requisitos
1. ✅ Backup do banco de dados
2. ✅ Acesso ao PostgreSQL
3. ✅ Node.js instalado
4. ✅ Servidor parado (recomendado)

### Passo a Passo

#### 1. **Fazer Backup**
```bash
# Backup completo
pg_dump -h localhost -U postgres -d portal_evolution > backup_antes_colunas.sql

# Verificar backup
ls -la backup_antes_colunas.sql
```

#### 2. **Executar Adição de Colunas**

**Opção A: Script Node.js (Recomendado)**
```bash
# Navegar para pasta do servidor
cd portalcliente/server

# Executar script
node add_hierarchical_columns.js
```

**Opção B: SQL Direto**
```bash
# Conectar ao PostgreSQL
psql -h localhost -U postgres -d portal_evolution

# Executar arquivo SQL
\i add_hierarchical_columns.sql
```

#### 3. **Verificar Resultado**
O script mostrará:
- ✅ Colunas adicionadas por tabela
- 📊 Dados populados automaticamente
- 🔗 Índices criados
- 🔗 Foreign keys adicionadas

## 📊 Verificação Manual

### Comandos SQL para Verificar

```sql
-- 1. Verificar se as colunas foram adicionadas
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name IN ('blocos', 'sub_blocos', 'gavetas', 'numeracoes_gavetas', 'logs_auditoria')
AND column_name IN ('codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco')
ORDER BY table_name, column_name;

-- 2. Verificar dados populados
SELECT 'blocos' as tabela, 
       COUNT(*) as total, 
       COUNT(codigo_cliente) as com_codigo_cliente,
       COUNT(codigo_estacao) as com_codigo_estacao
FROM blocos
UNION ALL
SELECT 'sub_blocos' as tabela, 
       COUNT(*) as total, 
       COUNT(codigo_cliente) as com_codigo_cliente,
       COUNT(codigo_estacao) as com_codigo_estacao
FROM sub_blocos
UNION ALL
SELECT 'gavetas' as tabela, 
       COUNT(*) as total, 
       COUNT(codigo_cliente) as com_codigo_cliente,
       COUNT(codigo_estacao) as com_codigo_estacao
FROM gavetas;

-- 3. Testar consulta hierárquica
SELECT 
    b.codigo_cliente,
    b.codigo_estacao,
    b.codigo_bloco,
    b.denominacao as bloco_nome,
    sb.codigo_sub_bloco,
    sb.denominacao as sub_bloco_nome,
    COUNT(g.numero_gaveta) as total_gavetas
FROM blocos b
LEFT JOIN sub_blocos sb ON b.codigo_cliente = sb.codigo_cliente 
                        AND b.codigo_estacao = sb.codigo_estacao 
                        AND b.codigo_bloco = sb.codigo_bloco
LEFT JOIN gavetas g ON sb.codigo_cliente = g.codigo_cliente 
                    AND sb.codigo_estacao = g.codigo_estacao 
                    AND sb.codigo_bloco = g.codigo_bloco 
                    AND sb.codigo_sub_bloco = g.codigo_sub_bloco
GROUP BY b.codigo_cliente, b.codigo_estacao, b.codigo_bloco, b.denominacao, sb.codigo_sub_bloco, sb.denominacao
ORDER BY b.codigo_cliente, b.codigo_estacao, b.codigo_bloco, sb.codigo_sub_bloco;
```

## 🔄 O que Acontece Automaticamente

### 1. **Adição de Colunas**
- Colunas são adicionadas com `ADD COLUMN IF NOT EXISTS`
- Não quebra se já existirem

### 2. **População de Dados**
- Dados são populados automaticamente usando JOINs
- Mantém integridade com dados existentes

### 3. **Criação de Índices**
- Índices otimizados para consultas hierárquicas
- Melhora performance de consultas por códigos

### 4. **Foreign Keys**
- Relacionamentos com tabela `clientes`
- Garante integridade referencial

## ✅ Resultado Esperado

Após a execução, você terá:

### Estrutura Atualizada
```
blocos:
├── id (PK - mantido)
├── produto_id (FK - mantido)
├── codigo_bloco (mantido)
├── nome (mantido)
├── codigo_cliente ⭐ NOVO
├── codigo_estacao ⭐ NOVO
├── denominacao ⭐ NOVO
└── ... outras colunas

sub_blocos:
├── id (PK - mantido)
├── bloco_id (FK - mantido)
├── codigo_sub_bloco (mantido)
├── nome (mantido)
├── codigo_cliente ⭐ NOVO
├── codigo_estacao ⭐ NOVO
├── codigo_bloco ⭐ NOVO
├── denominacao ⭐ NOVO
└── ... outras colunas

gavetas:
├── id (PK - mantido)
├── sub_bloco_id (FK - mantido)
├── numero_gaveta (mantido)
├── codigo_cliente ⭐ NOVO
├── codigo_estacao ⭐ NOVO
├── codigo_bloco ⭐ NOVO
├── codigo_sub_bloco ⭐ NOVO
└── ... outras colunas
```

### Consultas Possíveis
```sql
-- Buscar por códigos hierárquicos
SELECT * FROM gavetas 
WHERE codigo_cliente = 'SAF_001' 
AND codigo_estacao = 'ETEN_001' 
AND codigo_bloco = 'A' 
AND codigo_sub_bloco = 'A1';

-- Importar dados diretamente
INSERT INTO gavetas (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta, disponivel)
VALUES ('IMPORT_001', 'EST_001', 'X', 'X1', 1, true);
```

## ⚠️ Importante

1. **Compatibilidade**: Estrutura antiga mantida, nova funcionalidade adicionada
2. **Dados Existentes**: Preservados e populados automaticamente
3. **Performance**: Novos índices melhoram consultas
4. **Reversível**: Colunas podem ser removidas se necessário
5. **Importação**: Sistema pronto para receber dados externos

## 🆘 Rollback (Se Necessário)

```sql
-- Remover colunas adicionadas (se necessário)
ALTER TABLE blocos DROP COLUMN IF EXISTS codigo_cliente;
ALTER TABLE blocos DROP COLUMN IF EXISTS codigo_estacao;
ALTER TABLE blocos DROP COLUMN IF EXISTS denominacao;

ALTER TABLE sub_blocos DROP COLUMN IF EXISTS codigo_cliente;
ALTER TABLE sub_blocos DROP COLUMN IF EXISTS codigo_estacao;
ALTER TABLE sub_blocos DROP COLUMN IF EXISTS codigo_bloco;
ALTER TABLE sub_blocos DROP COLUMN IF EXISTS denominacao;

-- ... continuar para outras tabelas se necessário
```

---

**🎉 Pronto!** Suas tabelas agora têm as **referências hierárquicas** necessárias para facilitar a importação de dados de outras fontes, mantendo total compatibilidade com o sistema existente!
