#!/bin/bash

# Script de configuração manual da VPS (sem dependência do GitHub)
# Execute na VPS como root: bash setup-manual-vps.sh

echo "🚀 Configuração Manual da VPS - Portal Evolution"
echo "Este script não depende de autenticação do GitHub"
echo ""

# Verificar se é root
if [[ $EUID -ne 0 ]]; then
   echo "❌ Execute como root: sudo bash setup-manual-vps.sh"
   exit 1
fi

# Atualizar sistema
echo "📦 Atualizando sistema..."
apt update && apt upgrade -y

# Instalar dependências
echo "📦 Instalando dependências..."
apt install -y \
    curl \
    wget \
    git \
    unzip \
    nano \
    htop \
    ufw \
    software-properties-common \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release

# Instalar Docker
echo "🐳 Instalando Docker..."
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
rm get-docker.sh

# Instalar Docker Compose
echo "🐳 Instalando Docker Compose..."
DOCKER_COMPOSE_VERSION=$(curl -s https://api.github.com/repos/docker/compose/releases/latest | grep 'tag_name' | cut -d\" -f4)
curl -L "https://github.com/docker/compose/releases/download/${DOCKER_COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Verificar instalação
echo "✅ Verificando instalações..."
docker --version
docker-compose --version

# Configurar Docker
systemctl enable docker
systemctl start docker

# Configurar firewall
echo "🔥 Configurando firewall..."
ufw --force reset
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp
ufw --force enable

# Criar estrutura de diretórios
echo "📁 Criando estrutura de diretórios..."
mkdir -p /opt/portal-evolution/{backups,logs}
cd /opt/portal-evolution

# Baixar código do repositório
echo "📥 Baixando código do repositório..."
wget -O repo.zip https://github.com/MauricioFilh/portalevo/archive/refs/heads/master.zip
unzip -q repo.zip
mv portalevo-master/* .
mv portalevo-master/.* . 2>/dev/null || true
rm -rf portalevo-master repo.zip

# Tornar scripts executáveis
chmod +x portalcliente/*.sh

# Criar arquivo de configuração se não existir
if [[ ! -f "portalcliente/configuracao.env" ]]; then
    echo "⚙️ Criando arquivo de configuração..."
    cat > portalcliente/configuracao.env << 'EOF'
# ===================================
# CONFIGURAÇÃO DO PORTAL EVOLUTION
# ===================================

# DADOS DA VPS
VPS_HOST=************
VPS_USER=root
VPS_PORT=22

# DOMÍNIO (OPCIONAL)
DOMAIN=
SSL_EMAIL=

# BANCO DE DADOS
DB_HOST=database
DB_PORT=5432
DB_NAME=portal_evolution
DB_USER=portal_user
DB_PASSWORD=portal_senha_muito_segura_2025

# APLICAÇÃO
NODE_ENV=production
PORT=3001
JWT_SECRET=jwt_chave_muito_segura_portal_evolution_2025

# EMAIL (OPCIONAL)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=
EMAIL_PASS=

# CONFIGURAÇÕES AVANÇADAS
FRONTEND_REPLICAS=1
BACKEND_REPLICAS=1
FRONTEND_MEMORY=256m
BACKEND_MEMORY=512m
DATABASE_MEMORY=1g

# BACKUP
BACKUP_ENABLED=true
BACKUP_RETENTION_DAYS=7
EOF
fi

# Criar serviço systemd
echo "🔧 Configurando auto-start..."
cat > /etc/systemd/system/portal-evolution.service << 'EOF'
[Unit]
Description=Portal Evolution
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/opt/portal-evolution/portalcliente
ExecStart=/usr/local/bin/docker-compose -f docker-compose.prod.yml --env-file configuracao.env up -d
ExecStop=/usr/local/bin/docker-compose -f docker-compose.prod.yml down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable portal-evolution.service

# Criar script de backup
echo "💾 Configurando backup automático..."
cat > /opt/portal-evolution/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/opt/portal-evolution/backups"
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR

echo "$(date): Iniciando backup..." >> /var/log/portal-backup.log

# Backup do banco de dados
if docker ps | grep -q portal_database; then
    docker exec portal_database pg_dump -U portal_user portal_evolution > $BACKUP_DIR/db_backup_$DATE.sql
    echo "$(date): Backup criado: db_backup_$DATE.sql" >> /var/log/portal-backup.log
else
    echo "$(date): Container do banco não está rodando" >> /var/log/portal-backup.log
fi

# Manter apenas últimos 7 backups
find $BACKUP_DIR -name "db_backup_*.sql" -mtime +7 -delete 2>/dev/null

echo "$(date): Backup concluído" >> /var/log/portal-backup.log
EOF

chmod +x /opt/portal-evolution/backup.sh

# Configurar cron para backup
(crontab -l 2>/dev/null; echo "0 2 * * * /opt/portal-evolution/backup.sh") | crontab -

# Criar script de monitoramento
cat > /opt/portal-evolution/monitor.sh << 'EOF'
#!/bin/bash
echo "=== PORTAL EVOLUTION - MONITOR ==="
echo "Data: $(date)"
echo ""

cd /opt/portal-evolution/portalcliente

echo "=== STATUS DOS CONTAINERS ==="
if command -v docker-compose &> /dev/null; then
    docker-compose -f docker-compose.prod.yml ps
else
    docker ps
fi

echo ""
echo "=== SAÚDE DA APLICAÇÃO ==="
if curl -s http://localhost/health > /dev/null; then
    echo "✅ Frontend: OK"
else
    echo "❌ Frontend: ERRO"
fi

if curl -s http://localhost/api/health > /dev/null; then
    echo "✅ Backend: OK"
else
    echo "❌ Backend: ERRO"
fi

echo ""
echo "=== RECURSOS DO SISTEMA ==="
echo "Memória:"
free -h
echo ""
echo "Disco:"
df -h /opt
echo ""
echo "Containers Docker:"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"
EOF

chmod +x /opt/portal-evolution/monitor.sh

# Criar script de atualização manual
cat > /opt/portal-evolution/atualizar-manual.sh << 'EOF'
#!/bin/bash
echo "🔄 Atualizando Portal Evolution..."

cd /opt/portal-evolution

# Fazer backup da configuração atual
echo "💾 Fazendo backup da configuração..."
cp portalcliente/configuracao.env configuracao.env.backup

# Parar containers
echo "⏹️ Parando containers..."
cd portalcliente
docker-compose -f docker-compose.prod.yml down

# Baixar nova versão
echo "📥 Baixando nova versão..."
cd /opt/portal-evolution
wget -O repo.zip https://github.com/MauricioFilh/portalevo/archive/refs/heads/master.zip
unzip -q repo.zip

# Atualizar código
echo "🔄 Atualizando código..."
rm -rf portalcliente
mv portalevo-master/portalcliente .
rm -rf portalevo-master repo.zip

# Restaurar configuração
echo "⚙️ Restaurando configuração..."
mv configuracao.env.backup portalcliente/configuracao.env

# Tornar scripts executáveis
chmod +x portalcliente/*.sh

# Reiniciar containers
echo "🚀 Reiniciando containers..."
cd portalcliente
docker-compose -f docker-compose.prod.yml --env-file configuracao.env up -d --build

echo ""
echo "✅ Atualização concluída!"
echo "🔍 Verificando status..."
sleep 10
docker-compose -f docker-compose.prod.yml ps
EOF

chmod +x /opt/portal-evolution/atualizar-manual.sh

# Configurar logrotate
echo "📝 Configurando rotação de logs..."
cat > /etc/logrotate.d/portal-evolution << 'EOF'
/opt/portal-evolution/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
}

/var/log/portal-*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
}
EOF

echo ""
echo "🎉 CONFIGURAÇÃO DA VPS CONCLUÍDA!"
echo ""
echo "📋 PRÓXIMOS PASSOS:"
echo ""
echo "1. 📝 Editar configurações:"
echo "   nano /opt/portal-evolution/portalcliente/configuracao.env"
echo ""
echo "2. 🔐 ALTERAR OBRIGATORIAMENTE:"
echo "   - DB_PASSWORD (linha ~18)"
echo "   - JWT_SECRET (linha ~24)"
echo "   - DOMAIN (se tiver domínio)"
echo ""
echo "3. 🚀 Fazer deploy:"
echo "   cd /opt/portal-evolution/portalcliente"
echo "   bash deploy.sh"
echo ""
echo "🔧 COMANDOS ÚTEIS:"
echo "   Iniciar:    systemctl start portal-evolution"
echo "   Parar:      systemctl stop portal-evolution"
echo "   Status:     systemctl status portal-evolution"
echo "   Monitor:    bash /opt/portal-evolution/monitor.sh"
echo "   Atualizar:  bash /opt/portal-evolution/atualizar-manual.sh"
echo "   Backup:     bash /opt/portal-evolution/backup.sh"
echo ""
echo "🌐 ACESSO (após deploy):"
echo "   http://************"
echo ""
echo "👤 CREDENCIAIS PADRÃO:"
echo "   Admin:   <EMAIL> / adminnbr5410!"
echo "   Cliente: <EMAIL> / 54321"
echo ""
echo "⚠️  IMPORTANTE: Configure o arquivo configuracao.env antes do deploy!"
