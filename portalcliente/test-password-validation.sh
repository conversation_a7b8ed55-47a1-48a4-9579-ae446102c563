#!/bin/bash

# 🧪 SCRIPT DE TESTE - VALIDAÇÃO DE CRITÉRIOS DE SENHA
# ====================================================

echo "🧪 TESTE DE VALIDAÇÃO DE CRITÉRIOS DE SENHA"
echo "============================================="
echo ""

# Configurações
API_URL="https://portal.evo-eden.site/api/auth"
FRONTEND_URL="https://portal.evo-eden.site"

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para testar validação de senha
test_password_validation() {
    local senha="$1"
    local descricao="$2"
    local esperado="$3"
    
    echo -e "${BLUE}ℹ️  TESTE:${NC} $descricao"
    echo -e "${BLUE}   Senha:${NC} '$senha'"
    
    # Fazer requisição para API
    response=$(curl -s -X POST "$API_URL/validate-password" \
        -H "Content-Type: application/json" \
        -d "{\"senha\": \"$senha\"}")
    
    # Verificar se a resposta contém dados válidos
    if echo "$response" | jq -e . >/dev/null 2>&1; then
        valid=$(echo "$response" | jq -r '.valid')
        message=$(echo "$response" | jq -r '.message')
        criterios_nao_atendidos=$(echo "$response" | jq -r '.criterios_nao_atendidos | length')
        
        if [ "$valid" = "$esperado" ]; then
            echo -e "${GREEN}✅ PASS:${NC} Validação correta (válida: $valid)"
        else
            echo -e "${RED}❌ FAIL:${NC} Esperado: $esperado, Obtido: $valid"
        fi
        
        echo -e "${BLUE}   Mensagem:${NC} $message"
        echo -e "${BLUE}   Critérios não atendidos:${NC} $criterios_nao_atendidos"
    else
        echo -e "${RED}❌ FAIL:${NC} Resposta inválida da API"
        echo -e "${BLUE}   Resposta:${NC} $response"
    fi
    
    echo ""
}

# Função para testar critérios da API
test_password_criteria() {
    echo -e "${BLUE}ℹ️  TESTE:${NC} Obter critérios de senha da API"
    
    response=$(curl -s "$API_URL/password-criteria")
    
    if echo "$response" | jq -e . >/dev/null 2>&1; then
        success=$(echo "$response" | jq -r '.success')
        criterios_count=$(echo "$response" | jq -r '.criterios | length')
        exemplo=$(echo "$response" | jq -r '.exemplo_senha_valida')
        
        if [ "$success" = "true" ] && [ "$criterios_count" -gt "0" ]; then
            echo -e "${GREEN}✅ PASS:${NC} API de critérios funcionando"
            echo -e "${BLUE}   Critérios encontrados:${NC} $criterios_count"
            echo -e "${BLUE}   Exemplo de senha válida:${NC} $exemplo"
        else
            echo -e "${RED}❌ FAIL:${NC} API de critérios com problema"
        fi
    else
        echo -e "${RED}❌ FAIL:${NC} Resposta inválida da API de critérios"
    fi
    
    echo ""
}

# Verificar se a API está rodando
echo -e "${BLUE}ℹ️  INFO:${NC} Verificando se a API está rodando..."
if curl -s "$API_URL/../health" >/dev/null 2>&1; then
    echo -e "${GREEN}✅ PASS:${NC} API está rodando"
else
    echo -e "${RED}❌ FAIL:${NC} API não está rodando"
    echo -e "${YELLOW}⚠️  AVISO:${NC} Inicie a API com: cd portalcliente && npm run dev"
    exit 1
fi
echo ""

# Testar critérios da API
test_password_criteria

# TESTES DE VALIDAÇÃO DE SENHA
echo -e "${YELLOW}🔍 INICIANDO TESTES DE VALIDAÇÃO DE SENHA${NC}"
echo "================================================"
echo ""

# Teste 1: Senha muito curta
test_password_validation "123" "Senha muito curta (3 caracteres)" "false"

# Teste 2: Senha sem maiúscula (caso do usuário)
test_password_validation "nbr5410!@#" "Senha sem maiúscula (caso reportado)" "false"

# Teste 3: Senha com maiúscula (caso do usuário)
test_password_validation "Nbr5410!@#" "Senha com maiúscula (caso que funciona)" "true"

# Teste 4: Senha sem minúscula
test_password_validation "NBR5410!@#" "Senha sem minúscula" "false"

# Teste 5: Senha sem número
test_password_validation "MinhaSenh@" "Senha sem número" "false"

# Teste 6: Senha sem caractere especial
test_password_validation "MinhaSenh123" "Senha sem caractere especial" "false"

# Teste 7: Senha comum
test_password_validation "123456" "Senha muito comum" "false"

# Teste 8: Senha válida completa
test_password_validation "MinhaSenh@123" "Senha válida completa" "true"

# Teste 9: Senha muito longa
test_password_validation "$(printf 'a%.0s' {1..130})" "Senha muito longa (130 caracteres)" "false"

# Teste 10: Senha no limite mínimo
test_password_validation "Abc1@!" "Senha no limite mínimo (6 caracteres)" "true"

# Teste 11: Senha com todos os tipos de caracteres especiais
test_password_validation "Test123!@#$%^&*()" "Senha com vários caracteres especiais" "true"

# Teste 12: Senha com acentos (deve funcionar)
test_password_validation "Minhã123!@#" "Senha com acentos" "true"

echo "============================================="
echo -e "${YELLOW}🎯 TESTES CONCLUÍDOS${NC}"
echo ""

# Verificar se frontend está rodando
echo -e "${BLUE}ℹ️  INFO:${NC} Verificando se o frontend está rodando..."
if curl -s "$FRONTEND_URL" >/dev/null 2>&1; then
    echo -e "${GREEN}✅ PASS:${NC} Frontend está rodando"
    echo -e "${BLUE}   URL:${NC} $FRONTEND_URL"
else
    echo -e "${YELLOW}⚠️  AVISO:${NC} Frontend não está rodando"
    echo -e "${BLUE}   Para testar no frontend:${NC} cd portalcliente && npm start"
fi

echo ""
echo -e "${GREEN}🎉 SCRIPT DE TESTE CONCLUÍDO${NC}"
echo ""
echo -e "${BLUE}📋 PRÓXIMOS PASSOS PARA TESTE MANUAL:${NC}"
echo "1. Acesse: https://portal.evo-eden.site"
echo "2. Clique em 'Esqueci minha senha'"
echo "3. Digite um email válido"
echo "4. Acesse o link no email recebido"
echo "5. Teste as senhas:"
echo "   - ❌ 'nbr5410!@#' (sem maiúscula)"
echo "   - ✅ 'Nbr5410!@#' (com maiúscula)"
echo "   - ✅ 'MinhaSenh@123' (exemplo válido)"
