# 🚀 PORTAL EVOLUTION - GESTÃO DE AMBIENTES

## 📋 **VISÃO GERAL**

O Portal Evolution agora possui dois ambientes separados:

### 🌐 **PRODUÇÃO**
- **URL:** https://portal.evo-eden.site
- **Descrição:** Ambiente estável para usuários finais
- **Docker Compose:** `docker-compose.yml`
- **Stack:** `portal-evolution`

### 🔧 **DESENVOLVIMENTO**
- **URL:** https://portaldev.evo-eden.site
- **Descrição:** Ambiente para testes e desenvolvimento
- **Docker Compose:** `docker-compose.dev.yml`
- **Stack:** `portal-evolution-dev`

---

## 🔄 **FLUXO DE TRABALHO**

### 1. **Desenvolvimento**
```bash
# Fazer alterações no código
# Executar deploy no ambiente de desenvolvimento
./deploy-dev.sh
```

### 2. **Teste**
- Acessar https://portaldev.evo-eden.site
- Testar todas as funcionalidades
- Validar alterações

### 3. **Promoção para Produção**
```bash
# Quando tudo estiver OK
./promote-to-production.sh
```

---

## 🛠️ **COMANDOS ÚTEIS**

### **Deploy Desenvolvimento**
```bash
./deploy-dev.sh
```

### **Promoção para Produção**
```bash
./promote-to-production.sh
```

### **Verificar Status dos Serviços**
```bash
# Todos os serviços
docker service ls | grep portal

# Apenas produção
docker service ls | grep portal-evolution_

# Apenas desenvolvimento
docker service ls | grep portal-evolution-dev_
```

### **Verificar Containers**
```bash
docker ps | grep portal
```

### **Logs dos Serviços**
```bash
# Backend produção
docker service logs portal-evolution_portalevo-backend

# Frontend produção
docker service logs portal-evolution_portalevo-frontend

# Backend desenvolvimento
docker service logs portal-evolution-dev_portalevo-backend-dev

# Frontend desenvolvimento
docker service logs portal-evolution-dev_portalevo-frontend-dev
```

---

## 🗂️ **ESTRUTURA DE ARQUIVOS**

```
portalevo/
├── docker-compose.yml          # Produção
├── docker-compose.dev.yml      # Desenvolvimento
├── deploy-dev.sh              # Script deploy desenvolvimento
├── promote-to-production.sh   # Script promoção produção
├── AMBIENTES.md               # Este arquivo
└── portalcliente/
    ├── Dockerfile             # Frontend
    └── docker/production/
        └── Dockerfile.backend # Backend
```

---

## 🔒 **CONFIGURAÇÕES**

### **Banco de Dados**
- **Ambos os ambientes** utilizam o mesmo banco de dados
- **Host:** postgres_postgres
- **Database:** dbetens
- **Sincronização:** Automática entre ambientes

### **Portas**
- **Produção:**
  - Backend: 5000:3001
  - Frontend: 3000:80
- **Desenvolvimento:**
  - Backend: 5001:3001
  - Frontend: 3001:80

### **Domínios**
- **Produção:** portal.evo-eden.site
- **Desenvolvimento:** portaldev.evo-eden.site

---

## ⚠️ **IMPORTANTE**

1. **Sempre testar no ambiente de desenvolvimento primeiro**
2. **Nunca fazer alterações diretas em produção**
3. **Usar o script de promoção para mover para produção**
4. **Verificar logs em caso de problemas**
5. **Manter backups regulares**

---

## 🎯 **ULTRATHINK - AMBIENTE CONFIGURADO COM SUCESSO!**
