const express = require('express');
const bcrypt = require('bcryptjs');
const { query } = require('../database/connection');
const { logAction } = require('../utils/logger');

const router = express.Router();

// Middleware para verificar se é admin
const requireAdmin = (req, res, next) => {
  if (req.user.tipo_usuario !== 'admin') {
    return res.status(403).json({ error: 'Acesso negado. Apenas administradores.' });
  }
  next();
};

// Listar usuários (apenas admin)
router.get('/', requireAdmin, async (req, res) => {
  try {
    const result = await query(`
      SELECT 
        u.id, u.email, u.tipo_usuario, u.codigo_cliente, u.nome, u.ativo, u.created_at,
        c.nome as nome_cliente
      FROM usuarios u
      LEFT JOIN clientes c ON u.codigo_cliente = c.codigo_cliente
      WHERE u.ativo = true
      ORDER BY u.nome
    `);

    res.json(result.rows);
  } catch (error) {
    console.error('Erro ao listar usuários:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Buscar usuário por ID (apenas admin)
router.get('/:id', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    
    const result = await query(`
      SELECT 
        u.id, u.email, u.tipo_usuario, u.codigo_cliente, u.nome, u.ativo, u.created_at,
        c.nome as nome_cliente
      FROM usuarios u
      LEFT JOIN clientes c ON u.codigo_cliente = c.codigo_cliente
      WHERE u.id = $1
    `, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Usuário não encontrado' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Erro ao buscar usuário:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar novo usuário (apenas admin)
router.post('/', requireAdmin, async (req, res) => {
  try {
    const { email, senha, tipo_usuario, codigo_cliente, nome, produtos_acesso } = req.body;

    if (!email || !senha || !tipo_usuario || !nome) {
      return res.status(400).json({ error: 'Email, senha, tipo de usuário e nome são obrigatórios' });
    }

    if (tipo_usuario === 'cliente' && !codigo_cliente) {
      return res.status(400).json({ error: 'Código do cliente é obrigatório para usuários do tipo cliente' });
    }

    // Verificar se o email já existe
    const existingUser = await query(
      'SELECT id FROM usuarios WHERE email = $1',
      [email]
    );

    if (existingUser.rows.length > 0) {
      return res.status(400).json({ error: 'Email já está em uso' });
    }

    // Hash da senha
    const hashedPassword = await bcrypt.hash(senha, 10);

    const result = await query(`
      INSERT INTO usuarios (email, senha, tipo_usuario, codigo_cliente, nome, produtos_acesso)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING id, email, tipo_usuario, codigo_cliente, nome, produtos_acesso, ativo, created_at
    `, [email, hashedPassword, tipo_usuario, codigo_cliente, nome, produtos_acesso ? JSON.stringify(produtos_acesso) : null]);

    const novoUsuario = result.rows[0];

    // Registrar log
    await logAction(
      req.user.id,
      'CREATE',
      'usuarios',
      novoUsuario.id,
      null,
      novoUsuario,
      req.ip,
      req.get('User-Agent')
    );

    res.status(201).json({
      message: 'Usuário cadastrado com sucesso',
      usuario: novoUsuario
    });

  } catch (error) {
    console.error('Erro ao criar usuário:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar usuário (apenas admin)
router.put('/:id', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { email, senha, tipo_usuario, codigo_cliente, nome, ativo } = req.body;

    console.log('🔄 Atualizando usuário ID:', id);
    console.log('📥 Dados recebidos:', {
      email,
      senha: senha ? `*** (${senha.length} caracteres)` : 'não fornecida',
      tipo_usuario,
      codigo_cliente,
      nome,
      ativo
    });

    // Buscar dados anteriores
    const dadosAnteriores = await query(
      'SELECT id, email, tipo_usuario, codigo_cliente, nome, ativo FROM usuarios WHERE id = $1',
      [id]
    );

    if (dadosAnteriores.rows.length === 0) {
      return res.status(404).json({ error: 'Usuário não encontrado' });
    }

    let updateQuery;
    let updateParams;

    // Se senha foi fornecida, incluir na atualização
    if (senha && senha.trim()) {
      console.log('🔐 Nova senha fornecida, fazendo hash...');
      const hashedPassword = await bcrypt.hash(senha, 10);

      updateQuery = `
        UPDATE usuarios
        SET email = $1, senha = $2, tipo_usuario = $3, codigo_cliente = $4, nome = $5, ativo = $6, updated_at = CURRENT_TIMESTAMP
        WHERE id = $7
        RETURNING id, email, tipo_usuario, codigo_cliente, nome, ativo, created_at
      `;
      updateParams = [email, hashedPassword, tipo_usuario, codigo_cliente, nome, ativo, id];

      console.log('✅ Senha será atualizada junto com outros dados');
    } else {
      console.log('ℹ️ Senha não fornecida, mantendo senha atual');
      updateQuery = `
        UPDATE usuarios
        SET email = $1, tipo_usuario = $2, codigo_cliente = $3, nome = $4, ativo = $5, updated_at = CURRENT_TIMESTAMP
        WHERE id = $6
        RETURNING id, email, tipo_usuario, codigo_cliente, nome, ativo, created_at
      `;
      updateParams = [email, tipo_usuario, codigo_cliente, nome, ativo, id];
    }

    const result = await query(updateQuery, updateParams);
    const usuarioAtualizado = result.rows[0];

    console.log('✅ Usuário atualizado com sucesso:', usuarioAtualizado.email);

    // Registrar log
    const logData = senha && senha.trim()
      ? { ...usuarioAtualizado, senha_alterada: true }
      : usuarioAtualizado;

    await logAction(
      req.user.id,
      'UPDATE',
      'usuarios',
      usuarioAtualizado.id,
      dadosAnteriores.rows[0],
      logData,
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      message: senha && senha.trim()
        ? 'Usuário e senha atualizados com sucesso'
        : 'Usuário atualizado com sucesso',
      usuario: usuarioAtualizado
    });

  } catch (error) {
    console.error('❌ Erro ao atualizar usuário:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Alterar senha (próprio usuário ou admin)
router.put('/:id/senha', async (req, res) => {
  try {
    const { id } = req.params;
    const { senha_atual, nova_senha } = req.body;

    // Verificar se é o próprio usuário ou admin
    if (req.user.id != id && req.user.tipo_usuario !== 'admin') {
      return res.status(403).json({ error: 'Acesso negado' });
    }

    if (!nova_senha) {
      return res.status(400).json({ error: 'Nova senha é obrigatória' });
    }

    // Se não for admin, verificar senha atual
    if (req.user.tipo_usuario !== 'admin') {
      if (!senha_atual) {
        return res.status(400).json({ error: 'Senha atual é obrigatória' });
      }

      const usuario = await query('SELECT senha FROM usuarios WHERE id = $1', [id]);
      const senhaValida = await bcrypt.compare(senha_atual, usuario.rows[0].senha);
      
      if (!senhaValida) {
        return res.status(400).json({ error: 'Senha atual incorreta' });
      }
    }

    // Hash da nova senha
    const hashedPassword = await bcrypt.hash(nova_senha, 10);

    await query(
      'UPDATE usuarios SET senha = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
      [hashedPassword, id]
    );

    // Registrar log
    await logAction(
      req.user.id,
      'PASSWORD_CHANGE',
      'usuarios',
      id,
      null,
      { action: 'password_changed' },
      req.ip,
      req.get('User-Agent')
    );

    res.json({ message: 'Senha alterada com sucesso' });

  } catch (error) {
    console.error('Erro ao alterar senha:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar usuário (apenas admin)
router.delete('/:id', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // Verificar se o usuário existe
    const usuario = await query(
      'SELECT id, email, nome FROM usuarios WHERE id = $1',
      [id]
    );

    if (usuario.rows.length === 0) {
      return res.status(404).json({ error: 'Usuário não encontrado' });
    }

    // Não permitir deletar o próprio usuário
    if (req.user.id == id) {
      return res.status(400).json({ error: 'Não é possível deletar seu próprio usuário' });
    }

    // Deletar o usuário
    await query('DELETE FROM usuarios WHERE id = $1', [id]);

    // Registrar log
    await logAction(
      req.user.id,
      'DELETE',
      'usuarios',
      id,
      usuario.rows[0],
      null,
      req.ip,
      req.get('User-Agent')
    );

    res.json({ message: 'Usuário deletado com sucesso' });

  } catch (error) {
    console.error('Erro ao deletar usuário:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
