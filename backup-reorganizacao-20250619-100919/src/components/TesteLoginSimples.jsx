import React, { useState } from 'react';
import { Box, Button, TextField, Typography, Alert, Paper, Grid } from '@mui/material';

const TesteLoginSimples = () => {
  const [resultados, setResultados] = useState([]);
  const [testando, setTestando] = useState(false);
  const [emailTeste, setEmailTeste] = useState('<EMAIL>');
  const [senhaTeste, setSenhaTeste] = useState('admin123');

  const adicionarResultado = (teste, sucesso, detalhes) => {
    const resultado = {
      teste,
      sucesso,
      detalhes,
      timestamp: new Date().toLocaleTimeString()
    };
    setResultados(prev => [...prev, resultado]);
    // Log removido por segurança
  };

  const testarLogin = async () => {
    setTestando(true);
    setResultados([]);

    try {
      adicionarResultado('Iniciando teste de login', true, 'Começando teste simples');

      // Teste 1: Login com credenciais fornecidas
      try {
        console.log('🔐 Testando login com:', emailTeste, '/ ***');
        
        const response = await fetch('http://localhost:3001/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: emailTeste,
            senha: senhaTeste
          }),
        });

        console.log('📋 Status da resposta:', response.status);
        
        if (response.ok) {
          const data = await response.json();
          console.log('✅ Dados da resposta:', data);
          adicionarResultado('1. Login válido', true, `Login bem-sucedido para ${emailTeste}`);
        } else {
          const errorData = await response.json();
          console.log('❌ Erro da resposta:', errorData);
          adicionarResultado('1. Login válido', false, `Login falhou: ${errorData.error || 'Credenciais inválidas'}`);
        }
      } catch (error) {
        console.error('❌ Erro na requisição:', error);
        adicionarResultado('1. Login válido', false, `Erro na requisição: ${error.message}`);
      }

      // Teste 2: Login com credenciais inválidas
      try {
        console.log('🔐 Testando login com credenciais inválidas...');
        
        const response = await fetch('http://localhost:3001/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: emailTeste,
            senha: 'senha_incorreta'
          }),
        });

        console.log('📋 Status da resposta (inválida):', response.status);
        
        if (response.ok) {
          adicionarResultado('2. Login inválido', false, 'ERRO: Login funcionou com senha incorreta');
        } else {
          const errorData = await response.json();
          console.log('✅ Erro esperado:', errorData);
          adicionarResultado('2. Login inválido', true, 'Correto: Login falhou com senha incorreta');
        }
      } catch (error) {
        console.error('❌ Erro na requisição inválida:', error);
        adicionarResultado('2. Login inválido', true, 'Correto: Login falhou com senha incorreta');
      }

      // Teste 3: Verificar estrutura da resposta
      try {
        console.log('🔍 Verificando estrutura da resposta...');
        
        const response = await fetch('http://localhost:3001/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: emailTeste,
            senha: senhaTeste
          }),
        });

        if (response.ok) {
          const data = await response.json();
          console.log('📋 Estrutura da resposta:', Object.keys(data));
          
          const temToken = data.token ? true : false;
          const temUsuario = data.usuario ? true : false;
          
          adicionarResultado('3. Estrutura resposta', temToken && temUsuario, 
            `Token: ${temToken ? '✅' : '❌'}, Usuário: ${temUsuario ? '✅' : '❌'}`);
        } else {
          adicionarResultado('3. Estrutura resposta', false, 'Não foi possível verificar - login falhou');
        }
      } catch (error) {
        adicionarResultado('3. Estrutura resposta', false, `Erro: ${error.message}`);
      }

    } catch (error) {
      adicionarResultado('Erro geral', false, error.message);
    } finally {
      setTestando(false);
    }
  };

  return (
    <Box sx={{ p: 3, maxWidth: 800, margin: '0 auto' }}>
      <Typography variant="h4" sx={{ mb: 3 }}>
        🔐 Teste Simples de Login
      </Typography>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Configurações do Teste
        </Typography>
        
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Email"
              value={emailTeste}
              onChange={(e) => setEmailTeste(e.target.value)}
              disabled={testando}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Senha"
              type="password"
              value={senhaTeste}
              onChange={(e) => setSenhaTeste(e.target.value)}
              disabled={testando}
            />
          </Grid>
        </Grid>

        <Button
          variant="contained"
          onClick={testarLogin}
          disabled={testando}
          size="large"
          sx={{ mt: 3 }}
          fullWidth
        >
          {testando ? 'Executando Teste...' : 'Executar Teste de Login'}
        </Button>
      </Paper>

      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          📋 Resultados do Teste
        </Typography>

        {resultados.length === 0 && !testando && (
          <Typography color="text.secondary">
            Configure as credenciais acima e clique em "Executar Teste"
          </Typography>
        )}

        {resultados.map((resultado, index) => (
          <Alert
            key={index}
            severity={resultado.sucesso ? 'success' : 'error'}
            sx={{ mb: 1 }}
          >
            <Typography variant="body2">
              <strong>{resultado.teste}</strong> - {resultado.timestamp}
            </Typography>
            <Typography variant="body2">
              {resultado.detalhes}
            </Typography>
          </Alert>
        ))}
      </Paper>

      <Paper sx={{ p: 3, mt: 3, backgroundColor: 'grey.50' }}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          📝 O que este teste faz:
        </Typography>
        <Typography variant="body2" component="div">
          <ol>
            <li>Testa login com credenciais fornecidas</li>
            <li>Testa login com senha incorreta (deve falhar)</li>
            <li>Verifica estrutura da resposta (token e usuário)</li>
          </ol>
        </Typography>
        
        <Typography variant="body2" sx={{ mt: 2, fontWeight: 'bold' }}>
          ✅ Se todos os passos passarem: Sistema de login funcionando
        </Typography>
        <Typography variant="body2" sx={{ fontWeight: 'bold', color: 'error.main' }}>
          ❌ Se algum passo falhar: Problema no sistema de autenticação
        </Typography>
        
        <Typography variant="body2" sx={{ mt: 2, p: 2, backgroundColor: 'info.light', borderRadius: 1 }}>
          ℹ️ <strong>VANTAGEM:</strong> Este teste não requer login prévio e não interfere na navegação.
          Você pode executá-lo a qualquer momento para verificar se o sistema de login está funcionando.
        </Typography>
      </Paper>
    </Box>
  );
};

export default TesteLoginSimples;
