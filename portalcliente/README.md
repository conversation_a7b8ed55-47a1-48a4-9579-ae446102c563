# 🚀 Portal Evolution

Sistema de Gestão de Sepultamentos - ETENs e Ossuários

## 📁 Estrutura do Projeto

```
portalevo/
├── .github/workflows/      # GitHub Actions
├── docs/                   # Documentação
├── scripts/               # Scripts de deploy e manutenção
├── docker/                # Arquivos Docker
├── config/                # Configurações
├── frontend/              # Aplicação React (src/)
├── backend/               # API Node.js (server/)
├── database/              # Scripts de banco
└── deploy/                # Arquivos de deploy
```

## 🚀 Deploy Rápido

```bash
# Build
./scripts/deploy/build-production.sh

# Deploy
./scripts/deploy/deploy-production.sh
```

## 🔧 Configuração

1. Copie o arquivo de configuração:
```bash
cp config/environments/.env.example config/environments/production.env
```

2. Edite as variáveis de ambiente
3. Execute o deploy

## 📊 Monitoramento

- **API Health:** https://portal.evo-eden.site/api/health
- **Logs:** https://logs.portal.evo-eden.site
- **Sistema:** https://portal.evo-eden.site

## 🛠️ Manutenção

```bash
# Verificar status
docker stack services portal-evolution

# Ver logs
docker service logs portal-evolution_portal_backend

# Atualizar sistema
./scripts/maintenance/atualizar.sh
```
