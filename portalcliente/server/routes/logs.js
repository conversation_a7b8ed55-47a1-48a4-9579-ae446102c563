const express = require('express');
const { query } = require('../database/connection');

const router = express.Router();

// Listar logs de auditoria (apenas admin)
router.get('/', async (req, res) => {
  try {
    const { 
      usuario_id, 
      acao, 
      tabela_afetada, 
      data_inicio, 
      data_fim, 
      page = 1, 
      limit = 50 
    } = req.query;

    let whereClause = 'WHERE 1=1';
    let params = [];
    let paramCount = 0;

    // Por padr<PERSON>, mostrar apenas logs dos últimos 30 dias
    if (!data_inicio && !data_fim) {
      paramCount++;
      whereClause += ` AND l.created_at >= CURRENT_DATE - INTERVAL '30 days'`;
    }

    if (usuario_id) {
      paramCount++;
      whereClause += ` AND l.usuario_id = $${paramCount}`;
      params.push(usuario_id);
    }

    if (acao) {
      paramCount++;
      whereClause += ` AND l.acao = $${paramCount}`;
      params.push(acao);
    }

    if (tabela_afetada) {
      paramCount++;
      whereClause += ` AND l.tabela_afetada = $${paramCount}`;
      params.push(tabela_afetada);
    }

    if (data_inicio) {
      paramCount++;
      whereClause += ` AND l.created_at >= $${paramCount}`;
      params.push(data_inicio);
    }

    if (data_fim) {
      paramCount++;
      whereClause += ` AND l.created_at <= $${paramCount}`;
      params.push(data_fim);
    }

    // Calcular offset para paginação
    const offset = (page - 1) * limit;
    paramCount++;
    const limitParam = paramCount;
    paramCount++;
    const offsetParam = paramCount;
    params.push(limit, offset);

    // Buscar logs com informações do usuário
    const result = await query(`
      SELECT
        l.id,
        l.usuario_id,
        l.acao,
        l.tabela_afetada,
        l.registro_id,
        l.descricao,
        l.dados_anteriores,
        l.dados_novos,
        l.ip_address,
        l.user_agent,
        l.created_at,
        u.nome as nome_usuario,
        u.email as email_usuario,
        u.tipo_usuario
      FROM logs_auditoria l
      LEFT JOIN usuarios u ON l.usuario_id = u.id
      ${whereClause}
      ORDER BY l.created_at DESC
      LIMIT $${limitParam} OFFSET $${offsetParam}
    `, params);

    // Contar total de registros para paginação
    const countResult = await query(`
      SELECT COUNT(*) as total
      FROM logs_auditoria l
      ${whereClause}
    `, params.slice(0, -2)); // Remove limit e offset dos parâmetros

    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    res.json({
      logs: result.rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('Erro ao listar logs:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Buscar log por ID (apenas admin)
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const result = await query(`
      SELECT 
        l.*,
        u.nome as nome_usuario,
        u.email as email_usuario,
        u.tipo_usuario
      FROM logs_auditoria l
      LEFT JOIN usuarios u ON l.usuario_id = u.id
      WHERE l.id = $1
    `, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Log não encontrado' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Erro ao buscar log:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Estatísticas de logs (apenas admin)
router.get('/stats/summary', async (req, res) => {
  try {
    const { data_inicio, data_fim } = req.query;

    let whereClause = 'WHERE 1=1';
    let params = [];
    let paramCount = 0;

    if (data_inicio) {
      paramCount++;
      whereClause += ` AND created_at >= $${paramCount}`;
      params.push(data_inicio);
    }

    if (data_fim) {
      paramCount++;
      whereClause += ` AND created_at <= $${paramCount}`;
      params.push(data_fim);
    }

    // Estatísticas por ação
    const acaoStats = await query(`
      SELECT acao, COUNT(*) as total
      FROM logs_auditoria
      ${whereClause}
      GROUP BY acao
      ORDER BY total DESC
    `, params);

    // Estatísticas por tabela
    const tabelaStats = await query(`
      SELECT tabela_afetada, COUNT(*) as total
      FROM logs_auditoria
      ${whereClause}
      GROUP BY tabela_afetada
      ORDER BY total DESC
    `, params);

    // Estatísticas por usuário
    const usuarioStats = await query(`
      SELECT 
        l.usuario_id,
        u.nome as nome_usuario,
        u.email as email_usuario,
        COUNT(*) as total
      FROM logs_auditoria l
      LEFT JOIN usuarios u ON l.usuario_id = u.id
      ${whereClause}
      GROUP BY l.usuario_id, u.nome, u.email
      ORDER BY total DESC
      LIMIT 10
    `, params);

    // Total geral
    const totalResult = await query(`
      SELECT COUNT(*) as total
      FROM logs_auditoria
      ${whereClause}
    `, params);

    res.json({
      total: parseInt(totalResult.rows[0].total),
      por_acao: acaoStats.rows,
      por_tabela: tabelaStats.rows,
      por_usuario: usuarioStats.rows
    });

  } catch (error) {
    console.error('Erro ao buscar estatísticas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Função para limpar logs antigos (mais de 30 dias)
router.delete('/cleanup', async (req, res) => {
  try {
    console.log('🧹 Iniciando limpeza de logs antigos (mais de 30 dias)...');

    // Primeiro, contar quantos logs serão removidos
    const countResult = await query(`
      SELECT COUNT(*) as total
      FROM logs_auditoria
      WHERE created_at < CURRENT_DATE - INTERVAL '30 days'
    `);

    const totalToDelete = countResult.rows[0].total;
    console.log(`📊 Logs a serem removidos: ${totalToDelete}`);

    if (totalToDelete === 0) {
      return res.json({
        success: true,
        message: 'Nenhum log antigo encontrado para remoção',
        logs_removidos: 0
      });
    }

    // Remover logs antigos
    const deleteResult = await query(`
      DELETE FROM logs_auditoria
      WHERE created_at < CURRENT_DATE - INTERVAL '30 days'
    `);

    console.log(`✅ Logs removidos: ${deleteResult.rowCount}`);

    res.json({
      success: true,
      message: `${deleteResult.rowCount} logs antigos foram removidos com sucesso`,
      logs_removidos: deleteResult.rowCount
    });

  } catch (error) {
    console.error('❌ Erro ao limpar logs antigos:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor ao limpar logs'
    });
  }
});

// Executar limpeza automática na inicialização
const executarLimpezaAutomatica = async () => {
  try {
    console.log('🔄 Executando limpeza automática de logs...');

    const countResult = await query(`
      SELECT COUNT(*) as total
      FROM logs_auditoria
      WHERE created_at < CURRENT_DATE - INTERVAL '30 days'
    `);

    const totalToDelete = countResult.rows[0].total;

    if (totalToDelete > 0) {
      const deleteResult = await query(`
        DELETE FROM logs_auditoria
        WHERE created_at < CURRENT_DATE - INTERVAL '30 days'
      `);

      console.log(`🧹 Limpeza automática: ${deleteResult.rowCount} logs antigos removidos`);
    } else {
      console.log('✅ Limpeza automática: Nenhum log antigo encontrado');
    }

  } catch (error) {
    console.error('❌ Erro na limpeza automática de logs:', error);
  }
};

// Executar limpeza na inicialização
setTimeout(executarLimpezaAutomatica, 5000); // 5 segundos após inicialização

module.exports = router;
