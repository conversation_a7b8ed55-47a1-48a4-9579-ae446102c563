#!/bin/bash

# 🚀 SCRIPT DE DEPLOY AUTOMATIZADO - PORTAL EVOLUTION
# Este script automatiza todo o processo de deploy

set -e  # Parar em caso de erro

echo "🚀 INICIANDO DEPLOY AUTOMATIZADO DO PORTAL EVOLUTION"
echo "=================================================="

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log colorido
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Verificar se estamos no diretório correto
if [ ! -f "docker-compose.prod.yml" ]; then
    log_error "Arquivo docker-compose.prod.yml não encontrado!"
    log_error "Execute este script no diretório /home/<USER>/portalevo/portalcliente"
    exit 1
fi

# PASSO 1: Atualizar código
log_info "Atualizando código do repositório..."
git pull origin master
log_success "Código atualizado"

# PASSO 2: Parar serviços atuais
log_info "Parando serviços atuais..."
sudo docker stack rm portal-evolution || true
log_warning "Aguardando 10 segundos para limpeza..."
sleep 10
log_success "Serviços parados"

# PASSO 3: Construir imagens
log_info "Construindo imagem do backend..."
sudo docker build -f Dockerfile.backend -t portal-evolution-backend:latest . --no-cache
log_success "Backend construído"

log_info "Construindo imagem do frontend..."
sudo docker build -f Dockerfile.frontend -t portal-evolution-frontend:latest . --no-cache
log_success "Frontend construído"

log_info "Construindo imagem do sistema de logs..."
sudo docker build -f Dockerfile.logs -t portal-evolution-logs:latest . --no-cache
log_success "Sistema de logs construído"

# PASSO 4: Deploy do stack
log_info "Fazendo deploy do stack completo..."
sudo docker stack deploy -c docker-compose.prod.yml portal-evolution
log_success "Stack deployado"

# PASSO 5: Aguardar inicialização
log_warning "Aguardando 30 segundos para inicialização dos serviços..."
sleep 30

# PASSO 6: Verificar status dos serviços
log_info "Verificando status dos serviços..."
sudo docker stack services portal-evolution

# PASSO 7: Executar script de correção do banco
log_info "Executando script de correção do banco de dados..."
if node fix-all-issues.js; then
    log_success "Script de correção executado com sucesso"
else
    log_warning "Script de correção falhou, mas continuando..."
fi

# PASSO 8: Testes básicos
log_info "Executando testes básicos..."

# Testar Health
log_info "Testando API Health..."
if curl -s -f https://portal.evo-eden.site/api/health > /dev/null; then
    log_success "API Health OK"
else
    log_warning "API Health não respondeu (pode estar ainda inicializando)"
fi

# Testar Login Cliente
log_info "Testando login cliente..."
if curl -s -f -X POST https://portal.evo-eden.site/api/auth/login \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"54321"}' > /dev/null; then
    log_success "Login cliente OK"
else
    log_warning "Login cliente falhou"
fi

# Testar Sistema de Logs
log_info "Testando sistema de logs..."
if curl -s -f https://logs.portal.evo-eden.site > /dev/null; then
    log_success "Sistema de logs OK"
else
    log_warning "Sistema de logs não respondeu"
fi

# PASSO 9: Resumo final
echo ""
echo "🎉 DEPLOY CONCLUÍDO!"
echo "==================="
log_success "Portal Principal: https://portal.evo-eden.site"
log_success "Sistema de Logs: https://logs.portal.evo-eden.site"
log_success "API Health: https://portal.evo-eden.site/api/health"
echo ""
log_info "Credenciais de teste:"
echo "  Admin: <EMAIL> / adminnbr5410!"
echo "  Cliente: <EMAIL> / 54321"
echo ""
log_warning "Aguarde alguns minutos para todos os serviços estabilizarem"
log_info "Monitore os logs em: https://logs.portal.evo-eden.site"

# Verificação final dos serviços
echo ""
log_info "Status final dos serviços:"
sudo docker stack services portal-evolution

echo ""
log_success "Deploy automatizado concluído! 🚀"
