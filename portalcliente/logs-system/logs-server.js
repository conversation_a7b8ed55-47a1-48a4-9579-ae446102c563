const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const { exec } = require('child_process');
const cors = require('cors');
const helmet = require('helmet');
const path = require('path');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// Middlewares de segurança
app.use(helmet({
  contentSecurityPolicy: false // Permitir inline scripts para Socket.IO
}));
app.use(cors());
app.use(express.static(path.join(__dirname, 'public')));

// Servir página principal
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// API para obter logs
app.get('/api/logs/:service', (req, res) => {
  const { service } = req.params;
  const { lines = 50 } = req.query;

  const serviceMap = {
    'backend': 'portal-evolution_portal_backend',
    'frontend': 'portal-evolution_portal_frontend',
    'postgres': 'postgres_postgres',
    'traefik': 'traefik_traefik',
    'logs': 'portal-evolution_portal_logs'
  };

  const serviceName = serviceMap[service];
  if (!serviceName) {
    return res.status(400).json({ error: 'Serviço inválido' });
  }

  // Comando mais robusto para obter logs
  const command = `docker service logs ${serviceName} --tail ${lines} --no-trunc 2>/dev/null || echo "Serviço não encontrado ou sem logs"`;

  exec(command, (error, stdout, stderr) => {
    if (error) {
      console.error(`Erro ao obter logs do ${service}:`, error.message);
      return res.json({
        service,
        logs: [{
          timestamp: new Date().toISOString(),
          container: serviceName,
          message: `Erro ao obter logs: ${error.message}`,
          level: 'error'
        }]
      });
    }

    const logs = stdout.split('\n').filter(line => line.trim()).map(line => {
      // Tentar diferentes formatos de log
      let match = line.match(/^(.+?)\s+(.+?)\s+\|\s+(.+)$/);
      if (!match) {
        match = line.match(/^(.+?)\s+(.+)$/);
        if (match) {
          return {
            timestamp: match[1],
            container: serviceName,
            message: match[2],
            level: detectLogLevel(match[2])
          };
        }
      } else {
        return {
          timestamp: match[1],
          container: match[2],
          message: match[3],
          level: detectLogLevel(match[3])
        };
      }

      return {
        timestamp: new Date().toISOString(),
        container: serviceName,
        message: line,
        level: 'info'
      };
    });

    res.json({ service, logs });
  });
});

// API para obter status dos serviços
app.get('/api/services/status', (req, res) => {
  exec('docker stack services portal-evolution --format "table {{.Name}}\t{{.Replicas}}\t{{.Image}}"', (error, stdout, stderr) => {
    if (error) {
      return res.status(500).json({ error: error.message });
    }
    
    const lines = stdout.split('\n').filter(line => line.trim());
    const headers = lines[0];
    const services = lines.slice(1).map(line => {
      const parts = line.split('\t');
      return {
        name: parts[0],
        replicas: parts[1],
        image: parts[2],
        status: parts[1] && parts[1].includes('1/1') ? 'running' : 'error'
      };
    });
    
    res.json({ services });
  });
});

// Detectar nível do log
function detectLogLevel(message) {
  const msg = message.toLowerCase();
  if (msg.includes('error') || msg.includes('err') || msg.includes('❌')) return 'error';
  if (msg.includes('warn') || msg.includes('warning') || msg.includes('⚠️')) return 'warning';
  if (msg.includes('info') || msg.includes('✅') || msg.includes('🔍')) return 'info';
  if (msg.includes('debug') || msg.includes('🐛')) return 'debug';
  return 'info';
}

// WebSocket para logs em tempo real
io.on('connection', (socket) => {
  console.log('Cliente conectado para logs em tempo real');
  
  socket.on('subscribe', (service) => {
    console.log(`Cliente inscrito nos logs do serviço: ${service}`);
    
    // Enviar logs iniciais
    const serviceMap = {
      'backend': 'portal-evolution_portal_backend',
      'frontend': 'portal-evolution_portal_frontend',
      'postgres': 'postgres_postgres',
      'traefik': 'traefik_traefik'
    };
    
    const serviceName = serviceMap[service];
    if (serviceName) {
      // Monitorar logs em tempo real (simulação - em produção usaria docker logs -f)
      const interval = setInterval(() => {
        exec(`docker service logs ${serviceName} --tail 5 --no-trunc`, (error, stdout, stderr) => {
          if (!error && stdout) {
            const logs = stdout.split('\n').filter(line => line.trim()).map(line => {
              const match = line.match(/^(.+?)\s+(.+?)\s+\|\s+(.+)$/);
              if (match) {
                return {
                  timestamp: match[1],
                  container: match[2],
                  message: match[3],
                  level: detectLogLevel(match[3])
                };
              }
              return {
                timestamp: new Date().toISOString(),
                container: serviceName,
                message: line,
                level: 'info'
              };
            });
            
            socket.emit('logs', { service, logs });
          }
        });
      }, 2000); // Atualizar a cada 2 segundos
      
      socket.on('disconnect', () => {
        clearInterval(interval);
        console.log('Cliente desconectado');
      });
    }
  });
});

const PORT = process.env.PORT || 3003;
server.listen(PORT, '0.0.0.0', () => {
  console.log(`🔍 Sistema de Logs rodando na porta ${PORT}`);
  console.log(`📊 Acesse: http://localhost:${PORT}`);
});
