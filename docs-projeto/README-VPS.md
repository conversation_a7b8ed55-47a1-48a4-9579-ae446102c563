# 🏛️ Portal Evolution - Sistema de Gestão Funerária

Sistema completo de gestão de gavetas e sepultamentos para cemitérios, seguindo padrão de deploy em VPS com Docker e Traefik.

## 📋 **ESTRUTURA DO PROJETO SEGUINDO PADRÃO VPS**

```
portal-evolution/
├── app/                          # 📱 Código-fonte da aplicação
│   ├── src/                      # Frontend React
│   ├── server/                   # Backend Node.js
│   └── public/                   # Arquivos estáticos
├── docker/                       # 🐳 Dockerfiles customizados
│   ├── Dockerfile.frontend       # Build React + Nginx
│   ├── Dockerfile.backend        # Build Node.js
│   └── nginx.conf               # Configuração Nginx
├── scripts/                      # 📜 Scripts de automação
│   ├── iniciar-servico.sh       # Iniciar sistema
│   ├── backup-dados.sh          # Backup automático
│   ├── atualizar-containers.sh  # Atualizar sistema
│   └── monitorar-servico.sh     # Monitoramento
├── dados/                        # 💾 Volumes e dados persistentes
│   ├── postgres/                # Dados do banco
│   ├── backups/                 # Backups automáticos
│   └── uploads/                 # Arquivos enviados
├── .env                         # ⚙️ Configurações (CONFIGURE AQUI)
├── portal-evolution.yaml        # 🐳 Docker Compose principal
└── README.md                    # 📖 Esta documentação
```

## ⚙️ **CONFIGURAÇÃO INICIAL**

### **1. Configurar Variáveis de Ambiente**

Edite o arquivo `.env` com suas configurações:

```bash
# Para editar na VPS:
nano .env
```

**OBRIGATÓRIO CONFIGURAR:**

```env
# IP da VPS (Para coletar: execute "curl ifconfig.me" na VPS)
VPS_HOST=************

# Domínio do Portal Evolution (já configurado para apontar para o IP acima)
# Para verificar DNS: execute "nslookup portal.evo-eden.site"
DOMAIN=portal.evo-eden.site

# Senha do banco (Para gerar: execute "openssl rand -base64 32" na VPS)
DB_PASSWORD=sua_senha_muito_segura_aqui

# Chave JWT (Para gerar: execute "openssl rand -hex 64" na VPS)
JWT_SECRET=sua_chave_jwt_muito_segura_aqui

# Email para certificado SSL (obrigatório para HTTPS)
# Usado pelo Let's Encrypt para gerar certificado para portal.evo-eden.site
SSL_EMAIL=<EMAIL>
```

**OPCIONAL CONFIGURAR:**

```env
# Configurações de email (Para Gmail: ative "Senhas de app")
EMAIL_HOST=smtp.gmail.com
EMAIL_USER=<EMAIL>
EMAIL_PASS=sua_senha_de_app
```

### **2. Verificar Rede Traefik**

```bash
# Para verificar se rede traefik existe:
docker network ls | grep traefik

# Se não existir, criar:
docker network create traefik
```

### **3. Verificar Configuração DNS**

```bash
# Verificar se domínio aponta para o IP correto:
nslookup portal.evo-eden.site

# Deve retornar: ************
# Se não estiver apontando, configure no seu provedor DNS:
# Tipo: A
# Nome: portal.evo-eden.site
# Valor: ************
# TTL: 300 (ou padrão)
```

## 🚀 **DEPLOY E EXECUÇÃO**

### **Método 1: Script Automático (Recomendado)**

```bash
# Preparar scripts
bash scripts/preparar-scripts.sh

# Iniciar sistema completo
bash scripts/iniciar-servico.sh
```

### **Método 2: Manual**

```bash
# Iniciar containers
docker-compose -f portal-evolution.yaml up -d

# Verificar status
docker-compose -f portal-evolution.yaml ps

# Ver logs
docker-compose -f portal-evolution.yaml logs -f
```

## 📊 **MONITORAMENTO E MANUTENÇÃO**

### **Monitoramento Contínuo**

```bash
# Monitor em tempo real
bash scripts/monitorar-servico.sh

# Monitor automático (atualiza a cada 30 segundos)
watch -n 30 'bash scripts/monitorar-servico.sh'
```

### **Backup Automático**

```bash
# Backup manual
bash scripts/backup-dados.sh

# Para verificar backups existentes:
ls -la dados/backups/

# Para restaurar backup:
cat dados/backups/db_backup_YYYYMMDD_HHMMSS.sql | docker exec -i portal-database psql -U portal_user -d portal_evolution
```

### **Atualização do Sistema**

```bash
# Atualizar containers (rebuild completo)
bash scripts/atualizar-containers.sh

# Ou manualmente:
docker-compose -f portal-evolution.yaml down
docker-compose -f portal-evolution.yaml up -d --build
```

## 🔧 **COMANDOS ÚTEIS PARA DIAGNÓSTICO**

### **Verificar Sistema**

```bash
# Status dos containers
docker-compose -f portal-evolution.yaml ps

# Logs em tempo real
docker-compose -f portal-evolution.yaml logs -f

# Logs de um serviço específico
docker logs portal-backend -f
docker logs portal-frontend -f
docker logs portal-database -f
```

### **Verificar Conectividade**

```bash
# Testar saúde dos serviços localmente
curl http://localhost/health          # Frontend local
curl http://localhost/api/health      # Backend local

# Testar saúde dos serviços pelo domínio
curl https://portal.evo-eden.site/health     # Frontend via domínio
curl https://portal.evo-eden.site/api/health # Backend via domínio

# Verificar banco de dados
docker exec portal-database pg_isready -U portal_user

# Verificar portas em uso
ss -tlnp | grep -E ":80|:443|:3001|:5432"

# Verificar certificado SSL
openssl s_client -connect portal.evo-eden.site:443 -servername portal.evo-eden.site < /dev/null
```

### **Verificar Recursos**

```bash
# Uso de recursos dos containers
docker stats

# Uso de memória e disco do sistema
free -h && df -h

# Volumes Docker
docker volume ls | grep portal
```

### **Debug de Problemas**

```bash
# Conectar no container para debug
docker exec -it portal-backend sh
docker exec -it portal-frontend sh
docker exec -it portal-database psql -U portal_user -d portal_evolution

# Verificar configuração do Nginx
docker exec portal-frontend nginx -t

# Verificar variáveis de ambiente
docker exec portal-backend env | grep -E "DB_|JWT_"
```

## 🌐 **ACESSO AO SISTEMA**

### **URLs de Acesso**

```bash
# Para descobrir IP público da VPS:
curl ifconfig.me

# Acessos principais:
https://portal.evo-eden.site         # Sistema principal (HTTPS com SSL)
http://portal.evo-eden.site          # Sistema principal (HTTP - redireciona para HTTPS)
https://portal.evo-eden.site/api/health  # Status da API

# Acesso direto por IP (backup):
http://************                  # Sistema por IP
http://************/api/health       # Status da API por IP
```

### **Credenciais Padrão**

```
👤 Administrador:
   Email: <EMAIL>
   Senha: adminnbr5410!

👤 Cliente Teste:
   Email: <EMAIL>
   Senha: 54321
```

## 🚨 **TROUBLESHOOTING**

### **Problema: Containers não iniciam**

```bash
# Verificar logs
docker-compose -f portal-evolution.yaml logs

# Verificar se rede traefik existe
docker network ls | grep traefik

# Recriar rede se necessário
docker network create traefik
```

### **Problema: Banco de dados não conecta**

```bash
# Verificar se container está rodando
docker ps | grep portal-database

# Verificar logs do banco
docker logs portal-database

# Testar conexão
docker exec portal-database pg_isready -U portal_user
```

### **Problema: Frontend não carrega**

```bash
# Verificar se Nginx está funcionando
docker exec portal-frontend nginx -t

# Verificar logs do frontend
docker logs portal-frontend

# Testar conectividade com backend
docker exec portal-frontend ping portal-backend
```

### **Problema: Erro de permissão**

```bash
# Corrigir permissões dos scripts
chmod +x scripts/*.sh

# Corrigir permissões dos dados
sudo chown -R $USER:$USER dados/
```

## 📦 **BACKUP E RESTAURAÇÃO**

### **Backup Completo**

```bash
# Backup automático (banco + uploads + configuração)
bash scripts/backup-dados.sh

# Verificar backups criados
ls -la dados/backups/
```

### **Restauração**

```bash
# Restaurar banco de dados
cat dados/backups/db_backup_YYYYMMDD_HHMMSS.sql | docker exec -i portal-database psql -U portal_user -d portal_evolution

# Restaurar uploads
tar -xzf dados/backups/uploads_backup_YYYYMMDD_HHMMSS.tar.gz -C dados/

# Restaurar configuração
tar -xzf dados/backups/config_backup_YYYYMMDD_HHMMSS.tar.gz
```

## 🔄 **INTEGRAÇÃO COM VPS EXISTENTE**

Este projeto foi estruturado para integrar perfeitamente com VPS que já possuem:

- ✅ **Traefik** como proxy reverso
- ✅ **Docker** e **Docker Compose**
- ✅ **Rede traefik** configurada
- ✅ **Outros serviços** como PostgreSQL, Redis, etc.

### **Verificar Compatibilidade**

```bash
# Verificar serviços existentes na VPS
docker ps
docker network ls
docker volume ls

# Verificar portas em uso
ss -tlnp | grep -E ":80|:443|:3001|:5432"
```

## � **CONFIGURAR SSL (OBRIGATÓRIO PARA DOMÍNIO)**

### **Configuração SSL para portal.evo-eden.site:**

```bash
# 1. Instalar Certbot (se não estiver instalado)
apt update
apt install certbot python3-certbot-nginx -y

# 2. Parar Nginx temporariamente (se estiver rodando fora do Docker)
systemctl stop nginx 2>/dev/null || true

# 3. Gerar certificado SSL para o domínio
certbot certonly --standalone -d portal.evo-eden.site --email <EMAIL> --agree-tos --non-interactive

# 4. Verificar se certificado foi criado
ls -la /etc/letsencrypt/live/portal.evo-eden.site/

# 5. Configurar renovação automática
crontab -e
# Adicionar linha:
0 12 * * * /usr/bin/certbot renew --quiet --deploy-hook "docker-compose -f /opt/portal-evolution/portal-evolution.yaml restart portal-frontend"
```

### **Verificar SSL:**
```bash
# Testar certificado SSL
curl -I https://portal.evo-eden.site

# Verificar validade do certificado
openssl x509 -in /etc/letsencrypt/live/portal.evo-eden.site/fullchain.pem -text -noout | grep "Not After"

# Testar renovação
certbot renew --dry-run
```

### **Configurar Traefik para SSL:**

Se estiver usando Traefik na VPS, adicione estas labels no `portal-evolution.yaml`:

```yaml
labels:
  - "traefik.enable=true"
  - "traefik.http.routers.portal-web.rule=Host(`portal.evo-eden.site`)"
  - "traefik.http.routers.portal-web.entrypoints=websecure"
  - "traefik.http.routers.portal-web.tls.certresolver=letsencrypt"
  - "traefik.http.routers.portal-web-insecure.rule=Host(`portal.evo-eden.site`)"
  - "traefik.http.routers.portal-web-insecure.entrypoints=web"
  - "traefik.http.routers.portal-web-insecure.middlewares=redirect-to-https"
  - "traefik.http.middlewares.redirect-to-https.redirectscheme.scheme=https"
```

---

## �📞 **SUPORTE**

Para problemas ou dúvidas:

1. **Verificar logs**: `bash scripts/monitorar-servico.sh`
2. **Consultar troubleshooting** neste README
3. **Verificar configuração**: `cat .env`
4. **Testar conectividade**: `curl http://localhost/health`

---

**Sistema pronto para produção em VPS com Docker e Traefik!** 🚀
