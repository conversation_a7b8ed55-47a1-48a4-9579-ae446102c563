const axios = require('axios');

async function testGavetasAPI() {
  try {
    console.log('🔧 TESTANDO NOVA API DE GAVETAS POR PRODUTO\n');
    
    // Fazer login
    console.log('1. 🔐 Fazendo login...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: 'admin',
      senha: 'adminnbr5410!'
    });
    
    const token = loginResponse.data.token;
    console.log('   ✅ Login realizado com sucesso');
    
    // Configurar headers
    const headers = { Authorization: `Bearer ${token}` };
    
    // Testar nova API
    console.log('\n2. 🗂️ Testando nova API de gavetas por produto...');
    const gavetasDetails = await axios.get('http://localhost:3001/api/dashboard/gavetas-por-produto-details', { headers });
    console.log('   ✅ API funcionando! Retornou:', gavetasDetails.data.length, 'produtos');
    
    if (gavetasDetails.data.length > 0) {
      console.log('\n3. 📊 Dados retornados:');
      gavetasDetails.data.forEach((produto, index) => {
        console.log(`   ${index + 1}. ${produto.produto} (${produto.codigo_estacao})`);
        console.log(`      - Total gavetas: ${produto.total_gavetas}`);
        console.log(`      - Gavetas ocupadas: ${produto.gavetas_ocupadas}`);
        console.log(`      - Gavetas disponíveis: ${produto.gavetas_disponiveis}`);
        console.log(`      - Taxa de ocupação: ${produto.taxa_ocupacao}%`);
        console.log('');
      });
    }
    
    console.log('🎉 TESTE DA NOVA API CONCLUÍDO COM SUCESSO!');
    
  } catch (error) {
    console.error('❌ Erro no teste:', error.response?.data || error.message);
  }
}

testGavetasAPI();
