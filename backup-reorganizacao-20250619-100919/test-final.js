#!/usr/bin/env node

/**
 * TESTE FINAL - VERIFICAR TODAS AS CORREÇÕES
 */

const axios = require('axios');
const { Pool } = require('pg');

const BASE_URL = 'https://portal.evo-eden.site/api';

// Configuração do banco de dados
const pool = new Pool({
  host: '************',
  port: 5432,
  database: 'dbetens',
  user: 'postgres',
  password: 'ab3780bd73ee4e2804d566ce6fd96209',
  ssl: false,
});

async function testeFinal() {
  console.log('🧪 TESTE FINAL - VERIFICANDO TODAS AS CORREÇÕES\n');
  
  try {
    // 1. TESTAR CONEXÃO COM BANCO
    console.log('1. 🗄️ Testando conexão com banco...');
    await pool.query('SELECT NOW()');
    console.log('   ✅ Banco conectado');

    // 2. VERIFICAR USUÁRIOS NO BANCO
    console.log('\n2. 👤 Verificando usuários no banco...');
    const usuarios = await pool.query('SELECT email, nome, tipo_usuario FROM usuarios WHERE ativo = true');
    console.log(`   📊 Encontrados ${usuarios.rows.length} usuário(s):`);
    usuarios.rows.forEach(user => {
      console.log(`      - ${user.email} (${user.nome}) - ${user.tipo_usuario}`);
    });

    // 3. TESTAR LOGIN ADMIN
    console.log('\n3. 🔐 Testando login admin...');
    try {
      const loginAdmin = await axios.post(`${BASE_URL}/auth/login`, {
        email: '<EMAIL>',
        password: 'adminnbr5410!'
      });
      console.log('   ✅ Login admin OK');
      console.log(`   👤 Usuário: ${loginAdmin.data.usuario.nome}`);
      console.log(`   🎫 Token: ${loginAdmin.data.token ? 'Presente' : 'Ausente'}`);
      
      // Testar produtos com token admin
      if (loginAdmin.data.token) {
        console.log('\n4. 🏢 Testando produtos com admin...');
        try {
          const produtos = await axios.get(`${BASE_URL}/produtos`, {
            headers: { Authorization: `Bearer ${loginAdmin.data.token}` }
          });
          console.log(`   ✅ Produtos OK: ${produtos.data.length} produto(s)`);
          
          if (produtos.data.length > 0) {
            const produto = produtos.data[0];
            console.log(`   📊 Produto exemplo: ${produto.denominacao} (${produto.codigo_estacao})`);
          }
        } catch (error) {
          console.log(`   ❌ Produtos falhou: ${error.response?.status} - ${error.response?.data?.error || error.message}`);
        }
      }
      
    } catch (error) {
      console.log(`   ❌ Login admin falhou: ${error.response?.status} - ${error.response?.data?.error || error.message}`);
    }

    // 5. TESTAR LOGIN CLIENTE
    console.log('\n5. 👥 Testando login cliente...');
    try {
      const loginCliente = await axios.post(`${BASE_URL}/auth/login`, {
        email: '<EMAIL>',
        password: '54321'
      });
      console.log('   ✅ Login cliente OK');
      console.log(`   👤 Usuário: ${loginCliente.data.usuario.nome}`);
    } catch (error) {
      console.log(`   ❌ Login cliente falhou: ${error.response?.status} - ${error.response?.data?.error || error.message}`);
    }

    // 6. TESTAR HEALTH
    console.log('\n6. 🏥 Testando API Health...');
    try {
      const health = await axios.get(`${BASE_URL}/health`);
      console.log('   ✅ Health OK');
      console.log(`   📊 Status: ${health.data.status}`);
    } catch (error) {
      console.log(`   ❌ Health falhou: ${error.response?.status} - ${error.message}`);
    }

    // 7. TESTAR SISTEMA DE LOGS
    console.log('\n7. 📋 Testando sistema de logs...');
    try {
      const logs = await axios.get('https://logs.portal.evo-eden.site');
      console.log('   ✅ Sistema de logs OK');
      console.log(`   📊 Resposta: ${logs.status}`);
    } catch (error) {
      console.log(`   ❌ Sistema de logs falhou: ${error.response?.status} - ${error.message}`);
    }

    // 8. TESTAR SEPULTAMENTOS
    console.log('\n8. ⚰️ Testando sepultamentos...');
    try {
      const sepultamentos = await axios.get(`${BASE_URL}/sepultamentos`);
      console.log(`   ✅ Sepultamentos OK: ${sepultamentos.data.length} registro(s)`);
    } catch (error) {
      console.log(`   ❌ Sepultamentos falhou: ${error.response?.status} - ${error.response?.data?.error || error.message}`);
    }

    console.log('\n🎯 TESTE FINAL CONCLUÍDO!');
    console.log('=====================================');
    console.log('✅ Portal: https://portal.evo-eden.site');
    console.log('✅ Logs: https://logs.portal.evo-eden.site');
    console.log('✅ API: https://portal.evo-eden.site/api/health');

  } catch (error) {
    console.error('❌ Erro geral no teste:', error.message);
  } finally {
    await pool.end();
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  testeFinal();
}

module.exports = { testeFinal };
