const express = require('express');
const bcrypt = require('bcryptjs');
const { query } = require('./server/database/connection');

async function debugLogin() {
  try {
    console.log('🔍 Debug do login do admin...');
    
    // Buscar usuário no banco
    const result = await query(
      'SELECT * FROM usuarios WHERE email = $1 AND ativo = true',
      ['ma<PERSON><PERSON><PERSON>l<PERSON>@evolutionbr.tech']
    );

    if (result.rows.length === 0) {
      console.log('❌ Usuário não encontrado');
      return;
    }

    const usuario = result.rows[0];
    console.log('📋 Dados do usuário:');
    console.log(`   ID: ${usuario.id}`);
    console.log(`   Email: ${usuario.email}`);
    console.log(`   Nome: ${usuario.nome}`);
    console.log(`   Tipo: ${usuario.tipo_usuario}`);
    console.log(`   Ativo: ${usuario.ativo}`);
    console.log(`   <PERSON>ha armazenada: ${usuario.senha}`);
    console.log(`   Tamanho da senha: ${usuario.senha.length}`);
    
    const senhaTestada = 'adminnbr5410!';
    console.log(`   Senha testada: ${senhaTestada}`);
    console.log(`   Tamanho da senha testada: ${senhaTestada.length}`);
    
    // Testar bcrypt
    let senhaValida = false;
    try {
      senhaValida = await bcrypt.compare(senhaTestada, usuario.senha);
      console.log(`   Bcrypt compare: ${senhaValida}`);
    } catch (error) {
      console.log(`   Erro bcrypt: ${error.message}`);
    }
    
    // Testar comparação direta
    const comparacaoDireta = usuario.senha === senhaTestada;
    console.log(`   Comparação direta: ${comparacaoDireta}`);
    
    // Testar verificação especial do código
    const verificacaoEspecial = (usuario.email === '<EMAIL>') && (senhaTestada === 'adminnbr5410!');
    console.log(`   Verificação especial: ${verificacaoEspecial}`);
    
  } catch (error) {
    console.error('❌ Erro no debug:', error);
  }
}

debugLogin();
