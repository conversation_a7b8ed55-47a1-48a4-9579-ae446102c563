#!/bin/bash

# Script simplificado para configurar VPS
# Execute na VPS como root: bash configurar-vps-simples.sh

set -e

echo "🚀 Configurando VPS para Portal Evolution (Método Simples)..."

# Cores
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
    exit 1
}

# Verificar se está rodando como root
if [[ $EUID -ne 0 ]]; then
   error "Execute este script como root"
fi

# Atualizar sistema
log "Atualizando sistema..."
apt update && apt upgrade -y

# Instalar dependências básicas
log "Instalando dependências..."
apt install -y curl wget git unzip nano htop ufw

# Configurar firewall
log "Configurando firewall..."
ufw --force reset
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp
ufw --force enable

# Instalar Docker
log "Instalando Docker..."
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
systemctl enable docker
systemctl start docker

# Instalar Docker Compose
log "Instalando Docker Compose..."
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Criar diretório do projeto
log "Criando diretório do projeto..."
mkdir -p /opt/portal-evolution
cd /opt/portal-evolution

# Baixar código como ZIP (evita problemas de autenticação)
log "Baixando código do repositório..."
wget -O repo.zip https://github.com/MauricioFilh/portalevo/archive/refs/heads/master.zip
unzip -q repo.zip
mv portalevo-master/* .
mv portalevo-master/.* . 2>/dev/null || true
rm -rf portalevo-master repo.zip

# Navegar para diretório do portal
cd portalcliente

# Tornar scripts executáveis
chmod +x *.sh

# Criar serviço systemd para auto-start
log "Configurando auto-start..."
cat > /etc/systemd/system/portal-evolution.service << 'EOF'
[Unit]
Description=Portal Evolution
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/opt/portal-evolution/portalcliente
ExecStart=/usr/local/bin/docker-compose -f docker-compose.prod.yml --env-file configuracao.env up -d
ExecStop=/usr/local/bin/docker-compose -f docker-compose.prod.yml down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF

# Habilitar serviço
systemctl daemon-reload
systemctl enable portal-evolution.service

# Criar script de backup
log "Criando script de backup..."
cat > /opt/portal-evolution/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/opt/portal-evolution/backups"
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR

# Backup do banco de dados
docker exec portal_database pg_dump -U portal_user portal_evolution > $BACKUP_DIR/db_backup_$DATE.sql 2>/dev/null || echo "Erro no backup - container pode não estar rodando"

# Manter apenas últimos 7 backups
find $BACKUP_DIR -name "db_backup_*.sql" -mtime +7 -delete 2>/dev/null || true

echo "Backup executado: $DATE"
EOF

chmod +x /opt/portal-evolution/backup.sh

# Configurar cron para backup diário
(crontab -l 2>/dev/null; echo "0 2 * * * /opt/portal-evolution/backup.sh >> /var/log/portal-backup.log 2>&1") | crontab -

# Criar script de atualização manual
cat > /opt/portal-evolution/atualizar-manual.sh << 'EOF'
#!/bin/bash
echo "🔄 Atualizando Portal Evolution..."

cd /opt/portal-evolution

# Baixar nova versão
echo "Baixando nova versão..."
wget -O repo.zip https://github.com/MauricioFilh/portalevo/archive/refs/heads/master.zip
unzip -q repo.zip

# Fazer backup da configuração atual
cp portalcliente/configuracao.env configuracao.env.backup

# Atualizar código
rm -rf portalcliente
mv portalevo-master/portalcliente .
rm -rf portalevo-master repo.zip

# Restaurar configuração
mv configuracao.env.backup portalcliente/configuracao.env

# Tornar scripts executáveis
chmod +x portalcliente/*.sh

# Reiniciar containers
cd portalcliente
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml --env-file configuracao.env up -d --build

echo "✅ Atualização concluída!"
EOF

chmod +x /opt/portal-evolution/atualizar-manual.sh

# Criar script de monitoramento
cat > /opt/portal-evolution/monitor.sh << 'EOF'
#!/bin/bash
echo "=== PORTAL EVOLUTION - STATUS ==="
echo "Data: $(date)"
echo ""

cd /opt/portal-evolution/portalcliente

echo "=== CONTAINERS ==="
docker-compose -f docker-compose.prod.yml ps

echo ""
echo "=== SAÚDE DA APLICAÇÃO ==="
curl -s http://localhost/health && echo " - Frontend: OK" || echo " - Frontend: ERRO"
curl -s http://localhost/api/health && echo " - Backend: OK" || echo " - Backend: ERRO"

echo ""
echo "=== RECURSOS ==="
echo "Memória:"
free -h
echo ""
echo "Disco:"
df -h /opt

echo ""
echo "=== LOGS RECENTES ==="
echo "Últimas 5 linhas do log do sistema:"
journalctl -u portal-evolution --no-pager -n 5
EOF

chmod +x /opt/portal-evolution/monitor.sh

log "✅ Configuração da VPS concluída!"
log ""
log "📋 Próximos passos:"
log "1. Edite o arquivo de configuração:"
log "   nano /opt/portal-evolution/portalcliente/configuracao.env"
log ""
log "2. Altere pelo menos estas configurações:"
log "   - DB_PASSWORD (coloque uma senha forte)"
log "   - JWT_SECRET (coloque uma chave forte)"
log "   - DOMAIN (se tiver domínio)"
log ""
log "3. Execute o deploy:"
log "   cd /opt/portal-evolution/portalcliente"
log "   bash deploy.sh"
log ""
log "🔧 Comandos úteis:"
log "   Iniciar: systemctl start portal-evolution"
log "   Parar: systemctl stop portal-evolution"
log "   Status: systemctl status portal-evolution"
log "   Monitor: bash /opt/portal-evolution/monitor.sh"
log "   Atualizar: bash /opt/portal-evolution/atualizar-manual.sh"
log ""
log "📱 Acesso:"
log "   http://$(curl -s ifconfig.me) (quando estiver rodando)"
log ""
warn "⚠️  IMPORTANTE: Configure o arquivo configuracao.env antes de fazer deploy!"
log ""
log "🎉 VPS pronta para o Portal Evolution!"
