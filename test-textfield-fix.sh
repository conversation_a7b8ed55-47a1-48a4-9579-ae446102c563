#!/bin/bash

# Script para testar se o erro do TextField foi corrigido
# Verifica se todos os arquivos que usam TextField têm os imports corretos

echo "🔍 Testando correção do erro TextField..."
echo "=========================================="

# Função para log colorido
log_test() {
    local type=$1
    local message=$2
    case $type in
        "PASS") echo "✅ $message" ;;
        "FAIL") echo "❌ $message" ;;
        "INFO") echo "ℹ️  $message" ;;
        "WARN") echo "⚠️  $message" ;;
    esac
}

# Teste 1: Verificar se todos os arquivos que usam TextField têm import
echo ""
log_test "INFO" "TESTE 1: Verificando imports do TextField..."

# Buscar arquivos que usam TextField mas não têm import
files_with_textfield=$(grep -r "<TextField" portalcliente/src --include="*.jsx" --include="*.js" -l)
files_without_import=""

for file in $files_with_textfield; do
    if ! grep -q "import.*TextField" "$file"; then
        files_without_import="$files_without_import $file"
    fi
done

if [ -z "$files_without_import" ]; then
    log_test "PASS" "Todos os arquivos que usam TextField têm import correto"
else
    log_test "FAIL" "Arquivos sem import do TextField: $files_without_import"
fi

# Teste 2: Verificar se o build funciona sem erros
echo ""
log_test "INFO" "TESTE 2: Verificando se o build funciona..."

cd portalcliente
if npm run build > /dev/null 2>&1; then
    log_test "PASS" "Build executado com sucesso"
else
    log_test "FAIL" "Build falhou - verificar logs"
fi
cd ..

# Teste 3: Verificar se o serviço está rodando
echo ""
log_test "INFO" "TESTE 3: Verificando status do serviço..."

service_status=$(docker service ls --filter name=portalevo_portalevo-frontend --format "{{.Replicas}}")
if [ "$service_status" = "1/1" ]; then
    log_test "PASS" "Serviço está rodando corretamente (1/1 réplicas)"
else
    log_test "FAIL" "Serviço não está rodando corretamente: $service_status"
fi

# Teste 4: Verificar logs do serviço
echo ""
log_test "INFO" "TESTE 4: Verificando logs do serviço..."

recent_logs=$(docker service logs portalevo_portalevo-frontend --tail 5 2>/dev/null)
if echo "$recent_logs" | grep -q "Configuration complete"; then
    log_test "PASS" "Nginx iniciado corretamente"
else
    log_test "WARN" "Logs não mostram inicialização completa"
fi

# Teste 5: Verificar se não há erros de JavaScript no build
echo ""
log_test "INFO" "TESTE 5: Verificando erros de JavaScript..."

cd portalcliente
build_output=$(npm run build 2>&1)
if echo "$build_output" | grep -q "ReferenceError\|is not defined"; then
    log_test "FAIL" "Ainda há erros de referência no build"
else
    log_test "PASS" "Nenhum erro de referência encontrado no build"
fi
cd ..

# Resumo
echo ""
echo "=========================================="
log_test "INFO" "RESUMO DOS TESTES CONCLUÍDO"
echo "=========================================="

# Teste final: Tentar acessar o portal
echo ""
log_test "INFO" "Portal disponível em: https://portal.evo-eden.site"
log_test "INFO" "Teste manual: Acesse o portal e verifique se não há erros no console do navegador"

echo ""
echo "🎯 Correções implementadas:"
echo "   - Adicionado import TextField em CadastrosProdutosPage.jsx"
echo "   - Adicionado import TextField em BookSepultamentosDetalhePage.jsx"
echo "   - Adicionado import TextField em LoginPage.jsx"
echo "   - Adicionados imports Dialog, DialogTitle, DialogContent, DialogActions"
echo "   - Adicionados imports Grid, FormControl, InputLabel, Select, MenuItem, Paper"
echo ""
echo "✅ Deploy realizado com limpeza de imagens antigas e criação de nova imagem"
