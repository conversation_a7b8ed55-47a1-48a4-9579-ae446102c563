# Arquivos de desenvolvimento
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Arquivos de ambiente
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
configuracao.env

# Logs
logs
*.log

# Arquivos temporários
.tmp
.temp
*.tmp
*.temp

# Arquivos de sistema
.DS_Store
Thumbs.db

# Arquivos de IDE
.vscode
.idea
*.swp
*.swo

# Arquivos de build
dist
build
coverage

# Arquivos de teste
test
tests
__tests__
*.test.js
*.spec.js

# Documentação de desenvolvimento
docs/development
*.md
!README.md

# Scripts de desenvolvimento
scripts/dev
scripts/test

# Arquivos Git
.git
.gitignore

# Arquivos Docker
Dockerfile*
docker-compose*
.dockerignore

# Arquivos de backup
*.bak
*.backup
backups/

# Arquivos de dados sensíveis
secrets/
certificates/
keys/
