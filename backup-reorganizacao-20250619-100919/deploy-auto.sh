#!/bin/bash

# ===================================
# PORTAL EVOLUTION - DEPLOY AUTOMÁTICO
# ===================================
# Script para deploy automático na VPS
# Execute: bash deploy-auto.sh

set -e

echo "🚀 Portal Evolution - Deploy Automático"
echo "======================================="

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Verificar se está no diretório correto
if [[ ! -f "configuracao.env" ]]; then
    error "Arquivo configuracao.env não encontrado! Execute no diretório portalcliente"
fi

# Carregar configurações
source configuracao.env

log "Configurações carregadas:"
info "Domínio: $DOMAIN"
info "Banco: $DB_NAME"
info "SSL: ${ENABLE_SSL:-false}"

# Verificar se Docker está rodando
if ! docker info > /dev/null 2>&1; then
    error "Docker não está rodando!"
fi

# Verificar se Docker Swarm está ativo
if ! docker info | grep -q "Swarm: active"; then
    warn "Docker Swarm não está ativo. Inicializando..."
    docker swarm init --advertise-addr $(hostname -I | awk '{print $1}') || true
fi

# Verificar se rede redeinterna existe
if ! docker network ls | grep -q "redeinterna"; then
    warn "Rede 'redeinterna' não encontrada. Criando..."
    docker network create --driver overlay --attachable redeinterna || true
fi

# Verificar se Traefik está rodando
if ! docker service ls | grep -q "traefik_traefik"; then
    warn "Traefik não está rodando!"
    info "Verificando se existe stack traefik..."
    
    if ! docker stack ls | grep -q "traefik"; then
        error "Stack Traefik não encontrada! Configure o Traefik primeiro."
    fi
fi

log "Infraestrutura validada!"

# Atualizar código se for repositório Git
if [[ -d ".git" ]]; then
    log "Atualizando código via git pull..."
    
    # Fazer backup da configuração
    cp configuracao.env configuracao.env.backup
    
    # Tentar git pull
    if git pull origin master 2>/dev/null; then
        log "Git pull executado com sucesso"
        
        # Restaurar configuração se foi sobrescrita
        if [[ -f "configuracao.env.backup" ]]; then
            if ! cmp -s configuracao.env configuracao.env.backup; then
                warn "Arquivo configuracao.env foi alterado. Restaurando backup..."
                mv configuracao.env.backup configuracao.env
            else
                rm configuracao.env.backup
            fi
        fi
    else
        warn "Git pull falhou ou não é um repositório Git"
        rm -f configuracao.env.backup
    fi
else
    info "Não é um repositório Git - usando código atual"
fi

# Parar stack se existir
log "Removendo stack anterior (se existir)..."
docker stack rm portal-evolution 2>/dev/null || true

# Aguardar remoção completa
log "Aguardando remoção completa..."
sleep 15

# Limpar imagens antigas do portal
log "Limpando imagens antigas do portal..."
docker images | grep "portal-evolution" | awk '{print $3}' | xargs -r docker rmi -f 2>/dev/null || true

# Build das imagens
log "Construindo imagens..."

# Build backend
log "Construindo backend..."
if docker build -f Dockerfile.backend -t portal-evolution-backend:latest .; then
    log "✅ Backend construído com sucesso"
else
    error "❌ Erro ao construir backend"
fi

# Build frontend
log "Construindo frontend..."
if docker build -f Dockerfile.frontend -t portal-evolution-frontend:latest .; then
    log "✅ Frontend construído com sucesso"
else
    error "❌ Erro ao construir frontend"
fi

# Deploy da stack
log "Fazendo deploy da stack..."
if docker stack deploy -c docker-compose.prod.yml portal-evolution; then
    log "✅ Stack deployada com sucesso"
else
    error "❌ Erro ao fazer deploy da stack"
fi

# Aguardar serviços iniciarem
log "Aguardando serviços iniciarem..."
sleep 45

# Verificar status dos serviços
log "Verificando status dos serviços..."
docker stack services portal-evolution

# Aguardar mais um pouco para estabilizar
log "Aguardando estabilização..."
sleep 30

# Função para testar URL
test_url() {
    local url=$1
    local name=$2
    local max_attempts=10
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f -s --max-time 10 "$url" > /dev/null; then
            log "✅ $name funcionando: $url"
            return 0
        else
            warn "Tentativa $attempt/$max_attempts - $name não respondeu: $url"
            sleep 10
            ((attempt++))
        fi
    done
    
    warn "❌ $name não está respondendo após $max_attempts tentativas: $url"
    return 1
}

# Testar saúde da aplicação
log "Testando saúde da aplicação..."

# Testar backend
test_url "https://$DOMAIN/api/health" "Backend"
backend_ok=$?

# Testar frontend
test_url "https://$DOMAIN/health" "Frontend"
frontend_ok=$?

# Testar página principal
test_url "https://$DOMAIN" "Portal Principal"
portal_ok=$?

# Resumo do deploy
echo ""
log "🎉 Deploy concluído!"
echo ""
log "📋 RESUMO DO DEPLOY:"

if [[ $backend_ok -eq 0 ]]; then
    log "   ✅ Backend: OK"
else
    warn "   ❌ Backend: PROBLEMA"
fi

if [[ $frontend_ok -eq 0 ]]; then
    log "   ✅ Frontend: OK"
else
    warn "   ❌ Frontend: PROBLEMA"
fi

if [[ $portal_ok -eq 0 ]]; then
    log "   ✅ Portal: OK"
else
    warn "   ❌ Portal: PROBLEMA"
fi

echo ""
log "📋 ACESSO À APLICAÇÃO:"
log "   🌐 Portal: https://$DOMAIN"
log "   🔧 API: https://$DOMAIN/api/health"
echo ""
log "👥 Credenciais padrão:"
log "   Admin: <EMAIL> / adminnbr5410!"
log "   Cliente: <EMAIL> / 54321"
echo ""
log "📊 MONITORAMENTO:"
log "   Stack: docker stack services portal-evolution"
log "   Logs Backend: docker service logs -f portal-evolution_portal_backend"
log "   Logs Frontend: docker service logs -f portal-evolution_portal_frontend"
echo ""

# Verificar se há problemas
if [[ $backend_ok -ne 0 ]] || [[ $frontend_ok -ne 0 ]] || [[ $portal_ok -ne 0 ]]; then
    warn "⚠️ Deploy concluído com problemas!"
    echo ""
    warn "Para diagnosticar:"
    warn "   docker stack services portal-evolution"
    warn "   docker service logs portal-evolution_portal_backend"
    warn "   docker service logs portal-evolution_portal_frontend"
    echo ""
    exit 1
else
    log "🎉 Deploy concluído com sucesso!"
    echo ""
fi
