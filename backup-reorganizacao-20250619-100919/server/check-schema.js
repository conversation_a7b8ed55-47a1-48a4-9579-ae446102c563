const { query } = require('./server/database/connection');

async function checkSchema() {
  try {
    console.log('Verificando estrutura da tabela sepultamentos...');
    
    const result = await query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = $1 
      ORDER BY ordinal_position
    `, ['sepultamentos']);
    
    console.log('Colunas da tabela sepultamentos:');
    result.rows.forEach(row => {
      console.log(`- ${row.column_name}: ${row.data_type}`);
    });
    
    // Verificar se existe coluna status
    const hasStatus = result.rows.some(row => row.column_name === 'status');
    console.log('\nColuna status existe?', hasStatus);
    
    if (!hasStatus) {
      console.log('\n❌ A coluna status não existe na tabela sepultamentos');
      console.log('Isso explica o erro no dashboard');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Erro:', error);
    process.exit(1);
  }
}

checkSchema();
