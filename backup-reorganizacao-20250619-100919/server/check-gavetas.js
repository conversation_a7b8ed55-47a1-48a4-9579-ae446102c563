const { query } = require('./database/connection');

async function checkGavetas() {
  try {
    console.log('🔍 Verificando gavetas disponíveis...');
    
    // Verificar total de gavetas
    const totalGavetas = await query('SELECT COUNT(*) as total FROM gavetas WHERE ativo = true');
    console.log(`📊 Total de gavetas ativas: ${totalGavetas.rows[0].total}`);
    
    // Verificar gavetas disponíveis
    const gavetasDisponiveis = await query('SELECT COUNT(*) as total FROM gavetas WHERE ativo = true AND disponivel = true');
    console.log(`✅ Gavetas disponíveis: ${gavetasDisponiveis.rows[0].total}`);
    
    // Verificar gavetas ocupadas
    const gavetasOcupadas = await query('SELECT COUNT(*) as total FROM gavetas WHERE ativo = true AND disponivel = false');
    console.log(`🔴 Gavetas ocupadas: ${gavetasOcupadas.rows[0].total}`);
    
    // Verificar gavetas por sub-bloco
    const gavetasPorSubBloco = await query(`
      SELECT
        sb.id as sub_bloco_id,
        sb.codigo_sub_bloco,
        COUNT(g.id) as total_gavetas,
        COUNT(CASE WHEN g.disponivel = true THEN 1 END) as gavetas_disponiveis,
        COUNT(CASE WHEN g.disponivel = false THEN 1 END) as gavetas_ocupadas
      FROM sub_blocos sb
      LEFT JOIN gavetas g ON sb.id = g.sub_bloco_id AND g.ativo = true
      WHERE sb.ativo = true
      GROUP BY sb.id, sb.codigo_sub_bloco
      ORDER BY sb.codigo_sub_bloco
    `);
    
    console.log('\n📋 Gavetas por Sub-bloco:');
    gavetasPorSubBloco.rows.forEach(row => {
      console.log(`  ${row.codigo_sub_bloco}: ${row.gavetas_disponiveis}/${row.total_gavetas} disponíveis`);
    });
    
    // Verificar algumas gavetas específicas
    const gavetasDetalhes = await query(`
      SELECT 
        g.id,
        g.numero_gaveta,
        g.disponivel,
        sb.codigo_sub_bloco,
        b.codigo_bloco,
        p.codigo_estacao
      FROM gavetas g
      JOIN sub_blocos sb ON g.sub_bloco_id = sb.id
      JOIN blocos b ON sb.bloco_id = b.id
      JOIN produtos p ON b.produto_id = p.id
      WHERE g.ativo = true
      ORDER BY p.codigo_estacao, b.codigo_bloco, sb.codigo_sub_bloco, g.numero_gaveta
      LIMIT 20
    `);
    
    console.log('\n🔍 Primeiras 20 gavetas:');
    gavetasDetalhes.rows.forEach(row => {
      const status = row.disponivel ? '✅ DISPONÍVEL' : '🔴 OCUPADA';
      console.log(`  ${row.codigo_estacao}-${row.codigo_bloco}-${row.codigo_sub_bloco}-${row.numero_gaveta}: ${status}`);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Erro ao verificar gavetas:', error);
    process.exit(1);
  }
}

checkGavetas();
