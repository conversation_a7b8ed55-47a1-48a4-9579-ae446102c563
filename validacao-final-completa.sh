#!/bin/bash

# 🧪 VALIDAÇÃO FINAL COMPLETA - PORTAL EVOLUTION
# Valida se todos os erros foram corrigidos (405 e 500)

echo "🧪 VALIDAÇÃO FINAL COMPLETA DO PORTAL EVOLUTION"
echo "=============================================="

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log colorido
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Contador de testes
TOTAL_TESTS=0
PASSED_TESTS=0

# Função para executar teste
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_pattern="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    log_info "Teste $TOTAL_TESTS: $test_name"
    
    result=$(eval "$test_command" 2>/dev/null)
    
    if echo "$result" | grep -q "$expected_pattern"; then
        log_success "PASSOU: $test_name"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        log_error "FALHOU: $test_name"
        echo "  Resultado: $result"
        return 1
    fi
}

echo ""
log_info "Iniciando validação completa..."

# TESTE 1: Verificar se os serviços estão rodando
run_test "Serviços Docker Swarm" \
    "docker stack services portal-evolution | grep '1/1'" \
    "portal-evolution_portal_backend.*1/1"

# TESTE 2: Testar Health da API
run_test "API Health Check" \
    "curl -s https://portal.evo-eden.site/api/health" \
    '"status":"OK"'

# TESTE 3: Testar Frontend (página principal)
run_test "Frontend Carregando" \
    "curl -s -o /dev/null -w '%{http_code}' https://portal.evo-eden.site" \
    "200"

# TESTE 4: Testar Login (corrigir erro 405)
run_test "Login API (Erro 405 Corrigido)" \
    "curl -s -X POST https://portal.evo-eden.site/api/auth/login -H 'Content-Type: application/json' -d '{\"email\":\"<EMAIL>\",\"password\":\"54321\"}'" \
    '"message":"Login realizado com sucesso"'

# TESTE 5: Verificar se não há mais erro 405
run_test "Verificar Ausência de Erro 405" \
    "curl -s -X POST https://portal.evo-eden.site/api/auth/login -H 'Content-Type: application/json' -d '{}' -w '%{http_code}'" \
    "400"  # Deve retornar 400 (bad request) e não 405 (method not allowed)

# Obter token para testes autenticados
TOKEN=$(curl -s -X POST https://portal.evo-eden.site/api/auth/login -H "Content-Type: application/json" -d '{"email":"<EMAIL>","password":"54321"}' | jq -r '.token' 2>/dev/null || echo "")

if [ ! -z "$TOKEN" ] && [ "$TOKEN" != "null" ]; then
    log_info "Token obtido com sucesso para testes autenticados"
    
    # TESTE 6: Dashboard sem parâmetros
    run_test "Dashboard Stats (Sem Parâmetros)" \
        "curl -s -H 'Authorization: Bearer $TOKEN' https://portal.evo-eden.site/api/dashboard/stats" \
        '"total_sepultamentos"'
    
    # TESTE 7: Dashboard com cliente_id (que causava erro 500)
    run_test "Dashboard Stats com cliente_id (Erro 500 Corrigido)" \
        "curl -s -H 'Authorization: Bearer $TOKEN' https://portal.evo-eden.site/api/dashboard/stats?cliente_id=17" \
        '"total_sepultamentos"'
    
    # TESTE 8: Dashboard com código_cliente
    run_test "Dashboard Stats com codigo_cliente" \
        "curl -s -H 'Authorization: Bearer $TOKEN' https://portal.evo-eden.site/api/dashboard/stats?codigo_cliente=ITV_001" \
        '"total_sepultamentos"'
    
    # TESTE 9: Verificar se não retorna erro 500
    run_test "Verificar Ausência de Erro 500 no Dashboard" \
        "curl -s -H 'Authorization: Bearer $TOKEN' https://portal.evo-eden.site/api/dashboard/stats?cliente_id=999 -w '%{http_code}'" \
        "200"  # Deve retornar 200 e não 500
    
else
    log_error "Não foi possível obter token para testes autenticados"
    TOTAL_TESTS=$((TOTAL_TESTS + 4))
fi

# TESTE 10: Verificar logs do backend (sem erros críticos)
run_test "Logs do Backend (Sem Erros Críticos)" \
    "docker service logs portal-evolution_portal_backend --tail 10 | grep -v 'ERROR\\|FATAL\\|❌'" \
    "portal-evolution_portal_backend"

echo ""
echo "📊 RESUMO DA VALIDAÇÃO FINAL"
echo "============================"
log_info "Total de testes: $TOTAL_TESTS"
log_success "Testes aprovados: $PASSED_TESTS"

if [ $PASSED_TESTS -eq $TOTAL_TESTS ]; then
    echo ""
    log_success "🎉 TODOS OS TESTES PASSARAM!"
    log_success "✅ Erro 405 (Method Not Allowed) CORRIGIDO"
    log_success "✅ Erro 500 (Internal Server Error) CORRIGIDO"
    log_success "✅ Frontend e Backend funcionando perfeitamente"
    log_success "✅ API de dashboard operacional com todos os parâmetros"
    echo ""
    log_info "🌐 Portal totalmente funcional:"
    echo "  Portal: https://portal.evo-eden.site"
    echo "  API: https://portal.evo-eden.site/api"
    echo "  Dashboard: https://portal.evo-eden.site/api/dashboard/stats"
    echo ""
    log_info "🔑 Credenciais de teste:"
    echo "  Admin: <EMAIL> / adminnbr5410!"
    echo "  Cliente: <EMAIL> / 54321"
    echo ""
    log_success "🚀 PORTAL EVOLUTION TOTALMENTE OPERACIONAL!"
else
    echo ""
    log_error "❌ ALGUNS TESTES FALHARAM ($((TOTAL_TESTS - PASSED_TESTS)) de $TOTAL_TESTS)"
    log_warning "Verifique os logs dos serviços:"
    echo "  Backend: docker service logs portal-evolution_portal_backend"
    echo "  Frontend: docker service logs portal-evolution_portal_frontend"
fi

echo ""
log_info "Status atual dos serviços:"
docker stack services portal-evolution

echo ""
log_success "Validação final concluída! 🧪"
