# ===================================
# PORTAL EVOLUTION - DOCKER COMPOSE
# ===================================
# Arquivo de orquestração seguindo padrão da VPS
# Conecta à rede traefik existente

version: '3.8'

services:
  # ===================================
  # BANCO DE DADOS POSTGRESQL
  # ===================================
  portal-database:
    image: postgres:15-alpine
    container_name: portal-database
    restart: always
    environment:
      # Variáveis do banco - definidas no arquivo .env
      POSTGRES_DB: ${DB_NAME}
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      PGDATA: /var/lib/postgresql/data/pgdata
      TZ: ${TZ:-America/Sao_Paulo}
    volumes:
      # Volume persistente para dados do banco
      # Para verificar: docker volume ls | grep portal
      - portal_postgres_data:/var/lib/postgresql/data
      # Script de inicialização do banco
      - ./app/server/database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - traefik
      - portal_internal
    healthcheck:
      # Verificação de saúde do banco
      # Para testar: docker exec portal-database pg_isready -U ${DB_USER}
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER} -d ${DB_NAME}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: ${DATABASE_MEMORY:-1g}
        reservations:
          memory: 512M
    labels:
      # Labels para identificação
      - "traefik.enable=false"
      - "portal.service=database"

  # ===================================
  # BACKEND NODE.JS
  # ===================================
  portal-backend:
    build:
      context: ./app
      dockerfile: ../docker/Dockerfile.backend
    image: portal-evolution/backend:latest
    container_name: portal-backend
    restart: always
    environment:
      # Variáveis de ambiente do backend
      NODE_ENV: ${NODE_ENV:-production}
      DB_HOST: portal-database
      DB_PORT: ${DB_PORT:-5432}
      DB_NAME: ${DB_NAME}
      DB_USER: ${DB_USER}
      DB_PASSWORD: ${DB_PASSWORD}
      JWT_SECRET: ${JWT_SECRET}
      PORT: ${PORT:-3001}
      EMAIL_HOST: ${EMAIL_HOST}
      EMAIL_PORT: ${EMAIL_PORT}
      EMAIL_USER: ${EMAIL_USER}
      EMAIL_PASS: ${EMAIL_PASS}
      TZ: ${TZ:-America/Sao_Paulo}
    volumes:
      # Logs da aplicação
      - ./dados/logs:/app/logs
      # Uploads de arquivos
      - ./dados/uploads:/app/uploads
    depends_on:
      portal-database:
        condition: service_healthy
    networks:
      - traefik
      - portal_internal
    healthcheck:
      # Verificação de saúde do backend
      # Para testar: curl http://localhost:3001/api/health
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3001/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: ${BACKEND_MEMORY:-512m}
        reservations:
          memory: 256M
    labels:
      # Configuração Traefik para roteamento da API
      - "traefik.enable=true"
      - "traefik.http.routers.portal-api.rule=Host(`${DOMAIN:-${VPS_HOST}}`) && PathPrefix(`/api`)"
      - "traefik.http.routers.portal-api.entrypoints=websecure"
      - "traefik.http.routers.portal-api.tls.certresolver=letsencrypt"
      - "traefik.http.services.portal-api.loadbalancer.server.port=3001"
      - "portal.service=backend"

  # ===================================
  # FRONTEND REACT + NGINX
  # ===================================
  portal-frontend:
    build:
      context: ./app
      dockerfile: ../docker/Dockerfile.frontend
    image: portal-evolution/frontend:latest
    container_name: portal-frontend
    restart: always
    environment:
      TZ: ${TZ:-America/Sao_Paulo}
    volumes:
      # Configuração customizada do Nginx
      - ./docker/nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      portal-backend:
        condition: service_healthy
    networks:
      - traefik
    healthcheck:
      # Verificação de saúde do frontend
      # Para testar: curl http://localhost/health
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: ${FRONTEND_MEMORY:-256m}
        reservations:
          memory: 128M
    labels:
      # Configuração Traefik para roteamento principal
      - "traefik.enable=true"
      - "traefik.http.routers.portal-web.rule=Host(`${DOMAIN:-${VPS_HOST}}`)"
      - "traefik.http.routers.portal-web.entrypoints=websecure"
      - "traefik.http.routers.portal-web.tls.certresolver=letsencrypt"
      - "traefik.http.services.portal-web.loadbalancer.server.port=80"
      - "portal.service=frontend"

# ===================================
# VOLUMES PERSISTENTES
# ===================================
volumes:
  portal_postgres_data:
    driver: local
    # Para verificar: docker volume inspect portal_postgres_data

# ===================================
# REDES
# ===================================
networks:
  # Rede externa Traefik (já deve existir na VPS)
  # Para verificar: docker network ls | grep traefik
  traefik:
    external: true
  
  # Rede interna para comunicação entre serviços
  portal_internal:
    driver: bridge
    internal: false

# ===================================
# INSTRUÇÕES DE USO
# ===================================

# Para iniciar todos os serviços:
# docker-compose -f portal-evolution.yaml up -d

# Para verificar status:
# docker-compose -f portal-evolution.yaml ps

# Para ver logs:
# docker-compose -f portal-evolution.yaml logs -f

# Para parar todos os serviços:
# docker-compose -f portal-evolution.yaml down

# Para atualizar (rebuild):
# docker-compose -f portal-evolution.yaml up -d --build

# Para verificar saúde dos serviços:
# docker-compose -f portal-evolution.yaml exec portal-backend curl http://localhost:3001/api/health
# docker-compose -f portal-evolution.yaml exec portal-frontend curl http://localhost/health
