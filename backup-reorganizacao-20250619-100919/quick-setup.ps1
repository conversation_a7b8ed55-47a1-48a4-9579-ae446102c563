# ===================================
# PORTAL EVOLUTION - CONFIGURACAO RAPIDA
# ===================================
# Script para configuracao inicial rapida
# Execute: .\quick-setup.ps1

param(
    [switch]$SkipGit,     # Pular configuracao do Git
    [switch]$SkipSSH,     # Pular teste SSH
    [switch]$Force        # Forcar configuracao
)

# Funcao para log
function Write-Log {
    param([string]$Message, [string]$Color = "Green")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] $Message" -ForegroundColor $Color
}

function Write-Info {
    param([string]$Message)
    Write-Log $Message "Cyan"
}

function Write-Success {
    param([string]$Message)
    Write-Log $Message "Green"
}

function Write-Warning {
    param([string]$Message)
    Write-Log $Message "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-Log $Message "Red"
}

# Definir diretorio do script
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $scriptPath

Write-Host ""
Write-Host "PORTAL EVOLUTION - CONFIGURACAO RAPIDA" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan
Write-Host ""

# Verificar se ja existe configuracao
if ((Test-Path "configuracao.env") -and -not $Force) {
    Write-Warning "Arquivo configuracao.env ja existe!"
    Write-Host "Use -Force para sobrescrever ou edite manualmente." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Para editar: notepad configuracao.env" -ForegroundColor White
    Write-Host "Para forcar: .\quick-setup.ps1 -Force" -ForegroundColor White
    exit 0
}

Write-Info "Iniciando configuracao rapida..."

# Verificar dependencias
Write-Info "Verificando dependencias..."

# Git
if (-not $SkipGit) {
    try {
        $gitVersion = git --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Git: $gitVersion"
        } else {
            Write-Warning "Git nao encontrado - algumas funcionalidades podem nao funcionar"
        }
    } catch {
        Write-Warning "Git nao encontrado - algumas funcionalidades podem nao funcionar"
    }
}

# SSH
if (-not $SkipSSH) {
    try {
        $sshVersion = ssh -V 2>&1 | Select-Object -First 1
        Write-Success "SSH: $sshVersion"
    } catch {
        Write-Warning "SSH nao encontrado - deploy automatico pode nao funcionar"
        Write-Host "Instale o OpenSSH" -ForegroundColor Yellow
    }
}

# Node.js
try {
    $nodeVersion = node --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Node.js: $nodeVersion"
    } else {
        Write-Warning "Node.js nao encontrado - desenvolvimento local pode nao funcionar"
        Write-Host "Instale o Node.js: https://nodejs.org/" -ForegroundColor Yellow
    }
} catch {
    Write-Warning "Node.js nao encontrado - desenvolvimento local pode nao funcionar"
}

Write-Host ""

# Coletar informacoes
Write-Info "Coletando informacoes de configuracao..."
Write-Host ""

# VPS
Write-Host "CONFIGURACAO DA VPS:" -ForegroundColor Cyan
$vpsHost = Read-Host "IP da VPS (padrao: ************)"
if ([string]::IsNullOrWhiteSpace($vpsHost)) { $vpsHost = "************" }

$vpsUser = Read-Host "Usuario da VPS (padrao: root)"
if ([string]::IsNullOrWhiteSpace($vpsUser)) { $vpsUser = "root" }

Write-Host ""

# Dominio
Write-Host "CONFIGURACAO DO DOMINIO:" -ForegroundColor Cyan
$domain = Read-Host "Dominio (padrao: portal.evo-eden.site)"
if ([string]::IsNullOrWhiteSpace($domain)) { $domain = "portal.evo-eden.site" }

$sslEmail = Read-Host "Email para SSL (padrao: <EMAIL>)"
if ([string]::IsNullOrWhiteSpace($sslEmail)) { $sslEmail = "<EMAIL>" }

Write-Host ""

# Banco de dados
Write-Host "CONFIGURACAO DO BANCO:" -ForegroundColor Cyan
$dbHost = Read-Host "Host do banco (padrao: postgres_postgres)"
if ([string]::IsNullOrWhiteSpace($dbHost)) { $dbHost = "postgres_postgres" }

$dbName = Read-Host "Nome do banco (padrao: dbetens)"
if ([string]::IsNullOrWhiteSpace($dbName)) { $dbName = "dbetens" }

$dbUser = Read-Host "Usuario do banco (padrao: postgres)"
if ([string]::IsNullOrWhiteSpace($dbUser)) { $dbUser = "postgres" }

$dbPassword = Read-Host "Senha do banco (padrao: ab3780bd73ee4e2804d566ce6fd96209)" -AsSecureString
if ($dbPassword.Length -eq 0) { 
    $dbPassword = ConvertTo-SecureString "ab3780bd73ee4e2804d566ce6fd96209" -AsPlainText -Force 
}
$dbPasswordText = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($dbPassword))

Write-Host ""

# JWT Secret
Write-Host "CONFIGURACAO DE SEGURANCA:" -ForegroundColor Cyan
$jwtSecret = Read-Host "JWT Secret (deixe vazio para gerar automaticamente)"
if ([string]::IsNullOrWhiteSpace($jwtSecret)) {
    $jwtSecret = "portal_jwt_secret_key_muito_segura_" + (Get-Date -Format "yyyy")
}

Write-Host ""

# Testar SSH se solicitado
if (-not $SkipSSH) {
    Write-Info "Testando conectividade SSH..."
    try {
        $sshTest = ssh -o ConnectTimeout=10 -o BatchMode=yes $vpsUser@$vpsHost "echo 'SSH OK'" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "SSH conectado com sucesso!"
        } else {
            Write-Warning "Nao foi possivel conectar via SSH"
            Write-Host "Verifique:" -ForegroundColor Yellow
            Write-Host "   - Conectividade de rede" -ForegroundColor Yellow
            Write-Host "   - Credenciais SSH" -ForegroundColor Yellow
            Write-Host "   - Chaves SSH configuradas" -ForegroundColor Yellow
        }
    } catch {
        Write-Warning "Erro ao testar SSH: $_"
    }
    Write-Host ""
}

# Criar arquivo de configuracao
Write-Info "Criando arquivo de configuracao..."

$configContent = @"
# ===================================
# CONFIGURACAO DO PORTAL EVOLUTION
# ===================================
# Configuracao gerada automaticamente em $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")

# ===================================
# DADOS DA VPS
# ===================================
VPS_HOST=$vpsHost
VPS_USER=$vpsUser
VPS_PORT=22

# ===================================
# DOMINIO E SSL
# ===================================
DOMAIN=$domain
SSL_EMAIL=$sslEmail
ENABLE_SSL=true

# ===================================
# BANCO DE DADOS (PostgreSQL Existente)
# ===================================
DB_HOST=$dbHost
DB_PORT=5432
DB_NAME=$dbName
DB_USER=$dbUser
DB_PASSWORD=$dbPasswordText

# ===================================
# APLICACAO
# ===================================
NODE_ENV=production
PORT=3001
JWT_SECRET=$jwtSecret

# ===================================
# EMAIL (OPCIONAL)
# ===================================
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=
EMAIL_PASS=

# ===================================
# CONFIGURACOES AVANCADAS
# ===================================
# Numero de replicas dos servicos
FRONTEND_REPLICAS=1
BACKEND_REPLICAS=1

# Recursos dos containers
FRONTEND_MEMORY=256m
BACKEND_MEMORY=512m
DATABASE_MEMORY=1g

# ===================================
# BACKUP
# ===================================
BACKUP_ENABLED=true
BACKUP_RETENTION_DAYS=7
"@

# Salvar arquivo
$configContent | Out-File -FilePath "configuracao.env" -Encoding UTF8

Write-Success "Arquivo configuracao.env criado!"

# Tornar scripts executaveis (se no Linux/WSL)
if ($IsLinux -or $IsMacOS) {
    Write-Info "Tornando scripts executaveis..."
    chmod +x *.sh
}

Write-Host ""
Write-Success "CONFIGURACAO CONCLUIDA!"
Write-Host ""
Write-Host "PROXIMOS PASSOS:" -ForegroundColor Cyan
Write-Host ""
Write-Host "1. Para desenvolvimento local:" -ForegroundColor White
Write-Host "   .\start-app.ps1 -Local" -ForegroundColor Gray
Write-Host ""
Write-Host "2. Para deploy na VPS:" -ForegroundColor White
Write-Host "   .\start-app.ps1 -Deploy" -ForegroundColor Gray
Write-Host ""
Write-Host "3. Para ambos (local + deploy):" -ForegroundColor White
Write-Host "   .\start-app.ps1" -ForegroundColor Gray
Write-Host ""
Write-Host "4. Para editar configuracoes:" -ForegroundColor White
Write-Host "   notepad configuracao.env" -ForegroundColor Gray
Write-Host ""
Write-Host "DOCUMENTACAO:" -ForegroundColor Cyan
Write-Host "   README.md - Documentacao completa" -ForegroundColor White
Write-Host "   docs/ - Documentacao tecnica" -ForegroundColor White
Write-Host ""
