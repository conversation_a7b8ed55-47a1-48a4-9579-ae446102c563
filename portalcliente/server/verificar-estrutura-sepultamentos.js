const { Pool } = require('pg');

// Configuração do banco de dados
const pool = new Pool({
  host: '************',
  port: 5432,
  database: 'dbetens',
  user: 'postgres',
  password: 'ab3780bd73ee4e2804d566ce6fd96209',
  ssl: false,
});

async function verificarEstruturaSepultamentos() {
  try {
    console.log('🔍 VERIFICANDO ESTRUTURA DA TABELA SEPULTAMENTOS\n');
    
    // Verificar colunas existentes
    console.log('1. 📋 Colunas existentes na tabela sepultamentos:');
    const colunas = await pool.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'sepultamentos' 
      ORDER BY ordinal_position
    `);
    
    console.log('   Colunas encontradas:');
    colunas.rows.forEach((col, index) => {
      console.log(`   ${index + 1}. ${col.column_name} (${col.data_type}) - ${col.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'}`);
    });
    
    // Verificar se as colunas necessárias existem
    console.log('\n2. 🎯 Verificando colunas necessárias:');
    const colunasNecessarias = ['codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco'];
    const colunasExistentes = colunas.rows.map(col => col.column_name);
    
    colunasNecessarias.forEach(coluna => {
      const existe = colunasExistentes.includes(coluna);
      console.log(`   ${existe ? '✅' : '❌'} ${coluna}: ${existe ? 'EXISTE' : 'NÃO EXISTE'}`);
    });
    
    // Verificar alguns registros de exemplo
    console.log('\n3. 📊 Registros de exemplo (primeiros 5):');
    try {
      const registros = await pool.query(`
        SELECT id, nome_sepultado, data_sepultamento, 
               ${colunasExistentes.includes('codigo_cliente') ? 'codigo_cliente,' : ''} 
               ${colunasExistentes.includes('codigo_estacao') ? 'codigo_estacao,' : ''} 
               ${colunasExistentes.includes('codigo_bloco') ? 'codigo_bloco,' : ''} 
               ${colunasExistentes.includes('codigo_sub_bloco') ? 'codigo_sub_bloco,' : ''} 
               gaveta_id
        FROM sepultamentos 
        ORDER BY created_at DESC 
        LIMIT 5
      `);
      
      if (registros.rows.length > 0) {
        console.log('   Registros encontrados:');
        registros.rows.forEach((reg, index) => {
          console.log(`   ${index + 1}. ID: ${reg.id} | Nome: ${reg.nome_sepultado} | Data: ${reg.data_sepultamento}`);
          if (reg.codigo_cliente) console.log(`      Cliente: ${reg.codigo_cliente}`);
          if (reg.codigo_estacao) console.log(`      Estação: ${reg.codigo_estacao}`);
          if (reg.codigo_bloco) console.log(`      Bloco: ${reg.codigo_bloco}`);
          if (reg.codigo_sub_bloco) console.log(`      Sub-bloco: ${reg.codigo_sub_bloco}`);
          console.log(`      Gaveta ID: ${reg.gaveta_id}`);
          console.log('');
        });
      } else {
        console.log('   ⚠️  Nenhum registro encontrado');
      }
    } catch (error) {
      console.log('   ❌ Erro ao buscar registros:', error.message);
    }
    
    // Verificar foreign keys
    console.log('4. 🔗 Verificando foreign keys:');
    const foreignKeys = await pool.query(`
      SELECT 
        tc.constraint_name, 
        tc.table_name, 
        kcu.column_name, 
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name 
      FROM information_schema.table_constraints AS tc 
      JOIN information_schema.key_column_usage AS kcu
        ON tc.constraint_name = kcu.constraint_name
        AND tc.table_schema = kcu.table_schema
      JOIN information_schema.constraint_column_usage AS ccu
        ON ccu.constraint_name = tc.constraint_name
        AND ccu.table_schema = tc.table_schema
      WHERE tc.constraint_type = 'FOREIGN KEY' 
      AND tc.table_name = 'sepultamentos'
    `);
    
    if (foreignKeys.rows.length > 0) {
      console.log('   Foreign keys encontradas:');
      foreignKeys.rows.forEach((fk, index) => {
        console.log(`   ${index + 1}. ${fk.column_name} → ${fk.foreign_table_name}.${fk.foreign_column_name}`);
      });
    } else {
      console.log('   ⚠️  Nenhuma foreign key encontrada');
    }
    
    console.log('\n🎯 VERIFICAÇÃO CONCLUÍDA!');
    
  } catch (error) {
    console.error('❌ Erro ao verificar estrutura:', error);
    process.exit(1);
  } finally {
    await pool.end();
    console.log('🔌 Conexão encerrada');
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  verificarEstruturaSepultamentos();
}

module.exports = { verificarEstruturaSepultamentos };
