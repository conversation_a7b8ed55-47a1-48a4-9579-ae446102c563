import { useState, useEffect } from 'react';
import {
  Grid,
  Typography,
  Box,
  LinearProgress,
  Alert,
  Skeleton,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  useTheme,
  useMediaQuery,
  Chip,
  CircularProgress,
} from '@mui/material';
import {
  People as PeopleIcon,
  Apartment as ApartmentIcon,
  WarningAmber as WarningAmberIcon,
  ExpandMore as ExpandMoreIcon,
  Dashboard as DashboardIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { dashboardService, sepultamentoService, clienteService } from '../services/api';
import Modal from '../components/Modal';
import {
  StandardContainer,
  StandardButton,
  StatsCard,
  StandardSelect,
  StandardTable,
  ContentSection,
  FormGrid,
  FormGridItem,
} from '../components/common';

const HomePage = () => {
  const [stats, setStats] = useState(null);
  const [proximasExumacoes, setProximasExumacoes] = useState([]);
  const [loading, setLoading] = useState(true);

  const [selectedCard, setSelectedCard] = useState(null);
  const [cardDetails, setCardDetails] = useState(null);
  const [detailsLoading, setDetailsLoading] = useState(false);
  const [exumacoesPrevistas, setExumacoesPrevistas] = useState(0);

  // Estados para modais de detalhes
  const [modalTaxaSepultamento, setModalTaxaSepultamento] = useState(false);
  const [modalTaxaOcupacao, setModalTaxaOcupacao] = useState(false);
  const [modalExumacoes30Dias, setModalExumacoes30Dias] = useState(false);
  const [modalTodasExumacoes, setModalTodasExumacoes] = useState(false);

  const [taxaSepultamentoDetalhes, setTaxaSepultamentoDetalhes] = useState([]);
  const [taxaOcupacaoDetalhes, setTaxaOcupacaoDetalhes] = useState([]);
  const [exumacoes30Dias, setExumacoes30Dias] = useState([]);
  const [todasExumacoes, setTodasExumacoes] = useState({});

  // Estados para filtro de cliente (apenas para admin)
  const [clientes, setClientes] = useState([]);
  const [selectedClienteId, setSelectedClienteId] = useState('');

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  useEffect(() => {
    if (user) {
      loadDashboardData();
      if (user.tipo_usuario === 'admin') {
        loadClientes();
      }
    }
  }, [user]);

  useEffect(() => {
    // Recarregar dados quando o filtro de cliente mudar (apenas se user já estiver carregado)
    if (user) {
      loadDashboardData();
    }
  }, [selectedClienteId, user]);

  const loadClientes = async () => {
    try {
      console.log('🔍 Carregando clientes...');
      const response = await clienteService.listar();
      console.log('📋 Clientes carregados:', response.data);
      console.log('📋 Primeiro cliente:', response.data[0]);

      setClientes(response.data);
    } catch (error) {
      console.error('❌ Erro ao carregar clientes:', error);
    }
  };

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Preparar parâmetros de filtro
      const params = {};
      if (selectedClienteId && selectedClienteId !== '' && user?.tipo_usuario === 'admin') {
        params.cliente_id = parseInt(selectedClienteId);
        console.log('📊 Carregando dados para cliente:', selectedClienteId, 'ID:', params.cliente_id);
      } else if (user?.tipo_usuario === 'admin' && (!selectedClienteId || selectedClienteId === '')) {
        console.log('📊 Carregando dados para todos os clientes (admin)');
      } else {
        console.log('📊 Carregando dados para cliente logado');
      }

      // Carregar estatísticas
      const statsResponse = await dashboardService.getStats(params);
      setStats(statsResponse.data);

      // Carregar próximas exumações
      const exumacoesResponse = await dashboardService.getProximasExumacoes(params);
      setProximasExumacoes(exumacoesResponse.data);

      // Carregar exumações previstas para próximos 30 dias
      const exumacoesPrevistasResponse = await dashboardService.getExumacoesPrevistas30Dias(params);
      setExumacoesPrevistas(exumacoesPrevistasResponse.data.total || 0);

    } catch (error) {
      console.error('Erro ao carregar dados do dashboard:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '-';

    // CORREÇÃO TIMEZONE: Usar split para evitar problema de fuso horário
    // Problema: new Date(dateString) converte para UTC e subtrai 1 dia
    // Solução: Fazer parse manual da data no formato YYYY-MM-DD
    const dateParts = dateString.split('T')[0].split('-');
    if (dateParts.length === 3) {
      const year = parseInt(dateParts[0]);
      const month = parseInt(dateParts[1]) - 1; // Mês é 0-indexado
      const day = parseInt(dateParts[2]);
      const localDate = new Date(year, month, day);
      return localDate.toLocaleDateString('pt-BR');
    }

    // Fallback para formato antigo (não deveria acontecer)
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const getProgressColor = () => {
    // Sempre retorna vermelho conforme instrução
    return 'error';
  };

  const handleCardClick = async (cardType) => {
    if (cardType === 'total_sepultamentos') {
      setModalTaxaSepultamento(true);
      await buscarTaxaSepultamentoDetalhes();
    } else if (cardType === 'gavetas_ocupadas') {
      setModalTaxaOcupacao(true);
      await buscarTaxaOcupacaoDetalhes();
    } else if (cardType === 'exumacoes_previstas') {
      setModalExumacoes30Dias(true);
      await buscarExumacoes30Dias();
    } else if (cardType === 'todas_exumacoes') {
      setModalTodasExumacoes(true);
      await buscarTodasExumacoes();
    }
  };

  const handleCloseModal = () => {
    setSelectedCard(null);
    setCardDetails(null);
  };

  // Funções para buscar detalhes dos modais
  const buscarTaxaSepultamentoDetalhes = async () => {
    try {
      setDetailsLoading(true);
      const params = {};
      if (user?.tipo_usuario === 'admin' && selectedClienteId) {
        params.cliente_id = selectedClienteId;
      }
      const response = await dashboardService.getTaxaSepultamentoDetalhes(params);
      setTaxaSepultamentoDetalhes(response.data);
    } catch (error) {
      console.error('Erro ao buscar detalhes da taxa de sepultamento:', error);
    } finally {
      setDetailsLoading(false);
    }
  };

  const buscarTaxaOcupacaoDetalhes = async () => {
    try {
      setDetailsLoading(true);
      const params = {};
      if (user?.tipo_usuario === 'admin' && selectedClienteId) {
        params.cliente_id = selectedClienteId;
      }
      const response = await dashboardService.getTaxaOcupacaoDetalhes(params);
      setTaxaOcupacaoDetalhes(response.data);
    } catch (error) {
      console.error('Erro ao buscar detalhes da taxa de ocupação:', error);
    } finally {
      setDetailsLoading(false);
    }
  };

  const buscarExumacoes30Dias = async () => {
    try {
      setDetailsLoading(true);
      const params = {};
      if (user?.tipo_usuario === 'admin' && selectedClienteId) {
        params.cliente_id = selectedClienteId;
      }
      const response = await dashboardService.getExumacoes30Dias(params);
      setExumacoes30Dias(response.data);
    } catch (error) {
      console.error('Erro ao buscar exumações dos próximos 30 dias:', error);
    } finally {
      setDetailsLoading(false);
    }
  };

  const buscarTodasExumacoes = async () => {
    try {
      setDetailsLoading(true);
      const params = {};
      if (user?.tipo_usuario === 'admin' && selectedClienteId) {
        params.cliente_id = selectedClienteId;
      }
      const response = await dashboardService.getTodasExumacoes(params);
      setTodasExumacoes(response.data);
    } catch (error) {
      console.error('Erro ao buscar todas as exumações:', error);
    } finally {
      setDetailsLoading(false);
    }
  };

  const handleClienteChange = (event) => {
    const value = event.target.value;
    console.log('🔄 Filtro cliente alterado:', value, typeof value);
    console.log('🔄 Event target:', event.target);

    // Garantir que o valor seja sempre uma string válida
    let clienteId = '';
    if (value !== undefined && value !== null) {
      clienteId = value === '' ? '' : String(value);
    }

    console.log('🔄 Cliente ID final:', clienteId);
    setSelectedClienteId(clienteId);
  };

  const handleExumar = async (sepultamentoId) => {
    try {
      if (window.confirm('Tem certeza que deseja realizar a exumação?')) {
        await sepultamentoService.exumar(sepultamentoId);
        // Recarregar dados após exumação
        loadDashboardData();
        // Recarregar detalhes do modal se estiver aberto
        if (selectedCard === 'exumacoes_previstas') {
          // Preparar parâmetros de filtro para recarregar detalhes
          const params = {};
          if (selectedClienteId && selectedClienteId !== '' && user?.tipo_usuario === 'admin') {
            params.cliente_id = parseInt(selectedClienteId);
          }
          const response = await dashboardService.getExumacoesPrevistasDetalhes(params);
          setCardDetails(response.data);
        }
        alert('Exumação realizada com sucesso!');
      }
    } catch (error) {
      console.error('Erro ao realizar exumação:', error);
      alert('Erro ao realizar exumação. Tente novamente.');
    }
  };

  const renderStatsCard = (title, value, subtitle, icon, color, progress = null, onClick = null) => {
    if (loading) {
      return (
        <FormGridItem xs={12} sm={6} md={4}>
          <StatsCard
            title={<Skeleton variant="text" width="60%" />}
            value={<Skeleton variant="text" width="40%" />}
            subtitle={<Skeleton variant="text" width="80%" />}
            icon={<Skeleton variant="circular" width={56} height={56} />}
            sx={{
              height: '100%',
              minHeight: '200px',
              display: 'flex',
              flexDirection: 'column',
            }}
          />
        </FormGridItem>
      );
    }

    const progressComponent = progress ? (
      <Box sx={{ mt: 2 }}>
        <Typography
          variant="body2"
          color="text.secondary"
          sx={{
            mb: 1,
            fontSize: '0.875rem',
            fontWeight: 500,
          }}
        >
          {progress.text}
        </Typography>
        <LinearProgress
          variant="determinate"
          value={progress.value}
          color={progress.color}
          sx={{
            height: 8,
            borderRadius: 4,
            backgroundColor: 'grey.200',
            '& .MuiLinearProgress-bar': {
              borderRadius: 4,
            },
          }}
        />
      </Box>
    ) : null;

    return (
      <FormGridItem xs={12} sm={6} md={4}>
        <StatsCard
          title={title}
          value={value}
          subtitle={subtitle}
          icon={icon}
          color={color}
          progress={progressComponent}
          onClick={onClick}
          sx={{
            height: '100%',
            minHeight: '200px',
            display: 'flex',
            flexDirection: 'column',
            cursor: onClick ? 'pointer' : 'default',
            transition: 'all 0.3s ease',
            '&:hover': onClick ? {
              transform: 'translateY(-4px)',
              boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
            } : {},
          }}
        />
      </FormGridItem>
    );
  };

  const renderExumacoesList = () => {
    // Mostrar apenas as primeiras 10 exumações
    const proximasExumacoes10 = proximasExumacoes.slice(0, 10);

    // Definir colunas da tabela
    const columns = [
      {
        id: 'nome_sepultado',
        label: 'Nome do Sepultado',
        minWidth: 200,
        render: (value) => (
          <Typography variant="body2" sx={{ fontWeight: 500 }}>
            {value}
          </Typography>
        ),
      },
      {
        id: 'denominacao_produto',
        label: 'Produto',
        minWidth: 150,
      },
      {
        id: 'localizacao',
        label: 'Localização',
        minWidth: 200,
        render: (value, row) => {
          // Combinar denominação do bloco com gaveta
          const bloco = row.denominacao_bloco || 'Bloco não informado';
          const gaveta = row.numero_gaveta || 'N/A';
          return (
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              {bloco} → Gaveta {gaveta}
            </Typography>
          );
        },
      },
      {
        id: 'data_sepultamento',
        label: 'Data Sepultamento',
        minWidth: 120,
        render: (value) => formatDate(value),
      },
      {
        id: 'data_exumacao',
        label: 'Data Exumação',
        minWidth: 120,
        render: (value) => formatDate(value),
      },
      {
        id: 'dias_restantes',
        label: 'Dias Restantes',
        minWidth: 100,
        align: 'center',
        render: (value) => (
          <Typography
            variant="body2"
            color={value < 30 ? 'error.main' : 'text.primary'}
            sx={{ fontWeight: 500 }}
          >
            {value} dias
          </Typography>
        ),
      },
    ];

    return (
      <StandardTable
        columns={columns}
        data={proximasExumacoes10}
        loading={loading}
        emptyMessage="Nenhuma exumação programada para os próximos dias"
        sx={{ mt: 2 }}
      />
    );
  };

  return (
    <StandardContainer
      title="Dashboard"
      subtitle="Visão geral do sistema e estatísticas principais"
      headerAction={
        user?.tipo_usuario === 'admin' && (
          <Box sx={{ minWidth: 200 }}>
            <StandardSelect
              label="Filtrar por Cliente"
              value={selectedClienteId || ''}
              onChange={handleClienteChange}
              options={[
                { value: '', label: 'Todos os Clientes' },
                ...clientes.map(cliente => ({
                  value: cliente.id,
                  label: cliente.nome_fantasia
                }))
              ]}
              placeholder="Todos os Clientes"
              size="small"
            />
          </Box>
        )
      }
    >
      {/* Seção de Estatísticas */}
      <ContentSection title="Estatísticas Gerais">
        <FormGrid spacing={3}>
          {/* Cards de estatísticas */}
          {renderStatsCard(
            'Total de Sepultamentos',
            stats?.total_sepultamentos || 0,
            'Sepultamentos registrados',
            <PeopleIcon />,
            'primary',
            null,
            () => handleCardClick('total_sepultamentos')
          )}

          {renderStatsCard(
            'Gavetas Ocupadas',
            stats?.gavetas_ocupadas || 0,
            `${stats?.gavetas_disponiveis || 0} disponíveis`,
            <ApartmentIcon />,
            'secondary',
            {
              text: `Taxa de ocupação: ${stats?.taxa_ocupacao || 0}%`,
              value: stats?.taxa_ocupacao || 0,
              color: 'primary'
            },
            () => handleCardClick('gavetas_ocupadas')
          )}

          {renderStatsCard(
            'Exumações Previstas',
            exumacoesPrevistas,
            'Próximos 30 dias',
            <WarningAmberIcon />,
            'warning',
            null,
            () => handleCardClick('exumacoes_previstas')
          )}
        </FormGrid>
      </ContentSection>

      {/* Seção de Próximas Exumações */}
      <ContentSection
        title="Lista das Próximas Exumações"
        subtitle="Próximas 10 exumações de todos os produtos"
        headerAction={
          <StandardButton
            variant="outlined"
            size="small"
            onClick={() => handleCardClick('todas_exumacoes')}
            startIcon={<AssessmentIcon />}
          >
            Ver Todos
          </StandardButton>
        }
      >
        {renderExumacoesList()}
      </ContentSection>

      {/* Modal de Detalhes de Sepultamentos */}
      <Modal
        isOpen={selectedCard === 'sepultamentos'}
        onClose={handleCloseModal}
        title="Detalhes de Sepultamentos por Produto"
        maxWidth="lg"
      >
        {detailsLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <Skeleton variant="rectangular" height={400} />
          </Box>
        ) : (
          <Box>
            {cardDetails && cardDetails.length > 0 ? (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Produto</TableCell>
                      <TableCell>Total de Sepultamentos</TableCell>
                      <TableCell>Taxa por Dia</TableCell>
                      <TableCell>Primeiro Sepultamento</TableCell>
                      <TableCell>Último Sepultamento</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {cardDetails.map((produto, index) => {
                      const diasDiferenca = produto.primeiro_sepultamento && produto.ultimo_sepultamento
                        ? Math.ceil((new Date(produto.ultimo_sepultamento) - new Date(produto.primeiro_sepultamento)) / (1000 * 60 * 60 * 24))
                        : 0;
                      const taxaPorDia = diasDiferenca > 0 ? (produto.total_sepultamentos / diasDiferenca).toFixed(2) : '0.00';

                      return (
                        <TableRow key={index}>
                          <TableCell>{produto.produto}</TableCell>
                          <TableCell>{produto.total_sepultamentos}</TableCell>
                          <TableCell>{taxaPorDia} por dia</TableCell>
                          <TableCell>{formatDate(produto.primeiro_sepultamento)}</TableCell>
                          <TableCell>{formatDate(produto.ultimo_sepultamento)}</TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Alert severity="info">
                Nenhum dado de sepultamento encontrado
              </Alert>
            )}
          </Box>
        )}
      </Modal>

      {/* Modal de Detalhes de Gavetas */}
      <Modal
        isOpen={selectedCard === 'gavetas'}
        onClose={handleCloseModal}
        title="Detalhes de Gavetas por Produto"
        maxWidth="lg"
      >
        {detailsLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <Skeleton variant="rectangular" height={400} />
          </Box>
        ) : (
          <Box>
            {cardDetails && cardDetails.length > 0 ? (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Produto</TableCell>
                      <TableCell>Total de Gavetas</TableCell>
                      <TableCell>Gavetas Ocupadas</TableCell>
                      <TableCell>Gavetas Disponíveis</TableCell>
                      <TableCell>Taxa de Ocupação</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {cardDetails.map((produto, index) => (
                      <TableRow key={index}>
                        <TableCell>{produto.produto}</TableCell>
                        <TableCell>{produto.total_gavetas}</TableCell>
                        <TableCell>{produto.gavetas_ocupadas}</TableCell>
                        <TableCell>{produto.gavetas_disponiveis}</TableCell>
                        <TableCell>{produto.taxa_ocupacao}%</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Alert severity="info">
                Nenhum dado de gaveta encontrado
              </Alert>
            )}
          </Box>
        )}
      </Modal>

      {/* Modal de Todas as Exumações (Ver Mais) */}
      <Modal
        isOpen={selectedCard === 'todas_exumacoes'}
        onClose={handleCloseModal}
        title="Todas as Exumações - Próximos 30 Dias"
        maxWidth="lg"
      >
        {detailsLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <Skeleton variant="rectangular" height={400} />
          </Box>
        ) : (
          <StandardTable
            columns={[
              { id: 'nome_sepultado', label: 'Nome do Sepultado', minWidth: 200 },
              { id: 'denominacao_produto', label: 'Denominação do Produto', minWidth: 180 },
              { id: 'denominacao_bloco', label: 'Denominação do Bloco', minWidth: 150 },
              { id: 'numero_gaveta', label: 'Número da Gaveta', minWidth: 120, align: 'center' },
              {
                id: 'data_exumacao',
                label: 'Data Exumação',
                minWidth: 120,
                render: (value) => formatDate(value)
              },
            ]}
            data={cardDetails || []}
            loading={false}
            emptyMessage="Nenhuma exumação programada para os próximos 30 dias"
          />
        )}
      </Modal>

      {/* Modal de Exumações Previstas (Card clicável) */}
      <Modal
        isOpen={selectedCard === 'exumacoes_previstas'}
        onClose={handleCloseModal}
        title="Exumações Previstas - Próximos e Últimos 30 Dias"
        maxWidth="lg"
      >
        {detailsLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <Skeleton variant="rectangular" height={400} />
          </Box>
        ) : (
          <StandardTable
            columns={[
              { id: 'nome_sepultado', label: 'Nome do Sepultado', minWidth: 200 },
              { id: 'denominacao_produto', label: 'Denominação do Produto', minWidth: 180 },
              { id: 'denominacao_bloco', label: 'Denominação do Bloco', minWidth: 150 },
              { id: 'numero_gaveta', label: 'Número da Gaveta', minWidth: 120, align: 'center' },
              {
                id: 'data_exumacao',
                label: 'Data Exumação',
                minWidth: 120,
                render: (value) => formatDate(value)
              },
              {
                id: 'actions',
                label: 'Ações',
                minWidth: 100,
                align: 'center',
                render: (_, row) => (
                  <StandardButton
                    variant="contained"
                    color="error"
                    size="small"
                    onClick={() => handleExumar(row.sepultamento_id)}
                  >
                    Exumar
                  </StandardButton>
                ),
              },
            ]}
            data={cardDetails || []}
            loading={false}
            emptyMessage="Nenhuma exumação encontrada para este período"
          />
        )}
      </Modal>

      {/* Modal de Taxa de Sepultamento */}
      <Modal
        isOpen={modalTaxaSepultamento}
        onClose={() => setModalTaxaSepultamento(false)}
        title="Taxa de Sepultamento por Produto"
        size="lg"
      >
        {detailsLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <StandardTable
            columns={[
              {
                id: 'produto',
                label: 'Produto',
                minWidth: 200,
              },
              {
                id: 'total_sepultamentos',
                label: 'Total Sepultamentos',
                minWidth: 120,
                align: 'center',
              },
              {
                id: 'primeiro_sepultamento',
                label: 'Primeiro Sepultamento',
                minWidth: 150,
                render: (value) => formatDate(value),
              },
              {
                id: 'ultimo_sepultamento',
                label: 'Último Sepultamento',
                minWidth: 150,
                render: (value) => formatDate(value),
              },
              {
                id: 'taxa_sepultamento_por_dia',
                label: 'Taxa por Dia',
                minWidth: 100,
                align: 'center',
                render: (value) => `${value} sep/dia`,
              },
            ]}
            data={taxaSepultamentoDetalhes}
            loading={detailsLoading}
            emptyMessage="Nenhum sepultamento encontrado"
          />
        )}
      </Modal>

      {/* Modal de Taxa de Ocupação */}
      <Modal
        isOpen={modalTaxaOcupacao}
        onClose={() => setModalTaxaOcupacao(false)}
        title="Taxa de Ocupação por Produto"
        size="lg"
      >
        {detailsLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <StandardTable
            columns={[
              {
                id: 'produto',
                label: 'Produto',
                minWidth: 200,
              },
              {
                id: 'total_gavetas',
                label: 'Total Gavetas',
                minWidth: 120,
                align: 'center',
              },
              {
                id: 'gavetas_ocupadas',
                label: 'Gavetas Ocupadas',
                minWidth: 120,
                align: 'center',
              },
              {
                id: 'gavetas_disponiveis',
                label: 'Gavetas Disponíveis',
                minWidth: 120,
                align: 'center',
              },
              {
                id: 'taxa_ocupacao',
                label: 'Taxa de Ocupação',
                minWidth: 120,
                align: 'center',
                render: (value) => `${value}%`,
              },
            ]}
            data={taxaOcupacaoDetalhes}
            loading={detailsLoading}
            emptyMessage="Nenhum produto encontrado"
          />
        )}
      </Modal>

      {/* Modal de Exumações dos Próximos 30 Dias */}
      <Modal
        isOpen={modalExumacoes30Dias}
        onClose={() => setModalExumacoes30Dias(false)}
        title="Exumações dos Próximos 30 Dias"
        size="xl"
      >
        {detailsLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <StandardTable
            columns={[
              {
                id: 'nome_sepultado',
                label: 'Nome do Sepultado',
                minWidth: 200,
              },
              {
                id: 'denominacao_produto',
                label: 'Produto',
                minWidth: 150,
              },
              {
                id: 'denominacao_bloco',
                label: 'Bloco',
                minWidth: 100,
              },
              {
                id: 'numero_gaveta',
                label: 'Gaveta',
                minWidth: 80,
                align: 'center',
              },
              {
                id: 'data_exumacao',
                label: 'Data Exumação',
                minWidth: 120,
                render: (value) => formatDate(value),
              },
              {
                id: 'dias_restantes',
                label: 'Dias Restantes',
                minWidth: 100,
                align: 'center',
                render: (value) => (
                  <Typography
                    variant="body2"
                    color={value < 30 ? 'error.main' : 'text.primary'}
                    sx={{ fontWeight: 500 }}
                  >
                    {value} dias
                  </Typography>
                ),
              },
            ]}
            data={exumacoes30Dias}
            loading={detailsLoading}
            emptyMessage="Nenhuma exumação prevista para os próximos 30 dias"
          />
        )}
      </Modal>

      {/* Modal de Todas as Exumações */}
      <Modal
        isOpen={modalTodasExumacoes}
        onClose={() => setModalTodasExumacoes(false)}
        title="Todas as Exumações por Produto"
        size="xl"
      >
        {detailsLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <Box>
            {Object.keys(todasExumacoes).map((produto) => (
              <Box key={produto} sx={{ mb: 4 }}>
                <Typography variant="h6" sx={{ mb: 2, color: 'primary.main' }}>
                  {produto}
                </Typography>
                <StandardTable
                  columns={[
                    {
                      id: 'nome_sepultado',
                      label: 'Nome do Sepultado',
                      minWidth: 200,
                    },
                    {
                      id: 'denominacao_bloco',
                      label: 'Bloco',
                      minWidth: 100,
                    },
                    {
                      id: 'numero_gaveta',
                      label: 'Gaveta',
                      minWidth: 80,
                      align: 'center',
                    },
                    {
                      id: 'data_exumacao',
                      label: 'Data Exumação',
                      minWidth: 120,
                      render: (value) => formatDate(value),
                    },
                    {
                      id: 'status_exumacao',
                      label: 'Status',
                      minWidth: 100,
                      align: 'center',
                      render: (value) => (
                        <Chip
                          label={value}
                          color={value === 'Exumado' ? 'success' : 'warning'}
                          size="small"
                        />
                      ),
                    },
                  ]}
                  data={todasExumacoes[produto]}
                  loading={false}
                  emptyMessage="Nenhuma exumação encontrada para este produto"
                  sx={{ mb: 2 }}
                />
              </Box>
            ))}
            {Object.keys(todasExumacoes).length === 0 && (
              <Typography variant="body1" sx={{ textAlign: 'center', py: 4 }}>
                Nenhuma exumação encontrada
              </Typography>
            )}
          </Box>
        )}
      </Modal>
    </StandardContainer>
  );
};

export default HomePage;
