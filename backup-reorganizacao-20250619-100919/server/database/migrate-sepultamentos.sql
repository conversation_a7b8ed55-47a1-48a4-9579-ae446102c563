-- Migração para adicionar campos de responsável e datas aos sepultamentos

-- Adicionar colunas para dados do sepultado
ALTER TABLE sepultamentos 
ADD COLUMN IF NOT EXISTS data_nascimento DATE,
ADD COLUMN IF NOT EXISTS data_obito DATE;

-- Adicionar coluna para horário do sepultamento
ALTER TABLE sepultamentos
ADD COLUMN IF NOT EXISTS horario_sepultamento VARCHAR(5);

-- Adicionar colunas para códigos de cliente, bloco e sub-bloco
ALTER TABLE sepultamentos 
ADD COLUMN IF NOT EXISTS codigo_cliente VARCHAR(50),
ADD COLUMN IF NOT EXISTS codigo_sub_bloco VARCHAR(50);

-- Remover a coluna codigo_estacao se existir (não é mais necessária)
-- ALTER TABLE sepultamentos DROP COLUMN IF EXISTS codigo_estacao;

-- Remover a coluna hora_sepultamento se existir (não é mais necessária)
-- ALTER TABLE sepultamentos DROP COLUMN IF EXISTS hora_sepultamento;

-- Adicionar índices para os novos campos
CREATE INDEX IF NOT EXISTS idx_sepultamentos_responsavel_cpf ON sepultamentos(responsavel_cpf);
CREATE INDEX IF NOT EXISTS idx_sepultamentos_data_nascimento ON sepultamentos(data_nascimento);
CREATE INDEX IF NOT EXISTS idx_sepultamentos_data_obito ON sepultamentos(data_obito);

-- Comentário sobre a migração
COMMENT ON TABLE sepultamentos IS 'Tabela de sepultamentos com dados completos do sepultado e responsável';
