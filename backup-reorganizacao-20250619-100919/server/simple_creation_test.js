const { Pool } = require('pg');

// Configuração direta do banco de dados
const pool = new Pool({
  user: 'postgres',
  host: '************',
  database: 'dbetens',
  password: 'ab3780bd73ee4e2804d566ce6fd96209',
  port: 5432,
});

async function simpleCreationTest() {
  console.log('🧪 TESTE SIMPLES - Criação de Dados com Colunas Hierárquicas');
  console.log('=' .repeat(70));

  const client = await pool.connect();
  
  try {
    // Dados únicos para evitar conflitos
    const timestamp = Date.now();
    const testData = {
      codigo_cliente: `CLI_${timestamp}`,
      cnpj: `${timestamp.toString().slice(-8)}.${timestamp.toString().slice(-6, -4)}.${timestamp.toString().slice(-4)}/0001-99`,
      nome_fantasia: `Cliente Teste ${timestamp}`,
      codigo_estacao: `EST_${timestamp}`,
      denominacao_produto: `Estação Teste ${timestamp}`,
      codigo_bloco: `BL_${timestamp}`,
      nome_bloco: `Bloco Teste ${timestamp}`,
      codigo_sub_bloco: `SB_${timestamp}`,
      nome_sub_bloco: `Sub-bloco Teste ${timestamp}`
    };
    
    console.log(`📋 Dados de teste únicos:`);
    console.log(`   Cliente: ${testData.codigo_cliente}`);
    console.log(`   Produto: ${testData.codigo_estacao}`);
    console.log(`   Bloco: ${testData.codigo_bloco}`);
    console.log(`   Sub-bloco: ${testData.codigo_sub_bloco}`);
    
    await client.query('BEGIN');
    
    // 1. Criar cliente
    console.log('\n👤 1. Criando cliente...');
    await client.query(`
      INSERT INTO clientes (codigo_cliente, cnpj, nome_fantasia, razao_social, nome)
      VALUES ($1, $2, $3, $4, $5)
    `, [testData.codigo_cliente, testData.cnpj, testData.nome_fantasia, testData.nome_fantasia, testData.nome_fantasia]);
    console.log('   ✅ Cliente criado');
    
    // 2. Criar produto
    console.log('\n📦 2. Criando produto...');
    await client.query(`
      INSERT INTO produtos (codigo_cliente, codigo_estacao, denominacao, meses_para_exumar, nome, tipo)
      VALUES ($1, $2, $3, 24, $4, 'ETEN')
    `, [testData.codigo_cliente, testData.codigo_estacao, testData.denominacao_produto, testData.denominacao_produto]);
    console.log('   ✅ Produto criado');
    
    // Buscar ID do produto
    const produtoResult = await client.query(`
      SELECT id FROM produtos WHERE codigo_cliente = $1 AND codigo_estacao = $2
    `, [testData.codigo_cliente, testData.codigo_estacao]);
    const produtoId = produtoResult.rows[0].id;
    console.log(`   📋 Produto ID: ${produtoId}`);
    
    // 3. Criar bloco COM colunas hierárquicas
    console.log('\n🧱 3. Criando bloco com colunas hierárquicas...');
    await client.query(`
      INSERT INTO blocos (produto_id, codigo_bloco, nome, codigo_cliente, codigo_estacao, denominacao)
      VALUES ($1, $2, $3, $4, $5, $6)
    `, [produtoId, testData.codigo_bloco, testData.nome_bloco, testData.codigo_cliente, testData.codigo_estacao, testData.nome_bloco]);
    console.log('   ✅ Bloco criado com referências hierárquicas');
    
    // Buscar ID do bloco
    const blocoResult = await client.query(`
      SELECT id FROM blocos WHERE produto_id = $1 AND codigo_bloco = $2
    `, [produtoId, testData.codigo_bloco]);
    const blocoId = blocoResult.rows[0].id;
    console.log(`   📋 Bloco ID: ${blocoId}`);
    
    // 4. Criar sub-bloco COM colunas hierárquicas
    console.log('\n🔲 4. Criando sub-bloco com colunas hierárquicas...');
    await client.query(`
      INSERT INTO sub_blocos (bloco_id, codigo_sub_bloco, nome, codigo_cliente, codigo_estacao, codigo_bloco, denominacao)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
    `, [blocoId, testData.codigo_sub_bloco, testData.nome_sub_bloco, testData.codigo_cliente, testData.codigo_estacao, testData.codigo_bloco, testData.nome_sub_bloco]);
    console.log('   ✅ Sub-bloco criado com referências hierárquicas');
    
    // Buscar ID do sub-bloco
    const subBlocoResult = await client.query(`
      SELECT id FROM sub_blocos WHERE bloco_id = $1 AND codigo_sub_bloco = $2
    `, [blocoId, testData.codigo_sub_bloco]);
    const subBlocoId = subBlocoResult.rows[0].id;
    console.log(`   📋 Sub-bloco ID: ${subBlocoId}`);
    
    // 5. Criar gavetas COM colunas hierárquicas
    console.log('\n📊 5. Criando gavetas com colunas hierárquicas...');
    for (let i = 1; i <= 10; i++) {
      await client.query(`
        INSERT INTO gavetas (sub_bloco_id, numero_gaveta, posicao_x, posicao_y, codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco)
        VALUES ($1, $2, 1, 1, $3, $4, $5, $6)
      `, [subBlocoId, i, testData.codigo_cliente, testData.codigo_estacao, testData.codigo_bloco, testData.codigo_sub_bloco]);
    }
    console.log('   ✅ 10 gavetas criadas com referências hierárquicas');
    
    // 6. Criar 5 sepultamentos COM colunas hierárquicas
    console.log('\n⚰️  6. Criando 5 sepultamentos com colunas hierárquicas...');
    const sepultados = [
      { nome: 'João Silva Hierárquico', gaveta: 1, data: '2024-01-15' },
      { nome: 'Maria Santos Hierárquica', gaveta: 2, data: '2024-01-20' },
      { nome: 'Pedro Oliveira Hierárquico', gaveta: 3, data: '2024-01-25' },
      { nome: 'Ana Costa Hierárquica', gaveta: 4, data: '2024-02-01' },
      { nome: 'Carlos Lima Hierárquico', gaveta: 5, data: '2024-02-05' }
    ];
    
    for (const sepultado of sepultados) {
      // Buscar ID da gaveta
      const gavetaResult = await client.query(`
        SELECT id FROM gavetas WHERE sub_bloco_id = $1 AND numero_gaveta = $2
      `, [subBlocoId, sepultado.gaveta]);
      
      if (gavetaResult.rows.length > 0) {
        const gavetaId = gavetaResult.rows[0].id;
        
        await client.query(`
          INSERT INTO sepultamentos (
            gaveta_id, nome_sepultado, codigo_cliente, codigo_bloco, codigo_sub_bloco, 
            numero_gaveta, data_sepultamento, codigo_estacao, horario_sepultamento
          )
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, '14:00')
        `, [
          gavetaId, 
          sepultado.nome, 
          testData.codigo_cliente, 
          testData.codigo_bloco, 
          testData.codigo_sub_bloco, 
          sepultado.gaveta, 
          sepultado.data,
          testData.codigo_estacao
        ]);
        
        // Marcar gaveta como ocupada
        await client.query(`UPDATE gavetas SET disponivel = false WHERE id = $1`, [gavetaId]);
        
        console.log(`   ✅ ${sepultado.nome} - Gaveta ${sepultado.gaveta}`);
      }
    }
    
    await client.query('COMMIT');
    console.log('\n🎉 Todos os dados criados com sucesso!');
    
    // 7. VERIFICAR DADOS USANDO CONSULTAS HIERÁRQUICAS
    console.log('\n🔍 7. VERIFICANDO COM CONSULTAS POR CÓDIGOS HIERÁRQUICOS:');
    
    // Consulta hierárquica completa
    const hierarchyResult = await client.query(`
      SELECT 
        c.codigo_cliente,
        c.nome_fantasia,
        p.codigo_estacao,
        p.denominacao as produto_nome,
        b.codigo_bloco,
        b.denominacao as bloco_nome,
        sb.codigo_sub_bloco,
        sb.denominacao as sub_bloco_nome,
        COUNT(g.numero_gaveta) as total_gavetas,
        COUNT(s.id) as total_sepultamentos
      FROM clientes c
      JOIN produtos p ON c.codigo_cliente = p.codigo_cliente
      JOIN blocos b ON p.codigo_cliente = b.codigo_cliente AND p.codigo_estacao = b.codigo_estacao
      JOIN sub_blocos sb ON b.codigo_cliente = sb.codigo_cliente 
                         AND b.codigo_estacao = sb.codigo_estacao 
                         AND b.codigo_bloco = sb.codigo_bloco
      LEFT JOIN gavetas g ON sb.codigo_cliente = g.codigo_cliente 
                          AND sb.codigo_estacao = g.codigo_estacao 
                          AND sb.codigo_bloco = g.codigo_bloco 
                          AND sb.codigo_sub_bloco = g.codigo_sub_bloco
      LEFT JOIN sepultamentos s ON g.codigo_cliente = s.codigo_cliente 
                                AND g.codigo_estacao = s.codigo_estacao 
                                AND g.codigo_bloco = s.codigo_bloco 
                                AND g.codigo_sub_bloco = s.codigo_sub_bloco 
                                AND g.numero_gaveta = s.numero_gaveta
                                AND s.ativo = true
      WHERE c.codigo_cliente = $1
      GROUP BY c.codigo_cliente, c.nome_fantasia, p.codigo_estacao, p.denominacao, 
               b.codigo_bloco, b.denominacao, sb.codigo_sub_bloco, sb.denominacao
    `, [testData.codigo_cliente]);
    
    console.log(`   📊 Estrutura hierárquica: ${hierarchyResult.rows.length} registros`);
    hierarchyResult.rows.forEach(row => {
      console.log(`      📋 ${row.codigo_cliente}/${row.codigo_estacao}/${row.codigo_bloco}/${row.codigo_sub_bloco}`);
      console.log(`         Cliente: ${row.nome_fantasia}`);
      console.log(`         Produto: ${row.produto_nome}`);
      console.log(`         Bloco: ${row.bloco_nome}`);
      console.log(`         Sub-bloco: ${row.sub_bloco_nome}`);
      console.log(`         Gavetas: ${row.total_gavetas}, Sepultamentos: ${row.total_sepultamentos}`);
    });
    
    // Consulta direta por códigos
    const directQuery = await client.query(`
      SELECT 
        g.codigo_cliente,
        g.codigo_estacao,
        g.codigo_bloco,
        g.codigo_sub_bloco,
        g.numero_gaveta,
        g.disponivel,
        s.nome_sepultado
      FROM gavetas g
      LEFT JOIN sepultamentos s ON g.codigo_cliente = s.codigo_cliente 
                                AND g.codigo_estacao = s.codigo_estacao 
                                AND g.codigo_bloco = s.codigo_bloco 
                                AND g.codigo_sub_bloco = s.codigo_sub_bloco 
                                AND g.numero_gaveta = s.numero_gaveta
                                AND s.ativo = true
      WHERE g.codigo_cliente = $1 
      AND g.codigo_estacao = $2
      AND g.codigo_bloco = $3
      AND g.codigo_sub_bloco = $4
      ORDER BY g.numero_gaveta
    `, [testData.codigo_cliente, testData.codigo_estacao, testData.codigo_bloco, testData.codigo_sub_bloco]);
    
    console.log(`\n   🔍 Consulta direta por códigos: ${directQuery.rows.length} gavetas`);
    directQuery.rows.forEach(row => {
      const status = row.disponivel ? '🟢 Disponível' : '🔴 Ocupada';
      const sepultado = row.nome_sepultado ? ` - ${row.nome_sepultado}` : '';
      console.log(`      ${status} Gaveta ${row.numero_gaveta}${sepultado}`);
      console.log(`         Localização: ${row.codigo_cliente}/${row.codigo_estacao}/${row.codigo_bloco}/${row.codigo_sub_bloco}`);
    });
    
    // Teste de importação simulada
    console.log('\n📥 8. TESTE DE IMPORTAÇÃO SIMULADA POR CÓDIGOS:');
    
    // Simular dados vindos de sistema externo
    const dadosImportacao = {
      codigo_cliente: testData.codigo_cliente,
      codigo_estacao: testData.codigo_estacao,
      codigo_bloco: testData.codigo_bloco,
      codigo_sub_bloco: testData.codigo_sub_bloco,
      sepultamento: {
        numero_gaveta: 6,
        nome_sepultado: 'Importado Via Códigos',
        data_sepultamento: '2024-03-01'
      }
    };
    
    // Buscar gaveta usando apenas códigos
    const gavetaImportacao = await client.query(`
      SELECT id FROM gavetas 
      WHERE codigo_cliente = $1 
      AND codigo_estacao = $2
      AND codigo_bloco = $3
      AND codigo_sub_bloco = $4
      AND numero_gaveta = $5
    `, [
      dadosImportacao.codigo_cliente,
      dadosImportacao.codigo_estacao,
      dadosImportacao.codigo_bloco,
      dadosImportacao.codigo_sub_bloco,
      dadosImportacao.sepultamento.numero_gaveta
    ]);
    
    if (gavetaImportacao.rows.length > 0) {
      const gavetaId = gavetaImportacao.rows[0].id;
      
      await client.query(`
        INSERT INTO sepultamentos (
          gaveta_id, nome_sepultado, codigo_cliente, codigo_estacao, codigo_bloco, 
          codigo_sub_bloco, numero_gaveta, data_sepultamento, horario_sepultamento
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, '16:00')
      `, [
        gavetaId,
        dadosImportacao.sepultamento.nome_sepultado,
        dadosImportacao.codigo_cliente,
        dadosImportacao.codigo_estacao,
        dadosImportacao.codigo_bloco,
        dadosImportacao.codigo_sub_bloco,
        dadosImportacao.sepultamento.numero_gaveta,
        dadosImportacao.sepultamento.data_sepultamento
      ]);
      
      await client.query(`UPDATE gavetas SET disponivel = false WHERE id = $1`, [gavetaId]);
      
      console.log(`   ✅ Sepultamento importado usando códigos hierárquicos:`);
      console.log(`      Nome: ${dadosImportacao.sepultamento.nome_sepultado}`);
      console.log(`      Localização: ${dadosImportacao.codigo_cliente}/${dadosImportacao.codigo_estacao}/${dadosImportacao.codigo_bloco}/${dadosImportacao.codigo_sub_bloco}/Gaveta-${dadosImportacao.sepultamento.numero_gaveta}`);
    }
    
    console.log('\n🎉 TESTE SIMPLES CONCLUÍDO COM SUCESSO!');
    console.log('=' .repeat(70));
    console.log('✅ TODAS AS COLUNAS HIERÁRQUICAS ESTÃO FUNCIONANDO!');
    console.log('✅ CONSULTAS POR CÓDIGOS OPERACIONAIS!');
    console.log('✅ IMPORTAÇÃO DE DADOS POR CÓDIGOS TESTADA!');
    console.log('✅ SISTEMA 100% PRONTO PARA RECEBER DADOS EXTERNOS!');
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Erro durante o teste:', error);
    console.error('Stack:', error.stack);
  } finally {
    client.release();
    await pool.end();
  }
}

// Executar teste simples
if (require.main === module) {
  simpleCreationTest().catch(error => {
    console.error('❌ Erro fatal:', error);
    process.exit(1);
  });
}

module.exports = { simpleCreationTest };
