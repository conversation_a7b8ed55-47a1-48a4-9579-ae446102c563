const { Pool } = require('pg');

// Configuração do banco de dados
const pool = new Pool({
  host: '************',
  port: 5432,
  database: 'dbetens',
  user: 'postgres',
  password: 'ab3780bd73ee4e2804d566ce6fd96209',
  ssl: false,
});

async function addDescricaoColumn() {
  try {
    console.log('🔧 ADICIONANDO COLUNA DESCRIÇÃO À TABELA LOGS_AUDITORIA\n');
    
    // Verificar se a coluna já existe
    console.log('1. 🔍 Verificando se a coluna descrição já existe...');
    const checkColumn = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'logs_auditoria' 
      AND column_name = 'descricao'
    `);
    
    if (checkColumn.rows.length > 0) {
      console.log('   ✅ Coluna descrição já existe');
    } else {
      console.log('   ❌ Coluna descrição não existe, criando...');
      
      // Adicionar coluna descrição
      await pool.query(`
        ALTER TABLE logs_auditoria 
        ADD COLUMN descricao TEXT
      `);
      
      console.log('   ✅ Coluna descrição adicionada com sucesso');
    }
    
    // Atualizar logs existentes com descrições
    console.log('\n2. 📝 Atualizando logs existentes com descrições...');
    
    // Buscar todos os logs sem descrição
    const logsResult = await pool.query(`
      SELECT l.*, u.nome as nome_usuario, u.email as email_usuario
      FROM logs_auditoria l
      LEFT JOIN usuarios u ON l.usuario_id = u.id
      WHERE l.descricao IS NULL OR l.descricao = ''
      ORDER BY l.created_at DESC
    `);
    
    console.log(`   📋 Encontrados ${logsResult.rows.length} logs para atualizar`);
    
    for (const log of logsResult.rows) {
      let descricao = '';
      
      switch (log.acao) {
        case 'LOGIN':
          descricao = 'Usuário realizou login no sistema';
          break;
        case 'LOGOUT':
          descricao = 'Usuário realizou logout do sistema';
          break;
        case 'CREATE':
          if (log.tabela_afetada === 'usuarios') {
            descricao = 'Usuário realizou cadastro de novo usuário';
          } else if (log.tabela_afetada === 'clientes') {
            descricao = 'Usuário realizou cadastro de novo cliente';
          } else if (log.tabela_afetada === 'produtos') {
            descricao = 'Usuário realizou cadastro de novo produto';
          } else if (log.tabela_afetada === 'sepultamentos') {
            descricao = 'Usuário realizou novo sepultamento';
          } else if (log.tabela_afetada === 'blocos') {
            descricao = 'Usuário realizou cadastro de novo bloco';
          } else if (log.tabela_afetada === 'sub_blocos') {
            descricao = 'Usuário realizou cadastro de novo sub-bloco';
          } else {
            descricao = `Usuário realizou cadastro em ${log.tabela_afetada}`;
          }
          break;
        case 'EDIT':
        case 'UPDATE':
          if (log.tabela_afetada === 'usuarios') {
            descricao = 'Usuário realizou edição de usuário';
          } else if (log.tabela_afetada === 'clientes') {
            descricao = 'Usuário realizou edição de cliente';
          } else if (log.tabela_afetada === 'produtos') {
            descricao = 'Usuário realizou edição de produto';
          } else if (log.tabela_afetada === 'sepultamentos') {
            descricao = 'Usuário realizou edição de sepultamento';
          } else if (log.tabela_afetada === 'blocos') {
            descricao = 'Usuário realizou edição de bloco';
          } else if (log.tabela_afetada === 'sub_blocos') {
            descricao = 'Usuário realizou edição de sub-bloco';
          } else {
            descricao = `Usuário realizou edição em ${log.tabela_afetada}`;
          }
          break;
        case 'DELETE':
          if (log.tabela_afetada === 'usuarios') {
            descricao = 'Usuário realizou exclusão de usuário';
          } else if (log.tabela_afetada === 'clientes') {
            descricao = 'Usuário realizou exclusão de cliente';
          } else if (log.tabela_afetada === 'produtos') {
            descricao = 'Usuário realizou exclusão de produto';
          } else if (log.tabela_afetada === 'sepultamentos') {
            descricao = 'Usuário realizou exclusão de sepultamento';
          } else if (log.tabela_afetada === 'blocos') {
            descricao = 'Usuário realizou exclusão de bloco';
          } else if (log.tabela_afetada === 'sub_blocos') {
            descricao = 'Usuário realizou exclusão de sub-bloco';
          } else {
            descricao = `Usuário realizou exclusão em ${log.tabela_afetada}`;
          }
          break;
        case 'EXUMAR':
          descricao = 'Usuário realizou exumação de sepultamento';
          break;
        case 'RECUPERACAO_SENHA':
          descricao = 'Usuário solicitou recuperação de senha';
          break;
        default:
          descricao = `Usuário realizou ação: ${log.acao}`;
      }
      
      // Atualizar o log com a descrição
      await pool.query(`
        UPDATE logs_auditoria 
        SET descricao = $1 
        WHERE id = $2
      `, [descricao, log.id]);
    }
    
    console.log('   ✅ Logs atualizados com descrições');
    
    // Verificar resultado final
    console.log('\n3. ✅ Verificando resultado final...');
    const finalCheck = await pool.query(`
      SELECT COUNT(*) as total_logs,
             COUNT(CASE WHEN descricao IS NOT NULL AND descricao != '' THEN 1 END) as logs_com_descricao
      FROM logs_auditoria
    `);
    
    const { total_logs, logs_com_descricao } = finalCheck.rows[0];
    console.log(`   📊 Total de logs: ${total_logs}`);
    console.log(`   📝 Logs com descrição: ${logs_com_descricao}`);
    
    if (total_logs === logs_com_descricao) {
      console.log('   🎉 Todos os logs têm descrição!');
    } else {
      console.log(`   ⚠️  ${total_logs - logs_com_descricao} logs ainda sem descrição`);
    }
    
    console.log('\n🎯 ATUALIZAÇÃO CONCLUÍDA COM SUCESSO!');
    
  } catch (error) {
    console.error('❌ Erro ao adicionar coluna descrição:', error);
    process.exit(1);
  } finally {
    await pool.end();
    console.log('🔌 Conexão encerrada');
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  addDescricaoColumn();
}

module.exports = { addDescricaoColumn };
