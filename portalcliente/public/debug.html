<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - Portal Evolution</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #1e3a8a 0%, #059669 100%);
            color: white;
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .success { color: #10b981; }
        .error { color: #ef4444; }
        .warning { color: #f59e0b; }
        .info { color: #3b82f6; }
        .test-result {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #10b981;
        }
        button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid white;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Portal Evolution - Debug</h1>
        
        <div class="test-result">
            <h3>✅ Testes de Conectividade</h3>
            <p class="success">✅ Arquivo HTML carregado</p>
            <p class="success">✅ Servidor Vite respondendo</p>
            <p class="success">✅ Porta 5173 acessível</p>
            <p id="timestamp" class="info">Carregado em: <span></span></p>
        </div>
        
        <div class="test-result">
            <h3>🧪 Testes de Funcionalidade</h3>
            <p id="js-test" class="warning">⏳ Testando JavaScript...</p>
            <p id="fetch-test" class="warning">⏳ Testando API...</p>
            <p id="react-test" class="warning">⏳ Testando React...</p>
        </div>
        
        <div class="test-result">
            <h3>📊 Informações do Sistema</h3>
            <p><strong>User Agent:</strong> <span id="user-agent"></span></p>
            <p><strong>URL Atual:</strong> <span id="current-url"></span></p>
            <p><strong>Protocolo:</strong> <span id="protocol"></span></p>
            <p><strong>Host:</strong> <span id="host"></span></p>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <button onclick="testReactApp()">🧪 Testar App React</button>
            <button onclick="testBackend()">🔗 Testar Backend</button>
            <button onclick="window.location.reload()">🔄 Recarregar</button>
        </div>
        
        <div id="results" style="margin-top: 20px;"></div>
    </div>
    
    <script>
        // Atualizar informações básicas
        document.getElementById('timestamp').querySelector('span').textContent = new Date().toLocaleString('pt-BR');
        document.getElementById('user-agent').textContent = navigator.userAgent;
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('protocol').textContent = window.location.protocol;
        document.getElementById('host').textContent = window.location.host;
        
        // Teste JavaScript
        setTimeout(() => {
            document.getElementById('js-test').innerHTML = '<span class="success">✅ JavaScript funcionando</span>';
        }, 100);
        
        // Teste de fetch para a aplicação React
        setTimeout(() => {
            fetch('/')
                .then(response => {
                    if (response.ok) {
                        document.getElementById('react-test').innerHTML = '<span class="success">✅ Aplicação React respondendo</span>';
                    } else {
                        document.getElementById('react-test').innerHTML = '<span class="error">❌ Aplicação React com erro: ' + response.status + '</span>';
                    }
                })
                .catch(error => {
                    document.getElementById('react-test').innerHTML = '<span class="error">❌ Erro ao conectar com React: ' + error.message + '</span>';
                });
        }, 500);
        
        // Teste de API backend
        setTimeout(() => {
            fetch('http://localhost:3001/api/health')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('fetch-test').innerHTML = '<span class="success">✅ Backend API funcionando: ' + data.message + '</span>';
                })
                .catch(error => {
                    document.getElementById('fetch-test').innerHTML = '<span class="warning">⚠️ Backend não acessível: ' + error.message + '</span>';
                });
        }, 1000);
        
        function testReactApp() {
            const results = document.getElementById('results');
            results.innerHTML = '<p class="info">🔄 Redirecionando para aplicação React...</p>';
            setTimeout(() => {
                window.location.href = '/';
            }, 1000);
        }
        
        function testBackend() {
            const results = document.getElementById('results');
            results.innerHTML = '<p class="info">🔄 Testando backend...</p>';
            
            fetch('http://localhost:3001/api/health')
                .then(response => response.json())
                .then(data => {
                    results.innerHTML = '<div class="test-result"><h4 class="success">✅ Backend Funcionando</h4><pre>' + JSON.stringify(data, null, 2) + '</pre></div>';
                })
                .catch(error => {
                    results.innerHTML = '<div class="test-result"><h4 class="error">❌ Erro no Backend</h4><p>' + error.message + '</p></div>';
                });
        }
    </script>
</body>
</html>
