#!/usr/bin/env node

/**
 * SCRIPT PARA TESTAR TODAS AS APIS DO PORTAL EVOLUTION
 */

const axios = require('axios');

const BASE_URL = 'https://portal.evo-eden.site/api';

async function testAPIs() {
  console.log('🧪 TESTANDO APIS DO PORTAL EVOLUTION\n');
  
  try {
    // 1. TESTAR HEALTH
    console.log('1. 🏥 Testando API Health...');
    try {
      const health = await axios.get(`${BASE_URL}/health`);
      console.log('   ✅ Health OK:', health.data);
    } catch (error) {
      console.log('   ❌ Health falhou:', error.response?.status, error.response?.data || error.message);
    }

    // 2. TESTAR LOGIN ADMIN
    console.log('\n2. 🔐 Testando login admin...');
    try {
      const loginAdmin = await axios.post(`${BASE_URL}/auth/login`, {
        email: 'ma<PERSON><PERSON><PERSON><PERSON><PERSON>@evolutionbr.tech',
        password: 'adminnbr5410!'
      });
      console.log('   ✅ Login admin OK:', {
        token: loginAdmin.data.token ? 'presente' : 'ausente',
        usuario: loginAdmin.data.usuario?.nome || 'não encontrado'
      });
      
      // Testar produtos com token admin
      if (loginAdmin.data.token) {
        console.log('\n3. 🏢 Testando produtos com admin...');
        try {
          const produtos = await axios.get(`${BASE_URL}/produtos`, {
            headers: { Authorization: `Bearer ${loginAdmin.data.token}` }
          });
          console.log('   ✅ Produtos OK:', produtos.data.length, 'produto(s)');
          
          if (produtos.data.length > 0) {
            const produto = produtos.data[0];
            console.log('   📊 Produto exemplo:', {
              id: produto.id,
              nome: produto.denominacao,
              codigo: produto.codigo_estacao
            });
            
            // Testar sepultamentos por produto
            if (produto.id) {
              console.log('\n4. ⚰️ Testando sepultamentos por produto...');
              try {
                const sepultamentos = await axios.get(`${BASE_URL}/sepultamentos/produto/${produto.id}`, {
                  headers: { Authorization: `Bearer ${loginAdmin.data.token}` }
                });
                console.log('   ✅ Sepultamentos OK:', sepultamentos.data.length, 'sepultamento(s)');
              } catch (error) {
                console.log('   ❌ Sepultamentos falhou:', error.response?.status, error.response?.data || error.message);
              }
            }
          }
        } catch (error) {
          console.log('   ❌ Produtos falhou:', error.response?.status, error.response?.data || error.message);
        }
      }
      
    } catch (error) {
      console.log('   ❌ Login admin falhou:', error.response?.status, error.response?.data || error.message);
    }

    // 5. TESTAR LOGIN CLIENTE
    console.log('\n5. 👤 Testando login cliente...');
    try {
      const loginCliente = await axios.post(`${BASE_URL}/auth/login`, {
        email: '<EMAIL>',
        password: '54321'
      });
      console.log('   ✅ Login cliente OK:', {
        token: loginCliente.data.token ? 'presente' : 'ausente',
        usuario: loginCliente.data.usuario?.nome || 'não encontrado'
      });
    } catch (error) {
      console.log('   ❌ Login cliente falhou:', error.response?.status, error.response?.data || error.message);
    }

    // 6. TESTAR SEPULTAMENTOS GERAL
    console.log('\n6. 📋 Testando sepultamentos geral...');
    try {
      const sepultamentos = await axios.get(`${BASE_URL}/sepultamentos`);
      console.log('   ✅ Sepultamentos OK:', sepultamentos.data.length, 'sepultamento(s)');
    } catch (error) {
      console.log('   ❌ Sepultamentos falhou:', error.response?.status, error.response?.data || error.message);
    }

    console.log('\n🎯 TESTE CONCLUÍDO!');

  } catch (error) {
    console.error('❌ Erro geral:', error.message);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  testAPIs();
}

module.exports = { testAPIs };
