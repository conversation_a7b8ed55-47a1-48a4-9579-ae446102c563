# ===================================
# NGINX CONFIGURATION FOR TRAEFIK
# Portal Evolution - Optimized for Traefik Proxy
# ===================================

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # Configurações de segurança (mais permissivas para desenvolvimento)
    server_tokens off;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Configurações de performance
    sendfile        on;
    tcp_nopush      on;
    tcp_nodelay     on;
    keepalive_timeout  65;
    client_max_body_size 10M;
    client_body_timeout 12;
    client_header_timeout 12;
    send_timeout 10;

    # Compressão otimizada
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Rate limiting básico
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;

    # Logs
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;

    # Servidor principal (Traefik gerencia SSL) - Porta 8080 para evitar conflito
    server {
        listen 8080;
        server_name _;
        root /usr/share/nginx/html;
        index index.html;

        # Configurar headers para funcionar com Traefik
        real_ip_header X-Forwarded-For;
        set_real_ip_from 10.0.0.0/8;
        set_real_ip_from **********/12;
        set_real_ip_from ***********/16;

        # Servir arquivos estáticos
        location / {
            try_files $uri $uri/ /index.html;
            
            # Cache para arquivos estáticos (reduzido para desenvolvimento)
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
                expires 5m;
                add_header Cache-Control "public, max-age=300";
                access_log off;
            }

            # Cache para HTML (sem cache para desenvolvimento)
            location ~* \.html$ {
                expires -1;
                add_header Cache-Control "no-cache, no-store, must-revalidate";
                add_header Pragma "no-cache";
                add_header Expires "0";
            }
        }

        # Proxy para API do backend com rate limiting
        location /api/ {
            limit_req zone=api burst=20 nodelay;

            # Resolver dinâmico para evitar erro de inicialização
            resolver 127.0.0.11 valid=30s;
            set $backend "portal-evolution_portalevo-backend:3001";
            proxy_pass http://$backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
        }

        # Rate limiting especial para login
        location /api/auth/login {
            limit_req zone=login burst=3 nodelay;

            # Resolver dinâmico para evitar erro de inicialização
            resolver 127.0.0.11 valid=30s;
            set $backend "portal-evolution_portalevo-backend:3001";
            proxy_pass http://$backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
        }

        # Health check
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # Bloquear acesso a arquivos sensíveis
        location ~ /\. {
            deny all;
        }
        
        # Bloquear acesso a arquivos de configuração
        location ~* \.(env|conf|config|bak|backup|swp|tmp)$ {
            deny all;
        }

        # Favicon
        location = /favicon.ico {
            log_not_found off;
            access_log off;
        }

        # Robots.txt
        location = /robots.txt {
            log_not_found off;
            access_log off;
        }
    }
}
