import React, { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Box,
  Typography,
  CircularProgress,
  Alert,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Grid,
  Pagination,
} from '@mui/material';
import {
  Search as SearchIcon,
  Clear as ClearIcon,
} from '@mui/icons-material';
import { logService, clienteService, produtoService, usuarioService } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import Modal from '../components/Modal';
import { StandardContainer, StandardButton, StandardCard } from '../components/common';

const LogsPage = () => {
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({});
  const [filters, setFilters] = useState({
    codigo_cliente: '',
    codigo_estacao: '',
    usuario_id: '',
    data_inicio: '',
    data_fim: '',
    acao: '',
    page: 1,
    limit: 50
  });

  const [clientes, setClientes] = useState([]);
  const [produtos, setProdutos] = useState([]);
  const [usuarios, setUsuarios] = useState([]);
  const [selectedLog, setSelectedLog] = useState(null);
  const [showDetailModal, setShowDetailModal] = useState(false);

  const { user } = useAuth();

  useEffect(() => {
    loadLogs();
    loadFilterOptions();
  }, []);

  // Aplicar filtros automaticamente quando data início ou fim mudarem
  useEffect(() => {
    if (filters.data_inicio || filters.data_fim) {
      loadLogs();
    }
  }, [filters.data_inicio, filters.data_fim]);

  const loadLogs = async () => {
    try {
      setLoading(true);

      // Preparar filtros para envio
      const filtrosParaEnvio = { ...filters };

      // Se não há filtros de data, carregar apenas as últimas 50 ocorrências
      if (!filters.data_inicio && !filters.data_fim) {
        filtrosParaEnvio.ultimas_50 = true;
      }

      console.log('📋 Carregando logs com filtros:', filtrosParaEnvio);

      const response = await logService.listar(filtrosParaEnvio);
      setLogs(response.data.logs || response.data);
      setPagination(response.data.pagination || {});
    } catch (error) {
      console.error('Erro ao carregar logs:', error);
      alert('Erro ao carregar logs');
    } finally {
      setLoading(false);
    }
  };

  const loadFilterOptions = async () => {
    try {
      const clientesResponse = await clienteService.listar();
      setClientes(clientesResponse.data);

      const produtosResponse = await produtoService.listar();
      setProdutos(produtosResponse.data);

      const usuariosResponse = await usuarioService.listar();
      setUsuarios(usuariosResponse.data);
    } catch (error) {
      console.error('Erro ao carregar opções de filtro:', error);
    }
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value,
      page: 1
    }));
  };

  const handleApplyFilters = () => {
    loadLogs();
  };

  const handleClearFilters = () => {
    setFilters({
      codigo_cliente: '',
      codigo_estacao: '',
      usuario_id: '',
      data_inicio: '',
      data_fim: '',
      acao: '',
      page: 1,
      limit: 50
    });
  };

  const handlePageChange = (event, newPage) => {
    setFilters(prev => ({ ...prev, page: newPage }));
    setTimeout(loadLogs, 100);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const brasilDate = new Date(date.getTime() - (3 * 60 * 60 * 1000));
    return brasilDate.toLocaleString('pt-BR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const getActionLabel = (action) => {
    const labels = {
      'CREATE': 'Cadastro',
      'EDIT': 'Edição',
      'DELETE': 'Deleção',
      'EXUMAR': 'Exumação'
    };
    return labels[action] || action;
  };

  const getActionColor = (action) => {
    const colors = {
      'CREATE': 'success',
      'EDIT': 'warning',
      'DELETE': 'error',
      'EXUMAR': 'secondary'
    };
    return colors[action] || 'default';
  };

  const handleLogClick = (log) => {
    setSelectedLog(log);
    setShowDetailModal(true);
  };

  const formatJsonData = (data) => {
    if (!data) return 'Nenhum dado disponível';
    try {
      return JSON.stringify(data, null, 2);
    } catch (error) {
      return 'Erro ao formatar dados';
    }
  };

  if (loading && logs.length === 0) {
    return (
      <StandardContainer title="📋 Logs de Auditoria" subtitle="Histórico de atividades do sistema">
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
          <CircularProgress size={40} />
        </Box>
      </StandardContainer>
    );
  }

  return (
    <StandardContainer
      title="📋 Logs de Auditoria"
      subtitle="Histórico de atividades do sistema"
    >
      <StandardCard sx={{ mb: 3 }}>
        <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
          Filtros
        </Typography>
        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={12} sm={6} md={4}>
            <FormControl fullWidth size="small" sx={{ minWidth: 200 }}>
              <InputLabel>Cliente</InputLabel>
              <Select
                value={filters.codigo_cliente}
                label="Cliente"
                onChange={(e) => handleFilterChange('codigo_cliente', e.target.value)}
              >
                <MenuItem value="">Todos os clientes</MenuItem>
                {clientes.map(cliente => (
                  <MenuItem key={cliente.codigo_cliente} value={cliente.codigo_cliente}>
                    {cliente.nome_fantasia} ({cliente.codigo_cliente})
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <FormControl fullWidth size="small" sx={{ minWidth: 200 }}>
              <InputLabel>Estação/Produto</InputLabel>
              <Select
                value={filters.codigo_estacao}
                label="Estação/Produto"
                onChange={(e) => handleFilterChange('codigo_estacao', e.target.value)}
              >
                <MenuItem value="">Todas as estações</MenuItem>
                {produtos.map(produto => (
                  <MenuItem key={produto.codigo_estacao} value={produto.codigo_estacao}>
                    {produto.denominacao} ({produto.codigo_estacao})
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <FormControl fullWidth size="small" sx={{ minWidth: 200 }}>
              <InputLabel>Usuário</InputLabel>
              <Select
                value={filters.usuario_id}
                label="Usuário"
                onChange={(e) => handleFilterChange('usuario_id', e.target.value)}
              >
                <MenuItem value="">Todos os usuários</MenuItem>
                {usuarios.map(usuario => (
                  <MenuItem key={usuario.id} value={usuario.id}>
                    {usuario.nome} ({usuario.email})
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <FormControl fullWidth size="small" sx={{ minWidth: 200 }}>
              <InputLabel>Ação</InputLabel>
              <Select
                value={filters.acao}
                label="Ação"
                onChange={(e) => handleFilterChange('acao', e.target.value)}
              >
                <MenuItem value="">Todas as ações</MenuItem>
                <MenuItem value="CREATE">Cadastro</MenuItem>
                <MenuItem value="EDIT">Edição</MenuItem>
                <MenuItem value="DELETE">Deleção</MenuItem>
                <MenuItem value="EXUMAR">Exumação</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <TextField
              fullWidth
              size="small"
              label="Data Início"
              type="date"
              value={filters.data_inicio}
              onChange={(e) => handleFilterChange('data_inicio', e.target.value)}
              InputLabelProps={{ shrink: true }}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <TextField
              fullWidth
              size="small"
              label="Data Fim"
              type="date"
              value={filters.data_fim}
              onChange={(e) => handleFilterChange('data_fim', e.target.value)}
              InputLabelProps={{ shrink: true }}
            />
          </Grid>
        </Grid>

        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
          <StandardButton
            variant="outlined"
            startIcon={<ClearIcon />}
            onClick={handleClearFilters}
          >
            Limpar Filtros
          </StandardButton>
          <StandardButton
            variant="contained"
            startIcon={<SearchIcon />}
            onClick={handleApplyFilters}
          >
            Aplicar Filtros
          </StandardButton>
        </Box>
      </StandardCard>

      {logs.length === 0 ? (
        <StandardCard sx={{ textAlign: 'center', py: 8 }}>
          <Typography variant="h1" sx={{ fontSize: '3rem', mb: 2 }}>📋</Typography>
          <Typography variant="h5" sx={{ mb: 1, color: 'text.primary' }}>
            Nenhum log encontrado
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Ajuste os filtros para encontrar os logs desejados.
          </Typography>
        </StandardCard>
      ) : (
        <>
          {/* Informação sobre os logs exibidos */}
          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              {!filters.data_inicio && !filters.data_fim
                ? `Exibindo as últimas ${logs.length} ocorrências`
                : `Encontrados ${pagination.total || logs.length} registros`
              }
            </Typography>
            {pagination.totalPages > 1 && (
              <Typography variant="body2" color="text.secondary">
                Página {pagination.page} de {pagination.totalPages}
              </Typography>
            )}
          </Box>

          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Data e Hora</TableCell>
                  <TableCell>Ação</TableCell>
                  <TableCell>Usuário</TableCell>
                  <TableCell>Descrição</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {logs.map((log) => (
                  <TableRow 
                    key={log.id} 
                    hover 
                    sx={{ cursor: 'pointer' }}
                    onClick={() => handleLogClick(log)}
                  >
                    <TableCell>{formatDate(log.created_at)}</TableCell>
                    <TableCell>
                      <Chip
                        label={getActionLabel(log.acao)}
                        color={getActionColor(log.acao)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>{log.nome_usuario}</TableCell>
                    <TableCell>
                      <Typography
                        variant="body2"
                        sx={{
                          maxWidth: 400,
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap'
                        }}
                      >
                        {log.descricao}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          {pagination.totalPages > 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
              <Pagination
                count={pagination.totalPages}
                page={pagination.page}
                onChange={handlePageChange}
                color="primary"
                size="large"
              />
            </Box>
          )}
        </>
      )}

      {/* Modal de Detalhes */}
      <Modal
        isOpen={showDetailModal}
        onClose={() => setShowDetailModal(false)}
        title={`Detalhes do Log - ${selectedLog?.acao ? getActionLabel(selectedLog.acao) : ''}`}
        maxWidth="800px"
      >
        {selectedLog && (
          <Box sx={{ maxHeight: '70vh', overflow: 'auto' }}>
            <Grid container spacing={2} sx={{ mb: 3 }}>
              <Grid item xs={12} sm={6}>
                <Paper sx={{ p: 2, backgroundColor: 'grey.50' }}>
                  <Typography variant="caption" color="text.secondary">
                    Data e Hora
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                    {formatDate(selectedLog.created_at)}
                  </Typography>
                </Paper>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Paper sx={{ p: 2, backgroundColor: 'grey.50' }}>
                  <Typography variant="caption" color="text.secondary">
                    Ação
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                    {getActionLabel(selectedLog.acao)}
                  </Typography>
                </Paper>
              </Grid>
              <Grid item xs={12}>
                <Paper sx={{ p: 2, backgroundColor: 'grey.50' }}>
                  <Typography variant="caption" color="text.secondary">
                    Usuário
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                    {selectedLog.nome_usuario}
                  </Typography>
                </Paper>
              </Grid>

            </Grid>

            <Box sx={{ mb: 3 }}>
              <Typography variant="h6" sx={{ mb: 1, fontWeight: 600 }}>
                Descrição da Atividade
              </Typography>
              <Paper sx={{ p: 2, backgroundColor: 'primary.50', border: '1px solid', borderColor: 'primary.200' }}>
                <Typography variant="body2" sx={{ whiteSpace: 'pre-line' }}>
                  {selectedLog.descricao}
                </Typography>
              </Paper>
            </Box>

            {selectedLog.dados_anteriores && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" sx={{ mb: 1, fontWeight: 600 }}>
                  Dados Anteriores
                </Typography>
                <Paper sx={{ p: 2, backgroundColor: 'grey.100', maxHeight: 300, overflow: 'auto' }}>
                  <Typography variant="body2" component="pre" sx={{ fontSize: '0.75rem', fontFamily: 'monospace' }}>
                    {formatJsonData(selectedLog.dados_anteriores)}
                  </Typography>
                </Paper>
              </Box>
            )}

            {selectedLog.dados_novos && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" sx={{ mb: 1, fontWeight: 600 }}>
                  Dados Novos
                </Typography>
                <Paper sx={{ p: 2, backgroundColor: 'grey.100', maxHeight: 300, overflow: 'auto' }}>
                  <Typography variant="body2" component="pre" sx={{ fontSize: '0.75rem', fontFamily: 'monospace' }}>
                    {formatJsonData(selectedLog.dados_novos)}
                  </Typography>
                </Paper>
              </Box>
            )}
          </Box>
        )}
      </Modal>
    </StandardContainer>
  );
};

export default LogsPage;
