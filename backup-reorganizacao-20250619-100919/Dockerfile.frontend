# ===================================
# DOCKERFILE FRONTEND - PORTAL EVOLUTION
# ===================================
# Build React + Nginx seguindo padrão da VPS
# Para verificar build: docker build -f docker/Dockerfile.frontend -t portal-frontend .

FROM node:18-alpine AS builder

# Definir diretório de trabalho
WORKDIR /app

# Copiar arquivos de dependências
COPY package*.json ./

# Limpar cache npm e instalar dependências
RUN npm cache clean --force && \
    rm -rf node_modules && \
    rm -f package-lock.json && \
    npm install

# Copiar código fonte
COPY . .

# Limpar cache de build e construir aplicação
RUN rm -rf dist && \
    rm -rf .vite && \
    npm run build

# Estágio de produção com Nginx
FROM nginx:alpine

# Instalar wget para health check
RUN apk add --no-cache wget

# Copiar configuração customizada do Nginx
COPY docker/nginx.conf /etc/nginx/nginx.conf

# Copiar arquivos buildados
COPY --from=builder /app/dist /usr/share/nginx/html

# Criar diretório para logs
RUN mkdir -p /var/log/nginx

# Expor porta 80 (padrão para Traefik)
EXPOSE 80

# Health check na porta 80
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:80/health || exit 1

# Comando para iniciar
CMD ["nginx", "-g", "daemon off;"]
