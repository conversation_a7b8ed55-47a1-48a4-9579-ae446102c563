#!/bin/bash

# Script para corrigir tags de styled-components que foram renomeadas incorretamente

echo "🔧 Corrigindo tags de styled-components..."

# Lista de arquivos que podem ter problemas
files_to_check=(
    "portalcliente/src/components/NumeracaoGavetasModal.jsx"
    "portalcliente/src/components/ClienteModal.jsx"
    "portalcliente/src/components/BlocoModal.jsx"
    "portalcliente/src/components/SubBlocoModal.jsx"
)

for file in "${files_to_check[@]}"; do
    if [ -f "$file" ]; then
        echo "Verificando $file..."
        
        # Verifica se há tags não correspondentes
        if grep -q "const.*Group = styled" "$file"; then
            echo "  🔍 Verificando tags Group em $file..."
            
            # Renomeia styled components para Styled*
            sed -i 's/const \([A-Za-z]*Group\) = styled/const Styled\1 = styled/g' "$file"
            sed -i 's/const \([A-Za-z]*Container\) = styled/const Styled\1 = styled/g' "$file"
            sed -i 's/const \([A-Za-z]*Wrapper\) = styled/const Styled\1 = styled/g' "$file"
            
            # Atualiza tags de abertura e fechamento
            sed -i 's/<\([A-Za-z]*Group\)>/<Styled\1>/g' "$file"
            sed -i 's/<\/\([A-Za-z]*Group\)>/<\/Styled\1>/g' "$file"
            sed -i 's/<\([A-Za-z]*Container\)>/<Styled\1>/g' "$file"
            sed -i 's/<\/\([A-Za-z]*Container\)>/<\/Styled\1>/g' "$file"
            sed -i 's/<\([A-Za-z]*Wrapper\)>/<Styled\1>/g' "$file"
            sed -i 's/<\/\([A-Za-z]*Wrapper\)>/<\/Styled\1>/g' "$file"
            
            echo "  ✅ Tags corrigidas em $file"
        else
            echo "  ✅ Nenhum problema encontrado em $file"
        fi
    else
        echo "  ⚠️  Arquivo $file não encontrado"
    fi
done

echo ""
echo "🎯 Verificação final de tags não correspondentes..."

# Verifica se ainda há problemas
problem_files=$(find portalcliente/src -name "*.jsx" -o -name "*.js" | xargs grep -l "const.*= styled" | xargs -I {} sh -c 'if grep -q "StyledButton.*Group>" "{}"; then echo "{}"; fi')

if [ -z "$problem_files" ]; then
    echo "✅ Todas as tags de styled-components estão corretas!"
else
    echo "❌ Ainda há problemas nos arquivos:"
    echo "$problem_files"
fi

echo ""
echo "🚀 Correção de tags styled-components concluída!"
