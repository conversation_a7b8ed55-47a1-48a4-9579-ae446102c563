import React, { useState, useEffect } from 'react';
import {
  <PERSON>po<PERSON>,
  <PERSON>,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Breadcrumbs,
  Link,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Button,
  Container,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip,
} from '@mui/material';
import ConfirmationModal from '../components/ConfirmationModal';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Business as BusinessIcon,
  AccountTree as BlockIcon,
  Category as SubBlockIcon,
  ArrowBack as BackIcon,
  Settings as SettingsIcon,
  Home as HomeIcon,
  ToggleOn as ToggleOnIcon,
  ToggleOff as ToggleOffIcon,
} from '@mui/icons-material';
import { produtoService, clienteService } from '../services/api';
import rangeService from '../services/rangeService';
import { useAuth } from '../contexts/AuthContext';
import {
  StandardContainer,
  StandardButton,
  StandardCard,
  StandardForm,
  StandardTextField,
  StandardSelect,
  StandardTable,
  TableActions,
  StatusChip,
  FormGrid,
  FormGridItem,
  FormSection,
  ContentSection,
} from '../components/common';


const CadastrosProdutosPage = () => {
  const { isAdmin } = useAuth();
  const [produtos, setProdutos] = useState([]);
  const [clientes, setClientes] = useState([]);
  const [blocos, setBlocos] = useState([]);
  const [subBlocos, setSubBlocos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  
  // Estados para navegação hierárquica (NOVA ARQUITETURA)
  const [currentView, setCurrentView] = useState('produtos'); // produtos, blocos, subBlocos, ranges
  const [selectedProduto, setSelectedProduto] = useState(null);
  const [selectedBloco, setSelectedBloco] = useState(null);
  const [selectedSubBloco, setSelectedSubBloco] = useState(null);
  
  // Estados para modais
  const [showProdutoModal, setShowProdutoModal] = useState(false);
  const [showBlocoModal, setShowBlocoModal] = useState(false);
  const [showSubBlocoModal, setShowSubBlocoModal] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [confirmAction, setConfirmAction] = useState(null);
  const [actionLoading, setActionLoading] = useState(false);

  // Estados para ranges (NOVA ARQUITETURA - SEM MODAL)
  const [ranges, setRanges] = useState([]);

  // Estado para formulário de ranges (CORREÇÃO ERRO REACT #310)
  const [rangeFormData, setRangeFormData] = useState({ numero_inicio: '', numero_fim: '' });
  
  // Estados para formulários
  const [produtoForm, setProdutoForm] = useState({
    denominacao: '',
    codigo_estacao: '',
    codigo_cliente: '',
    meses_para_exumar: '',
    ativo: true
  });
  
  const [blocoForm, setBlocoForm] = useState({
    nome: '',
    codigo_bloco: '',
    descricao: '',
    ativo: true
  });
  
  const [subBlocoForm, setSubBlocoForm] = useState({
    nome: '',
    codigo_sub_bloco: '',
    descricao: '',
    numero_inicio: '',
    numero_fim: '',
    ativo: true
  });

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadProdutos(),
        loadClientes()
      ]);
    } catch (error) {
      console.error('Erro ao carregar dados iniciais:', error);
      setError('Erro ao carregar dados');
    } finally {
      setLoading(false);
    }
  };

  const loadProdutos = async () => {
    try {
      const response = await produtoService.listar();
      setProdutos(response.data || []);
    } catch (error) {
      console.error('Erro ao carregar produtos:', error);
      throw error;
    }
  };

  const loadClientes = async () => {
    try {
      const response = await clienteService.listar();
      setClientes(response.data || []);
    } catch (error) {
      console.error('Erro ao carregar clientes:', error);
      throw error;
    }
  };

  const loadBlocos = async (produtoId) => {
    try {
      const response = await produtoService.listarBlocosPorProduto(produtoId);
      setBlocos(response.data || []);
    } catch (error) {
      console.error('Erro ao carregar blocos:', error);
      setError('Erro ao carregar blocos');
    }
  };

  const loadSubBlocos = async (blocoId) => {
    try {
      const response = await produtoService.listarSubBlocosPorBloco(blocoId);
      setSubBlocos(response.data || []);
    } catch (error) {
      console.error('Erro ao carregar sub-blocos:', error);
      setError('Erro ao carregar sub-blocos');
    }
  };

  // Navegação hierárquica
  const handleViewBlocos = async (produto) => {
    setSelectedProduto(produto);
    await loadBlocos(produto.id);
    setCurrentView('blocos');
  };

  const handleViewSubBlocos = async (bloco) => {
    setSelectedBloco(bloco);
    await loadSubBlocos(bloco.id);
    setCurrentView('subBlocos');
  };

  // Nova função para navegar para ranges (CORREÇÃO ERRO REACT #310)
  const handleViewRanges = async (subBloco) => {
    try {
      setError('');
      setSelectedSubBloco(subBloco);
      // Resetar formulário ao navegar
      setRangeFormData({ numero_inicio: '', numero_fim: '' });
      await loadRanges(subBloco);
      setCurrentView('ranges');
    } catch (err) {
      console.error('Erro ao navegar para ranges:', err);
      setError('Erro ao carregar página de ranges: ' + err.message);
    }
  };

  const handleBackToProdutos = () => {
    setCurrentView('produtos');
    setSelectedProduto(null);
    setSelectedBloco(null);
  };

  const handleBackToBlocos = () => {
    setCurrentView('blocos');
    setSelectedBloco(null);
    setSelectedSubBloco(null);
    setRanges([]);
  };

  const handleBackToSubBlocos = () => {
    setCurrentView('subBlocos');
    setSelectedSubBloco(null);
    setRanges([]);
    // Resetar formulário de ranges (CORREÇÃO ERRO REACT #310)
    setRangeFormData({ numero_inicio: '', numero_fim: '' });
  };

  // Função para carregar ranges (NOVA ARQUITETURA)
  const loadRanges = async (subBloco) => {
    try {
      setLoading(true);
      setError('');

      const response = await rangeService.listRanges(
        subBloco.codigo_cliente,
        subBloco.codigo_estacao,
        subBloco.codigo_bloco,
        subBloco.codigo_sub_bloco
      );

      if (Array.isArray(response)) {
        setRanges(response);
      } else if (response && Array.isArray(response.ranges)) {
        setRanges(response.ranges);
      } else {
        setRanges([]);
      }
    } catch (err) {
      console.error('Erro ao carregar ranges:', err);
      setError('Erro ao carregar ranges: ' + err.message);
      setRanges([]);
    } finally {
      setLoading(false);
    }
  };

  // Funções de CRUD para Produtos
  const handleAddProduto = () => {
    setEditingItem(null);
    setProdutoForm({
      denominacao: '',
      codigo_estacao: '',
      codigo_cliente: '',
      meses_para_exumar: '',
      ativo: true
    });
    setShowProdutoModal(true);
  };

  const handleEditProduto = (produto) => {
    console.log('🔍 DEBUG: Produto recebido para edição:', produto);
    console.log('🔍 DEBUG: codigo_cliente:', produto.codigo_cliente);
    console.log('🔍 DEBUG: codigo_estacao:', produto.codigo_estacao);

    setEditingItem(produto);
    setProdutoForm({
      denominacao: produto.denominacao || '',
      codigo_estacao: produto.codigo_estacao || '',
      codigo_cliente: produto.codigo_cliente || '',
      meses_para_exumar: produto.meses_para_exumar || '',
      ativo: produto.ativo !== false
    });
    setShowProdutoModal(true);
  };

  const handleSaveProduto = async () => {
    try {
      if (editingItem) {
        console.log('🔍 DEBUG: editingItem na atualização:', editingItem);
        console.log('🔍 DEBUG: Chamando atualizar com:', {
          codigo_cliente: editingItem.codigo_cliente,
          codigo_estacao: editingItem.codigo_estacao,
          dados: produtoForm
        });

        // CORREÇÃO: Usar codigo_cliente e codigo_estacao para atualização
        await produtoService.atualizar(editingItem.codigo_cliente, editingItem.codigo_estacao, produtoForm);
      } else {
        await produtoService.criar(produtoForm);
      }
      setShowProdutoModal(false);
      await loadProdutos();
    } catch (error) {
      console.error('Erro ao salvar produto:', error);

      // Tratamento específico de erros de atualização
      if (error.response?.data) {
        const errorData = error.response.data;

        if (errorData.tipo_erro === 'ALTERACAO_CODIGO_BLOQUEADA') {
          // Feedback visual para alteração bloqueada
          const message = `🔒 ALTERAÇÃO BLOQUEADA\n\n${errorData.message}\n\nEsta alteração não é permitida para manter a integridade dos dados.`;
          alert(message);
          setError(`🔒 ${errorData.error}: ${errorData.message}`);
        } else if (errorData.error) {
          setError(errorData.error);
        } else {
          setError('Erro ao salvar produto');
        }
      } else {
        setError('Erro ao salvar produto');
      }
    }
  };

  const handleDeleteProduto = async (produto) => {
    if (!window.confirm(`Tem certeza que deseja deletar o produto "${produto.denominacao}"?\n\nEsta ação só será permitida se não houver blocos associados.`)) {
      return;
    }

    try {
      await produtoService.deletar(produto.id);
      await loadProdutos();
    } catch (error) {
      console.error('Erro ao deletar produto:', error);

      if (error.response?.status === 400) {
        const errorData = error.response.data;

        // Tratamento específico baseado no tipo de erro
        if (errorData.tipo_erro === 'SEPULTAMENTOS_ATIVOS') {
          // Erro crítico - sepultamentos ativos
          let message = `🚨 OPERAÇÃO BLOQUEADA 🚨\n\n`;
          message += `${errorData.message}\n\n`;
          message += `⚠️ AÇÃO OBRIGATÓRIA: ${errorData.acao_obrigatoria}\n\n`;
          message += `📍 Produto: ${errorData.codigo_produto}`;

          // Feedback visual mais forte para sepultamentos ativos
          const confirmed = window.confirm(
            `${message}\n\n` +
            `⚠️ ATENÇÃO: Esta é uma operação crítica que envolve sepultamentos ativos.\n\n` +
            `Clique OK para entender que você deve exumar os sepultamentos primeiro, ou Cancelar para fechar.`
          );

          if (confirmed) {
            setError(`🚨 OPERAÇÃO BLOQUEADA: ${errorData.message}`);
          }
        } else if (errorData.tipo_erro === 'INTEGRIDADE_REFERENCIAL') {
          // Erro de integridade referencial
          let message = `❌ ${errorData.error}\n\n`;
          message += `${errorData.message}\n\n`;

          if (errorData.passos_necessarios && errorData.passos_necessarios.length > 0) {
            message += `🔧 Para deletar este produto, você deve primeiro:\n`;
            message += errorData.passos_necessarios.map((passo, index) => `${index + 1}. ${passo}`).join('\n');
          }

          alert(message);
        } else if (errorData.dependencias) {
          // Erro de validação hierárquica (formato antigo)
          let message = `❌ ${errorData.error}\n\n`;
          message += `📋 ${errorData.detalhes}\n\n`;
          message += `🔧 ${errorData.acao_necessaria}\n`;

          if (errorData.passos_obrigatorios && errorData.passos_obrigatorios.length > 0) {
            message += errorData.passos_obrigatorios.map((passo, index) => `${index + 1}. ${passo}`).join('\n');
          }

          if (errorData.blocos_nomes) {
            message += `\n\n📦 Blocos encontrados: ${errorData.blocos_nomes}`;
          }

          alert(message);
        } else {
          alert(errorData.error || errorData.message || 'Não é possível deletar este produto pois existem dados associados.');
        }
      } else if (error.response?.status === 403) {
        alert('Acesso negado. Apenas administradores podem deletar produtos.');
      } else if (error.response?.status === 404) {
        alert('Produto não encontrado. Pode ter sido removido por outro usuário.');
      } else {
        alert('Erro ao deletar produto. Tente novamente.');
      }
    }
  };

  const handleToggleStatus = (produto) => {
    const action = produto.ativo ? 'inativar' : 'ativar';

    setSelectedProduto(produto);
    setConfirmAction({
      type: action,
      title: `${action === 'inativar' ? 'Inativar' : 'Ativar'} Produto`,
      message: `Tem certeza que deseja ${action.toUpperCase()} este produto?`,
      consequences: produto.ativo ? [
        'O produto ficará inativo no sistema',
        'Não aparecerá nas listagens padrão',
        'TODOS os dados subsequentes serão MANTIDOS INTACTOS',
        'Blocos, sub-blocos, gavetas e sepultamentos permanecem inalterados',
        'Pode ser reativado a qualquer momento'
      ] : [
        'O produto ficará ativo no sistema',
        'Voltará a aparecer nas listagens',
        'Todos os dados subsequentes continuam intactos'
      ],
      dataIntegrity: {
        'blocos': '?',
        'sub-blocos': '?',
        'gavetas': '?',
        'sepultamentos': '?'
      }
    });
    setShowConfirmModal(true);
  };

  const executeToggleStatus = async () => {
    if (!selectedProduto || !confirmAction) return;

    const action = confirmAction.type;
    setActionLoading(true);

    try {
      console.log(`🔄 ${action.toUpperCase()} produto:`, selectedProduto.id);

      const response = await produtoService.toggleStatus(selectedProduto.id);
      console.log('✅ Status alterado com sucesso:', response.data);

      // Recarregar lista de produtos
      await loadProdutos();

      // Fechar modal e limpar estados
      setShowConfirmModal(false);
      setSelectedProduto(null);
      setConfirmAction(null);

      // Feedback para o usuário
      alert(`Produto ${action}do com sucesso!\n\nTodos os dados subsequentes foram mantidos intactos.`);

    } catch (error) {
      console.error(`❌ Erro ao ${action} produto:`, error);

      let errorMessage = `Erro ao ${action} produto`;
      let errorDetails = '';

      if (error.response) {
        // Erro da API
        const status = error.response.status;
        const data = error.response.data;

        switch (status) {
          case 401:
            errorMessage = 'Sessão expirada. Faça login novamente.';
            break;
          case 403:
            errorMessage = 'Acesso negado. Apenas administradores podem alterar status de produtos.';
            break;
          case 404:
            errorMessage = 'Produto não encontrado. Pode ter sido removido por outro usuário.';
            break;
          case 400:
            errorMessage = data?.error || 'Dados inválidos para a operação.';
            break;
          case 500:
            errorMessage = 'Erro interno do servidor. Tente novamente em alguns minutos.';
            errorDetails = 'Se o problema persistir, contate o suporte técnico.';
            break;
          default:
            errorMessage = data?.error || `Erro ${status} ao ${action} produto`;
        }
      } else if (error.request) {
        // Erro de rede
        errorMessage = 'Erro de conexão. Verifique sua internet e tente novamente.';
        errorDetails = 'O servidor pode estar temporariamente indisponível.';
      } else {
        // Erro desconhecido
        errorMessage = 'Erro inesperado. Tente recarregar a página.';
      }

      // Exibir erro detalhado
      const fullMessage = errorDetails
        ? `${errorMessage}\n\n${errorDetails}`
        : errorMessage;

      alert(fullMessage);

      // Log detalhado para debugging
      console.error('🔍 Detalhes do erro:', {
        action,
        status: error.response?.status,
        data: error.response?.data,
        message: error.message,
        stack: error.stack
      });
    } finally {
      setActionLoading(false);
    }
  };

  // Funções de CRUD para Blocos
  const handleAddBloco = () => {
    setEditingItem(null);
    setBlocoForm({
      nome: '',
      codigo_bloco: '',
      descricao: '',
      ativo: true
    });
    setShowBlocoModal(true);
  };

  const handleEditBloco = (bloco) => {
    setEditingItem(bloco);
    setBlocoForm({
      nome: bloco.nome || '',
      codigo_bloco: bloco.codigo_bloco || '',
      descricao: bloco.descricao || '',
      ativo: bloco.ativo !== false
    });
    setShowBlocoModal(true);
  };

  const handleSaveBloco = async () => {
    try {
      const blocoData = {
        ...blocoForm,
        produto_id: selectedProduto.id
      };

      if (editingItem) {
        await produtoService.atualizarBlocoSimples(editingItem.id, blocoData);
      } else {
        await produtoService.criarBlocoSimples(blocoData);
      }
      setShowBlocoModal(false);
      await loadBlocos(selectedProduto.id);
    } catch (error) {
      console.error('Erro ao salvar bloco:', error);
      setError('Erro ao salvar bloco');
    }
  };

  const handleDeleteBloco = async (bloco) => {
    if (!window.confirm(`Tem certeza que deseja deletar o bloco "${bloco.nome}"?\n\nEsta ação só será permitida se não houver sub-blocos ou sepultamentos associados.`)) {
      return;
    }

    try {
      await produtoService.deletarBlocoSimples(bloco.id);
      await loadBlocos(selectedProduto.id);
    } catch (error) {
      console.error('Erro ao deletar bloco:', error);
      if (error.response?.status === 400) {
        alert('Não é possível deletar este bloco pois existem sub-blocos ou sepultamentos associados a ele.');
      } else {
        alert('Erro ao deletar bloco');
      }
    }
  };

  // Funções de CRUD para Sub-Blocos
  const handleAddSubBloco = () => {
    setEditingItem(null);
    setSubBlocoForm({
      nome: '',
      codigo_sub_bloco: '',
      descricao: '',
      numero_inicio: '',
      numero_fim: '',
      ativo: true
    });
    setShowSubBlocoModal(true);
  };

  const handleEditSubBloco = (subBloco) => {
    setEditingItem(subBloco);
    setSubBlocoForm({
      nome: subBloco.nome || '',
      codigo_sub_bloco: subBloco.codigo_sub_bloco || '',
      descricao: subBloco.descricao || '',
      numero_inicio: subBloco.numero_inicio || '',
      numero_fim: subBloco.numero_fim || '',
      ativo: subBloco.ativo !== false
    });
    setShowSubBlocoModal(true);
  };

  const validateGavetaRange = async (numeroInicio, numeroFim, excludeId = null) => {
    try {
      // Verificar se há sobreposição com outros sub-blocos do mesmo bloco
      const response = await produtoService.listarSubBlocosPorBloco(selectedBloco.id);
      const subBlocosExistentes = response.data || [];

      const inicio = parseInt(numeroInicio);
      const fim = parseInt(numeroFim);

      if (inicio >= fim) {
        throw new Error('O número de início deve ser menor que o número fim');
      }

      for (const subBloco of subBlocosExistentes) {
        if (excludeId && subBloco.id === excludeId) continue;

        const existeInicio = parseInt(subBloco.numero_inicio);
        const existeFim = parseInt(subBloco.numero_fim);

        // Verificar sobreposição
        if ((inicio >= existeInicio && inicio <= existeFim) ||
            (fim >= existeInicio && fim <= existeFim) ||
            (inicio <= existeInicio && fim >= existeFim)) {
          throw new Error(`Range de gavetas conflita com o sub-bloco "${subBloco.nome}" (gavetas ${existeInicio}-${existeFim})`);
        }
      }

      return true;
    } catch (error) {
      throw error;
    }
  };

  const handleSaveSubBloco = async () => {
    try {
      if (editingItem) {
        // EDIÇÃO: Validar range de gavetas se fornecido
        if (subBlocoForm.numero_inicio && subBlocoForm.numero_fim) {
          await validateGavetaRange(
            subBlocoForm.numero_inicio,
            subBlocoForm.numero_fim,
            editingItem?.id
          );
        }

        const subBlocoData = {
          ...subBlocoForm,
          bloco_id: selectedBloco.id,
          numeracao_gavetas: subBlocoForm.numero_inicio && subBlocoForm.numero_fim
            ? `${subBlocoForm.numero_inicio}-${subBlocoForm.numero_fim}`
            : undefined
        };

        await produtoService.atualizarSubBlocoSimples(editingItem.id, subBlocoData);
      } else {
        // CRIAÇÃO: SEM ranges obrigatórios
        const subBlocoData = {
          nome: subBlocoForm.nome,
          codigo_sub_bloco: subBlocoForm.codigo_sub_bloco,
          descricao: subBlocoForm.descricao,
          bloco_id: selectedBloco.id
          // NÃO incluir numero_inicio, numero_fim, numeracao_gavetas
        };

        await produtoService.criarSubBlocoSimples(subBlocoData);
        alert('Sub-bloco criado com sucesso!\n\nUse o botão "Gerenciar Ranges" para definir as numerações das gavetas.');
      }

      setShowSubBlocoModal(false);
      await loadSubBlocos(selectedBloco.id);
    } catch (error) {
      console.error('Erro ao salvar sub-bloco:', error);
      alert(error.message || 'Erro ao salvar sub-bloco');
    }
  };

  const handleDeleteSubBloco = async (subBloco) => {
    if (!window.confirm(`Tem certeza que deseja deletar o sub-bloco "${subBloco.nome}"?\n\nEsta ação só será permitida se não houver sepultamentos associados.`)) {
      return;
    }

    try {
      await produtoService.deletarSubBlocoSimples(subBloco.id);
      await loadSubBlocos(selectedBloco.id);
    } catch (error) {
      console.error('Erro ao deletar sub-bloco:', error);
      if (error.response?.status === 400) {
        alert('Não é possível deletar este sub-bloco pois existem sepultamentos associados a ele.');
      } else {
        alert('Erro ao deletar sub-bloco');
      }
    }
  };

  // Funções para ranges (NOVA ARQUITETURA - SEM MODAL)
  const handleAddRange = async (rangeData) => {
    try {
      setLoading(true);
      setError('');

      // Validações conforme instrucao.md são feitas no backend
      console.log('📝 Criando range conforme instrucao.md:', {
        ...rangeData,
        codigo_cliente: selectedSubBloco.codigo_cliente,
        codigo_estacao: selectedSubBloco.codigo_estacao,
        codigo_bloco: selectedSubBloco.codigo_bloco,
        codigo_sub_bloco: selectedSubBloco.codigo_sub_bloco
      });

      const response = await rangeService.createRange({
        ...rangeData,
        codigo_cliente: selectedSubBloco.codigo_cliente,
        codigo_estacao: selectedSubBloco.codigo_estacao,
        codigo_bloco: selectedSubBloco.codigo_bloco,
        codigo_sub_bloco: selectedSubBloco.codigo_sub_bloco
      });

      if (response.success) {
        await loadRanges(selectedSubBloco);
        // Recarregar sub-blocos para atualizar total de gavetas
        if (selectedBloco) {
          loadSubBlocos(selectedBloco.id);
        }
        // Mostrar mensagem de sucesso
        console.log('✅ Range adicionado com sucesso!');
      } else {
        setError(response.error || 'Erro ao adicionar range');
      }
    } catch (err) {
      setError('Erro ao adicionar range: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteRange = async (range) => {
    if (!window.confirm(`Confirma a exclusão do range ${range.numero_inicio}-${range.numero_fim}?\n\nEsta ação removerá todas as gavetas deste range que não estejam ocupadas.`)) {
      return;
    }

    try {
      setLoading(true);
      setError(''); // Limpar erros anteriores

      console.log('🗑️ Deletando range:', range.id, {
        codigo_cliente: selectedSubBloco.codigo_cliente,
        codigo_estacao: selectedSubBloco.codigo_estacao,
        codigo_bloco: selectedSubBloco.codigo_bloco,
        codigo_sub_bloco: selectedSubBloco.codigo_sub_bloco
      });

      const response = await rangeService.deleteRange(range.id, {
        codigo_cliente: selectedSubBloco.codigo_cliente,
        codigo_estacao: selectedSubBloco.codigo_estacao,
        codigo_bloco: selectedSubBloco.codigo_bloco,
        codigo_sub_bloco: selectedSubBloco.codigo_sub_bloco
      });

      console.log('📝 Resposta da deleção:', response);

      if (response.success) {
        console.log('✅ Range deletado com sucesso');
        await loadRanges(selectedSubBloco);
        // Recarregar sub-blocos para atualizar total de gavetas
        if (selectedBloco) {
          loadSubBlocos(selectedBloco.id);
        }
      } else {
        console.error('❌ Erro na deleção:', response.error);
        setError(response.error || 'Erro ao remover range');
      }
    } catch (err) {
      console.error('❌ Erro na requisição:', err);
      setError('Erro ao remover range: ' + (err.response?.data?.error || err.message));
    } finally {
      setLoading(false);
    }
  };

  const renderBreadcrumbs = () => (
    <Breadcrumbs sx={{ mb: 3 }}>
      <Link
        component="button"
        variant="body1"
        onClick={handleBackToProdutos}
        sx={{ textDecoration: 'none' }}
      >
        Produtos
      </Link>
      {selectedProduto && (
        <Link
          component="button"
          variant="body1"
          onClick={currentView === 'subBlocos' || currentView === 'ranges' ? handleBackToBlocos : undefined}
          sx={{ textDecoration: 'none' }}
        >
          {selectedProduto.denominacao} - Blocos
        </Link>
      )}
      {selectedBloco && (
        <Link
          component="button"
          variant="body1"
          onClick={currentView === 'ranges' ? handleBackToSubBlocos : undefined}
          sx={{ textDecoration: 'none' }}
        >
          {selectedBloco.nome} - Sub-blocos
        </Link>
      )}
      {selectedSubBloco && (
        <Typography color="text.primary">
          {selectedSubBloco.nome} - Ranges
        </Typography>
      )}
    </Breadcrumbs>
  );

  const renderProdutosList = () => (
    <>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Cadastros dos Produtos
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddProduto}
        >
          Cadastrar Novo Produto
        </Button>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Denominação</TableCell>
              <TableCell>Código Estação</TableCell>
              <TableCell>Cliente</TableCell>
              <TableCell>Meses p/ Exumar</TableCell>
              <TableCell>Status</TableCell>
              <TableCell align="center">Ações</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {produtos.map((produto) => (
              <TableRow
                key={produto.id}
                sx={{
                  ...(produto.ativo ? {} : {
                    backgroundColor: 'error.50',
                    opacity: 0.7,
                    '&:hover': {
                      backgroundColor: 'error.100',
                      opacity: 1,
                    }
                  })
                }}
              >
                <TableCell>{produto.denominacao}</TableCell>
                <TableCell>{produto.codigo_estacao}</TableCell>
                <TableCell>{produto.nome_cliente || produto.codigo_cliente}</TableCell>
                <TableCell>{produto.meses_para_exumar} meses</TableCell>
                <TableCell>
                  <Chip
                    label={produto.ativo ? 'Ativo' : 'Inativo'}
                    color={produto.ativo ? 'success' : 'error'}
                    variant={produto.ativo ? 'filled' : 'outlined'}
                    size="small"
                  />
                </TableCell>
                <TableCell align="center">
                  <Tooltip title="Ver Blocos">
                    <IconButton onClick={() => handleViewBlocos(produto)} color="primary">
                      <ViewIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Editar">
                    <IconButton onClick={() => handleEditProduto(produto)} color="primary">
                      <EditIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title={produto.ativo ? 'Inativar Produto' : 'Ativar Produto'}>
                    <IconButton
                      onClick={() => handleToggleStatus(produto)}
                      color={produto.ativo ? 'error' : 'success'}
                    >
                      {produto.ativo ? <ToggleOffIcon /> : <ToggleOnIcon />}
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Deletar">
                    <IconButton onClick={() => handleDeleteProduto(produto)} color="error">
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </>
  );

  const renderBlocosList = () => (
    <>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Blocos - {selectedProduto?.denominacao}
        </Typography>
        <Box>
          <Button
            variant="outlined"
            startIcon={<BackIcon />}
            onClick={handleBackToProdutos}
            sx={{ mr: 2 }}
          >
            Voltar
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAddBloco}
          >
            Adicionar Novo Bloco
          </Button>
        </Box>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Nome</TableCell>
              <TableCell>Código Bloco</TableCell>
              <TableCell>Descrição</TableCell>
              <TableCell>Status</TableCell>
              <TableCell align="center">Ações</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {blocos.map((bloco) => (
              <TableRow key={bloco.id}>
                <TableCell>{bloco.nome}</TableCell>
                <TableCell>{bloco.codigo_bloco}</TableCell>
                <TableCell>{bloco.descricao || '-'}</TableCell>
                <TableCell>
                  <Chip
                    label={bloco.ativo ? 'Ativo' : 'Inativo'}
                    color={bloco.ativo ? 'success' : 'default'}
                    size="small"
                  />
                </TableCell>
                <TableCell align="center">
                  <Tooltip title="Ver Sub-blocos">
                    <IconButton onClick={() => handleViewSubBlocos(bloco)} color="primary">
                      <ViewIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Editar">
                    <IconButton onClick={() => handleEditBloco(bloco)} color="primary">
                      <EditIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Deletar">
                    <IconButton onClick={() => handleDeleteBloco(bloco)} color="error">
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </>
  );

  const renderSubBlocosList = () => (
    <>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Sub-blocos - {selectedBloco?.nome}
        </Typography>
        <Box>
          <Button
            variant="outlined"
            startIcon={<BackIcon />}
            onClick={handleBackToBlocos}
            sx={{ mr: 2 }}
          >
            Voltar
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAddSubBloco}
          >
            Adicionar Sub-bloco
          </Button>
        </Box>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Nome</TableCell>
              <TableCell>Código Sub-bloco</TableCell>
              <TableCell>Range Gavetas</TableCell>
              <TableCell>Total Gavetas (por Ranges)</TableCell>
              <TableCell>Numerações</TableCell>
              <TableCell>Descrição</TableCell>
              <TableCell>Status</TableCell>
              <TableCell align="center">Ações</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {subBlocos.map((subBloco) => (
              <TableRow key={subBloco.id}>
                <TableCell>{subBloco.nome}</TableCell>
                <TableCell>{subBloco.codigo_sub_bloco}</TableCell>
                <TableCell>
                  {subBloco.ranges_gavetas ? (
                    <Box sx={{ maxWidth: 300 }}>
                      <Tooltip
                        title={`Ranges completos: ${subBloco.ranges_gavetas}`}
                        placement="top"
                      >
                        <Chip
                          label={
                            subBloco.ranges_gavetas.length > 50
                              ? `${subBloco.ranges_gavetas.substring(0, 47)}...`
                              : subBloco.ranges_gavetas
                          }
                          variant="outlined"
                          color="primary"
                          size="small"
                          sx={{
                            maxWidth: '100%',
                            '& .MuiChip-label': {
                              display: 'block',
                              whiteSpace: 'nowrap',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis'
                            }
                          }}
                        />
                      </Tooltip>
                      {subBloco.total_numeracoes > 1 && (
                        <Typography variant="caption" color="textSecondary" sx={{ display: 'block', mt: 0.5 }}>
                          {subBloco.total_numeracoes} ranges definidos
                        </Typography>
                      )}
                    </Box>
                  ) : (
                    <Chip
                      label={`${subBloco.numero_inicio || 'N/A'} - ${subBloco.numero_fim || 'N/A'}`}
                      variant="outlined"
                      color="default"
                      size="small"
                      title="Range da tabela sub_blocos (sem numerações)"
                    />
                  )}
                </TableCell>
                <TableCell>
                  {subBloco.total_gavetas > 0 ? (
                    <Chip
                      label={`${subBloco.total_gavetas} gavetas`}
                      color="success"
                      size="small"
                      title="Total baseado nos ranges da tabela numeracoes_gavetas"
                    />
                  ) : (
                    <Chip
                      label="0 gavetas"
                      color="warning"
                      size="small"
                      title="Nenhum range definido - use 'Gerenciar Ranges' para configurar"
                    />
                  )}
                </TableCell>
                <TableCell>
                  {subBloco.total_numeracoes > 0 ? (
                    <Chip
                      label={`${subBloco.total_numeracoes} range(s)`}
                      color="success"
                      size="small"
                      title="Ranges definidos na tabela numeracoes_gavetas"
                    />
                  ) : (
                    <Chip
                      label="Sem ranges"
                      color="warning"
                      size="small"
                      title="Nenhum range definido na tabela numeracoes_gavetas"
                    />
                  )}
                </TableCell>
                <TableCell>{subBloco.descricao || '-'}</TableCell>
                <TableCell>
                  <Chip
                    label={subBloco.ativo ? 'Ativo' : 'Inativo'}
                    color={subBloco.ativo ? 'success' : 'default'}
                    size="small"
                  />
                </TableCell>
                <TableCell align="center">
                  <Tooltip title="Gerenciar Ranges">
                    <IconButton onClick={() => handleViewRanges(subBloco)} color="secondary">
                      <SettingsIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Editar">
                    <IconButton onClick={() => handleEditSubBloco(subBloco)} color="primary">
                      <EditIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Deletar">
                    <IconButton onClick={() => handleDeleteSubBloco(subBloco)} color="error">
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </>
  );

  // Nova função para renderizar página de ranges (CORREÇÃO ERRO REACT #310)
  const renderRangesList = () => {
    // Estado movido para nível principal para evitar erro React #310

    const isFormValid = () => {
      const inicio = parseInt(rangeFormData.numero_inicio);
      const fim = parseInt(rangeFormData.numero_fim);

      // Validações básicas
      if (!(inicio > 0 && fim > 0 && inicio <= fim && (fim - inicio + 1) <= 1000)) {
        return false;
      }

      // Verificar conflitos (CONFORME INSTRUCAO.MD)
      const conflito = verificarConflitoLocal(inicio, fim);
      return !conflito; // Válido apenas se não há conflito
    };

    const getFormValidationMessage = () => {
      const inicio = parseInt(rangeFormData.numero_inicio);
      const fim = parseInt(rangeFormData.numero_fim);

      if (!rangeFormData.numero_inicio || !rangeFormData.numero_fim) {
        return 'Preencha ambos os campos';
      }
      if (inicio <= 0 || fim <= 0) {
        return 'Números devem ser maiores que zero';
      }
      if (inicio > fim) {
        return 'Número início deve ser menor ou igual ao fim';
      }
      if ((fim - inicio + 1) > 1000) {
        return 'Range muito grande (máximo 1000 gavetas)';
      }

      // VALIDAÇÃO DE CONFLITOS NO FRONTEND (CONFORME INSTRUCAO.MD)
      const conflito = verificarConflitoLocal(inicio, fim);
      if (conflito) {
        return conflito;
      }

      return `Range válido: ${fim - inicio + 1} gavetas`;
    };

    // Função para verificar conflitos localmente antes de enviar ao backend
    const verificarConflitoLocal = (inicio, fim) => {
      if (!Array.isArray(ranges) || ranges.length === 0) {
        return null; // Sem ranges existentes, sem conflito
      }

      for (const range of ranges) {
        const rangeInicio = range.numero_inicio;
        const rangeFim = range.numero_fim;

        // Verificar se há sobreposição (conforme instrucao.md)
        const haConflito = (
          (inicio >= rangeInicio && inicio <= rangeFim) ||  // Início dentro do range existente
          (fim >= rangeInicio && fim <= rangeFim) ||        // Fim dentro do range existente
          (rangeInicio >= inicio && rangeInicio <= fim) ||  // Range existente começa dentro do novo
          (rangeFim >= inicio && rangeFim <= fim)           // Range existente termina dentro do novo
        );

        if (haConflito) {
          const numerosConflitantes = [];
          for (let num = Math.max(inicio, rangeInicio); num <= Math.min(fim, rangeFim); num++) {
            numerosConflitantes.push(num);
          }
          return `❌ Conflito com range ${rangeInicio}-${rangeFim} (números ${numerosConflitantes.join(', ')} já ocupados)`;
        }
      }

      return null; // Sem conflito
    };

    const handleSubmit = async (e) => {
      e.preventDefault();
      if (isFormValid()) {
        await handleAddRange({
          numero_inicio: parseInt(rangeFormData.numero_inicio),
          numero_fim: parseInt(rangeFormData.numero_fim)
        });
        setRangeFormData({ numero_inicio: '', numero_fim: '' });
      }
    };

    return (
      <>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box>
            <Typography variant="h4" component="h1">
              Ranges de Gavetas - {selectedSubBloco?.nome}
            </Typography>
            <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
              Filtrado para: Cliente {selectedSubBloco?.codigo_cliente} |
              Estação {selectedSubBloco?.codigo_estacao} |
              Bloco {selectedSubBloco?.codigo_bloco} |
              Sub-bloco {selectedSubBloco?.codigo_sub_bloco}
            </Typography>
          </Box>
          <Button
            variant="outlined"
            startIcon={<BackIcon />}
            onClick={handleBackToSubBlocos}
          >
            Voltar para Sub-blocos
          </Button>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Formulário para adicionar range */}
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Adicionar Novo Range
          </Typography>
          <Box component="form" onSubmit={handleSubmit} sx={{ display: 'flex', gap: 2, alignItems: 'flex-start', flexWrap: 'wrap' }}>
            <TextField
              label="Número Início"
              type="number"
              value={rangeFormData.numero_inicio}
              onChange={(e) => setRangeFormData({ ...rangeFormData, numero_inicio: e.target.value })}
              size="small"
              inputProps={{ min: 1 }}
              required
              error={rangeFormData.numero_inicio && parseInt(rangeFormData.numero_inicio) <= 0}
              helperText={rangeFormData.numero_inicio && parseInt(rangeFormData.numero_inicio) <= 0 ? "Deve ser maior que 0" : ""}
            />
            <TextField
              label="Número Fim"
              type="number"
              value={rangeFormData.numero_fim}
              onChange={(e) => setRangeFormData({ ...rangeFormData, numero_fim: e.target.value })}
              size="small"
              inputProps={{ min: 1 }}
              required
              error={rangeFormData.numero_fim && parseInt(rangeFormData.numero_fim) <= 0}
              helperText={rangeFormData.numero_fim && parseInt(rangeFormData.numero_fim) <= 0 ? "Deve ser maior que 0" : ""}
            />
            <Button
              type="submit"
              variant="contained"
              startIcon={<AddIcon />}
              disabled={!isFormValid() || loading}
            >
              {loading ? 'Adicionando...' : 'Adicionar Range'}
            </Button>
            <Box sx={{ minWidth: 200, display: 'flex', alignItems: 'center' }}>
              <Typography
                variant="body2"
                color={isFormValid() ? 'success.main' : 'text.secondary'}
                sx={{ fontWeight: isFormValid() ? 'bold' : 'normal' }}
              >
                {getFormValidationMessage()}
              </Typography>
            </Box>
          </Box>
        </Paper>

        {/* Lista de ranges */}
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Range</TableCell>
                <TableCell>Total Gavetas</TableCell>
                <TableCell>Disponíveis</TableCell>
                <TableCell>Ocupadas</TableCell>
                <TableCell>Status</TableCell>
                <TableCell align="center">Ações</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {Array.isArray(ranges) && ranges.length > 0 ? (
                ranges.map((range) => (
                  <TableRow key={range.id || `range-${range.numero_inicio}-${range.numero_fim}`}>
                    <TableCell>
                      <Chip
                        label={`${range.numero_inicio} - ${range.numero_fim}`}
                        color="primary"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={`${range.total_gavetas_range || (range.numero_fim - range.numero_inicio + 1)} gavetas`}
                        color="info"
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={range.gavetas_disponiveis || 0}
                        color="success"
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={range.gavetas_ocupadas || 0}
                        color={(range.gavetas_ocupadas || 0) > 0 ? "error" : "default"}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={range.ativo ? "Ativo" : "Inativo"}
                        color={range.ativo ? "success" : "default"}
                        size="small"
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Tooltip title="Deletar range">
                        <IconButton
                          color="error"
                          size="small"
                          onClick={() => handleDeleteRange(range)}
                          disabled={loading}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    <Typography color="textSecondary">
                      {loading ? 'Carregando ranges...' : 'Nenhum range definido'}
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Informações sobre regras conforme instrucao.md */}
        <Paper sx={{ p: 2, mt: 3, bgcolor: 'info.light', color: 'info.contrastText' }}>
          <Typography variant="body2" sx={{ mb: 1 }}>
            <strong>Regras de Ranges (conforme instrucao.md):</strong>
          </Typography>
          <Typography variant="body2" sx={{ mb: 1 }}>
            • ✅ <strong>Permitido:</strong> Ranges 1-10 e 20-30 (sem conflito)
          </Typography>
          <Typography variant="body2" sx={{ mb: 1 }}>
            • ❌ <strong>Bloqueado:</strong> Ranges 1-10 e 5-20 (conflito nos números 5,6,7,8,9,10)
          </Typography>
          <Typography variant="body2" sx={{ mb: 1 }}>
            • 📊 <strong>Cálculo:</strong> Range 1 (21-30) = 10 gavetas + Range 2 (51-80) = 30 gavetas = Total: 40 gavetas
          </Typography>
          <Typography variant="body2">
            • 🔧 <strong>Gavetas:</strong> Criadas automaticamente 1 por 1 para cada número do range
          </Typography>
        </Paper>
      </>
    );
  };

  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ py: 4 }}>
        <Typography>Carregando...</Typography>
      </Container>
    );
  }

  // Função para renderizar conteúdo baseado na view (CORREÇÃO ERRO REACT #310)
  const renderCurrentView = () => {
    switch (currentView) {
      case 'produtos':
        return renderProdutosList();
      case 'blocos':
        return renderBlocosList();
      case 'subBlocos':
        return renderSubBlocosList();
      case 'ranges':
        return renderRangesList();
      default:
        return renderProdutosList();
    }
  };

  const produtosAtivos = produtos.filter(p => p.ativo).length;
  const produtosInativos = produtos.filter(p => !p.ativo).length;

  return (
    <StandardContainer
      title={
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          Cadastros dos Produtos
          {produtos.length > 0 && (
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Chip
                label={`${produtosAtivos} Ativos`}
                color="success"
                size="small"
                variant="outlined"
              />
              {produtosInativos > 0 && (
                <Chip
                  label={`${produtosInativos} Inativos`}
                  color="error"
                  size="small"
                  variant="outlined"
                />
              )}
            </Box>
          )}
        </Box>
      }
      subtitle="Gerencie produtos, blocos, sub-blocos e ranges de gavetas (ativos e inativos)"
    >
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {currentView !== 'produtos' && renderBreadcrumbs()}

      {renderCurrentView()}

      {/* Modal de Produto */}
      <Dialog open={showProdutoModal} onClose={() => setShowProdutoModal(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingItem ? 'Editar Produto' : 'Cadastrar Novo Produto'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Denominação"
                value={produtoForm.denominacao}
                onChange={(e) => setProdutoForm({...produtoForm, denominacao: e.target.value})}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Código da Estação"
                value={produtoForm.codigo_estacao}
                onChange={(e) => setProdutoForm({...produtoForm, codigo_estacao: e.target.value})}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel>Cliente</InputLabel>
                <Select
                  value={produtoForm.codigo_cliente}
                  onChange={(e) => setProdutoForm({...produtoForm, codigo_cliente: e.target.value})}
                  label="Cliente"
                >
                  {clientes.map((cliente) => (
                    <MenuItem key={cliente.codigo_cliente} value={cliente.codigo_cliente}>
                      {cliente.nome_fantasia} ({cliente.codigo_cliente})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Meses para Exumar"
                type="number"
                value={produtoForm.meses_para_exumar}
                onChange={(e) => setProdutoForm({...produtoForm, meses_para_exumar: e.target.value})}
                required
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowProdutoModal(false)}>Cancelar</Button>
          <Button onClick={handleSaveProduto} variant="contained">
            {editingItem ? 'Atualizar' : 'Cadastrar'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Modal de Bloco */}
      <Dialog open={showBlocoModal} onClose={() => setShowBlocoModal(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingItem ? 'Editar Bloco' : 'Adicionar Novo Bloco'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Nome do Bloco"
                value={blocoForm.nome}
                onChange={(e) => setBlocoForm({...blocoForm, nome: e.target.value})}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Código do Bloco"
                value={blocoForm.codigo_bloco}
                onChange={(e) => setBlocoForm({...blocoForm, codigo_bloco: e.target.value})}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Descrição"
                multiline
                rows={3}
                value={blocoForm.descricao}
                onChange={(e) => setBlocoForm({...blocoForm, descricao: e.target.value})}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowBlocoModal(false)}>Cancelar</Button>
          <Button onClick={handleSaveBloco} variant="contained">
            {editingItem ? 'Atualizar' : 'Adicionar'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Modal de Sub-bloco */}
      <Dialog open={showSubBlocoModal} onClose={() => setShowSubBlocoModal(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingItem ? 'Editar Sub-bloco' : 'Adicionar Sub-bloco'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Nome do Sub-bloco"
                value={subBlocoForm.nome}
                onChange={(e) => setSubBlocoForm({...subBlocoForm, nome: e.target.value})}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Código do Sub-bloco"
                value={subBlocoForm.codigo_sub_bloco}
                onChange={(e) => setSubBlocoForm({...subBlocoForm, codigo_sub_bloco: e.target.value})}
                required
                disabled={editingItem} // Não permite editar código quando editando
              />
            </Grid>
            {/* RANGES REMOVIDOS DA CRIAÇÃO - CONFORME SOLICITAÇÃO */}
            {!editingItem && (
              <Grid item xs={12}>
                <Alert severity="info">
                  📝 Após criar o sub-bloco, use o botão "Gerenciar Ranges" para definir as numerações das gavetas.
                </Alert>
              </Grid>
            )}
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Descrição do Sub-bloco"
                multiline
                rows={3}
                value={subBlocoForm.descricao}
                onChange={(e) => setSubBlocoForm({...subBlocoForm, descricao: e.target.value})}
              />
            </Grid>
            {editingItem && (
              <Grid item xs={12}>
                <Alert severity="info">
                  Para gerenciar ranges de gavetas, use o botão "Gerenciar Ranges" na tabela.
                </Alert>
              </Grid>
            )}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowSubBlocoModal(false)}>Cancelar</Button>
          <Button onClick={handleSaveSubBloco} variant="contained">
            {editingItem ? 'Atualizar' : 'Adicionar'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Modal de Confirmação */}
      <ConfirmationModal
        open={showConfirmModal}
        onClose={() => {
          setShowConfirmModal(false);
          setSelectedProduto(null);
          setConfirmAction(null);
        }}
        onConfirm={executeToggleStatus}
        title={confirmAction?.title}
        message={confirmAction?.message}
        type={confirmAction?.type === 'inativar' ? 'warning' : 'success'}
        confirmText={confirmAction?.type === 'inativar' ? 'Inativar' : 'Ativar'}
        loading={actionLoading}
        itemName={selectedProduto?.denominacao}
        action={confirmAction?.type}
        consequences={confirmAction?.consequences}
        dataIntegrity={confirmAction?.dataIntegrity}
      />
    </StandardContainer>
  );
};

export default CadastrosProdutosPage;
