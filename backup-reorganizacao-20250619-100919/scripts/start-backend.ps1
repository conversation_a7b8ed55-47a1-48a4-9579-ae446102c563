# Script para iniciar o Backend do Portal Evolution
Write-Host "=== INICIANDO BACKEND PORTAL EVOLUTION ===" -ForegroundColor Green
Write-Host ""

# Navegar para o diretório do servidor
$serverPath = "C:\Users\<USER>\Documents\portalevo\portal-evolution\server"
Write-Host "Navegando para: $serverPath" -ForegroundColor Yellow

if (Test-Path $serverPath) {
    Set-Location $serverPath
    Write-Host "✅ Diretório encontrado" -ForegroundColor Green
} else {
    Write-Host "❌ Diretório do servidor não encontrado!" -ForegroundColor Red
    Write-Host "Verifique se o caminho está correto: $serverPath" -ForegroundColor Red
    exit 1
}

# Verificar se o arquivo index.js existe
if (Test-Path "index.js") {
    Write-Host "✅ Arquivo index.js encontrado" -ForegroundColor Green
} else {
    Write-Host "❌ Arquivo index.js não encontrado!" -ForegroundColor Red
    exit 1
}

# Verificar se o PostgreSQL está rodando
Write-Host ""
Write-Host "Verificando conexão com PostgreSQL..." -ForegroundColor Yellow
try {
    # Tentar conectar ao PostgreSQL (assumindo que está na porta padrão 5432)
    $connection = Test-NetConnection -ComputerName localhost -Port 5432 -WarningAction SilentlyContinue
    if ($connection.TcpTestSucceeded) {
        Write-Host "✅ PostgreSQL está rodando" -ForegroundColor Green
    } else {
        Write-Host "⚠️ PostgreSQL pode não estar rodando na porta 5432" -ForegroundColor Yellow
        Write-Host "   O servidor tentará conectar mesmo assim..." -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️ Não foi possível verificar o PostgreSQL" -ForegroundColor Yellow
}

# Iniciar o servidor
Write-Host ""
Write-Host "Iniciando servidor..." -ForegroundColor Yellow
Write-Host "Backend será executado em: http://localhost:3001" -ForegroundColor Cyan
Write-Host "API disponível em: http://localhost:3001/api" -ForegroundColor Cyan
Write-Host ""
Write-Host "Para parar o servidor, pressione Ctrl+C" -ForegroundColor Yellow
Write-Host ""

# Executar o servidor
node index.js
