import React, { useState, useEffect } from 'react';
import { useParams, useLocation, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Chip,
  Skeleton,
  Alert,
  Breadcrumbs,
  Link,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  RemoveCircle as ExumarIcon,
  ArrowBack as ArrowBackIcon,
  Home as HomeIcon,
  Business as BusinessIcon,
  Person as PersonIcon,
  CalendarToday as CalendarIcon,
  Search as SearchIcon,
  Clear as ClearIcon,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { sepultamentoService } from '../services/api';
import BookSepultamentoModal from '../components/BookSepultamentoModal';
import {
  StandardContainer,
  StandardButton,
  StandardTable,
  TableActions,
  StatusChip,
  ContentSection,
} from '../components/common';

const BookSepultamentosDetalhePage = () => {
  const { codigoEstacao } = useParams();
  const location = useLocation();
  const navigate = useNavigate();
  const { user, canAddEditExhumeSepultamentos, canDeleteSepultamentos } = useAuth();
  
  const [produto] = useState(location.state?.produto || {});
  const [sepultamentos, setSepultamentos] = useState([]);
  const [sepultamentosFiltrados, setSepultamentosFiltrados] = useState([]);
  const [filtroTexto, setFiltroTexto] = useState('');
  const [filtroAtivo, setFiltroAtivo] = useState(''); // Filtro que está sendo aplicado
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedSepultamento, setSelectedSepultamento] = useState(null);
  const [showSepultamentoModal, setShowSepultamentoModal] = useState(false);
  const [showExumarDialog, setShowExumarDialog] = useState(false);
  const [exumarData, setExumarData] = useState('');
  const [exumarObservacoes, setExumarObservacoes] = useState('');

  useEffect(() => {
    loadSepultamentos();
  }, [codigoEstacao]);

  // useEffect para filtrar sepultamentos quando o filtro ativo ou lista mudar
  useEffect(() => {
    if (!filtroAtivo.trim()) {
      setSepultamentosFiltrados(sepultamentos);
    } else {
      const filtrados = sepultamentos.filter(sepultamento => {
        const textoFiltro = filtroAtivo.toLowerCase();

        // Buscar por nome
        const nomeMatch = sepultamento.nome_sepultado?.toLowerCase().includes(textoFiltro);

        // Buscar por número da gaveta
        const gavetaMatch = sepultamento.numero_gaveta?.toString().includes(textoFiltro);

        // Buscar por data (formato DD/MM/YYYY)
        const dataFormatada = sepultamento.data_sepultamento ?
          new Date(sepultamento.data_sepultamento).toLocaleDateString('pt-BR') : '';
        const dataMatch = dataFormatada.includes(textoFiltro);

        // Buscar por localização/bloco
        const localizacaoMatch = sepultamento.denominacao_bloco?.toLowerCase().includes(textoFiltro);

        return nomeMatch || gavetaMatch || dataMatch || localizacaoMatch;
      });

      setSepultamentosFiltrados(filtrados);
      console.log(`🔍 Filtro "${filtroAtivo}" aplicado: ${filtrados.length} sepultamentos encontrados`);
    }
  }, [sepultamentos, filtroAtivo]);

  // Função para executar a busca
  const executarBusca = () => {
    setFiltroAtivo(filtroTexto);
    console.log(`🔍 Busca executada: "${filtroTexto}"`);
  };

  // Função para limpar a busca
  const limparBusca = () => {
    setFiltroTexto('');
    setFiltroAtivo('');
    console.log('🧹 Busca limpa');
  };

  // Função para capturar Enter no campo de busca
  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      executarBusca();
    }
  };

  const loadSepultamentos = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await sepultamentoService.listar({
        codigo_estacao: codigoEstacao,
        ativo: true
      });
      
      const sepultamentosData = response.data || [];
      setSepultamentos(sepultamentosData);
      setSepultamentosFiltrados(sepultamentosData); // Inicializar lista filtrada
      console.log(`📋 ${sepultamentosData.length} sepultamentos carregados para ${codigoEstacao}`);
    } catch (error) {
      console.error('Erro ao carregar sepultamentos:', error);
      setError('Erro ao carregar sepultamentos');
    } finally {
      setLoading(false);
    }
  };

  const handleAddSepultamento = () => {
    setSelectedSepultamento(null);
    setShowSepultamentoModal(true);
  };

  const handleEditSepultamento = (sepultamento) => {
    setSelectedSepultamento(sepultamento);
    setShowSepultamentoModal(true);
  };

  const handleExumarClick = (sepultamento) => {
    setSelectedSepultamento(sepultamento);
    setExumarData(new Date().toISOString().split('T')[0]);
    setExumarObservacoes('');
    setShowExumarDialog(true);
  };

  const handleExumarConfirm = async () => {
    try {
      await sepultamentoService.exumar(selectedSepultamento.id, {
        data_exumacao: exumarData,
        observacoes_exumacao: exumarObservacoes || 'Exumação realizada via Book de Sepultamentos'
      });
      
      setShowExumarDialog(false);
      loadSepultamentos();
    } catch (error) {
      console.error('Erro ao exumar sepultamento:', error);
      alert('Erro ao exumar sepultamento: ' + (error.response?.data?.error || error.message));
    }
  };

  const handleDeleteSepultamento = async (sepultamento) => {
    // Verificar se o sepultamento já foi exumado e se o usuário não é admin
    if (sepultamento.status_exumacao && !isAdmin()) {
      alert('Não é possível deletar sepultamentos que já foram exumados');
      return;
    }

    if (!window.confirm(`Tem certeza que deseja DELETAR PERMANENTEMENTE o sepultamento de ${sepultamento.nome_sepultado}?\n\nEsta ação não pode ser desfeita e removerá o registro do banco de dados.`)) {
      return;
    }

    try {
      await sepultamentoService.deletar(sepultamento.id);
      loadSepultamentos();
      alert('Sepultamento deletado permanentemente com sucesso!');
    } catch (error) {
      console.error('Erro ao deletar sepultamento:', error);
      alert('Erro ao deletar sepultamento: ' + (error.response?.data?.error || error.message));
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '-';

    // CORREÇÃO TIMEZONE: Usar split para evitar problema de fuso horário
    // Problema: new Date(dateString) converte para UTC e subtrai 1 dia
    // Solução: Fazer parse manual da data no formato YYYY-MM-DD
    const dateParts = dateString.split('T')[0].split('-');
    if (dateParts.length === 3) {
      const year = parseInt(dateParts[0]);
      const month = parseInt(dateParts[1]) - 1; // Mês é 0-indexado
      const day = parseInt(dateParts[2]);
      const localDate = new Date(year, month, day);
      return localDate.toLocaleDateString('pt-BR');
    }

    // Fallback para formato antigo (não deveria acontecer)
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const getStatusColor = (statusExumacao) => {
    return statusExumacao ? 'error' : 'success';
  };

  const getStatusText = (statusExumacao) => {
    return statusExumacao ? 'Exumado' : 'Sepultado';
  };

  const TableSkeleton = () => (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            {['Nome', 'Localização', 'Data', 'Status', 'Ações'].map((header) => (
              <TableCell key={header}>
                <Skeleton variant="text" width="80%" />
              </TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {[1, 2, 3, 4, 5].map((row) => (
            <TableRow key={row}>
              {[1, 2, 3, 4, 5].map((cell) => (
                <TableCell key={cell}>
                  <Skeleton variant="text" width="90%" />
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  return (
    <StandardContainer
      title={produto.denominacao || produto.nome || `Produto ${codigoEstacao}`}
      subtitle="Gerencie os sepultamentos deste produto de forma organizada"
      headerAction={
        canAddEditExhumeSepultamentos() && (
          <StandardButton
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAddSepultamento}
          >
            Adicionar Sepultamento
          </StandardButton>
        )
      }
    >
      {/* Breadcrumbs */}
      <Box sx={{ mb: 3 }}>
        <Breadcrumbs>
          <Link
            color="inherit"
            href="#"
            onClick={() => navigate('/dashboard')}
            sx={{ display: 'flex', alignItems: 'center', textDecoration: 'none' }}
          >
            <HomeIcon sx={{ mr: 0.5, fontSize: 16 }} />
            Início
          </Link>
          <Link
            color="inherit"
            href="#"
            onClick={() => navigate('/dashboard/book-sepultamentos')}
            sx={{ display: 'flex', alignItems: 'center', textDecoration: 'none' }}
          >
            <BusinessIcon sx={{ mr: 0.5, fontSize: 16 }} />
            Book de Sepultamentos
          </Link>
          <Typography color="text.primary">
            {produto.denominacao || produto.nome || codigoEstacao}
          </Typography>
        </Breadcrumbs>
      </Box>

      {/* Conteúdo Principal */}
      <ContentSection title="Lista de Sepultamentos">
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Campo de Busca com Botão */}
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'flex-start' }}>
            <TextField
              fullWidth
              label="Buscar sepultamentos"
              placeholder="Digite o nome, número da gaveta, data ou localização... (Pressione ENTER ou clique em BUSCAR)"
              value={filtroTexto}
              onChange={(e) => setFiltroTexto(e.target.value)}
              onKeyPress={handleKeyPress}
              variant="outlined"
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: '12px',
                },
              }}
              helperText={
                filtroAtivo ?
                  `Filtro ativo: "${filtroAtivo}" - ${sepultamentosFiltrados.length} de ${sepultamentos.length} sepultamentos encontrados` :
                  `Total: ${sepultamentos.length} sepultamentos - Digite e pressione ENTER para buscar`
              }
            />
            <StandardButton
              variant="contained"
              startIcon={<SearchIcon />}
              onClick={executarBusca}
              disabled={!filtroTexto.trim()}
              sx={{
                minWidth: '120px',
                height: '56px',
                borderRadius: '12px',
                textTransform: 'none',
                fontWeight: 600
              }}
            >
              BUSCAR
            </StandardButton>
            {filtroAtivo && (
              <StandardButton
                variant="outlined"
                startIcon={<ClearIcon />}
                onClick={limparBusca}
                sx={{
                  minWidth: '100px',
                  height: '56px',
                  borderRadius: '12px',
                  textTransform: 'none',
                  fontWeight: 600
                }}
              >
                LIMPAR
              </StandardButton>
            )}
          </Box>
        </Box>

        <StandardTable
          columns={[
            {
              id: 'nome_sepultado',
              label: 'Nome do Sepultado',
              minWidth: 200,
              render: (value) => (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <PersonIcon sx={{ mr: 1, color: 'text.secondary', fontSize: 20 }} />
                  <Typography variant="body2" fontWeight="medium">
                    {value}
                  </Typography>
                </Box>
              ),
            },
            {
              id: 'localizacao',
              label: 'Localização',
              minWidth: 180,
              render: (_, row) => {
                const localizacao = row.denominacao_bloco
                  ? `${row.denominacao_bloco} → Gaveta ${row.numero_gaveta}`
                  : `Gaveta ${row.numero_gaveta}`;
                return (
                  <Typography variant="body2" color="text.secondary">
                    {localizacao}
                  </Typography>
                );
              },
            },
            {
              id: 'numero_gaveta',
              label: 'Gaveta',
              minWidth: 100,
              align: 'center',
              render: (value) => (
                <Typography variant="body2" fontWeight="medium" color="primary.main">
                  {value || '-'}
                </Typography>
              ),
            },
            {
              id: 'data_sepultamento',
              label: 'Data Sepultamento',
              minWidth: 130,
              render: (value) => (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <CalendarIcon sx={{ mr: 1, color: 'text.secondary', fontSize: 16 }} />
                  <Typography variant="body2">
                    {formatDate(value)}
                  </Typography>
                </Box>
              ),
            },
            {
              id: 'horario_sepultamento',
              label: 'Horário',
              minWidth: 100,
              render: (value) => (
                <Typography variant="body2">
                  {value || '-'}
                </Typography>
              ),
            },
            {
              id: 'status_exumacao',
              label: 'Status',
              minWidth: 120,
              render: (value) => (
                <StatusChip
                  status={getStatusText(value)}
                  color={getStatusColor(value)}
                />
              ),
            },
            {
              id: 'actions',
              label: 'Ações',
              minWidth: 150,
              align: 'center',
              render: (_, row) => {
                const actions = [];

                if (canAddEditExhumeSepultamentos() && !row.status_exumacao) {
                  actions.push({
                    icon: <EditIcon />,
                    tooltip: 'Editar',
                    onClick: () => handleEditSepultamento(row),
                    color: 'primary',
                  });
                  actions.push({
                    icon: <ExumarIcon />,
                    tooltip: 'Exumar',
                    onClick: () => handleExumarClick(row),
                    color: 'warning',
                  });
                }

                if (canDeleteSepultamentos()) {
                  actions.push({
                    icon: <DeleteIcon />,
                    tooltip: 'Deletar',
                    onClick: () => handleDeleteSepultamento(row),
                    color: 'error',
                  });
                }

                return <TableActions actions={actions} />;
              },
            },
          ]}
          data={sepultamentosFiltrados}
          loading={loading}
          emptyMessage={
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Typography variant="h6" gutterBottom>
                {filtroAtivo ? 'Nenhum sepultamento encontrado com este filtro' : 'Nenhum sepultamento encontrado'}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                {filtroAtivo ?
                  `Tente ajustar o filtro "${filtroAtivo}" ou limpe a busca para ver todos os sepultamentos.` :
                  'Este produto ainda não possui sepultamentos registrados.'
                }
              </Typography>
              {canAddEditExhumeSepultamentos() && !filtroAtivo && (
                <StandardButton
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleAddSepultamento}
                >
                  Adicionar Primeiro Sepultamento
                </StandardButton>
              )}
              {filtroAtivo && (
                <StandardButton
                  variant="outlined"
                  onClick={limparBusca}
                >
                  Limpar Filtro
                </StandardButton>
              )}
            </Box>
          }
        />
      </ContentSection>

      {/* Modal de Sepultamento */}
      <BookSepultamentoModal
        isOpen={showSepultamentoModal}
        onClose={() => {
          setShowSepultamentoModal(false);
          setSelectedSepultamento(null);
        }}
        sepultamento={selectedSepultamento}
        produto={produto}
        onSuccess={loadSepultamentos}
      />

      {/* Dialog de Exumação */}
      <Dialog open={showExumarDialog} onClose={() => setShowExumarDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Confirmar Exumação</DialogTitle>
        <DialogContent>
          <Typography variant="body1" sx={{ mb: 3 }}>
            Tem certeza que deseja exumar o sepultamento de <strong>{selectedSepultamento?.nome_sepultado}</strong>?
          </Typography>
          
          <TextField
            label="Data da Exumação"
            type="date"
            value={exumarData}
            onChange={(e) => setExumarData(e.target.value)}
            fullWidth
            sx={{ mb: 2 }}
            InputLabelProps={{ shrink: true }}
            required
          />
          
          <TextField
            label="Observações (opcional)"
            multiline
            rows={3}
            value={exumarObservacoes}
            onChange={(e) => setExumarObservacoes(e.target.value)}
            fullWidth
            placeholder="Observações sobre a exumação..."
          />
        </DialogContent>
        <DialogActions>
          <StandardButton
            variant="outlined"
            onClick={() => setShowExumarDialog(false)}
          >
            Cancelar
          </StandardButton>
          <StandardButton
            variant="contained"
            color="warning"
            onClick={handleExumarConfirm}
            disabled={!exumarData}
          >
            Confirmar Exumação
          </StandardButton>
        </DialogActions>
      </Dialog>
    </StandardContainer>
  );
};

export default BookSepultamentosDetalhePage;
