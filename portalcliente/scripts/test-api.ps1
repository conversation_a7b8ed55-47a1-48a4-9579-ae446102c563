# Script de teste para a API do Portal Evolution
Write-Host "🧪 Testando API do Portal Evolution" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

$baseUrl = "http://localhost:3001/api"

# Função para fazer requisições
function Invoke-ApiRequest {
    param(
        [string]$Method,
        [string]$Endpoint,
        [object]$Body = $null,
        [string]$Token = $null
    )
    
    $headers = @{
        "Content-Type" = "application/json"
    }
    
    if ($Token) {
        $headers["Authorization"] = "Bearer $Token"
    }
    
    try {
        $params = @{
            Uri = "$baseUrl$Endpoint"
            Method = $Method
            Headers = $headers
        }
        
        if ($Body) {
            $params["Body"] = ($Body | ConvertTo-Json -Depth 10)
        }
        
        $response = Invoke-RestMethod @params
        return @{ Success = $true; Data = $response }
    }
    catch {
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

# 1. Teste de Health Check
Write-Host "`n1. 🏥 Testando Health Check..." -ForegroundColor Yellow
$healthResult = Invoke-ApiRequest -Method GET -Endpoint "/health"
if ($healthResult.Success) {
    Write-Host "   ✅ Health Check OK: $($healthResult.Data.message)" -ForegroundColor Green
} else {
    Write-Host "   ❌ Health Check falhou: $($healthResult.Error)" -ForegroundColor Red
    exit 1
}

# 2. Teste de Login Admin
Write-Host "`n2. 🔐 Testando Login Admin..." -ForegroundColor Yellow
$loginBody = @{
    email = "admin"
    senha = "adminnbr5410!"
}
$loginResult = Invoke-ApiRequest -Method POST -Endpoint "/auth/login" -Body $loginBody
if ($loginResult.Success) {
    $adminToken = $loginResult.Data.token
    Write-Host "   ✅ Login Admin OK" -ForegroundColor Green
    Write-Host "   👤 Usuário: $($loginResult.Data.usuario.nome)" -ForegroundColor Cyan
} else {
    Write-Host "   ❌ Login Admin falhou: $($loginResult.Error)" -ForegroundColor Red
    exit 1
}

# 3. Teste de Login Cliente
Write-Host "`n3. 🔐 Testando Login Cliente..." -ForegroundColor Yellow
$clienteLoginBody = @{
    email = "<EMAIL>"
    senha = "cliente123"
}
$clienteLoginResult = Invoke-ApiRequest -Method POST -Endpoint "/auth/login" -Body $clienteLoginBody
if ($clienteLoginResult.Success) {
    $clienteToken = $clienteLoginResult.Data.token
    Write-Host "   ✅ Login Cliente OK" -ForegroundColor Green
    Write-Host "   👤 Usuário: $($clienteLoginResult.Data.usuario.nome)" -ForegroundColor Cyan
} else {
    Write-Host "   ❌ Login Cliente falhou: $($clienteLoginResult.Error)" -ForegroundColor Red
}

# 4. Teste de Listagem de Sepultamentos (Admin)
Write-Host "`n4. ⚰️ Testando Listagem de Sepultamentos (Admin)..." -ForegroundColor Yellow
$sepultamentosResult = Invoke-ApiRequest -Method GET -Endpoint "/sepultamentos" -Token $adminToken
if ($sepultamentosResult.Success) {
    $count = $sepultamentosResult.Data.Count
    Write-Host "   ✅ Sepultamentos carregados: $count registros" -ForegroundColor Green
    if ($count -gt 0) {
        $primeiro = $sepultamentosResult.Data[0]
        Write-Host "   📋 Primeiro registro: $($primeiro.nome_sepultado)" -ForegroundColor Cyan
    }
} else {
    Write-Host "   ❌ Listagem de Sepultamentos falhou: $($sepultamentosResult.Error)" -ForegroundColor Red
}

# 5. Teste de Listagem de Produtos
Write-Host "`n5. 🏢 Testando Listagem de Produtos..." -ForegroundColor Yellow
$produtosResult = Invoke-ApiRequest -Method GET -Endpoint "/produtos" -Token $adminToken
if ($produtosResult.Success) {
    $count = $produtosResult.Data.Count
    Write-Host "   ✅ Produtos carregados: $count registros" -ForegroundColor Green
    if ($count -gt 0) {
        $primeiro = $produtosResult.Data[0]
        Write-Host "   📋 Primeiro produto: $($primeiro.nome) ($($primeiro.tipo))" -ForegroundColor Cyan
    }
} else {
    Write-Host "   ❌ Listagem de Produtos falhou: $($produtosResult.Error)" -ForegroundColor Red
}

# 6. Teste de Listagem de Gavetas
Write-Host "`n6. 🗃️ Testando Listagem de Gavetas..." -ForegroundColor Yellow
$gavetasResult = Invoke-ApiRequest -Method GET -Endpoint "/gavetas?sub_bloco_id=1" -Token $adminToken
if ($gavetasResult.Success) {
    $count = $gavetasResult.Data.Count
    Write-Host "   ✅ Gavetas carregadas: $count registros" -ForegroundColor Green
    if ($count -gt 0) {
        $ocupadas = ($gavetasResult.Data | Where-Object { -not $_.disponivel }).Count
        $disponiveis = $count - $ocupadas
        Write-Host "   📊 Ocupadas: $ocupadas | Disponíveis: $disponiveis" -ForegroundColor Cyan
    }
} else {
    Write-Host "   ❌ Listagem de Gavetas falhou: $($gavetasResult.Error)" -ForegroundColor Red
}

# 7. Teste de Verificação de Token
Write-Host "`n7. 🔍 Testando Verificação de Token..." -ForegroundColor Yellow
$verifyResult = Invoke-ApiRequest -Method GET -Endpoint "/auth/verify" -Token $adminToken
if ($verifyResult.Success) {
    Write-Host "   ✅ Token válido" -ForegroundColor Green
    Write-Host "   👤 Usuário verificado: $($verifyResult.Data.usuario.nome)" -ForegroundColor Cyan
} else {
    Write-Host "   ❌ Verificação de Token falhou: $($verifyResult.Error)" -ForegroundColor Red
}

# 8. Teste de Acesso Restrito (Cliente tentando acessar logs)
if ($clienteToken) {
    Write-Host "`n8. 🚫 Testando Acesso Restrito (Cliente -> Logs)..." -ForegroundColor Yellow
    $logsResult = Invoke-ApiRequest -Method GET -Endpoint "/logs" -Token $clienteToken
    if (-not $logsResult.Success) {
        Write-Host "   ✅ Acesso negado corretamente para cliente" -ForegroundColor Green
    } else {
        Write-Host "   ❌ Cliente conseguiu acessar logs (erro de segurança!)" -ForegroundColor Red
    }
}

# 9. Teste de Logs (Admin)
Write-Host "`n9. 📋 Testando Logs de Auditoria (Admin)..." -ForegroundColor Yellow
$logsResult = Invoke-ApiRequest -Method GET -Endpoint "/logs" -Token $adminToken
if ($logsResult.Success) {
    $count = $logsResult.Data.logs.Count
    Write-Host "   ✅ Logs carregados: $count registros" -ForegroundColor Green
    if ($count -gt 0) {
        $ultimo = $logsResult.Data.logs[0]
        Write-Host "   📋 Última ação: $($ultimo.acao) por $($ultimo.nome_usuario)" -ForegroundColor Cyan
    }
} else {
    Write-Host "   ❌ Listagem de Logs falhou: $($logsResult.Error)" -ForegroundColor Red
}

# 10. Resumo dos Testes
Write-Host "`n" -ForegroundColor White
Write-Host "📊 RESUMO DOS TESTES" -ForegroundColor Green
Write-Host "===================" -ForegroundColor Green
Write-Host "✅ API funcionando corretamente" -ForegroundColor Green
Write-Host "✅ Autenticação implementada" -ForegroundColor Green
Write-Host "✅ Controle de acesso funcionando" -ForegroundColor Green
Write-Host "✅ Endpoints principais operacionais" -ForegroundColor Green
Write-Host "✅ Logs de auditoria ativos" -ForegroundColor Green
Write-Host ""
Write-Host "🌐 Frontend disponível em: http://localhost:5173" -ForegroundColor Cyan
Write-Host "🔧 API disponível em: http://localhost:3001/api" -ForegroundColor Cyan
Write-Host ""
Write-Host "🔑 Credenciais de teste:" -ForegroundColor Yellow
Write-Host "   Admin: admin / adminnbr5410!" -ForegroundColor White
Write-Host "   Cliente: <EMAIL> / cliente123" -ForegroundColor White
Write-Host ""
Write-Host "🎉 Portal Evolution está pronto para uso!" -ForegroundColor Green
