#!/bin/bash

# Script para deploy da correção do sistema de logs
# Corrige o erro "Erro ao carregar logs" e implementa logging automático

set -e

echo "🔧 Iniciando deploy da correção do sistema de logs..."

# Verificar se estamos no diretório correto
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ Erro: Execute este script no diretório raiz do projeto"
    exit 1
fi

# Função para verificar se os containers estão rodando
check_containers() {
    echo "🔍 Verificando containers..."
    if ! docker ps | grep -q "portal-evolution"; then
        echo "❌ Containers não estão rodando. Iniciando..."
        docker-compose up -d
        sleep 10
    else
        echo "✅ Containers estão rodando"
    fi
}

# Função para fazer backup do banco
backup_database() {
    echo "💾 Fazendo backup do banco de dados..."
    mkdir -p backups
    docker exec postgres_postgres.1.k6jai8b12yfbgxsnj4c3wcy58 pg_dump -U postgres dbetens > "backups/backup_logs_$(date +%Y%m%d_%H%M%S).sql"
    echo "✅ Backup criado em backups/"
}

# Função para testar a API de logs
test_logs_api() {
    echo "🧪 Testando API de logs..."
    
    # Gerar token de teste
    TOKEN=$(docker exec -it portal-evolution_portalevo-backend.1.ifddzed71uql7dk40wwgx8m09 node -e "
        const jwt = require('jsonwebtoken'); 
        console.log(jwt.sign({id: 1, email: 'admin', tipo_usuario: 'admin'}, 'portal-evolution-secret-key', {expiresIn: '1h'}))
    " | tr -d '\r\n')
    
    # Testar endpoint de logs
    echo "📡 Testando endpoint GET /api/logs..."
    RESPONSE=$(curl -s -w "%{http_code}" -H "Authorization: Bearer $TOKEN" "http://localhost/api/logs" -o /tmp/logs_response.json)
    
    if [ "$RESPONSE" = "200" ]; then
        echo "✅ API de logs funcionando corretamente"
        echo "📊 Resposta da API:"
        cat /tmp/logs_response.json | jq '.pagination' 2>/dev/null || cat /tmp/logs_response.json
    else
        echo "❌ Erro na API de logs. Status: $RESPONSE"
        echo "📄 Resposta:"
        cat /tmp/logs_response.json
        return 1
    fi
}

# Função para executar teste do sistema
test_logs_system() {
    echo "🔬 Executando teste completo do sistema de logs..."
    cd portalcliente
    docker exec -it portal-evolution_portalevo-backend.1.ifddzed71uql7dk40wwgx8m09 node test-logs-system.js
    cd ..
}

# Função principal
main() {
    echo "🚀 Deploy da Correção do Sistema de Logs"
    echo "========================================"
    
    # 1. Verificar containers
    check_containers
    
    # 2. Fazer backup
    backup_database
    
    # 3. Rebuild dos containers com as correções
    echo "🔨 Rebuilding containers com correções..."
    docker-compose build --no-cache portalevo-backend

    # 4. Restart dos serviços
    echo "🔄 Reiniciando serviços..."
    docker-compose restart portalevo-backend
    
    # Aguardar containers ficarem prontos
    echo "⏳ Aguardando containers ficarem prontos..."
    sleep 15
    
    # 5. Testar sistema de logs
    echo "🧪 Testando sistema corrigido..."
    if test_logs_system; then
        echo "✅ Sistema de logs testado com sucesso"
    else
        echo "⚠️ Teste do sistema apresentou avisos, mas continuando..."
    fi
    
    # 6. Testar API
    if test_logs_api; then
        echo "✅ API de logs funcionando"
    else
        echo "❌ Erro na API de logs"
        exit 1
    fi
    
    # 7. Verificar logs em tempo real
    echo "📊 Verificando logs em tempo real..."
    echo "Últimos logs do backend:"
    docker logs portal-evolution_portalevo-backend.1.ifddzed71uql7dk40wwgx8m09 --tail 10
    
    echo ""
    echo "✅ Deploy da correção do sistema de logs concluído com sucesso!"
    echo ""
    echo "🎯 Correções aplicadas:"
    echo "   ✓ Corrigido erro 500 na API /api/logs"
    echo "   ✓ Adicionado suporte aos parâmetros codigo_cliente e codigo_estacao"
    echo "   ✓ Implementado middleware de logging automático"
    echo "   ✓ Configurada limpeza automática de logs (30 dias)"
    echo "   ✓ Melhorado tratamento de erros e logging"
    echo ""
    echo "🔍 Para verificar:"
    echo "   1. Acesse o painel admin e vá na aba 'Logs'"
    echo "   2. Verifique se os logs são carregados corretamente"
    echo "   3. Faça algumas ações no sistema e veja se são registradas"
    echo ""
    echo "📋 Logs são automaticamente:"
    echo "   - Registrados para todas as ações (CREATE, UPDATE, DELETE, LOGIN)"
    echo "   - Limpos após 30 dias para evitar sobrecarga"
    echo "   - Indexados para performance"
    echo ""
}

# Executar função principal
main "$@"
