const axios = require('axios');

async function testDashboard() {
  try {
    console.log('🔄 Testando APIs do dashboard...');
    
    // Fazer login
    console.log('1. Fazendo login...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: 'admin',
      senha: 'adminnbr5410!'
    });
    
    const token = loginResponse.data.token;
    console.log('✅ Login realizado com sucesso');
    
    // Configurar headers
    const headers = { Authorization: `Bearer ${token}` };
    
    // Testar stats
    console.log('2. Testando /api/dashboard/stats...');
    const statsResponse = await axios.get('http://localhost:3001/api/dashboard/stats', { headers });
    console.log('✅ Stats:', JSON.stringify(statsResponse.data, null, 2));
    
    // Testar próximas exumações
    console.log('3. Testando /api/dashboard/proximas-exumacoes...');
    const proximasResponse = await axios.get('http://localhost:3001/api/dashboard/proximas-exumacoes', { headers });
    console.log('✅ Próximas exumações:', proximasResponse.data.length, 'registros');
    
    // Testar atividades recentes
    console.log('4. Testando /api/dashboard/atividades-recentes...');
    const atividadesResponse = await axios.get('http://localhost:3001/api/dashboard/atividades-recentes', { headers });
    console.log('✅ Atividades recentes:', atividadesResponse.data.length, 'registros');
    
    // Testar detalhes de sepultamentos
    console.log('5. Testando /api/dashboard/sepultamentos-details...');
    const sepultamentosResponse = await axios.get('http://localhost:3001/api/dashboard/sepultamentos-details', { headers });
    console.log('✅ Detalhes de sepultamentos:', sepultamentosResponse.data.length, 'registros');
    
    console.log('🎉 Todos os testes passaram!');
    
  } catch (error) {
    console.error('❌ Erro no teste:', error.response?.data || error.message);
    if (error.response?.status) {
      console.error('Status:', error.response.status);
    }
  }
}

testDashboard();
