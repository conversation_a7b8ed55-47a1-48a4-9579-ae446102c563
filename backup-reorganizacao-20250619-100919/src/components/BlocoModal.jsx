import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import Modal from './Modal';
import { produtoService } from '../services/api';

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const Label = styled.label`
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
`;

const Input = styled.input`
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  &:disabled {
    background-color: #f9fafb;
    color: #6b7280;
  }
`;

const TextArea = styled.textarea`
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  min-height: 100px;
  resize: vertical;
  font-family: inherit;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
`;

const Button = styled.button`
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;

  &.primary {
    background: #3b82f6;
    color: white;

    &:hover {
      background: #2563eb;
    }

    &:disabled {
      background: #9ca3af;
      cursor: not-allowed;
    }
  }

  &.secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;

    &:hover {
      background: #e5e7eb;
    }
  }

  &.danger {
    background: #ef4444;
    color: white;

    &:hover {
      background: #dc2626;
    }

    &:disabled {
      background: #9ca3af;
      cursor: not-allowed;
    }
  }
`;

const ErrorMessage = styled.div`
  background: #fef2f2;
  color: #dc2626;
  padding: 12px;
  border-radius: 8px;
  font-size: 0.875rem;
`;

const BlocoModal = ({ isOpen, onClose, bloco, produtoId, onSuccess }) => {
  const [formData, setFormData] = useState({
    codigo_bloco: '',
    nome: '',
    descricao: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (isOpen) {
      if (bloco) {
        setFormData({
          codigo_bloco: bloco.codigo_bloco || '',
          nome: bloco.nome || '',
          descricao: bloco.descricao || ''
        });
      } else {
        setFormData({
          codigo_bloco: '',
          nome: '',
          descricao: ''
        });
      }
      setError('');
    }
  }, [isOpen, bloco]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      if (bloco) {
        await produtoService.atualizarBloco(bloco.id, formData);
        alert('Bloco atualizado com sucesso!');
      } else {
        await produtoService.criarBloco(produtoId, formData);
        alert('Bloco criado com sucesso!');
      }

      onSuccess();
      onClose();
    } catch (error) {
      console.error('Erro ao salvar bloco:', error);
      setError(error.response?.data?.error || 'Erro ao salvar bloco');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!bloco || !window.confirm('Tem certeza que deseja deletar este bloco? Esta ação não pode ser desfeita.')) {
      return;
    }

    setLoading(true);
    setError('');

    try {
      await produtoService.deletarBloco(bloco.id);
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Erro ao deletar bloco:', error);
      setError(error.response?.data?.error || 'Erro ao deletar bloco');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={bloco ? 'Editar Bloco' : 'Novo Bloco'}
      maxWidth="600px"
    >
      <Form onSubmit={handleSubmit}>
        <FormGroup>
          <Label>Código do Bloco *</Label>
          <Input
            type="text"
            name="codigo_bloco"
            value={formData.codigo_bloco}
            onChange={handleChange}
            required
            placeholder="Ex: BL01"
            disabled={!!bloco}
          />
        </FormGroup>

        <FormGroup>
          <Label>Denominação *</Label>
          <Input
            type="text"
            name="nome"
            value={formData.nome}
            onChange={handleChange}
            required
            placeholder="Ex: Bloco Principal"
          />
        </FormGroup>

        <FormGroup>
          <Label>Observações</Label>
          <TextArea
            name="descricao"
            value={formData.descricao}
            onChange={handleChange}
            placeholder="Detalhes sobre o bloco..."
          />
        </FormGroup>

        {error && <ErrorMessage>{error}</ErrorMessage>}

        <ButtonGroup>
          <Button type="button" className="secondary" onClick={onClose}>
            Cancelar
          </Button>
          {bloco && (
            <Button
              type="button"
              className="danger"
              onClick={handleDelete}
              disabled={loading}
            >
              {loading ? 'Deletando...' : 'Deletar'}
            </Button>
          )}
          <Button type="submit" className="primary" disabled={loading}>
            {loading ? 'Salvando...' : (bloco ? 'Atualizar' : 'Criar')}
          </Button>
        </ButtonGroup>
      </Form>
    </Modal>
  );
};

export default BlocoModal;
