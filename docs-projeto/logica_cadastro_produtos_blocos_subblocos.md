Lógica de funcionamento dos cadastros, edições, remoções de produtos, blocos e sub-blocos.


Caso 1: Todo cadastro de produto (que possui um código_estacao associado à um código_cliente), deve estar associado à um cliente. Em hipótese nenhuma um produto poderá ser criado sem estar associado à um cliente e não podem haver dois produtos com o mesmo código_estacao para o mesmo cliente. O usuário deve ter a opção de Editar e Deletar a estação, considerando que, na Edição é impossível de mudar o código_estacao e codigo_cliente que foi usado. Caso o usuário queira mudar o codigo_cliente ou codigo_estacao, será preciso deletar o produto e criar um novo. Toda ação de deleção deve ser precedida de uma mensagem perguntando ao usuário se ele confirma o procedimento. Exemplo: "Tem certeza que deseja deletar este produto?" e informando o motivo da deleção. Este motivo deve ser armazenado no histórico de criação daquele (isso ficará na aba do LOG)


Caso 2: Todo cadastro de bloco (que possui um codigo_bloco associado à um codigo_cliente, codigo_estacao e consequentemente codigo_cliente), deve estar associado à um produto. Em hipótese nenhuma um bloco poderá ser criado sem estar associado à um produto e não podem haver dois blocos com o mesmo codigo_bloco para o mesmo produto. O usuário deve ter a opção de Editar e Deletar o bloco, considerando que, na Edição é impossível de mudar o codigo_bloco e codigo_cliente já criado. 

  - Nome do sepultado -> Descrever o nome do Sepultado
  - Código do bloco -> Abra todas as opções disponíveis de blocos, baseado no codigo_cliente e codigo_estacao de todos os filtros até onde o usuário chegou, para que o usuário selecione apenas uma.
  - Número da gaveta -> Abra todas as opções disponíveis de gavetas, baseado no codigo_cliente, codigo_estacao e codigo_bloco. Caso já tenha algum sepultado em alguma gaveta, esta não deverá aparecer dentre as opções de numerações de gavetas.
  - Data do sepultamento -> A data do sepultamento.
  - Hora do sepultamento -> A hora do sepultamento.
  - Observações -> Observações adicionais sobre o sepultamento...


Caso 3: Caso o usuário clique em editar em algum dos sepultamentos que estão listados na aba "Listar Sepultados", ele poderá editar os seguintes dados:

  - Nome do sepultado -> Descrever o nome do Sepultado
  - Data do sepultamento -> A data do sepultamento.
  - Hora do sepultamento -> A hora do sepultamento.
  - Observações -> Observações adicionais sobre o sepultamento.

    Se o usuário tiver cadastrado no Bloco ou Gaveta errado, ele precisará deletar o sepultamento e cadastrar novamente.

Caso 4: Caso o usuário clique em deletar em algum dos sepultamentos que estão listados na aba "Listar Sepultados", ele poderá deletar o sepultamento, desde que preencha o motivo da deleção. Este motivo da deleção deve ficar registrado junto ao registro desta gaveta específica.

Caso 5: Todo registro de sepultamento deve estar associado à um Status sendo "Disponível", "Sepultada", "Deletada" ou "Exumada". 
    Disponível -> Onde o Ativo é uma gaveta que foi criada e está disponível para o usuário cliente cadastrar novo sepultamento nela.
    Sepultada -> Onde a gaveta já tem um cadastro de sepultado nela.
    Deletada -> Onde o sepultamento foi deletado e informado o motivo da deleção da mesma.
    Exumada -> Onde foi realizado a exumação do sepultado que estava na mesma, registrando a data e hora da exumação e observações, e tornando a gaveta Disponível novamente.

Toda vez que o usuário fizer algum procedimento, envie uma mensagem perguntando ao usuário se ele confirma o procedimento. Exemplo: "Tem certeza que deseja deletar este sepultamento?" ou "Tem certeza que deseja exumar este sepultamento?", ou "Você confirma todos os dados registrados deste Sepultamento?"
