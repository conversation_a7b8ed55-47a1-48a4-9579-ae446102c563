import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Alert,
  Pagination,
  CircularProgress,
  Grid,
  Paper,
} from '@mui/material';
import {
  Search as SearchIcon,
  Clear as ClearIcon,
  History as HistoryIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  AccessTime as TimeIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { logService, clienteService, produtoService, usuarioService } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import { useAsyncOperation } from '../contexts/LoadingContext';
import Modal from '../components/Modal';
import {
  StandardContainer,
  StandardButton,
  StandardCard,
  StandardForm,
  StandardTextField,
  StandardSelect,
  StandardTable,
  StatusChip,
  FormGrid,
  FormGridItem,
  FormSection,
  ContentSection,
} from '../components/common';

const LogsPage = () => {
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({});
  const [filters, setFilters] = useState({
    codigo_cliente: '',
    codigo_estacao: '',
    usuario_id: '',
    data_inicio: '',
    data_fim: '',
    acao: '',
    page: 1,
    limit: 50
  });

  const [clientes, setClientes] = useState([]);
  const [produtos, setProdutos] = useState([]);
  const [usuarios, setUsuarios] = useState([]);
  const [selectedLog, setSelectedLog] = useState(null);
  const { executeAsync } = useAsyncOperation();
  const [showDetailModal, setShowDetailModal] = useState(false);

  const { user } = useAuth();

  useEffect(() => {
    loadLogs();
    loadFilterOptions();
  }, []);

  // Aplicar filtros automaticamente quando data início ou fim mudarem
  useEffect(() => {
    if (filters.data_inicio || filters.data_fim) {
      loadLogs();
    }
  }, [filters.data_inicio, filters.data_fim]);

  const loadLogs = async () => {
    try {
      setLoading(true);

      // Preparar filtros para envio
      const filtrosParaEnvio = { ...filters };

      // Se não há filtros de data, carregar apenas as últimas 50 ocorrências
      if (!filters.data_inicio && !filters.data_fim) {
        filtrosParaEnvio.ultimas_50 = true;
      }

      console.log('📋 Carregando logs com filtros:', filtrosParaEnvio);

      const response = await logService.listar(filtrosParaEnvio);
      setLogs(response.data.logs || response.data);
      setPagination(response.data.pagination || {});
    } catch (error) {
      console.error('Erro ao carregar logs:', error);
      alert('Erro ao carregar logs');
    } finally {
      setLoading(false);
    }
  };

  const loadFilterOptions = async () => {
    try {
      const clientesResponse = await clienteService.listar();
      setClientes(clientesResponse.data);

      const produtosResponse = await produtoService.listar();
      setProdutos(produtosResponse.data);

      const usuariosResponse = await usuarioService.listar();
      setUsuarios(usuariosResponse.data);
    } catch (error) {
      console.error('Erro ao carregar opções de filtro:', error);
    }
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value,
      page: 1
    }));
  };

  const handleApplyFilters = () => {
    loadLogs();
  };

  const handleClearFilters = () => {
    setFilters({
      codigo_cliente: '',
      codigo_estacao: '',
      usuario_id: '',
      data_inicio: '',
      data_fim: '',
      acao: '',
      page: 1,
      limit: 50
    });
  };

  const handlePageChange = (event, newPage) => {
    setFilters(prev => ({ ...prev, page: newPage }));
    setTimeout(loadLogs, 100);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const brasilDate = new Date(date.getTime() - (3 * 60 * 60 * 1000));
    return brasilDate.toLocaleString('pt-BR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const getActionLabel = (action) => {
    const labels = {
      'CREATE': 'Cadastro',
      'EDIT': 'Edição',
      'DELETE': 'Deleção',
      'EXUMAR': 'Exumação'
    };
    return labels[action] || action;
  };

  const getActionColor = (action) => {
    const colors = {
      'CREATE': 'success',
      'EDIT': 'warning',
      'DELETE': 'error',
      'EXUMAR': 'secondary'
    };
    return colors[action] || 'default';
  };

  const handleLogClick = (log) => {
    setSelectedLog(log);
    setShowDetailModal(true);
  };

  const formatJsonData = (data) => {
    if (!data) return 'Nenhum dado disponível';
    try {
      return JSON.stringify(data, null, 2);
    } catch (error) {
      return 'Erro ao formatar dados';
    }
  };

  // Função para formatar data e hora
  const formatDateTime = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString('pt-BR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  // Função para buscar logs
  const handleSearch = () => {
    loadLogs();
  };

  if (loading && logs.length === 0) {
    return (
      <StandardContainer title="📋 Logs de Auditoria" subtitle="Histórico de atividades do sistema">
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
          <CircularProgress size={40} />
        </Box>
      </StandardContainer>
    );
  }

  return (
    <StandardContainer
      title="📋 Logs de Auditoria"
      subtitle="Histórico de atividades do sistema"
    >
      <ContentSection title="Filtros de Pesquisa" subtitle="Configure os filtros para encontrar logs específicos">
        <StandardForm>
          <FormSection>
            <FormGrid spacing={3}>
              <FormGridItem xs={12} sm={6} md={4}>
                <StandardSelect
                  label="Cliente"
                  value={filters.codigo_cliente}
                  onChange={(e) => handleFilterChange('codigo_cliente', e.target.value)}
                  options={[
                    { value: '', label: 'Todos os clientes' },
                    ...clientes.map(cliente => ({
                      value: cliente.codigo_cliente,
                      label: `${cliente.nome_fantasia} (${cliente.codigo_cliente})`
                    }))
                  ]}
                  size="small"
                />
              </FormGridItem>

              <FormGridItem xs={12} sm={6} md={4}>
                <StandardSelect
                  label="Estação/Produto"
                  value={filters.codigo_estacao}
                  onChange={(e) => handleFilterChange('codigo_estacao', e.target.value)}
                  options={[
                    { value: '', label: 'Todas as estações' },
                    ...produtos.map(produto => ({
                      value: produto.codigo_estacao,
                      label: `${produto.denominacao} (${produto.codigo_estacao})`
                    }))
                  ]}
                  size="small"
                />
              </FormGridItem>

              <FormGridItem xs={12} sm={6} md={4}>
                <StandardSelect
                  label="Usuário"
                  value={filters.usuario_id}
                  onChange={(e) => handleFilterChange('usuario_id', e.target.value)}
                  options={[
                    { value: '', label: 'Todos os usuários' },
                    ...usuarios.map(usuario => ({
                      value: usuario.id,
                      label: `${usuario.nome} (${usuario.email})`
                    }))
                  ]}
                  size="small"
                />
              </FormGridItem>

              <FormGridItem xs={12} sm={6} md={3}>
                <StandardTextField
                  label="Data Início"
                  type="date"
                  value={filters.data_inicio}
                  onChange={(e) => handleFilterChange('data_inicio', e.target.value)}
                  InputLabelProps={{ shrink: true }}
                  size="small"
                />
              </FormGridItem>

              <FormGridItem xs={12} sm={6} md={3}>
                <StandardTextField
                  label="Data Fim"
                  type="date"
                  value={filters.data_fim}
                  onChange={(e) => handleFilterChange('data_fim', e.target.value)}
                  InputLabelProps={{ shrink: true }}
                  size="small"
                />
              </FormGridItem>

              <FormGridItem xs={12} sm={6} md={3}>
                <StandardSelect
                  label="Ação"
                  value={filters.acao}
                  onChange={(e) => handleFilterChange('acao', e.target.value)}
                  options={[
                    { value: '', label: 'Todas as ações' },
                    { value: 'CREATE', label: 'Criação' },
                    { value: 'UPDATE', label: 'Atualização' },
                    { value: 'DELETE', label: 'Exclusão' },
                    { value: 'LOGIN', label: 'Login' },
                    { value: 'LOGOUT', label: 'Logout' },
                  ]}
                  size="small"
                />
              </FormGridItem>

              <FormGridItem xs={12} sm={6} md={3}>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <StandardButton
                    variant="contained"
                    startIcon={<SearchIcon />}
                    onClick={handleSearch}
                    disabled={loading}
                    size="small"
                  >
                    Buscar
                  </StandardButton>
                  <StandardButton
                    variant="outlined"
                    startIcon={<ClearIcon />}
                    onClick={handleClearFilters}
                    size="small"
                  >
                    Limpar
                  </StandardButton>
                </Box>
              </FormGridItem>
            </FormGrid>
          </FormSection>
        </StandardForm>
      </ContentSection>

      {/* Seção de Resultados */}
      <ContentSection
        title="Histórico de Logs"
        subtitle={
          !filters.data_inicio && !filters.data_fim
            ? `Exibindo as últimas ${logs.length} ocorrências`
            : `Encontrados ${pagination.total || logs.length} registros`
        }
      >
        <StandardTable
          columns={[
            {
              id: 'created_at',
              label: 'Data e Hora',
              minWidth: 180,
              render: (value) => (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <TimeIcon sx={{ mr: 1, color: 'text.secondary', fontSize: 16 }} />
                  <Typography variant="body2">
                    {formatDateTime(value)}
                  </Typography>
                </Box>
              ),
            },
            {
              id: 'acao',
              label: 'Ação',
              minWidth: 120,
              render: (value) => {
                const getActionColor = (action) => {
                  switch (action) {
                    case 'CREATE': return 'success';
                    case 'UPDATE': return 'warning';
                    case 'DELETE': return 'error';
                    case 'LOGIN': return 'info';
                    case 'LOGOUT': return 'default';
                    default: return 'primary';
                  }
                };

                return (
                  <StatusChip
                    status={getActionLabel(value)}
                    color={getActionColor(value)}
                  />
                );
              },
            },
            {
              id: 'nome_usuario',
              label: 'Usuário',
              minWidth: 200,
              render: (value, row) => (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <PersonIcon sx={{ mr: 1, color: 'text.secondary', fontSize: 16 }} />
                  <Box>
                    <Typography variant="body2" fontWeight="medium">
                      {value || 'Sistema'}
                    </Typography>
                    {row.email_usuario && (
                      <Typography variant="caption" color="text.secondary">
                        {row.email_usuario}
                      </Typography>
                    )}
                  </Box>
                </Box>
              ),
            },
            {
              id: 'cliente_nome',
              label: 'Cliente',
              minWidth: 150,
              render: (value) => value ? (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <BusinessIcon sx={{ mr: 1, color: 'text.secondary', fontSize: 16 }} />
                  <Typography variant="body2">
                    {value}
                  </Typography>
                </Box>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  -
                </Typography>
              ),
            },
            {
              id: 'descricao',
              label: 'Descrição',
              minWidth: 300,
              render: (value) => (
                <Typography
                  variant="body2"
                  sx={{
                    maxWidth: 300,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  }}
                  title={value}
                >
                  {value}
                </Typography>
              ),
            },
            {
              id: 'actions',
              label: 'Ações',
              minWidth: 100,
              align: 'center',
              render: (_, row) => (
                <StandardButton
                  variant="outlined"
                  size="small"
                  startIcon={<InfoIcon />}
                  onClick={() => setSelectedLog(row)}
                >
                  Detalhes
                </StandardButton>
              ),
            },
          ]}
          data={logs}
          loading={loading}
          emptyMessage={
            <Box sx={{ textAlign: 'center', py: 8 }}>
              <HistoryIcon sx={{ fontSize: '3rem', mb: 2, color: 'text.secondary' }} />
              <Typography variant="h5" sx={{ mb: 1, color: 'text.primary' }}>
                Nenhum log encontrado
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Ajuste os filtros para encontrar os logs desejados.
              </Typography>
            </Box>
          }
        />

        {/* Paginação */}
        {pagination.totalPages > 1 && (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
            <Pagination
              count={pagination.totalPages}
              page={pagination.page}
              onChange={handlePageChange}
              color="primary"
              size="large"
            />
          </Box>
        )}
      </ContentSection>


      {/* Modal de Detalhes */}
      <Modal
        isOpen={showDetailModal}
        onClose={() => setShowDetailModal(false)}
        title={`Detalhes do Log - ${selectedLog?.acao ? getActionLabel(selectedLog.acao) : ''}`}
        maxWidth="800px"
      >
        {selectedLog && (
          <Box sx={{ maxHeight: '70vh', overflow: 'auto' }}>
            <Grid container spacing={2} sx={{ mb: 3 }}>
              <Grid item xs={12} sm={6}>
                <Paper sx={{ p: 2, backgroundColor: 'grey.50' }}>
                  <Typography variant="caption" color="text.secondary">
                    Data e Hora
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                    {formatDate(selectedLog.created_at)}
                  </Typography>
                </Paper>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Paper sx={{ p: 2, backgroundColor: 'grey.50' }}>
                  <Typography variant="caption" color="text.secondary">
                    Ação
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                    {getActionLabel(selectedLog.acao)}
                  </Typography>
                </Paper>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Paper sx={{ p: 2, backgroundColor: 'grey.50' }}>
                  <Typography variant="caption" color="text.secondary">
                    Usuário
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                    {selectedLog.nome_usuario || 'Usuário não identificado'}
                  </Typography>
                </Paper>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Paper sx={{ p: 2, backgroundColor: 'grey.50' }}>
                  <Typography variant="caption" color="text.secondary">
                    Tabela Afetada
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                    {selectedLog.tabela_afetada || 'N/A'}
                  </Typography>
                </Paper>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Paper sx={{ p: 2, backgroundColor: 'grey.50' }}>
                  <Typography variant="caption" color="text.secondary">
                    ID do Registro
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                    {selectedLog.registro_id || 'N/A'}
                  </Typography>
                </Paper>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Paper sx={{ p: 2, backgroundColor: 'grey.50' }}>
                  <Typography variant="caption" color="text.secondary">
                    IP do Usuário
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                    {selectedLog.ip_address || 'N/A'}
                  </Typography>
                </Paper>
              </Grid>

            </Grid>

            <Box sx={{ mb: 3 }}>
              <Typography variant="h6" sx={{ mb: 1, fontWeight: 600 }}>
                Descrição da Atividade
              </Typography>
              <Paper sx={{ p: 2, backgroundColor: 'primary.50', border: '1px solid', borderColor: 'primary.200' }}>
                <Typography variant="body2" sx={{ whiteSpace: 'pre-line' }}>
                  {selectedLog.descricao}
                </Typography>
              </Paper>
            </Box>

            {selectedLog.dados_anteriores && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" sx={{ mb: 1, fontWeight: 600 }}>
                  Dados Anteriores
                </Typography>
                <Paper sx={{ p: 2, backgroundColor: 'grey.100', maxHeight: 300, overflow: 'auto' }}>
                  <Typography variant="body2" component="pre" sx={{ fontSize: '0.75rem', fontFamily: 'monospace' }}>
                    {formatJsonData(selectedLog.dados_anteriores)}
                  </Typography>
                </Paper>
              </Box>
            )}

            {selectedLog.dados_novos && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" sx={{ mb: 1, fontWeight: 600 }}>
                  Dados Novos
                </Typography>
                <Paper sx={{ p: 2, backgroundColor: 'grey.100', maxHeight: 300, overflow: 'auto' }}>
                  <Typography variant="body2" component="pre" sx={{ fontSize: '0.75rem', fontFamily: 'monospace' }}>
                    {formatJsonData(selectedLog.dados_novos)}
                  </Typography>
                </Paper>
              </Box>
            )}
          </Box>
        )}
      </Modal>
    </StandardContainer>
  );
};

export default LogsPage;
