{"name": "portal-evolution-server", "version": "1.0.0", "description": "Backend para o Portal Evolution - Sistema de gerenciamento de ETENs e Ossuários", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "dependencies": {"axios": "^1.6.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "nodemailer": "^6.9.7", "pg": "^8.11.3"}, "devDependencies": {"nodemon": "^3.1.10"}, "keywords": ["portal", "evolution", "etens", "ossuarios"], "author": "Portal Evolution Team", "license": "MIT"}