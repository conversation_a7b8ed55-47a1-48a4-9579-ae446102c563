#!/bin/bash

# 🚀 SCRIPT DE DEPLOY COMPLETO - PORTAL EVOLUTION
# Corrige erro 405 e faz deploy completo do frontend e backend

set -e  # Parar em caso de erro

echo "🚀 INICIANDO DEPLOY COMPLETO DO PORTAL EVOLUTION"
echo "================================================"

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log colorido
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Verificar se estamos no diretório correto
if [ ! -f "portalcliente/docker-compose.prod.yml" ]; then
    log_error "Arquivo docker-compose.prod.yml não encontrado!"
    log_error "Execute este script no diretório /root/portalevo"
    exit 1
fi

# PASSO 1: Parar serviços atuais
log_info "Parando serviços atuais do portal..."
docker stack rm portalevo || true
docker service rm portalevo_portalevo-frontend || true
log_warning "Aguardando 15 segundos para limpeza completa..."
sleep 15
log_success "Serviços parados"

# PASSO 2: Limpar imagens antigas
log_info "Limpando imagens antigas..."
docker image prune -f
docker system prune -f
log_success "Limpeza concluída"

# PASSO 3: Construir imagem do backend
log_info "Construindo imagem do backend..."
cd portalcliente
docker build -f docker/Dockerfile.backend -t portal-evolution-backend:latest . --no-cache
log_success "Backend construído"

# PASSO 4: Construir imagem do frontend
log_info "Construindo imagem do frontend..."
docker build -f Dockerfile -t portal-evolution-frontend:latest . --no-cache
log_success "Frontend construído"

# PASSO 5: Deploy do stack completo
log_info "Fazendo deploy do stack completo..."
docker stack deploy -c docker-compose.prod.yml portal-evolution
log_success "Stack deployado"

# PASSO 6: Aguardar inicialização
log_warning "Aguardando 60 segundos para inicialização dos serviços..."
sleep 60

# PASSO 7: Verificar status dos serviços
log_info "Verificando status dos serviços..."
docker stack services portal-evolution

# PASSO 8: Testes básicos
log_info "Executando testes básicos..."

# Testar Health do Backend
log_info "Testando API Health..."
for i in {1..5}; do
    if curl -s -f https://portal.evo-eden.site/api/health > /dev/null; then
        log_success "API Health OK"
        break
    else
        log_warning "Tentativa $i/5: API Health não respondeu, aguardando..."
        sleep 10
    fi
done

# Testar Login
log_info "Testando login..."
if curl -s -f -X POST https://portal.evo-eden.site/api/auth/login \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"54321"}' > /dev/null; then
    log_success "Login OK"
else
    log_warning "Login falhou - verificar logs"
fi

# PASSO 9: Resumo final
echo ""
echo "🎉 DEPLOY COMPLETO CONCLUÍDO!"
echo "============================="
log_success "Portal Principal: https://portal.evo-eden.site"
log_success "API Health: https://portal.evo-eden.site/api/health"
log_success "API Login: https://portal.evo-eden.site/api/auth/login"
echo ""
log_info "Credenciais de teste:"
echo "  Admin: <EMAIL> / adminnbr5410!"
echo "  Cliente: <EMAIL> / 54321"
echo ""

# Verificação final dos serviços
echo ""
log_info "Status final dos serviços:"
docker stack services portal-evolution

echo ""
log_info "Logs dos serviços:"
echo "Backend: docker service logs portal-evolution_portal_backend"
echo "Frontend: docker service logs portal-evolution_portal_frontend"

echo ""
log_success "Deploy completo concluído! 🚀"
echo "O erro 405 deve estar resolvido com o backend funcionando."
