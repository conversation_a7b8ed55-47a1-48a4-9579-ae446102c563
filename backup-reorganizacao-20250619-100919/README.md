# Portal Evolution

Sistema de gestão cemiterial integrado com Traefik e Docker Swarm.

## 🚀 Características

- **Frontend**: React 19 + Material-UI + Vite
- **Backend**: Node.js + Express + PostgreSQL
- **Infraestrutura**: Docker Swarm + Traefik + Let's Encrypt
- **Domínio**: portal.evo-eden.site
- **SSL**: Automático via Let's Encrypt

## 📋 Pré-requisitos

- Docker Swarm ativo
- Traefik configurado com Let's Encrypt
- Rede `redeinterna` criada
- Domínio `portal.evo-eden.site` apontando para o servidor

## ⚙️ Configuração

1. **Configure o arquivo de ambiente:**
```bash
cp configuracao.env.example configuracao.env
nano configuracao.env
```

2. **Principais configurações:**
```env
DOMAIN=portal.evo-eden.site
SSL_EMAIL=<EMAIL>
DB_NAME=portal_evolution
DB_USER=portal_user
DB_PASSWORD=sua_senha_segura
JWT_SECRET=sua_chave_jwt_segura
```

## 🚀 Deploy

### Deploy Completo
```bash
bash deploy-traefik.sh
```

### Monitoramento
```bash
bash monitor.sh
```

### Remover Stack
```bash
bash remove-stack.sh
```

## 📊 Monitoramento

### Verificar Status
```bash
docker stack services portal-evolution
docker stack ps portal-evolution
```

### Logs
```bash
# Backend
docker service logs -f portal-evolution_portal_backend

# Frontend
docker service logs -f portal-evolution_portal_frontend

# Database
docker service logs -f portal-evolution_portal_database
```

### Escalar Serviços
```bash
docker service scale portal-evolution_portal_backend=2
docker service scale portal-evolution_portal_frontend=2
```

## 🌐 Acesso

- **Portal**: https://portal.evo-eden.site
- **API**: https://portal.evo-eden.site/api/health
- **Health Check**: https://portal.evo-eden.site/health

## 👥 Credenciais Padrão

### Administrador
- **Email**: <EMAIL>
- **Senha**: adminnbr5410!

### Cliente Teste
- **Email**: <EMAIL>
- **Senha**: 54321

## 🔧 Desenvolvimento

### Executar Localmente
```bash
# Backend
cd server
npm install
npm start

# Frontend
npm install
npm run dev
```

### Build Manual
```bash
# Backend
docker build -f Dockerfile.backend -t portal-evolution-backend .

# Frontend
docker build -f Dockerfile.frontend -t portal-evolution-frontend .
```

## 📁 Estrutura do Projeto

```
portalcliente/
├── src/                    # Frontend React
├── server/                 # Backend Node.js
├── public/                 # Arquivos estáticos
├── docs/                   # Documentação
├── scripts/                # Scripts auxiliares
├── backup/                 # Configurações antigas
├── docker-compose.prod.yml # Configuração Docker Swarm
├── Dockerfile.backend      # Build do backend
├── Dockerfile.frontend     # Build do frontend
├── nginx-traefik.conf      # Configuração Nginx para Traefik
├── configuracao.env        # Variáveis de ambiente
├── deploy-traefik.sh       # Script de deploy
├── monitor.sh              # Script de monitoramento
└── remove-stack.sh         # Script para remover stack
```

## 🔒 Segurança

- SSL/TLS automático via Let's Encrypt
- Headers de segurança configurados
- Rate limiting implementado
- Autenticação JWT
- Validação de entrada
- Logs de auditoria

## 🐛 Troubleshooting

### Stack não inicia
```bash
# Verificar logs
docker service logs portal-evolution_portal_backend
docker service logs portal-evolution_portal_frontend

# Verificar rede
docker network ls | grep redeinterna

# Verificar Traefik
docker service ls | grep traefik
```

### SSL não funciona
```bash
# Verificar certificados
docker exec $(docker ps -q -f name=traefik) ls -la /etc/traefik/letsencrypt/

# Verificar logs do Traefik
docker service logs traefik_traefik
```

### Banco não conecta
```bash
# Verificar se PostgreSQL está rodando
docker service ls | grep postgres

# Testar conexão
docker exec -it $(docker ps -q -f name=portal_database) psql -U portal_user -d portal_evolution
```

## 📞 Suporte

Para suporte técnico, entre em contato:
- **Email**: <EMAIL>
- **Logs**: Use `bash monitor.sh` para diagnóstico
