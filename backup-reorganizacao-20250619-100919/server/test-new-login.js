const axios = require('axios');

async function testNewLogin() {
  try {
    console.log('🧪 TESTANDO NOVO LOGIN DO USUÁRIO ADMIN\n');
    
    // Testar login com novas credenciais
    console.log('1. 🔐 Testando login com novas credenciais...');
    console.log('   Email: ma<PERSON><PERSON><PERSON><PERSON><PERSON>@evolutionbr.tech');
    console.log('   Senha: adminnbr5410!');
    
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: 'mauricio<PERSON>l<PERSON>@evolutionbr.tech',
      senha: 'adminnbr5410!'
    });
    
    console.log('   ✅ Login realizado com sucesso!');
    console.log('   📋 Resposta completa:', loginResponse.data);

    if (loginResponse.data.user) {
      console.log('   📋 Dados do usuário:', {
        id: loginResponse.data.user.id,
        nome: loginResponse.data.user.nome,
        email: loginResponse.data.user.email,
        tipo_usuario: loginResponse.data.user.tipo_usuario,
        codigo_cliente: loginResponse.data.user.codigo_cliente
      });
    }
    
    const token = loginResponse.data.token;
    console.log('   🔑 Token JWT gerado com sucesso');
    
    // Testar verificação de token
    console.log('\n2. 🔍 Testando verificação de token...');
    const verifyResponse = await axios.get('http://localhost:3001/api/auth/verify', {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log('   ✅ Token verificado com sucesso!');
    console.log('   📋 Dados verificados:', {
      id: verifyResponse.data.id,
      nome: verifyResponse.data.nome,
      email: verifyResponse.data.email,
      tipo_usuario: verifyResponse.data.tipo_usuario
    });
    
    // Testar acesso a rota protegida
    console.log('\n3. 🛡️ Testando acesso a rota protegida...');
    const dashboardResponse = await axios.get('http://localhost:3001/api/dashboard/stats', {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log('   ✅ Acesso autorizado ao dashboard!');
    console.log('   📊 Estatísticas carregadas:', {
      total_sepultamentos: dashboardResponse.data.total_sepultamentos,
      gavetas_ocupadas: dashboardResponse.data.gavetas_ocupadas,
      gavetas_disponiveis: dashboardResponse.data.gavetas_disponiveis,
      total_gavetas: dashboardResponse.data.total_gavetas
    });
    
    console.log('\n🎉 TESTE DE LOGIN CONCLUÍDO COM SUCESSO!');
    console.log('\n📋 RESUMO:');
    console.log('   ✅ Login funcionando com email válido');
    console.log('   ✅ Token JWT gerado corretamente');
    console.log('   ✅ Verificação de token funcionando');
    console.log('   ✅ Acesso a rotas protegidas autorizado');
    console.log('   ✅ Permissões de admin mantidas');
    
    console.log('\n🚀 SISTEMA PRONTO PARA USO COM NOVAS CREDENCIAIS!');
    console.log('\n📧 CREDENCIAIS PARA LOGIN:');
    console.log('   Email: <EMAIL>');
    console.log('   Senha: adminnbr5410!');
    
  } catch (error) {
    console.error('❌ Erro no teste de login:', error.response?.data || error.message);
  }
}

testNewLogin();
