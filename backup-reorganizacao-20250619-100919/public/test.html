<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste - Portal Evolution</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #1e3a8a 0%, #059669 100%);
            color: white;
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .success {
            color: #10b981;
            font-weight: bold;
        }
        button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid white;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        button:hover {
            background: rgba(255, 255, 255, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 Portal Evolution System - Teste</h1>
        
        <div class="status">
            <h3>Status dos Serviços:</h3>
            <p class="success">✅ Servidor Vite: Funcionando</p>
            <p class="success">✅ Porta 5173: Aberta</p>
            <p class="success">✅ Arquivo HTML: Carregado</p>
        </div>
        
        <div class="status">
            <h3>Próximos Passos:</h3>
            <p>1. ✅ Servidor funcionando</p>
            <p>2. 🔄 Testando React...</p>
            <p>3. ⏳ Carregando aplicação completa...</p>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <button onclick="window.location.href='/'">🏠 Ir para Aplicação Principal</button>
            <button onclick="window.location.reload()">🔄 Recarregar</button>
        </div>
        
        <div style="text-align: center; margin-top: 20px; font-size: 14px; opacity: 0.8;">
            <p>Se você está vendo esta página, o servidor está funcionando corretamente!</p>
            <p>Timestamp: <span id="timestamp"></span></p>
        </div>
    </div>
    
    <script>
        document.getElementById('timestamp').textContent = new Date().toLocaleString('pt-BR');
        
        // Testar se a aplicação React está funcionando
        setTimeout(() => {
            fetch('/')
                .then(response => {
                    if (response.ok) {
                        console.log('✅ Aplicação React respondendo');
                    } else {
                        console.log('⚠️ Problema com aplicação React');
                    }
                })
                .catch(error => {
                    console.log('❌ Erro ao conectar com aplicação React:', error);
                });
        }, 1000);
    </script>
</body>
</html>
