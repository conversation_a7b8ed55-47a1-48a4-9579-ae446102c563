#!/bin/bash

# ===================================
# SCRIPT PARA INICIAR PORTAL EVOLUTION
# ===================================
# Execute: bash scripts/iniciar-servico.sh
# Para verificar se está rodando: docker-compose -f portal-evolution.yaml ps

set -e

echo "🚀 Iniciando Portal Evolution..."

# Cores para output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
    exit 1
}

# Verificar se arquivo .env existe
# Para criar: cp .env.example .env && nano .env
if [[ ! -f ".env" ]]; then
    error "Arquivo .env não encontrado! Crie baseado no .env.example"
fi

# Verificar se rede traefik existe
# Para criar: docker network create traefik
log "Verificando rede traefik..."
if ! docker network ls | grep -q traefik; then
    warn "Rede traefik não encontrada. Criando..."
    docker network create traefik
fi

# Verificar se há containers antigos rodando
# Para parar: docker-compose -f portal-evolution.yaml down
log "Verificando containers existentes..."
if docker ps | grep -q "portal-"; then
    warn "Containers do portal já estão rodando. Parando primeiro..."
    docker-compose -f portal-evolution.yaml down
fi

# Iniciar serviços
log "Iniciando serviços..."
docker-compose -f portal-evolution.yaml up -d

# Aguardar containers iniciarem
log "Aguardando containers iniciarem..."
sleep 30

# Verificar status dos containers
log "Verificando status dos containers..."
docker-compose -f portal-evolution.yaml ps

# Verificar saúde dos serviços
log "Verificando saúde dos serviços..."

# Verificar banco de dados
# Para testar manualmente: docker exec portal-database pg_isready -U portal_user
if docker exec portal-database pg_isready -U portal_user > /dev/null 2>&1; then
    log "✅ Banco de dados: OK"
else
    warn "❌ Banco de dados: ERRO"
fi

# Verificar backend
# Para testar manualmente: curl http://localhost:3001/api/health
sleep 10
if curl -f -s http://localhost:3001/api/health > /dev/null; then
    log "✅ Backend: OK"
else
    warn "❌ Backend: ERRO"
fi

# Verificar frontend
# Para testar manualmente: curl http://localhost/health
if curl -f -s http://localhost/health > /dev/null; then
    log "✅ Frontend: OK"
else
    warn "❌ Frontend: ERRO"
fi

# Mostrar informações de acesso
log "🎉 Portal Evolution iniciado!"
log ""
log "📋 Informações de acesso:"

# Obter IP da VPS
# Para verificar IP: curl ifconfig.me
VPS_IP=$(curl -s ifconfig.me 2>/dev/null || echo "SEU_IP")
log "   🌐 IP: http://$VPS_IP"

# Verificar se domínio está configurado
if grep -q "DOMAIN=" .env && ! grep -q "DOMAIN=$" .env; then
    DOMAIN=$(grep "DOMAIN=" .env | cut -d'=' -f2)
    log "   🌐 Domínio: http://$DOMAIN"
fi

log ""
log "👥 Credenciais padrão:"
log "   Admin: <EMAIL> / adminnbr5410!"
log "   Cliente: <EMAIL> / 54321"

log ""
log "🔧 Comandos úteis:"
log "   Status: docker-compose -f portal-evolution.yaml ps"
log "   Logs: docker-compose -f portal-evolution.yaml logs -f"
log "   Parar: docker-compose -f portal-evolution.yaml down"
log "   Reiniciar: docker-compose -f portal-evolution.yaml restart"

log ""
log "📊 Para monitorar:"
log "   bash scripts/monitorar-servico.sh"
