#!/bin/bash

# Script para corrigir todos os conflitos de Button em arquivos styled-components

echo "🔧 Corrigindo conflitos de Button em todos os arquivos..."

# Lista de arquivos que podem ter conflitos
files_to_check=(
    "portalcliente/src/components/NumeracaoGavetasModal.jsx"
    "portalcliente/src/components/UsuarioModal.jsx"
    "portalcliente/src/components/ClienteModal.jsx"
    "portalcliente/src/components/BlocoModal.jsx"
    "portalcliente/src/components/TransferenciaGavetasModal.jsx"
    "portalcliente/src/components/SubBlocoModal.jsx"
)

for file in "${files_to_check[@]}"; do
    if [ -f "$file" ]; then
        echo "Verificando $file..."
        
        # Verifica se tem styled Button e import Button
        if grep -q "const Button = styled" "$file" && grep -q "import.*Button" "$file"; then
            echo "  ❌ Conflito encontrado em $file - corrigindo..."
            
            # <PERSON><PERSON><PERSON> styled Button para StyledButton
            sed -i 's/const Button = styled/const StyledButton = styled/g' "$file"
            
            # Substitui todas as ocorrências de <Button por <StyledButton
            sed -i 's/<Button/<StyledButton/g' "$file"
            
            # Substitui todas as ocorrências de </Button> por </StyledButton>
            sed -i 's/<\/Button>/<\/StyledButton>/g' "$file"
            
            echo "  ✅ Conflito corrigido em $file"
        else
            echo "  ✅ Nenhum conflito encontrado em $file"
        fi
    else
        echo "  ⚠️  Arquivo $file não encontrado"
    fi
done

echo ""
echo "🎯 Verificação final de conflitos..."

# Verifica se ainda há conflitos
conflict_files=$(find portalcliente/src -name "*.jsx" -o -name "*.js" | xargs grep -l "const Button = styled" | xargs -I {} sh -c 'if grep -q "import.*Button" "{}"; then echo "{}"; fi')

if [ -z "$conflict_files" ]; then
    echo "✅ Todos os conflitos de Button foram resolvidos!"
else
    echo "❌ Ainda há conflitos nos arquivos:"
    echo "$conflict_files"
fi

echo ""
echo "🚀 Correção de conflitos Button concluída!"
