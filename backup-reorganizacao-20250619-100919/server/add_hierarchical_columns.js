const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
require('dotenv').config();

// Configuração do banco de dados
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'portal_evolution',
  password: process.env.DB_PASSWORD || 'postgres',
  port: process.env.DB_PORT || 5432,
});

async function addHierarchicalColumns() {
  const client = await pool.connect();
  
  try {
    console.log('🔄 Adicionando colunas hierárquicas às tabelas existentes...');
    console.log('=' .repeat(70));
    
    // Ler o arquivo SQL
    const sqlPath = path.join(__dirname, 'add_hierarchical_columns.sql');
    const sqlScript = fs.readFileSync(sqlPath, 'utf8');
    
    console.log('📄 Executando script SQL...');
    
    // Executar o script SQL
    await client.query(sqlScript);
    
    console.log('✅ Script executado com sucesso!');
    
    // Verificar as colunas adicionadas
    console.log('\n🔍 Verificando colunas adicionadas...');
    
    const columnsCheck = await client.query(`
      SELECT 
        table_name,
        column_name,
        data_type,
        is_nullable
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name IN ('blocos', 'sub_blocos', 'gavetas', 'numeracoes_gavetas', 'sepultamentos', 'logs_auditoria')
      AND column_name IN ('codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco', 'denominacao', 'descricao')
      ORDER BY table_name, 
               CASE column_name 
                 WHEN 'codigo_cliente' THEN 1
                 WHEN 'codigo_estacao' THEN 2
                 WHEN 'codigo_bloco' THEN 3
                 WHEN 'codigo_sub_bloco' THEN 4
                 WHEN 'denominacao' THEN 5
                 WHEN 'descricao' THEN 6
                 ELSE 7
               END
    `);
    
    // Agrupar por tabela
    const tableColumns = {};
    columnsCheck.rows.forEach(row => {
      if (!tableColumns[row.table_name]) {
        tableColumns[row.table_name] = [];
      }
      tableColumns[row.table_name].push(row);
    });
    
    console.log('\n📋 Colunas hierárquicas por tabela:');
    Object.keys(tableColumns).forEach(tableName => {
      console.log(`\n   🗂️  ${tableName.toUpperCase()}:`);
      tableColumns[tableName].forEach(column => {
        const nullInfo = column.is_nullable === 'NO' ? ' (NOT NULL)' : ' (NULL)';
        console.log(`      ✅ ${column.column_name} (${column.data_type})${nullInfo}`);
      });
    });
    
    // Verificar dados populados
    console.log('\n📊 Verificando dados populados...');
    
    const dataCheck = await client.query(`
      SELECT 'blocos' as tabela, 
             COUNT(*) as total, 
             COUNT(codigo_cliente) as com_codigo_cliente,
             COUNT(codigo_estacao) as com_codigo_estacao,
             COUNT(codigo_bloco) as com_codigo_bloco
      FROM blocos
      UNION ALL
      SELECT 'sub_blocos' as tabela, 
             COUNT(*) as total, 
             COUNT(codigo_cliente) as com_codigo_cliente,
             COUNT(codigo_estacao) as com_codigo_estacao,
             COUNT(codigo_bloco) as com_codigo_bloco
      FROM sub_blocos
      UNION ALL
      SELECT 'gavetas' as tabela, 
             COUNT(*) as total, 
             COUNT(codigo_cliente) as com_codigo_cliente,
             COUNT(codigo_estacao) as com_codigo_estacao,
             COUNT(codigo_bloco) as com_codigo_bloco
      FROM gavetas
      UNION ALL
      SELECT 'numeracoes_gavetas' as tabela, 
             COUNT(*) as total, 
             COUNT(codigo_cliente) as com_codigo_cliente,
             COUNT(codigo_estacao) as com_codigo_estacao,
             COUNT(codigo_bloco) as com_codigo_bloco
      FROM numeracoes_gavetas
      UNION ALL
      SELECT 'sepultamentos' as tabela, 
             COUNT(*) as total, 
             COUNT(codigo_cliente) as com_codigo_cliente,
             COUNT(codigo_estacao) as com_codigo_estacao,
             COUNT(codigo_bloco) as com_codigo_bloco
      FROM sepultamentos
      UNION ALL
      SELECT 'logs_auditoria' as tabela, 
             COUNT(*) as total, 
             COUNT(codigo_cliente) as com_codigo_cliente,
             COUNT(codigo_estacao) as com_codigo_estacao,
             COUNT(codigo_bloco) as com_codigo_bloco
      FROM logs_auditoria
    `);
    
    console.log('\n📈 Estatísticas de dados:');
    dataCheck.rows.forEach(row => {
      console.log(`   📋 ${row.tabela}:`);
      console.log(`      Total de registros: ${row.total}`);
      console.log(`      Com codigo_cliente: ${row.com_codigo_cliente}`);
      console.log(`      Com codigo_estacao: ${row.com_codigo_estacao}`);
      console.log(`      Com codigo_bloco: ${row.com_codigo_bloco}`);
    });
    
    // Verificar índices criados
    console.log('\n🔍 Verificando índices criados...');
    
    const indexCheck = await client.query(`
      SELECT 
        schemaname,
        tablename,
        indexname,
        indexdef
      FROM pg_indexes 
      WHERE schemaname = 'public' 
      AND tablename IN ('blocos', 'sub_blocos', 'gavetas', 'numeracoes_gavetas', 'sepultamentos', 'logs_auditoria')
      AND indexname LIKE '%codigo_%'
      ORDER BY tablename, indexname
    `);
    
    console.log(`\n📊 ${indexCheck.rows.length} índices hierárquicos criados:`);
    indexCheck.rows.forEach(row => {
      console.log(`   ✅ ${row.tablename}.${row.indexname}`);
    });
    
    // Verificar foreign keys
    console.log('\n🔗 Verificando foreign keys...');
    
    const fkCheck = await client.query(`
      SELECT 
        tc.table_name,
        tc.constraint_name,
        kcu.column_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name
      FROM information_schema.table_constraints tc
      JOIN information_schema.key_column_usage kcu ON tc.constraint_name = kcu.constraint_name
      JOIN information_schema.constraint_column_usage ccu ON ccu.constraint_name = tc.constraint_name
      WHERE tc.constraint_type = 'FOREIGN KEY'
      AND tc.table_name IN ('blocos', 'sub_blocos', 'gavetas', 'numeracoes_gavetas', 'logs_auditoria')
      AND kcu.column_name = 'codigo_cliente'
      ORDER BY tc.table_name
    `);
    
    console.log(`\n🔗 ${fkCheck.rows.length} foreign keys para codigo_cliente:`);
    fkCheck.rows.forEach(row => {
      console.log(`   ✅ ${row.table_name}.${row.column_name} → ${row.foreign_table_name}.${row.foreign_column_name}`);
    });
    
    console.log('\n🎉 COLUNAS HIERÁRQUICAS ADICIONADAS COM SUCESSO!');
    console.log('=' .repeat(70));
    console.log('✅ Todas as tabelas agora têm as referências hierárquicas apropriadas:');
    console.log('   📋 blocos: codigo_cliente + codigo_estacao + codigo_bloco');
    console.log('   🔲 sub_blocos: codigo_cliente + codigo_estacao + codigo_bloco + codigo_sub_bloco');
    console.log('   📊 gavetas: codigo_cliente + codigo_estacao + codigo_bloco + codigo_sub_bloco');
    console.log('   🔢 numeracoes_gavetas: codigo_cliente + codigo_estacao + codigo_bloco + codigo_sub_bloco');
    console.log('   ⚰️  sepultamentos: codigo_cliente + codigo_estacao + codigo_bloco + codigo_sub_bloco (já existiam)');
    console.log('   📝 logs_auditoria: codigo_cliente + codigo_estacao + codigo_bloco + codigo_sub_bloco');
    console.log('\n🚀 O sistema está pronto para importação de dados usando referências por códigos!');
    console.log('📥 Todas as tabelas podem ser facilmente relacionadas usando os códigos hierárquicos.');
    
  } catch (error) {
    console.error('❌ Erro ao adicionar colunas hierárquicas:', error);
    console.error('💡 Verifique se o banco de dados está acessível e se você tem as permissões necessárias.');
    process.exit(1);
  } finally {
    client.release();
    await pool.end();
  }
}

// Função para verificar se as colunas já foram adicionadas
async function checkIfColumnsExist() {
  const client = await pool.connect();
  
  try {
    const result = await client.query(`
      SELECT COUNT(*) as count
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'blocos'
      AND column_name = 'codigo_cliente'
    `);
    
    return parseInt(result.rows[0].count) > 0;
  } catch (error) {
    console.error('❌ Erro ao verificar colunas:', error);
    return false;
  } finally {
    client.release();
  }
}

// Executar apenas se chamado diretamente
if (require.main === module) {
  (async () => {
    console.log('🚀 Portal Evolution - Adição de Colunas Hierárquicas');
    console.log('=' .repeat(70));
    
    const columnsExist = await checkIfColumnsExist();
    
    if (columnsExist) {
      console.log('✅ As colunas hierárquicas já foram adicionadas.');
      console.log('ℹ️  Nenhuma ação necessária.');
      process.exit(0);
    }
    
    console.log('🔍 Colunas hierárquicas não encontradas. Iniciando adição...');
    await addHierarchicalColumns();
  })().catch(error => {
    console.error('❌ Erro fatal:', error);
    process.exit(1);
  });
}

module.exports = { addHierarchicalColumns, checkIfColumnsExist };
