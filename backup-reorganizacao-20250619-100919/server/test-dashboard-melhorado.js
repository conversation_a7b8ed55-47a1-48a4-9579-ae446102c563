const axios = require('axios');

async function testDashboardMelhorado() {
  try {
    console.log('🎯 TESTE FINAL - DASHBOARD MELHORADO COM CARDS CLICÁVEIS\n');
    
    // Fazer login
    console.log('1. 🔐 Fazendo login...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: 'admin',
      senha: 'adminnbr5410!'
    });
    
    const token = loginResponse.data.token;
    console.log('   ✅ Login realizado com sucesso');
    
    // Configurar headers
    const headers = { Authorization: `Bearer ${token}` };
    
    // Testar APIs principais do dashboard
    console.log('\n2. 📊 Testando APIs principais do dashboard...');
    
    const stats = await axios.get('http://localhost:3001/api/dashboard/stats', { headers });
    console.log('   ✅ Stats carregadas:', {
      sepultamentos: stats.data.total_sepultamentos,
      gavetas_ocupadas: stats.data.gavetas_ocupadas,
      gavetas_disponiveis: stats.data.gavetas_disponiveis,
      total_gavetas: stats.data.total_gavetas,
      taxa_sepultamento: stats.data.taxa_sepultamento
    });
    
    const proximas = await axios.get('http://localhost:3001/api/dashboard/proximas-exumacoes', { headers });
    console.log('   ✅ Próximas exumações (limitado a 10):', proximas.data.length, 'registros');
    
    // Testar funcionalidade do card de sepultamentos
    console.log('\n3. 📋 Testando funcionalidade do card de sepultamentos...');
    const sepultamentosDetails = await axios.get('http://localhost:3001/api/dashboard/sepultamentos-details', { headers });
    console.log('   ✅ Detalhes de sepultamentos por produto:', sepultamentosDetails.data.length, 'produtos');
    
    if (sepultamentosDetails.data.length > 0) {
      const produto = sepultamentosDetails.data[0];
      const diasDiferenca = produto.primeiro_sepultamento && produto.ultimo_sepultamento 
        ? Math.ceil((new Date(produto.ultimo_sepultamento) - new Date(produto.primeiro_sepultamento)) / (1000 * 60 * 60 * 24))
        : 0;
      const taxaPorDia = diasDiferenca > 0 ? (produto.total_sepultamentos / diasDiferenca).toFixed(2) : '0.00';
      
      console.log('   📊 Exemplo de produto:', {
        nome: produto.produto,
        codigo: produto.codigo_estacao,
        total_sepultamentos: produto.total_sepultamentos,
        taxa_por_dia: taxaPorDia + ' por dia',
        primeiro_sepultamento: produto.primeiro_sepultamento,
        ultimo_sepultamento: produto.ultimo_sepultamento
      });
    }
    
    // Testar funcionalidade do card de gavetas
    console.log('\n4. 🗂️ Testando funcionalidade do card de gavetas...');
    const gavetasDetails = await axios.get('http://localhost:3001/api/dashboard/gavetas-por-produto-details', { headers });
    console.log('   ✅ Detalhes de gavetas por produto:', gavetasDetails.data.length, 'produtos');
    
    if (gavetasDetails.data.length > 0) {
      const produto = gavetasDetails.data[0];
      console.log('   🗂️ Exemplo de produto:', {
        nome: produto.produto,
        codigo: produto.codigo_estacao,
        total_gavetas: produto.total_gavetas,
        gavetas_ocupadas: produto.gavetas_ocupadas,
        gavetas_disponiveis: produto.gavetas_disponiveis,
        taxa_ocupacao: produto.taxa_ocupacao + '%'
      });
    }
    
    // Testar funcionalidade "Ver Todos" das exumações
    console.log('\n5. 🔍 Testando funcionalidade "Ver Todos" das exumações...');
    const todasExumacoes = await axios.get('http://localhost:3001/api/dashboard/proximas-exumacoes', { headers });
    console.log('   ✅ Todas as exumações:', todasExumacoes.data.length, 'registros');
    
    if (todasExumacoes.data.length > 0) {
      const exumacao = todasExumacoes.data[0];
      console.log('   📋 Exemplo de exumação:', {
        nome: exumacao.nome_sepultado,
        produto: exumacao.denominacao_produto,
        data_sepultamento: exumacao.data_sepultamento,
        data_exumacao: exumacao.data_exumacao,
        dias_restantes: exumacao.dias_restantes
      });
    }
    
    console.log('\n🎉 TESTE FINAL CONCLUÍDO COM SUCESSO!');
    console.log('\n📋 RESUMO DAS MELHORIAS IMPLEMENTADAS:');
    console.log('   ✅ 1. Cards reorganizados em coluna à esquerda');
    console.log('   ✅ 2. Próximas exumações à direita dos cards');
    console.log('   ✅ 3. Card de sepultamentos clicável com detalhes por produto');
    console.log('   ✅ 4. Card de gavetas clicável com detalhes por produto');
    console.log('   ✅ 5. Taxa de sepultamento por dia calculada para cada produto');
    console.log('   ✅ 6. Informações de gavetas por produto (total, ocupadas, disponíveis)');
    console.log('   ✅ 7. Botão "Ver Todos" para lista completa de exumações');
    console.log('   ✅ 8. Layout responsivo mantido');
    console.log('   ✅ 9. Modais específicos para cada tipo de informação');
    console.log('   ✅ 10. Nova API para gavetas por produto implementada');
    
    console.log('\n🚀 DASHBOARD 100% FUNCIONAL E MELHORADO!');
    
  } catch (error) {
    console.error('❌ Erro no teste final:', error.response?.data || error.message);
  }
}

testDashboardMelhorado();
