#!/bin/bash

# ===================================
# TESTE DE INTEGRAÇÃO - DELETAR USUÁRIO
# ===================================
# Testa todo o fluxo de gerenciamento de usuários

set -e

echo "🧪 ====================================="
echo "🧪 TESTE DE INTEGRAÇÃO - DELETAR USUÁRIO"
echo "🧪 ====================================="

# Função para log com timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Função para obter token de admin
get_admin_token() {
    curl -s -X POST https://portal.evo-eden.site/api/auth/login \
        -H "Content-Type: application/json" \
        -d '{"email":"admin","senha":"123456"}' | \
        grep -o '"token":"[^"]*"' | cut -d'"' -f4
}

# 1. OBTER TOKEN DE ADMIN
log "🔑 Obtendo token de admin..."
TOKEN=$(get_admin_token)

if [ -z "$TOKEN" ]; then
    log "❌ Falha ao obter token de admin"
    exit 1
fi

log "✅ Token obtido com sucesso"

# 2. CRIAR USUÁRIO DE TESTE
log "👤 Criando usuário de teste..."
TIMESTAMP=$(date +%s)
TEST_EMAIL="teste.delete.${TIMESTAMP}@exemplo.com"

CREATE_RESPONSE=$(curl -s -X POST https://portal.evo-eden.site/api/usuarios \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d "{
        \"email\": \"$TEST_EMAIL\",
        \"senha\": \"123456\",
        \"nome\": \"Usuário Teste Delete $TIMESTAMP\",
        \"tipo_usuario\": \"cliente\",
        \"ativo\": true
    }")

# Extrair ID do usuário criado
USER_ID=$(echo "$CREATE_RESPONSE" | grep -o '"id":[0-9]*' | cut -d':' -f2)

if [ -z "$USER_ID" ]; then
    log "❌ Falha ao criar usuário de teste"
    echo "Resposta: $CREATE_RESPONSE"
    exit 1
fi

log "✅ Usuário criado com ID: $USER_ID"
log "📧 Email: $TEST_EMAIL"

# 3. VERIFICAR SE O USUÁRIO FOI CRIADO
log "🔍 Verificando se usuário foi criado..."
GET_RESPONSE=$(curl -s -X GET https://portal.evo-eden.site/api/usuarios/$USER_ID \
    -H "Authorization: Bearer $TOKEN")

if echo "$GET_RESPONSE" | grep -q "$TEST_EMAIL"; then
    log "✅ Usuário encontrado no sistema"
else
    log "❌ Usuário não encontrado"
    echo "Resposta: $GET_RESPONSE"
    exit 1
fi

# 4. CRIAR LOGS DE AUDITORIA PARA O USUÁRIO
log "📝 Criando logs de auditoria para o usuário..."
docker exec -i $(docker ps -q --filter "name=postgres") psql -U postgres -d dbetens -c "
INSERT INTO logs_auditoria (usuario_id, acao, tabela_afetada, descricao) 
VALUES 
($USER_ID, 'CREATE', 'usuarios', 'Log de teste 1 para usuário $USER_ID'),
($USER_ID, 'UPDATE', 'usuarios', 'Log de teste 2 para usuário $USER_ID'),
($USER_ID, 'LOGIN', 'auth', 'Log de teste 3 para usuário $USER_ID');

SELECT COUNT(*) as logs_criados FROM logs_auditoria WHERE usuario_id = $USER_ID;
" > /dev/null

log "✅ Logs de auditoria criados"

# 5. TENTAR DELETAR O USUÁRIO
log "🗑️ Tentando deletar usuário..."
DELETE_RESPONSE=$(curl -s -X DELETE https://portal.evo-eden.site/api/usuarios/$USER_ID \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json")

log "📋 Resposta da exclusão:"
echo "$DELETE_RESPONSE" | jq . 2>/dev/null || echo "$DELETE_RESPONSE"

# 6. VERIFICAR SE O USUÁRIO FOI DELETADO
log "🔍 Verificando se usuário foi deletado..."
GET_AFTER_DELETE=$(curl -s -X GET https://portal.evo-eden.site/api/usuarios/$USER_ID \
    -H "Authorization: Bearer $TOKEN")

if echo "$GET_AFTER_DELETE" | grep -q "não encontrado\|not found\|404"; then
    log "✅ Usuário foi deletado com sucesso"
else
    log "❌ Usuário ainda existe no sistema"
    echo "Resposta: $GET_AFTER_DELETE"
    exit 1
fi

# 7. VERIFICAR SE OS LOGS FORAM PRESERVADOS
log "📊 Verificando se logs foram preservados..."
LOGS_NULL=$(docker exec -i $(docker ps -q --filter "name=postgres") psql -U postgres -d dbetens -c "
SELECT COUNT(*) FROM logs_auditoria WHERE usuario_id IS NULL AND descricao LIKE '%usuário $USER_ID%';
" | grep -o '[0-9]*' | tail -1)

if [ "$LOGS_NULL" -gt 0 ]; then
    log "✅ $LOGS_NULL logs foram preservados com usuario_id = NULL"
else
    log "⚠️ Nenhum log foi encontrado com usuario_id = NULL"
fi

# 8. VERIFICAR LOGS DO BACKEND
log "📋 Verificando logs do backend..."
BACKEND_LOGS=$(docker service logs portal-evolution_portal_backend --tail 10 | grep -i "deletar\|delete" | tail -3)

if [ -n "$BACKEND_LOGS" ]; then
    log "✅ Logs do backend encontrados:"
    echo "$BACKEND_LOGS"
else
    log "⚠️ Nenhum log específico de exclusão encontrado"
fi

# 9. RESUMO DO TESTE
log "📊 ====================================="
log "📊 RESUMO DO TESTE"
log "📊 ====================================="

if echo "$DELETE_RESPONSE" | grep -q "sucesso\|success"; then
    log "🎉 TESTE PASSOU!"
    log "✅ Usuário criado: $TEST_EMAIL (ID: $USER_ID)"
    log "✅ Usuário deletado com sucesso"
    log "✅ Logs preservados: $LOGS_NULL"
    log "✅ Sistema funcionando corretamente"
    
    echo ""
    echo "🎯 FUNCIONALIDADE VALIDADA:"
    echo "- ✅ Criação de usuários"
    echo "- ✅ Exclusão de usuários"
    echo "- ✅ Preservação de logs de auditoria"
    echo "- ✅ Tratamento de foreign keys"
    echo "- ✅ API respondendo corretamente"
    
    exit 0
else
    log "❌ TESTE FALHOU!"
    log "❌ Problema na exclusão do usuário"
    echo "Resposta completa: $DELETE_RESPONSE"
    exit 1
fi
