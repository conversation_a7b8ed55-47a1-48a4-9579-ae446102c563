const { Pool } = require('pg');
require('dotenv').config();

// Configuração do banco de dados
const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
});

async function testDatabaseConnection() {
  console.log('🔍 Testando conexão com PostgreSQL...');
  console.log('=' .repeat(60));
  console.log(`Host: ${process.env.DB_HOST}`);
  console.log(`Porta: ${process.env.DB_PORT}`);
  console.log(`Banco: ${process.env.DB_NAME}`);
  console.log(`Usuário: ${process.env.DB_USER}`);
  console.log('=' .repeat(60));

  const client = await pool.connect();
  
  try {
    // Teste de conexão
    console.log('✅ Conexão estabelecida com sucesso!');
    
    // Verificar versão do PostgreSQL
    const versionResult = await client.query('SELECT version()');
    console.log(`📋 Versão PostgreSQL: ${versionResult.rows[0].version.split(' ')[1]}`);
    
    // Listar todas as tabelas
    console.log('\n📊 Tabelas existentes no banco:');
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    
    tablesResult.rows.forEach(row => {
      console.log(`   📋 ${row.table_name}`);
    });
    
    // Verificar estrutura das tabelas principais
    console.log('\n🔍 Verificando estrutura das tabelas principais...');
    
    const tables = ['blocos', 'sub_blocos', 'gavetas', 'numeracoes_gavetas', 'sepultamentos', 'logs_auditoria'];
    
    for (const tableName of tables) {
      console.log(`\n📋 Estrutura da tabela: ${tableName.toUpperCase()}`);
      
      try {
        const columnsResult = await client.query(`
          SELECT 
            column_name,
            data_type,
            is_nullable,
            column_default
          FROM information_schema.columns 
          WHERE table_schema = 'public' 
          AND table_name = $1
          ORDER BY ordinal_position
        `, [tableName]);
        
        if (columnsResult.rows.length === 0) {
          console.log(`   ❌ Tabela ${tableName} não encontrada`);
          continue;
        }
        
        // Verificar se tem colunas hierárquicas
        const hierarchicalColumns = ['codigo_cliente', 'codigo_estacao', 'codigo_bloco', 'codigo_sub_bloco'];
        const existingHierarchical = columnsResult.rows.filter(row => 
          hierarchicalColumns.includes(row.column_name)
        );
        
        console.log(`   📊 Total de colunas: ${columnsResult.rows.length}`);
        console.log(`   🏗️  Colunas hierárquicas: ${existingHierarchical.length}/${hierarchicalColumns.length}`);
        
        // Mostrar colunas hierárquicas existentes
        if (existingHierarchical.length > 0) {
          console.log('   ✅ Colunas hierárquicas encontradas:');
          existingHierarchical.forEach(col => {
            console.log(`      - ${col.column_name} (${col.data_type})`);
          });
        }
        
        // Mostrar colunas hierárquicas faltantes
        const missingColumns = hierarchicalColumns.filter(hCol => 
          !existingHierarchical.some(eCol => eCol.column_name === hCol)
        );
        
        if (missingColumns.length > 0) {
          console.log('   ❌ Colunas hierárquicas faltantes:');
          missingColumns.forEach(col => {
            console.log(`      - ${col}`);
          });
        }
        
        // Contar registros
        const countResult = await client.query(`SELECT COUNT(*) as total FROM ${tableName}`);
        console.log(`   📈 Total de registros: ${countResult.rows[0].total}`);
        
      } catch (error) {
        console.log(`   ❌ Erro ao verificar tabela ${tableName}: ${error.message}`);
      }
    }
    
    // Verificar dados de exemplo
    console.log('\n📊 Verificando dados de exemplo...');
    
    try {
      // Verificar clientes
      const clientesResult = await client.query('SELECT codigo_cliente, nome_fantasia FROM clientes LIMIT 3');
      console.log(`\n👥 Clientes (${clientesResult.rows.length} exemplos):`);
      clientesResult.rows.forEach(row => {
        console.log(`   - ${row.codigo_cliente}: ${row.nome_fantasia}`);
      });
      
      // Verificar produtos
      const produtosResult = await client.query('SELECT codigo_cliente, codigo_estacao, denominacao FROM produtos LIMIT 3');
      console.log(`\n📦 Produtos (${produtosResult.rows.length} exemplos):`);
      produtosResult.rows.forEach(row => {
        console.log(`   - ${row.codigo_cliente}/${row.codigo_estacao}: ${row.denominacao || 'Sem denominação'}`);
      });
      
      // Verificar blocos
      const blocosResult = await client.query('SELECT codigo_bloco, nome, codigo_cliente, codigo_estacao FROM blocos LIMIT 3');
      console.log(`\n🧱 Blocos (${blocosResult.rows.length} exemplos):`);
      blocosResult.rows.forEach(row => {
        console.log(`   - ${row.codigo_bloco}: ${row.nome} (Cliente: ${row.codigo_cliente || 'N/A'}, Estação: ${row.codigo_estacao || 'N/A'})`);
      });
      
    } catch (error) {
      console.log(`❌ Erro ao verificar dados: ${error.message}`);
    }
    
    // Resumo final
    console.log('\n📋 RESUMO DA VERIFICAÇÃO:');
    console.log('=' .repeat(60));
    
    // Verificar quais tabelas precisam de colunas hierárquicas
    const needsUpdate = [];
    
    for (const tableName of ['blocos', 'sub_blocos', 'gavetas', 'numeracoes_gavetas']) {
      try {
        const checkResult = await client.query(`
          SELECT COUNT(*) as count
          FROM information_schema.columns 
          WHERE table_schema = 'public' 
          AND table_name = $1
          AND column_name = 'codigo_cliente'
        `, [tableName]);
        
        if (parseInt(checkResult.rows[0].count) === 0) {
          needsUpdate.push(tableName);
        }
      } catch (error) {
        needsUpdate.push(tableName);
      }
    }
    
    if (needsUpdate.length === 0) {
      console.log('✅ Todas as tabelas já possuem colunas hierárquicas!');
      console.log('🎉 Sistema pronto para importação de dados por códigos.');
    } else {
      console.log('❌ Tabelas que precisam de colunas hierárquicas:');
      needsUpdate.forEach(table => {
        console.log(`   - ${table}`);
      });
      console.log('\n💡 Execute o script de adição de colunas:');
      console.log('   node add_hierarchical_columns.js');
    }
    
  } catch (error) {
    console.error('❌ Erro na conexão ou consulta:', error);
    console.error('💡 Verifique as credenciais e se o banco está acessível.');
  } finally {
    client.release();
    await pool.end();
  }
}

// Executar teste
if (require.main === module) {
  testDatabaseConnection().catch(error => {
    console.error('❌ Erro fatal:', error);
    process.exit(1);
  });
}

module.exports = { testDatabaseConnection };
