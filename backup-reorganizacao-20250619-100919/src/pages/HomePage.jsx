import { useState, useEffect } from 'react';
import {
  Grid,
  Typography,
  Stack,
  Box,
  Paper,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  Skeleton,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  useTheme,
  useMediaQuery,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  People as PeopleIcon,
  Apartment as ApartmentIcon,
  WarningAmber as WarningAmberIcon,
  ExpandMore as ExpandMoreIcon
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { dashboardService, sepultamentoService, clienteService } from '../services/api';
import Modal from '../components/Modal';
import { StandardContainer, StandardButton, StatsCard } from '../components/common';

const HomePage = () => {
  const [stats, setStats] = useState(null);
  const [proximasExumacoes, setProximasExumacoes] = useState([]);
  const [loading, setLoading] = useState(true);

  const [selectedCard, setSelectedCard] = useState(null);
  const [cardDetails, setCardDetails] = useState(null);
  const [detailsLoading, setDetailsLoading] = useState(false);
  const [exumacoesPrevistas, setExumacoesPrevistas] = useState(0);

  // Estados para filtro de cliente (apenas para admin)
  const [clientes, setClientes] = useState([]);
  const [selectedClienteId, setSelectedClienteId] = useState('');

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  useEffect(() => {
    if (user) {
      loadDashboardData();
      if (user.tipo_usuario === 'admin') {
        loadClientes();
      }
    }
  }, [user]);

  useEffect(() => {
    // Recarregar dados quando o filtro de cliente mudar (apenas se user já estiver carregado)
    if (user) {
      loadDashboardData();
    }
  }, [selectedClienteId, user]);

  const loadClientes = async () => {
    try {
      console.log('🔍 Carregando clientes...');
      const response = await clienteService.listar();
      console.log('📋 Clientes carregados:', response.data);
      console.log('📋 Primeiro cliente:', response.data[0]);

      setClientes(response.data);
    } catch (error) {
      console.error('❌ Erro ao carregar clientes:', error);
    }
  };

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Preparar parâmetros de filtro
      const params = {};
      if (selectedClienteId && selectedClienteId !== '' && user?.tipo_usuario === 'admin') {
        params.cliente_id = parseInt(selectedClienteId);
        console.log('📊 Carregando dados para cliente:', selectedClienteId, 'ID:', params.cliente_id);
      } else if (user?.tipo_usuario === 'admin' && (!selectedClienteId || selectedClienteId === '')) {
        console.log('📊 Carregando dados para todos os clientes (admin)');
      } else {
        console.log('📊 Carregando dados para cliente logado');
      }

      // Carregar estatísticas
      const statsResponse = await dashboardService.getStats(params);
      setStats(statsResponse.data);

      // Carregar próximas exumações
      const exumacoesResponse = await dashboardService.getProximasExumacoes(params);
      setProximasExumacoes(exumacoesResponse.data);

      // Carregar exumações previstas para próximos 30 dias
      const exumacoesPrevistasResponse = await dashboardService.getExumacoesPrevistas30Dias(params);
      setExumacoesPrevistas(exumacoesPrevistasResponse.data.total || 0);

    } catch (error) {
      console.error('Erro ao carregar dados do dashboard:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const getProgressColor = () => {
    // Sempre retorna vermelho conforme instrução
    return 'error';
  };

  const handleCardClick = async (cardType) => {
    try {
      setDetailsLoading(true);
      setSelectedCard(cardType);

      // Preparar parâmetros de filtro para os modais
      const params = {};
      if (selectedClienteId && selectedClienteId !== '' && user?.tipo_usuario === 'admin') {
        params.cliente_id = parseInt(selectedClienteId);
        console.log('📊 Carregando detalhes do modal para cliente:', selectedClienteId, 'ID:', params.cliente_id);
      } else if (user?.tipo_usuario === 'admin' && (!selectedClienteId || selectedClienteId === '')) {
        console.log('📊 Carregando detalhes do modal para todos os clientes (admin)');
      } else {
        console.log('📊 Carregando detalhes do modal para cliente logado');
      }

      if (cardType === 'sepultamentos') {
        const response = await dashboardService.getSepultamentosDetails(params);
        setCardDetails(response.data);
      } else if (cardType === 'gavetas') {
        const response = await dashboardService.getGavetasPorProdutoDetails(params);
        setCardDetails(response.data);
      } else if (cardType === 'exumacoes_previstas') {
        const response = await dashboardService.getExumacoesPrevistasDetalhes(params);
        setCardDetails(response.data);
      } else if (cardType === 'todas_exumacoes') {
        const response = await dashboardService.getTodasExumacoes30Dias(params);
        setCardDetails(response.data);
      }
    } catch (error) {
      console.error('Erro ao carregar detalhes:', error);
    } finally {
      setDetailsLoading(false);
    }
  };

  const handleCloseModal = () => {
    setSelectedCard(null);
    setCardDetails(null);
  };

  const handleClienteChange = (event) => {
    const value = event.target.value;
    console.log('🔄 Filtro cliente alterado:', value, typeof value);
    console.log('🔄 Event target:', event.target);

    // Garantir que o valor seja sempre uma string válida
    let clienteId = '';
    if (value !== undefined && value !== null) {
      clienteId = value === '' ? '' : String(value);
    }

    console.log('🔄 Cliente ID final:', clienteId);
    setSelectedClienteId(clienteId);
  };

  const handleExumar = async (sepultamentoId) => {
    try {
      if (window.confirm('Tem certeza que deseja realizar a exumação?')) {
        await sepultamentoService.exumar(sepultamentoId);
        // Recarregar dados após exumação
        loadDashboardData();
        // Recarregar detalhes do modal se estiver aberto
        if (selectedCard === 'exumacoes_previstas') {
          // Preparar parâmetros de filtro para recarregar detalhes
          const params = {};
          if (selectedClienteId && selectedClienteId !== '' && user?.tipo_usuario === 'admin') {
            params.cliente_id = parseInt(selectedClienteId);
          }
          const response = await dashboardService.getExumacoesPrevistasDetalhes(params);
          setCardDetails(response.data);
        }
        alert('Exumação realizada com sucesso!');
      }
    } catch (error) {
      console.error('Erro ao realizar exumação:', error);
      alert('Erro ao realizar exumação. Tente novamente.');
    }
  };

  const renderStatsCard = (title, value, subtitle, icon, color, progress = null, onClick = null) => {
    if (loading) {
      return (
        <Grid size={{ xs: 12, sm: 4, md: 4 }}>
          <StatsCard
            title={<Skeleton variant="text" width="60%" />}
            value={<Skeleton variant="text" width="40%" />}
            subtitle={<Skeleton variant="text" width="80%" />}
            icon={<Skeleton variant="circular" width={56} height={56} />}
            sx={{
              height: 'auto',
              minHeight: '180px',
              display: 'flex',
              flexDirection: 'column'
            }}
          />
        </Grid>
      );
    }

    const progressComponent = progress ? (
      <Box sx={{ mt: 2 }}>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
          {progress.text}
        </Typography>
        <LinearProgress
          variant="determinate"
          value={progress.value}
          color={progress.color}
          sx={{
            height: 10,
            borderRadius: 5,
            backgroundColor: 'grey.200'
          }}
        />
      </Box>
    ) : null;

    return (
      <Grid size={{ xs: 12, sm: 4, md: 4 }}>
        <StatsCard
          title={title}
          value={value}
          subtitle={subtitle}
          icon={icon}
          color={color}
          progress={progressComponent}
          onClick={onClick}
          sx={{
            height: 'auto',
            minHeight: '180px',
            display: 'flex',
            flexDirection: 'column'
          }}
        />
      </Grid>
    );
  };

  const renderExumacoesList = () => {
    if (loading) {
      return (
        <Grid size={{ xs: 12 }}>
          <Paper elevation={3} sx={{ p: 2, borderRadius: 3 }}>
            <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 2 }}>
              <Skeleton variant="circular" width={24} height={24} />
              <Skeleton variant="text" width="200px" />
            </Stack>
            {[...Array(5)].map((_, index) => (
              <Skeleton key={index} variant="rectangular" height={60} sx={{ mb: 1 }} />
            ))}
          </Paper>
        </Grid>
      );
    }

    // Mostrar apenas as primeiras 10 exumações sem filtro
    const proximasExumacoes10 = proximasExumacoes.slice(0, 10);

    if (proximasExumacoes10.length === 0) {
      return (
        <Grid size={{ xs: 12 }}>
          <Paper elevation={3} sx={{ p: 2, borderRadius: 3 }}>
            <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 2 }}>
              <WarningAmberIcon color="warning" />
              <Typography variant="h6">Próximas Exumações</Typography>
            </Stack>
            <Alert severity="info">Nenhuma exumação programada.</Alert>
          </Paper>
        </Grid>
      );
    }

    return (
      <Grid size={{ xs: 12 }}>
        <Paper elevation={3} sx={{ p: 2, borderRadius: 3 }}>
          <Stack direction="row" spacing={2} alignItems="center" justifyContent="space-between" sx={{ mb: 2 }}>
            <Stack direction="row" spacing={1} alignItems="center">
              <WarningAmberIcon color="warning" />
              <Typography variant="h6">Próximas Exumações</Typography>
            </Stack>
            <StandardButton
              variant="outlined"
              size="small"
              onClick={() => handleCardClick('todas_exumacoes')}
            >
              Ver Mais
            </StandardButton>
          </Stack>

          {isMobile ? (
            // Versão mobile com Accordion
            <Box>
              {proximasExumacoes10.map((item, index) => (
                <Accordion key={index}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%', pr: 2 }}>
                      <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                        {item.nome_sepultado}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {formatDate(item.data_exumacao)}
                      </Typography>
                    </Box>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Stack spacing={1}>
                      <Typography variant="body2">
                        <strong>Produto:</strong> {item.denominacao_produto}
                      </Typography>
                      <Typography variant="body2">
                        <strong>Data Sepultamento:</strong> {formatDate(item.data_sepultamento)}
                      </Typography>
                      <Typography variant="body2">
                        <strong>Dias Restantes:</strong> {item.dias_restantes}
                      </Typography>
                    </Stack>
                  </AccordionDetails>
                </Accordion>
              ))}
            </Box>
          ) : (
            // Versão desktop com Table
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Nome do Sepultado</TableCell>
                    <TableCell>Produto</TableCell>
                    <TableCell>Data Sepultamento</TableCell>
                    <TableCell>Data Exumação</TableCell>
                    <TableCell>Dias Restantes</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {proximasExumacoes10.map((item, index) => (
                    <TableRow
                      key={index}
                      sx={{
                        '&:nth-of-type(odd)': {
                          backgroundColor: 'action.hover'
                        },
                        '&:hover': {
                          backgroundColor: 'action.selected'
                        }
                      }}
                    >
                      <TableCell>
                        <Tooltip title={item.nome_sepultado}>
                          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                            {item.nome_sepultado}
                          </Typography>
                        </Tooltip>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {item.denominacao_produto}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Tooltip title={`Sepultado em ${formatDate(item.data_sepultamento)}`}>
                          <Typography variant="body2">
                            {formatDate(item.data_sepultamento)}
                          </Typography>
                        </Tooltip>
                      </TableCell>
                      <TableCell>
                        <Tooltip title={`Exumação prevista para ${formatDate(item.data_exumacao)}`}>
                          <Typography variant="body2">
                            {formatDate(item.data_exumacao)}
                          </Typography>
                        </Tooltip>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color={item.dias_restantes < 30 ? 'error' : 'text.primary'}>
                          {item.dias_restantes} dias
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </Paper>
      </Grid>
    );
  };

  return (
    <StandardContainer
      title="Dashboard"
      subtitle="Visão Geral"
      headerAction={
        user?.tipo_usuario === 'admin' && (
          <FormControl size="small" sx={{ minWidth: 200 }}>
            <InputLabel id="cliente-filter-label" shrink>
              Filtrar por Cliente
            </InputLabel>
            <Select
              labelId="cliente-filter-label"
              value={selectedClienteId || ''}
              onChange={handleClienteChange}
              label="Filtrar por Cliente"
              displayEmpty
              notched
              multiple={false}
              variant="outlined"
              size="small"
              renderValue={(selected) => {
                if (!selected || selected === '') {
                  return <em>Todos os Clientes</em>;
                }
                console.log('🔍 Procurando cliente com ID:', selected, 'Tipo:', typeof selected);
                console.log('🔍 Clientes disponíveis:', clientes.map(c => ({ id: c.id, nome: c.nome_fantasia })));
                const cliente = clientes.find(c => c.id === parseInt(selected));
                console.log('🔍 Cliente encontrado:', cliente);
                return cliente ? cliente.nome_fantasia : 'Cliente não encontrado';
              }}
            >
              <MenuItem value="">
                <em>Todos os Clientes</em>
              </MenuItem>
              {clientes.map((cliente) => (
                <MenuItem key={cliente.id} value={cliente.id}>
                  {cliente.nome_fantasia}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        )
      }
    >
      <Grid container spacing={3}>
        {/* Cards de Estatísticas lado a lado */}
        {renderStatsCard(
          'Total de Sepultamentos',
          stats?.total_sepultamentos || '0',
          `Taxa Estimada: ${stats?.taxa_sepultamento || '0.00'} Sepultamentos por dia`,
          <PeopleIcon />,
          'primary.main',
          null,
          () => handleCardClick('sepultamentos')
        )}

        {renderStatsCard(
          'Total de Gavetas',
          stats?.total_gavetas || '0',
          '',
          <ApartmentIcon />,
          'info.main',
          stats ? {
            text: `Ocupadas: ${stats.gavetas_ocupadas} (${((stats.gavetas_ocupadas / stats.total_gavetas) * 100).toFixed(2)}%) | Disponíveis: ${stats.gavetas_disponiveis} (${((stats.gavetas_disponiveis / stats.total_gavetas) * 100).toFixed(2)}%)`,
            value: (stats.gavetas_ocupadas / stats.total_gavetas) * 100,
            color: getProgressColor(stats.gavetas_ocupadas, stats.total_gavetas)
          } : null,
          () => handleCardClick('gavetas')
        )}

        {renderStatsCard(
          'Exumações Previstas',
          exumacoesPrevistas.toString(),
          'Quantidade prevista para os próximos 30 dias',
          <WarningAmberIcon />,
          'warning.main',
          null,
          () => handleCardClick('exumacoes_previstas')
        )}

        {/* Lista de Próximas Exumações alinhada com os cards */}
        {renderExumacoesList()}
      </Grid>

      {/* Modal de Detalhes de Sepultamentos */}
      <Modal
        isOpen={selectedCard === 'sepultamentos'}
        onClose={handleCloseModal}
        title="Detalhes de Sepultamentos por Produto"
        maxWidth="lg"
      >
        {detailsLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <Skeleton variant="rectangular" height={400} />
          </Box>
        ) : (
          <Box>
            {cardDetails && cardDetails.length > 0 ? (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Produto</TableCell>
                      <TableCell>Total de Sepultamentos</TableCell>
                      <TableCell>Taxa por Dia</TableCell>
                      <TableCell>Primeiro Sepultamento</TableCell>
                      <TableCell>Último Sepultamento</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {cardDetails.map((produto, index) => {
                      const diasDiferenca = produto.primeiro_sepultamento && produto.ultimo_sepultamento
                        ? Math.ceil((new Date(produto.ultimo_sepultamento) - new Date(produto.primeiro_sepultamento)) / (1000 * 60 * 60 * 24))
                        : 0;
                      const taxaPorDia = diasDiferenca > 0 ? (produto.total_sepultamentos / diasDiferenca).toFixed(2) : '0.00';

                      return (
                        <TableRow key={index}>
                          <TableCell>{produto.produto}</TableCell>
                          <TableCell>{produto.total_sepultamentos}</TableCell>
                          <TableCell>{taxaPorDia} por dia</TableCell>
                          <TableCell>{formatDate(produto.primeiro_sepultamento)}</TableCell>
                          <TableCell>{formatDate(produto.ultimo_sepultamento)}</TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Alert severity="info">
                Nenhum dado de sepultamento encontrado
              </Alert>
            )}
          </Box>
        )}
      </Modal>

      {/* Modal de Detalhes de Gavetas */}
      <Modal
        isOpen={selectedCard === 'gavetas'}
        onClose={handleCloseModal}
        title="Detalhes de Gavetas por Produto"
        maxWidth="lg"
      >
        {detailsLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <Skeleton variant="rectangular" height={400} />
          </Box>
        ) : (
          <Box>
            {cardDetails && cardDetails.length > 0 ? (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Produto</TableCell>
                      <TableCell>Total de Gavetas</TableCell>
                      <TableCell>Gavetas Ocupadas</TableCell>
                      <TableCell>Gavetas Disponíveis</TableCell>
                      <TableCell>Taxa de Ocupação</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {cardDetails.map((produto, index) => (
                      <TableRow key={index}>
                        <TableCell>{produto.produto}</TableCell>
                        <TableCell>{produto.total_gavetas}</TableCell>
                        <TableCell>{produto.gavetas_ocupadas}</TableCell>
                        <TableCell>{produto.gavetas_disponiveis}</TableCell>
                        <TableCell>{produto.taxa_ocupacao}%</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Alert severity="info">
                Nenhum dado de gaveta encontrado
              </Alert>
            )}
          </Box>
        )}
      </Modal>

      {/* Modal de Todas as Exumações (Ver Mais) */}
      <Modal
        isOpen={selectedCard === 'todas_exumacoes'}
        onClose={handleCloseModal}
        title="Todas as Exumações - Próximos 30 Dias"
        maxWidth="lg"
      >
        {detailsLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <Skeleton variant="rectangular" height={400} />
          </Box>
        ) : (
          <Box>
            {cardDetails && cardDetails.length > 0 ? (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Nome do Sepultado</TableCell>
                      <TableCell>Denominação do Produto</TableCell>
                      <TableCell>Denominação do Bloco</TableCell>
                      <TableCell>Número da Gaveta</TableCell>
                      <TableCell>Data Exumação</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {cardDetails.map((item, index) => (
                      <TableRow key={index}>
                        <TableCell>{item.nome_sepultado}</TableCell>
                        <TableCell>{item.denominacao_produto}</TableCell>
                        <TableCell>{item.denominacao_bloco}</TableCell>
                        <TableCell>{item.numero_gaveta}</TableCell>
                        <TableCell>{formatDate(item.data_exumacao)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Alert severity="info">
                Nenhuma exumação programada para os próximos 30 dias
              </Alert>
            )}
          </Box>
        )}
      </Modal>

      {/* Modal de Exumações Previstas (Card clicável) */}
      <Modal
        isOpen={selectedCard === 'exumacoes_previstas'}
        onClose={handleCloseModal}
        title="Exumações Previstas - Próximos e Últimos 30 Dias"
        maxWidth="lg"
      >
        {detailsLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <Skeleton variant="rectangular" height={400} />
          </Box>
        ) : (
          <Box>
            {cardDetails && cardDetails.length > 0 ? (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Nome do Sepultado</TableCell>
                      <TableCell>Denominação do Produto</TableCell>
                      <TableCell>Denominação do Bloco</TableCell>
                      <TableCell>Número da Gaveta</TableCell>
                      <TableCell>Data Exumação</TableCell>
                      <TableCell>Ações</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {cardDetails.map((item, index) => (
                      <TableRow key={index}>
                        <TableCell>{item.nome_sepultado}</TableCell>
                        <TableCell>{item.denominacao_produto}</TableCell>
                        <TableCell>{item.denominacao_bloco}</TableCell>
                        <TableCell>{item.numero_gaveta}</TableCell>
                        <TableCell>{formatDate(item.data_exumacao)}</TableCell>
                        <TableCell>
                          <StandardButton
                            variant="contained"
                            color="error"
                            size="small"
                            onClick={() => handleExumar(item.sepultamento_id)}
                          >
                            Exumar
                          </StandardButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Alert severity="info">
                Nenhuma exumação encontrada para este período
              </Alert>
            )}
          </Box>
        )}
      </Modal>
    </StandardContainer>
  );
};

export default HomePage;
