Perfeito. A partir das imagens enviadas e das suas instruções, aqui está um arquivo `instruction.md` completo, detalhado, robusto e adequado para ser lido por uma IA responsável pela reestilização e padronização do front-end do projeto utilizando Material UI.

---

# instruction.md

## Objetivo

Realizar a reestilização completa do front-end deste projeto utilizando **Material UI (MUI)**, garantindo **padronização visual, responsividade e manutenção escalável**. As diretrizes visam alinhar as interfaces às melhores práticas de UI/UX, desenvolvimento front-end moderno e escalabilidade de código.

---

## 🧠 Diretrizes Gerais

1. **Adotar Material UI como framework principal de componentes.**
2. Todo o código deverá seguir os padrões atuais do **React + TypeScript**.
3. Componentes devem ser 100% desacoplados, reaproveitáveis e facilmente testáveis.
4. Utilizar **Theme Provider do MUI** para definir estilos globais.
5. Aplicar **Design System consistente** em todo o projeto.
6. Eliminar estilos inline e CSS avulso; tudo deve ser centralizado no tema ou nos componentes.

---

## 🎨 Design System Global

### 🎯 Paleta de Cores (exemplo, adaptar conforme projeto):

* **Primária:** #1976d2
* **Secundária:** #9c27b0
* **Erro:** #d32f2f
* **Sucesso:** #388e3c
* **Background:** #f5f5f5
* **Texto Principal:** #212121
* **Texto Secundário:** #757575

> 🔥 *A IA deve consolidar a paleta com base nas cores mais frequentes nas telas fornecidas.*

---

### 🔠 Tipografia

* Utilizar a fonte padrão do MUI (`Roboto`) ou especificar outra se necessário.

| Elemento      | Tamanho | Peso    | Altura da Linha | Observações                         |
| ------------- | ------- | ------- | --------------- | ----------------------------------- |
| Título Página | 32px    | Bold    | 40px            | Centralizado ou alinhado à esquerda |
| Subtítulo     | 24px    | Medium  | 32px            |                                     |
| Seções        | 20px    | Medium  | 28px            |                                     |
| Texto Base    | 16px    | Regular | 24px            | Texto corrido e labels              |
| Legendas      | 14px    | Regular | 20px            | Descritivos menores                 |
| Botões        | 16px    | Medium  | 24px            | Caixa alta                          |

> ⚠️ **Padronizar todos os títulos e textos conforme a tabela acima, eliminando variações incoerentes entre páginas.**

---

### 🧩 Botões

* Todos os botões devem seguir a mesma padronização visual:

  * **Altura:** 48px
  * **Borda:** 8px (ou `borderRadius` do tema)
  * **Padding horizontal:** 24px
  * **Tamanho da fonte:** 16px
  * **Peso:** 600 (Medium)
  * **Texto em caixa alta**
* Estados:

  * Hover: Sombra leve e darken na cor.
  * Pressionado: Darken ainda maior.
  * Disabled: Opacidade reduzida, cursor not-allowed.
* Seguir as variantes do Material UI:

  * `variant="contained"` para ações principais.
  * `variant="outlined"` para ações secundárias.
  * `variant="text"` para ações menos importantes.

---

### 🗂️ Cards e Containers

* Bordas arredondadas: **16px**
* Sombras leves padrão MUI (`elevation={2}`)
* Padding interno dos cards: **24px**
* Margem entre componentes: **16px**
* Background padrão branco (`#FFFFFF`)

---

### 📑 Abas (Tabs)

* Padronizar todas as abas:

  * Fonte: 16px, Medium
  * Altura mínima: 48px
  * Indicador de aba ativa com **espessura de 3px**, na cor primária.
  * Abas com espaçamento horizontal adequado (padding interno mínimo de 16px).
* Estado ativo com cor primária, inativo com texto secundário.
* Consistência nas transições.

---

## 🏗️ Estrutura de Componentes

Organizar os componentes seguindo arquitetura escalável, exemplo:

```
/src
 ├── components
 │    ├── Button/
 │    ├── Card/
 │    ├── Tabs/
 │    ├── Header/
 │    └── ...
 ├── pages
 │    ├── Dashboard/
 │    ├── Relatorios/
 │    ├── Configuracoes/
 │    └── ...
 ├── hooks/
 ├── services/
 ├── contexts/
 ├── utils/
 └── theme/
```

### 🗂️ Diretório `/theme`

* Definir:

  * Paleta de cores
  * Tipografia
  * Border radius
  * Sombra padrão
  * Espaçamentos (`spacing`)

---

## 📏 Grid & Layout

* Utilizar o sistema de grid do Material UI (`Grid` e `Box`).
* Espaçamento padrão entre seções: **24px**
* Margens laterais em telas: **32px desktop**, **16px mobile**
* Padding interno dos componentes principais: **24px**

---

## 🪟 Responsividade

* Aplicar `breakpoints` do MUI:

  * xs (mobile)
  * sm (tablet)
  * md (desktop pequeno)
  * lg (desktop grande)
  * xl (telas muito grandes)
* Todos os componentes devem ser **100% responsivos**.

---

## 🔍 Observações Específicas Encontradas nas Telas

1. **Inconsistência nas tipografias**:

   * Alguns títulos estão muito grandes, outros pequenos. Uniformizar todos os títulos conforme especificado.

2. **Botões desalinhados e com estilos diferentes**:

   * Corrigir para que todos os botões tenham tamanho, borda e fonte consistentes.

3. **Títulos de abas despadronizados**:

   * Alguns estão em caixa baixa, outros em caixa alta, com espaçamentos e fontes distintos. Padronizar.

4. **Formulários**:

   * Inputs devem ter altura mínima de **48px**, labels consistentes, placeholder legível e alinhamento correto.

5. **Falta de alinhamento visual em alguns componentes**:

   * Usar o sistema de Grid e Box para garantir consistência horizontal e vertical.

---

## 🚀 Melhores Práticas de Código

* Componentização máxima.
* Remoção de código morto, console logs e estilos inline.
* Tipagem estrita com TypeScript.
* Validação de props utilizando TypeScript.
* Uso consistente do `sx` ou classes estilizadas com `styled()` do MUI.
* Separar lógica de interface (hooks, contexts e services).

---

## ✅ Checklist Final

* [ ] Todos os textos, títulos e labels padronizados.
* [ ] Todos os botões seguem o mesmo tamanho, cor, fonte e comportamento.
* [ ] Abas com mesma tipografia, espaçamento e indicador.
* [ ] Inputs e formulários consistentes.
* [ ] Cards, containers e seções alinhados visualmente.
* [ ] Tema centralizado com Material UI ThemeProvider.
* [ ] Projeto 100% responsivo.
* [ ] Código organizado, escalável e consistente.

---