Lógica de funcionamento do sistema.

Toda vez que eu crio um cliente, adiciono um codigo_cliente.

Toda vez que crio um produto, adiciono um codigo_estacao e preciso, obrigatoriamente, selecionar o cliente ao qual este produto pertence (ou seja, ele está associado à um codigo_cliente).

Toda vez que crio um bloco, adiciono um codigo_bloco e preciso, obrigatoriamente, selecionar o codigo_estacao ao qual este bloco pertence (ou seja, ele está associado à um codigo_estacao e, consequentemente, ele está associado à um codigo_cliente).

Toda vez que crio um subbloco, adiciono um codigo_sub_bloco e preciso, obrigatoriamente, selecionar o codigo_bloco, ao qual este subbloco pertence (ou seja, ele está associado à um codigo_bloco e consequentemente está associado à um codigo_estacao e, consequentemente, ele está associado à um codigo_cliente).

Ao cadastrar um sub_bloco, também devo sempre estar criando os ranges ao qual ele irá abrangir em numerações. Por exemplo: 1 até 10, 20 até 25, 50 até 80. Eu não posso, jamais, ter numeração repetida entre os sub_bloco que estão associados à um codigo_bloco, que está associado à um codigo_estacao, que está associado à codigo_cliente.

Por exemplo:

Dados do Cliente 1 e Produto 1
codigo_cliente = ITV_001
codigo_estacao = ETEN_001
codigo_bloco = BL_001 e BL_002
codigo_sub = SUB_001 e SUB_002

Dados do Cliente 2 e Produto 1
codigo_cliente = PTB_001
codigo_estacao = ETEN_001
codigo_bloco = BL_001 e BL_002
código_sub = SUB_001 e SUB_002

Caso 1: Eu já tenha cadastrado no SUB_001, do BL_001 do ETEN_001 do ITV_001, os ranges de 1-10, logo, eu posso cadastrar no SUB_002 do ETEN_001 do ITV_001 os ranges de 11-20. Caso eu tente cadastrar no SUB_002 os ranges de 10-20, eu não poderei criar devido já existir uma gaveta Nº10 criada no SUB_001.

Caso 2: Eu já tenha cadastrado no SUB_001, do BL_001 do ETEN_001 do ITV_001, os ranges de 1-10, logo, eu também posso cadastrar no SUB_001, do BL_001 do ETEN_001 do PTB_001 o range de 1-10 também. Pois o codigo_cliente é diferente.

Caso 3: Suponha que criei um ETEN_002 para ITV_001 e eu já tenha cadastrado no SUB_001, do BL_001 do ETEN_002 do ITV_001, os ranges de 1-10, logo, eu também posso cadastrar no SUB_001, do BL_001 do ETEN_001 do PTB_001 o range de 1-10 também. Pois o codigo_estacao é diferente.

Caso 4: Eu já tenha cadastrado no SUB_001, do BL_001 do ETEN_001 do ITV_001, os ranges de 1-10, logo, eu também posso cadastrar no SUB_001, do BL_002 do ETEN_001 do ITV_001 o range de 1-10 também. Pois o codigo_bloco é diferente.