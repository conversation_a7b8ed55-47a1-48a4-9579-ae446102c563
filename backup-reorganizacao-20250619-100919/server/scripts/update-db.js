const { Pool } = require('pg');
require('dotenv').config();

// Configuração do banco de dados
const pool = new Pool({
  host: process.env.DB_HOST || '************',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'dbetens',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'ab3780bd73ee4e2804d566ce6fd96209',
  ssl: false,
});

async function updateDatabase() {
  try {
    console.log('🔄 Atualizando estrutura do banco de dados...');

    // Adicionar campo produtos_acesso na tabela usuarios se não existir
    console.log('📋 Verificando campo produtos_acesso...');
    const checkColumn = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'usuarios' 
      AND column_name = 'produtos_acesso'
    `);

    if (checkColumn.rows.length === 0) {
      console.log('➕ Adicionando campo produtos_acesso...');
      await pool.query(`
        ALTER TABLE usuarios 
        ADD COLUMN produtos_acesso JSONB
      `);
      console.log('✅ Campo produtos_acesso adicionado');
    } else {
      console.log('✅ Campo produtos_acesso já existe');
    }

    console.log('✅ Banco de dados atualizado com sucesso!');
    
  } catch (error) {
    console.error('❌ Erro ao atualizar banco de dados:', error);
    process.exit(1);
  } finally {
    await pool.end();
    console.log('🔌 Conexão encerrada');
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  updateDatabase();
}

module.exports = { updateDatabase };
