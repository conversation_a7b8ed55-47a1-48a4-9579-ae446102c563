#!/bin/bash

# Script de Deploy da Padronização do Portal Cliente
# Este script limpa imagens antigas, constrói novas imagens e atualiza o Docker Swarm

set -e  # Parar execução em caso de erro

echo "🚀 Iniciando deploy da padronização do Portal Cliente..."

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log colorido
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Verificar se estamos no diretório correto
if [ ! -f "portalcliente/package.json" ]; then
    log_error "Arquivo portalcliente/package.json não encontrado. Execute este script no diretório raiz do projeto."
    exit 1
fi

# Verificar se Docker está rodando
if ! docker info > /dev/null 2>&1; then
    log_error "Docker não está rodando. Inicie o Docker e tente novamente."
    exit 1
fi

# Verificar se Docker Swarm está ativo
if ! docker node ls > /dev/null 2>&1; then
    log_error "Docker Swarm não está ativo. Inicialize o swarm primeiro."
    exit 1
fi

log_info "Verificações iniciais concluídas com sucesso"

# 1. Parar serviços relacionados ao portal (se existirem)
log_info "Parando serviços do portal..."
docker service ls --format "table {{.Name}}" | grep -E "(portal|cliente)" | while read service; do
    if [ ! -z "$service" ]; then
        log_warning "Parando serviço: $service"
        docker service rm "$service" || true
    fi
done

# Aguardar um pouco para os serviços pararem completamente
sleep 5

# 2. Limpar imagens antigas do portal
log_info "Limpando imagens antigas do portal..."

# Remover containers parados
docker container prune -f || true

# Remover imagens não utilizadas
docker image prune -f || true

# Remover imagens específicas do portal (se existirem)
docker images --format "table {{.Repository}}:{{.Tag}}" | grep -E "(portal|cliente)" | while read image; do
    if [ ! -z "$image" ] && [ "$image" != "REPOSITORY:TAG" ]; then
        log_warning "Removendo imagem: $image"
        docker rmi "$image" -f || true
    fi
done

log_success "Limpeza de imagens concluída"

# 3. Construir nova imagem do frontend
log_info "Construindo nova imagem do frontend..."

# Construir imagem Docker (o Dockerfile já faz o build da aplicação)
log_info "Construindo imagem Docker do frontend..."
docker build -t portalevo-frontend:latest portalcliente/

log_success "Imagem do frontend construída com sucesso"

# 4. Verificar se existe docker-compose.yml ou stack file
STACK_FILE=""
if [ -f "docker-compose.yml" ]; then
    STACK_FILE="docker-compose.yml"
elif [ -f "docker-stack.yml" ]; then
    STACK_FILE="docker-stack.yml"
elif [ -f "stack.yml" ]; then
    STACK_FILE="stack.yml"
fi

if [ -z "$STACK_FILE" ]; then
    log_warning "Nenhum arquivo de stack encontrado. Criando um básico..."
    
    # Criar um docker-compose.yml básico se não existir
    cat > docker-compose.yml << EOF
version: '3.8'

services:
  portalevo-frontend:
    image: portalevo-frontend:latest
    ports:
      - "3000:80"
    networks:
      - redeinterna
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.portal.rule=Host(\`portal.evo-eden.site\`)"
        - "traefik.http.routers.portal.entrypoints=websecure"
        - "traefik.http.routers.portal.tls.certresolver=letsencrypt"
        - "traefik.http.services.portal.loadbalancer.server.port=80"

networks:
  redeinterna:
    external: true
EOF
    STACK_FILE="docker-compose.yml"
    log_success "Arquivo docker-compose.yml criado"
fi

# 5. Deploy no Docker Swarm
log_info "Fazendo deploy no Docker Swarm..."
docker stack deploy -c "$STACK_FILE" portalevo

log_success "Deploy realizado com sucesso!"

# 6. Verificar status dos serviços
log_info "Verificando status dos serviços..."
sleep 10

docker service ls | grep portalevo

# 7. Mostrar logs dos serviços (últimas 20 linhas)
log_info "Últimos logs dos serviços:"
docker service logs --tail 20 portalevo_portalevo-frontend 2>/dev/null || log_warning "Não foi possível obter logs do serviço"

# 8. Limpeza final
log_info "Executando limpeza final..."
docker system prune -f || true

log_success "🎉 Deploy da padronização concluído com sucesso!"
log_info "Portal disponível em: https://portal.evo-eden.site"
log_info "Para verificar logs: docker service logs portalevo_portalevo-frontend"
log_info "Para verificar status: docker service ls"

echo ""
echo "📋 Resumo das mudanças implementadas:"
echo "✅ Tema global padronizado com Material-UI"
echo "✅ Componentes comuns reutilizáveis criados"
echo "✅ Todas as páginas padronizadas"
echo "✅ Tabelas, formulários e modais uniformizados"
echo "✅ Header e navegação atualizados"
echo "✅ Responsividade melhorada"
echo "✅ Loading states implementados"
echo ""
echo "🔧 Para futuras atualizações, use este mesmo script."
