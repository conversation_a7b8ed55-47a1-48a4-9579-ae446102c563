import React, { useState, useEffect } from 'react';
import {
  Grid,
  Typography,
  Box,
  Chip,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Alert,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
} from '@mui/icons-material';
import { produtoService, sepultamentoService } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import ProdutoModal from '../components/ProdutoModal';
import SepultamentoModal from '../components/SepultamentoModal';
import Modal from '../components/Modal';
import { StandardContainer, StandardButton, StandardCard, StandardTabs } from '../components/common';

// Componentes removidos - usando Material-UI

// Todos os styled-components removidos - usando Material-UI

const DashboardGeralPage = () => {
  const [produtos, setProdutos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedProduto, setSelectedProduto] = useState(null);
  const [showProdutoModal, setShowProdutoModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showSepultamentoModal, setShowSepultamentoModal] = useState(false);
  const [sepultamentosProduto, setSepultamentosProduto] = useState([]);
  const [selectedSepultamento, setSelectedSepultamento] = useState(null);
  const [loadingSepultamentos, setLoadingSepultamentos] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const { canEditProdutos, user } = useAuth();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  useEffect(() => {
    loadProdutos();
  }, []);

  const loadProdutos = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await produtoService.estatisticas();
      setProdutos(response.data);
    } catch (error) {
      console.error('Erro ao carregar produtos:', error);
      setError('Erro ao carregar produtos');
    } finally {
      setLoading(false);
    }
  };

  const handleAddProduct = () => {
    setSelectedProduto(null);
    setShowProdutoModal(true);
  };

  const handleEditProduct = (produto) => {
    setSelectedProduto(produto);
    setShowProdutoModal(true);
  };

  const handleViewDetails = (produto) => {
    setSelectedProduto(produto);
    setShowDetailModal(true);
    // Carregar sepultamentos automaticamente usando codigo_estacao
    loadSepultamentos(produto.codigo_estacao);
  };

  const loadSepultamentos = async (codigoEstacao) => {
    try {
      setLoadingSepultamentos(true);
      console.log('🔍 Carregando sepultamentos para estação:', codigoEstacao);
      const response = await sepultamentoService.listar({
        codigo_estacao: codigoEstacao,
        status_exumacao: false,
        ativo: true
      });
      setSepultamentosProduto(response.data || []);
    } catch (error) {
      console.error('❌ Erro ao carregar sepultamentos:', error);
      alert('Erro ao carregar sepultamentos do produto');
    } finally {
      setLoadingSepultamentos(false);
    }
  };



  const handleAddSepultamento = () => {
    setSelectedSepultamento(null);
    setShowSepultamentoModal(true);
  };

  const handleEditSepultamento = (sepultamento) => {
    setSelectedSepultamento(sepultamento);
    setShowSepultamentoModal(true);
  };

  const handleDeleteSepultamento = async (sepultamento) => {
    const motivo = prompt('Informe o motivo da deleção:');
    if (!motivo) return;

    if (window.confirm(`Tem certeza que deseja deletar o sepultamento de "${sepultamento.nome_sepultado}"?`)) {
      try {
        await sepultamentoService.deletar(sepultamento.id, { motivo_delecao: motivo });
        loadSepultamentos(selectedProduto.codigo_estacao);
        alert('Sepultamento deletado com sucesso');
      } catch (error) {
        console.error('Erro ao deletar sepultamento:', error);
        alert('Erro ao deletar sepultamento');
      }
    }
  };

  const handleExumarSepultamento = async (sepultamento) => {
    const observacoes = prompt('Observações sobre a exumação:');

    if (window.confirm(`Tem certeza que deseja exumar o sepultamento de "${sepultamento.nome_sepultado}"?`)) {
      try {
        await sepultamentoService.exumar(sepultamento.id, {
          data_exumacao: new Date().toISOString().split('T')[0],
          horario_exumacao: new Date().toTimeString().split(' ')[0],
          observacoes_exumacao: observacoes || ''
        });
        loadSepultamentos(selectedProduto.codigo_estacao);
        alert('Exumação realizada com sucesso');
      } catch (error) {
        console.error('Erro ao exumar sepultamento:', error);
        alert('Erro ao realizar exumação');
      }
    }
  };

  const handleSepultamentoSuccess = () => {
    setShowSepultamentoModal(false);
    if (selectedProduto) {
      loadSepultamentos(selectedProduto.codigo_estacao);
    }
  };



  if (loading) {
    return (
      <StandardContainer title="Produtos" subtitle="Gerenciamento de produtos">
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
          <CircularProgress size={40} />
        </Box>
      </StandardContainer>
    );
  }

  if (error) {
    return (
      <StandardContainer title="Produtos" subtitle="Gerenciamento de produtos">
        <Alert severity="error" sx={{ textAlign: 'center' }}>
          {error}
        </Alert>
      </StandardContainer>
    );
  }

  return (
    <StandardContainer
      title="Produtos"
      subtitle="Gerenciamento de produtos"
      headerAction={
        canEditProdutos() && (
          <StandardButton
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAddProduto}
          >
            Novo Produto
          </StandardButton>
        )
      }
    >
      {produtos.length === 0 ? (
        <StandardCard sx={{ textAlign: 'center', py: 8 }}>
          <Typography variant="h1" sx={{ fontSize: '3rem', mb: 2 }}>🏢</Typography>
          <Typography variant="h5" sx={{ mb: 1, color: 'text.primary' }}>
            Nenhum produto encontrado
          </Typography>
          <Typography variant="body1" color="text.secondary">
            {canEditProdutos() ? 'Comece criando o primeiro produto.' : 'Nenhum produto disponível para sua conta.'}
          </Typography>
        </StandardCard>
      ) : (
        <Grid container spacing={3}>
          {produtos.map((produto) => (
            <Grid size={{ xs: 12, md: 6, lg: 4 }} key={produto.id}>
              <StandardCard
                clickable
                onClick={() => handleViewDetails(produto)}
                sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}
              >
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Typography variant="h6" sx={{ flex: 1, fontWeight: 600 }}>
                    {produto.denominacao}
                  </Typography>
                  <Chip
                    label={produto.codigo_estacao}
                    color="primary"
                    size="small"
                    sx={{ ml: 1 }}
                  />
                </Box>

                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                    Cliente: {produto.nome_cliente || produto.codigo_cliente}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                    Meses para Exumar: {produto.meses_para_exumar} meses
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="body2" color="text.secondary">
                      Status:
                    </Typography>
                    <Chip
                      label={produto.ativo ? 'Ativo' : 'Inativo'}
                      color={produto.ativo ? 'success' : 'error'}
                      size="small"
                    />
                  </Box>
                </Box>

                <Grid container spacing={1} sx={{ mb: 2 }}>
                  <Grid size={{ xs: 4 }}>
                    <Paper sx={{ p: 1.5, textAlign: 'center', backgroundColor: 'grey.50' }}>
                      <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                        {produto.total_blocos || 0}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Blocos
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid size={{ xs: 4 }}>
                    <Paper sx={{ p: 1.5, textAlign: 'center', backgroundColor: 'grey.50' }}>
                      <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                        {produto.total_sub_blocos || 0}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Sub-blocos
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid size={{ xs: 4 }}>
                    <Paper sx={{ p: 1.5, textAlign: 'center', backgroundColor: 'grey.50' }}>
                      <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                        {produto.total_gavetas || 0}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Gavetas
                      </Typography>
                    </Paper>
                  </Grid>
                </Grid>

                <Box sx={{ mt: 'auto' }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">Ocupação</Typography>
                    <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                      {produto.percentual_ocupacao || 0}%
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={produto.percentual_ocupacao || 0}
                    color="success"
                    sx={{
                      height: 8,
                      borderRadius: 4,
                      backgroundColor: 'grey.200',
                      mb: 1,
                    }}
                  />
                  <Typography variant="caption" color="text.secondary" sx={{ textAlign: 'center', display: 'block' }}>
                    {produto.gavetas_ocupadas || 0} ocupadas de {produto.total_gavetas || 0} gavetas
                  </Typography>
                </Box>
              </StandardCard>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Modal de Produto */}
      <ProdutoModal
        isOpen={showProdutoModal}
        onClose={() => setShowProdutoModal(false)}
        produto={selectedProduto}
        onSuccess={loadProdutos}
      />

      {/* Modal de Lista de Sepultados */}
      <Modal
        isOpen={showDetailModal}
        onClose={() => setShowDetailModal(false)}
        title={`Lista de Sepultados - ${selectedProduto?.denominacao}`}
        maxWidth="1200px"
      >
        {selectedProduto && (
          <Box sx={{ maxHeight: '70vh', overflow: 'auto' }}>
            <Box sx={{ mb: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Sepultamentos ({sepultamentosProduto.length})
                </Typography>
                <StandardButton
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleAddSepultamento}
                  size="small"
                >
                  Cadastrar Novo Sepultamento
                </StandardButton>
              </Box>

              {loadingSepultamentos ? (
                <Box sx={{ textAlign: 'center', py: 6 }}>
                  <CircularProgress />
                  <Typography variant="body2" sx={{ mt: 2 }}>
                    Carregando sepultamentos...
                  </Typography>
                </Box>
              ) : sepultamentosProduto.length === 0 ? (
                <Box sx={{ textAlign: 'center', py: 6 }}>
                  <Typography variant="body1" color="text.secondary">
                    Nenhum sepultamento encontrado para esta estação.
                  </Typography>
                </Box>
              ) : (
                <TableContainer component={Paper} sx={{ mt: 2 }}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Nome do Sepultado</TableCell>
                        <TableCell>Denominação do Bloco</TableCell>
                        <TableCell>Número da Gaveta</TableCell>
                        <TableCell>Data e Hora</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Ações</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {sepultamentosProduto.map((sepultamento) => (
                        <TableRow key={sepultamento.id} hover>
                          <TableCell>{sepultamento.nome_sepultado}</TableCell>
                          <TableCell>{sepultamento.denominacao_bloco}</TableCell>
                          <TableCell>{sepultamento.numero_gaveta}</TableCell>
                          <TableCell>
                            {(() => {
                              // CORREÇÃO TIMEZONE: Usar split para evitar problema de fuso horário
                              const dateParts = sepultamento.data_sepultamento.split('T')[0].split('-');
                              if (dateParts.length === 3) {
                                const year = parseInt(dateParts[0]);
                                const month = parseInt(dateParts[1]) - 1;
                                const day = parseInt(dateParts[2]);
                                const localDate = new Date(year, month, day);
                                return localDate.toLocaleDateString('pt-BR');
                              }
                              return new Date(sepultamento.data_sepultamento).toLocaleDateString('pt-BR');
                            })()}
                            {sepultamento.horario_sepultamento && ` às ${sepultamento.horario_sepultamento}`}
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={sepultamento.status_exumacao ? 'Exumado' : 'Sepultado'}
                              color={sepultamento.status_exumacao ? 'error' : 'success'}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <StandardButton
                                variant="outlined"
                                color="warning"
                                size="small"
                                startIcon={<EditIcon />}
                                onClick={() => handleEditSepultamento(sepultamento)}
                                disabled={user?.tipo_usuario !== 'admin' && sepultamento.data_exumacao}
                              >
                                Editar
                              </StandardButton>
                              {!sepultamento.data_exumacao && (
                                <>
                                  <StandardButton
                                    variant="outlined"
                                    color="secondary"
                                    size="small"
                                    onClick={() => handleExumarSepultamento(sepultamento)}
                                  >
                                    Exumar
                                  </StandardButton>
                                  <StandardButton
                                    variant="outlined"
                                    color="error"
                                    size="small"
                                    startIcon={<DeleteIcon />}
                                    onClick={() => handleDeleteSepultamento(sepultamento)}
                                  >
                                    Deletar
                                  </StandardButton>
                                </>
                              )}
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </Box>
          </Box>
        )}
      </Modal>

      {/* Modal de Sepultamento */}
      <SepultamentoModal
        isOpen={showSepultamentoModal}
        onClose={() => setShowSepultamentoModal(false)}
        sepultamento={selectedSepultamento}
        produtoSelecionado={selectedProduto}
        onSuccess={handleSepultamentoSuccess}
      />
    </StandardContainer>
  );
};

export default DashboardGeralPage;
