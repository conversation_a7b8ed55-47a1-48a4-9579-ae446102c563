const { Pool } = require('pg');
require('dotenv').config();

// Configuração do banco de dados
const pool = new Pool({
  host: process.env.DB_HOST || '************',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'dbetens',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'ab3780bd73ee4e2804d566ce6fd96209',
  ssl: false,
});

async function seedDatabase() {
  try {
    console.log('🌱 Inserindo dados de exemplo...');

    // Inserir clientes de exemplo
    console.log('👥 Criando clientes...');
    await pool.query(`
      INSERT INTO clientes (codigo_cliente, nome, email, telefone, endereco) VALUES
      ('CLI001', 'Cemitério Municipal São João', '<EMAIL>', '(11) 3456-7890', '<PERSON><PERSON>, 123 - <PERSON> Paulo/SP'),
      ('CLI002', '<PERSON><PERSON><PERSON><PERSON>', '<EMAIL>', '(11) 2345-6789', 'Av. da Saudade, 456 - São Paulo/SP'),
      ('CLI003', 'Memorial Jardim Eterno', '<EMAIL>', '(11) 4567-8901', 'Rua do Descanso, 789 - São Paulo/SP')
      ON CONFLICT (codigo_cliente) DO NOTHING
    `);

    // Inserir produtos (ETENs e Ossuários)
    console.log('🏢 Criando produtos...');
    await pool.query(`
      INSERT INTO produtos (codigo_cliente, nome, tipo, descricao, localizacao) VALUES
      ('CLI001', 'ETEN Central', 'ETEN', 'Estação de Tratamento de Efluentes principal', 'Setor A - Área Central'),
      ('CLI001', 'Ossuário Norte', 'OSSUARIO', 'Ossuário para múltiplos sepultamentos', 'Setor B - Área Norte'),
      ('CLI002', 'ETEN Leste', 'ETEN', 'Estação de Tratamento de Efluentes do setor leste', 'Setor C - Área Leste'),
      ('CLI002', 'Ossuário Sul', 'OSSUARIO', 'Ossuário familiar', 'Setor D - Área Sul'),
      ('CLI003', 'ETEN Principal', 'ETEN', 'Estação principal do memorial', 'Área Central')
      ON CONFLICT DO NOTHING
    `);

    // Inserir blocos
    console.log('🧱 Criando blocos...');
    const produtos = await pool.query('SELECT id, codigo_cliente, nome FROM produtos ORDER BY id');
    
    for (const produto of produtos.rows) {
      await pool.query(`
        INSERT INTO blocos (produto_id, codigo_bloco, nome, descricao) VALUES
        ($1, 'BL01', 'Bloco A', 'Primeiro bloco do ${produto.nome}'),
        ($1, 'BL02', 'Bloco B', 'Segundo bloco do ${produto.nome}')
        ON CONFLICT (produto_id, codigo_bloco) DO NOTHING
      `, [produto.id]);
    }

    // Inserir sub-blocos
    console.log('📦 Criando sub-blocos...');
    const blocos = await pool.query('SELECT id, codigo_bloco FROM blocos ORDER BY id');
    
    for (const bloco of blocos.rows) {
      await pool.query(`
        INSERT INTO sub_blocos (bloco_id, codigo_sub_bloco, nome, descricao) VALUES
        ($1, 'SB01', 'Sub-bloco 1', 'Primeira seção do ${bloco.codigo_bloco}'),
        ($1, 'SB02', 'Sub-bloco 2', 'Segunda seção do ${bloco.codigo_bloco}')
        ON CONFLICT (bloco_id, codigo_sub_bloco) DO NOTHING
      `, [bloco.id]);
    }

    // Inserir gavetas
    console.log('🗃️ Criando gavetas...');
    const subBlocos = await pool.query('SELECT id FROM sub_blocos ORDER BY id');
    
    for (const subBloco of subBlocos.rows) {
      // Criar uma grade 5x4 de gavetas para cada sub-bloco
      for (let y = 1; y <= 4; y++) {
        for (let x = 1; x <= 5; x++) {
          const numeroGaveta = (y - 1) * 5 + x;
          await pool.query(`
            INSERT INTO gavetas (sub_bloco_id, numero_gaveta, posicao_x, posicao_y, altura_especial, disponivel) VALUES
            ($1, $2, $3, $4, $5, $6)
            ON CONFLICT (sub_bloco_id, numero_gaveta) DO NOTHING
          `, [subBloco.id, numeroGaveta, x, y, 1.0, true]);
        }
      }
    }

    // Inserir alguns sepultamentos de exemplo
    console.log('⚰️ Criando sepultamentos de exemplo...');
    const gavetas = await pool.query(`
      SELECT g.id, g.numero_gaveta, sb.codigo_sub_bloco, b.codigo_bloco, p.codigo_cliente
      FROM gavetas g
      JOIN sub_blocos sb ON g.sub_bloco_id = sb.id
      JOIN blocos b ON sb.bloco_id = b.id
      JOIN produtos p ON b.produto_id = p.id
      WHERE g.disponivel = true
      ORDER BY g.id
      LIMIT 10
    `);

    const nomes = [
      'João Silva Santos', 'Maria Oliveira Costa', 'José Pereira Lima',
      'Ana Paula Rodrigues', 'Carlos Eduardo Souza', 'Francisca Alves',
      'Antonio Carlos Ferreira', 'Rosa Maria Santos', 'Pedro Henrique Lima',
      'Isabel Cristina Oliveira'
    ];

    for (let i = 0; i < gavetas.rows.length && i < nomes.length; i++) {
      const gaveta = gavetas.rows[i];
      const dataBase = new Date('2024-01-01');
      const dataSepultamento = new Date(dataBase.getTime() + (i * 30 * 24 * 60 * 60 * 1000)); // 30 dias de diferença

      await pool.query(`
        INSERT INTO sepultamentos (
          gaveta_id, nome_sepultado, codigo_cliente, codigo_bloco, codigo_sub_bloco, 
          numero_gaveta, posicao, localizacao, data_sepultamento, observacoes
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        ON CONFLICT DO NOTHING
      `, [
        gaveta.id,
        nomes[i],
        gaveta.codigo_cliente,
        gaveta.codigo_bloco,
        gaveta.codigo_sub_bloco,
        gaveta.numero_gaveta,
        1,
        `Gaveta ${gaveta.numero_gaveta} - ${gaveta.codigo_sub_bloco}`,
        dataSepultamento.toISOString(),
        'Sepultamento de exemplo criado pelo sistema'
      ]);

      // Marcar gaveta como ocupada
      await pool.query('UPDATE gavetas SET disponivel = false WHERE id = $1', [gaveta.id]);
    }

    // Criar um usuário cliente de exemplo
    console.log('👤 Criando usuário cliente...');
    const bcrypt = require('bcryptjs');
    const senhaHash = await bcrypt.hash('cliente123', 10);
    
    await pool.query(`
      INSERT INTO usuarios (email, senha, tipo_usuario, codigo_cliente, nome) VALUES
      ('<EMAIL>', $1, 'cliente', 'CLI001', 'Cliente Teste')
      ON CONFLICT (email) DO NOTHING
    `, [senhaHash]);

    console.log('✅ Dados de exemplo inseridos com sucesso!');
    console.log('');
    console.log('📋 Resumo dos dados criados:');
    
    // Mostrar estatísticas
    const stats = await pool.query(`
      SELECT 
        (SELECT COUNT(*) FROM clientes) as clientes,
        (SELECT COUNT(*) FROM produtos) as produtos,
        (SELECT COUNT(*) FROM blocos) as blocos,
        (SELECT COUNT(*) FROM sub_blocos) as sub_blocos,
        (SELECT COUNT(*) FROM gavetas) as gavetas,
        (SELECT COUNT(*) FROM sepultamentos) as sepultamentos,
        (SELECT COUNT(*) FROM usuarios WHERE tipo_usuario = 'cliente') as usuarios_cliente
    `);

    const stat = stats.rows[0];
    console.log(`  - ${stat.clientes} clientes`);
    console.log(`  - ${stat.produtos} produtos`);
    console.log(`  - ${stat.blocos} blocos`);
    console.log(`  - ${stat.sub_blocos} sub-blocos`);
    console.log(`  - ${stat.gavetas} gavetas`);
    console.log(`  - ${stat.sepultamentos} sepultamentos`);
    console.log(`  - ${stat.usuarios_cliente} usuários cliente`);
    console.log('');
    console.log('🔑 Credenciais de teste:');
    console.log('  Admin: admin / adminnbr5410!');
    console.log('  Cliente: <EMAIL> / cliente123');

  } catch (error) {
    console.error('❌ Erro ao inserir dados:', error);
    process.exit(1);
  } finally {
    await pool.end();
    console.log('🔌 Conexão encerrada');
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  seedDatabase();
}

module.exports = { seedDatabase };
