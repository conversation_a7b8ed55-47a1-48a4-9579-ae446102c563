const https = require('https');

// Função para fazer requisição
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const req = https.request(url, options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(data);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });
    
    req.on('error', reject);
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function testEstacao() {
  try {
    console.log('🔍 Testando login...');
    
    // Login
    const loginResponse = await makeRequest('https://portal.evo-eden.site/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: 'admin',
        senha: 'adminnbr5410!'
      })
    });
    
    console.log('Login status:', loginResponse.status);
    
    if (loginResponse.status !== 200) {
      console.log('❌ Erro no login:', loginResponse.data);
      return;
    }
    
    const token = loginResponse.data.token;
    console.log('✅ Login realizado com sucesso');
    
    // Testar rota de estação
    console.log('🔍 Testando rota de estação...');
    
    const estacaoResponse = await makeRequest('https://portal.evo-eden.site/api/sepultamentos/estacao/ETEN_002', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('Estação status:', estacaoResponse.status);
    
    if (estacaoResponse.status === 200) {
      console.log('✅ Rota funcionando! Registros encontrados:', estacaoResponse.data.length);
      console.log('Primeiro registro:', estacaoResponse.data[0]);
    } else {
      console.log('❌ Erro na rota:', estacaoResponse.data);
    }
    
  } catch (error) {
    console.error('❌ Erro:', error.message);
  }
}

testEstacao();
